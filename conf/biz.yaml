client:
  funds: 10.0.0.205:5200
  pay_center:
    endpoint: discovery:///bv_pay_center.grpc
    timeout: 5s
  client_portal:
    endpoint: 10.0.0.205:19999
    timeout: 5s
rocketMq:
  topic_task_name: "vh_task"
  group_task_name: "vh_task_consumer"
  tag_task_name: "vh_task"

loginConf:
  secret: "7BVVnOvpFrjpkxzcTQobd9MGjQZ4g0VYb9ENmF_G9w0"

tencentOss:
  bucket_url: https://bv-hk-vision-hub-1306172567.cos.accelerate.myqcloud.com
  bucket_base_url: https://bv-hk-vision-hub-1306172567.cos.ap-hongkong.myqcloud.com
  secret_id: AKIDDG2YnnX4KJ1SdhW4LL5ZonVw6zbw1pMc
  secret_key: 3pKYV8VbQxEFzSdXAWbPwCBtKVO0LPCj
  region: ap-hongkong
  scheme: https
  appid: "1306172567"
  bucket_name: "bv-hk-vision-hub-1306172567"
  # 上传路径前缀
  prefix: /storage/vision_hub/
  cdn_name: "https://vision-hub-global.bluemediacdn.com"

feishu:
  login_app_id: "cli_a7315f7cd138500b"
  login_app_secret: "vQqAtsFIye5FtDuK8uC2AdTO6NtXIsmR"
  get_app_access_token_url: "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
  get_user_access_token_url: "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
  get_user_info_url: "https://open.feishu.cn/open-apis/authen/v1/user_info"
  business_chat_id: "oc_28ae4cd83154b055d23f6d3a55bad86c"

tiktokVideo:
  base_url: "https://business-api.tiktok.com/open_api"
  token: "55d9ef2dc5abb7941ca9cc243aff998298f0fa95"
  advertiser_id: "7486729743866560529"

tiktokToken:
  bf: "ba788a5b17ca2f99aa8fc0b74367ebe2d9a6e271"
  biz: "bf8c165dd6a1ca2446f5c4250dd1af4684c99c1b"
  biz1: "2f09b34cde04dfd0afc615e6ec0386c87eee69c0"
  biz2: "46094285fbb7ec763b15820106926df1103ecae9"
  biz3: "ea6a01396b52c7e3a455fc5b72902fb6f255a8a9"
  biz4: "55d9ef2dc5abb7941ca9cc243aff998298f0fa95"
  biz5: "0948a61f8fe4f7aabfb4e6007b986737622032cc"
  biz6: "2ca9c8c7088d58c3020af8e6eb63f73e5b81c9ff"
  biz7: "9ce7c611902a77b272a36fd41d4f7fd41b8fd40a"

tiktokOAuth:
  app_id: "7517109773217890305"
  secret: "f5dffa501b7dba3f653cd8ad5530b03b42e03972"
  base_url: "https://business-api.tiktok.com/open_api"

sysConf:
  proxy: "http://127.0.0.1:7890"
  env: "【dev】"