genrule(
  name = "build_gen",
  outs = ["build_gen.sh"],
  executable = True,
  srcs = glob(["run_build_gen.sh"]),
  cmd = """
    if test -z \"$(SRCS)\"; then
      cat <<EOD > $@
#!/bin/sh
echo build_gen needs to be run internally, not on GitHub repository.
echo Please file an issue on GitHub if you have any questions:
echo https://github.com/googleapis/rules_gapic/issues/new
EOD
    else
      cp $(SRCS) $@
    fi
  """,
)
