# This build file includes a target for the Ruby wrapper library for
# grafeas.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for grafeas.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "grafeas_ruby_wrapper",
    srcs = ["//grafeas/v1:grafeas_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=grafeas",
        "ruby-cloud-env-prefix=GRAFEAS",
        "ruby-cloud-wrapper-of=v1:0.14",
        "ruby-cloud-product-url=https://grafeas.io",
        "ruby-cloud-migration-version=1.0",
        "ruby-cloud-generic-endpoint=true",
    ],
    ruby_cloud_description = "The Grafeas API stores, and enables querying and retrieval of, critical metadata about all of your software artifacts.",
    ruby_cloud_title = "Grafeas",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "grafeas-ruby",
    deps = [
        ":grafeas_ruby_wrapper",
    ],
)
