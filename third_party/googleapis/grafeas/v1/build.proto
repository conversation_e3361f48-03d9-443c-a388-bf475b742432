// Copyright 2019 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "grafeas/v1/intoto_provenance.proto";
import "grafeas/v1/intoto_statement.proto";
import "grafeas/v1/provenance.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// Note holding the version of the provider's builder and the signature of the
// provenance message in the build details occurrence.
message BuildNote {
  // Required. Immutable. Version of the builder which produced this build.
  string builder_version = 1;
}

// Details of a build occurrence.
message BuildOccurrence {
  // The actual provenance for the build.
  grafeas.v1.BuildProvenance provenance = 1;

  // Serialized JSON representation of the provenance, used in generating the
  // build signature in the corresponding build note. After verifying the
  // signature, `provenance_bytes` can be unmarshalled and compared to the
  // provenance to confirm that it is unchanged. A base64-encoded string
  // representation of the provenance bytes is used for the signature in order
  // to interoperate with openssl which expects this format for signature
  // verification.
  //
  // The serialized form is captured both to avoid ambiguity in how the
  // provenance is marshalled to json as well to prevent incompatibilities with
  // future changes.
  string provenance_bytes = 2;

  // Deprecated. See InTotoStatement for the replacement.
  // In-toto Provenance representation as defined in spec.
  InTotoProvenance intoto_provenance = 3;

  // In-toto Statement representation as defined in spec.
  // The intoto_statement can contain any type of provenance. The serialized
  // payload of the statement can be stored and signed in the Occurrence's
  // envelope.
  InTotoStatement intoto_statement = 4;

  // In-Toto Slsa Provenance V1 represents a slsa provenance meeting the slsa
  // spec, wrapped in an in-toto statement. This allows for direct
  // jsonification of a to-spec in-toto slsa statement with a to-spec
  // slsa provenance.
  InTotoSlsaProvenanceV1 in_toto_slsa_provenance_v1 = 5;
}
