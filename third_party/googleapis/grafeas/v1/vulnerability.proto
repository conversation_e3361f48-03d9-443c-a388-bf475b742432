// Copyright 2019 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "grafeas/v1/common.proto";
import "grafeas/v1/cvss.proto";
import "grafeas/v1/package.proto";
import "grafeas/v1/severity.proto";
import "grafeas/v1/vex.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// A security vulnerability that can be found in resources.
message VulnerabilityNote {
  // The CVSS score of this vulnerability. CVSS score is on a scale of 0 - 10
  // where 0 indicates low severity and 10 indicates high severity.
  float cvss_score = 1;

  // The note provider assigned severity of this vulnerability.
  grafeas.v1.Severity severity = 2;

  // Details of all known distros and packages affected by this vulnerability.
  repeated Detail details = 3;

  // A detail for a distro and package affected by this vulnerability and its
  // associated fix (if one is available).
  message Detail {
    // The distro assigned severity of this vulnerability.
    string severity_name = 1;

    // A vendor-specific description of this vulnerability.
    string description = 2;

    // The type of package; whether native or non native (e.g., ruby gems,
    // node.js packages, etc.).
    string package_type = 3;

    // Required. The [CPE URI](https://cpe.mitre.org/specification/) this
    // vulnerability affects.
    string affected_cpe_uri = 4;

    // Required. The package this vulnerability affects.
    string affected_package = 5;

    // The version number at the start of an interval in which this
    // vulnerability exists. A vulnerability can affect a package between
    // version numbers that are disjoint sets of intervals (example:
    // [1.0.0-1.1.0], [2.4.6-2.4.8] and [4.5.6-4.6.8]) each of which will be
    // represented in its own Detail. If a specific affected version is provided
    // by a vulnerability database, affected_version_start and
    // affected_version_end will be the same in that Detail.
    grafeas.v1.Version affected_version_start = 6;

    // The version number at the end of an interval in which this vulnerability
    // exists. A vulnerability can affect a package between version numbers
    // that are disjoint sets of intervals (example: [1.0.0-1.1.0],
    // [2.4.6-2.4.8] and [4.5.6-4.6.8]) each of which will be represented in its
    // own Detail. If a specific affected version is provided by a vulnerability
    // database, affected_version_start and affected_version_end will be the
    // same in that Detail.
    grafeas.v1.Version affected_version_end = 7;

    // The distro recommended [CPE URI](https://cpe.mitre.org/specification/)
    // to update to that contains a fix for this vulnerability. It is possible
    // for this to be different from the affected_cpe_uri.
    string fixed_cpe_uri = 8;

    // The distro recommended package to update to that contains a fix for this
    // vulnerability. It is possible for this to be different from the
    // affected_package.
    string fixed_package = 9;

    // The distro recommended version to update to that contains a
    // fix for this vulnerability. Setting this to VersionKind.MAXIMUM means no
    // such version is yet available.
    grafeas.v1.Version fixed_version = 10;

    // Whether this detail is obsolete. Occurrences are expected not to point to
    // obsolete details.
    bool is_obsolete = 11;

    // The time this information was last changed at the source. This is an
    // upstream timestamp from the underlying information source - e.g. Ubuntu
    // security tracker.
    google.protobuf.Timestamp source_update_time = 12;

    // The source from which the information in this Detail was obtained.
    string source = 13;

    // The name of the vendor of the product.
    string vendor = 14;
  }

  // The full description of the CVSSv3 for this vulnerability.
  CVSSv3 cvss_v3 = 4;

  // Windows details get their own format because the information format and
  // model don't match a normal detail. Specifically Windows updates are done as
  // patches, thus Windows vulnerabilities really are a missing package, rather
  // than a package being at an incorrect version.
  repeated WindowsDetail windows_details = 5;

  message WindowsDetail {
    // Required. The [CPE URI](https://cpe.mitre.org/specification/) this
    // vulnerability affects.
    string cpe_uri = 1;

    // Required. The name of this vulnerability.
    string name = 2;

    // The description of this vulnerability.
    string description = 3;

    // Required. The names of the KBs which have hotfixes to mitigate this
    // vulnerability. Note that there may be multiple hotfixes (and thus
    // multiple KBs) that mitigate a given vulnerability. Currently any listed
    // KBs presence is considered a fix.
    repeated KnowledgeBase fixing_kbs = 4;

    message KnowledgeBase {
      // The KB name (generally of the form KB[0-9]+ (e.g., KB123456)).
      string name = 1;
      // A link to the KB in the [Windows update catalog]
      // (https://www.catalog.update.microsoft.com/).
      string url = 2;
    }
  }

  // The time this information was last changed at the source. This is an
  // upstream timestamp from the underlying information source - e.g. Ubuntu
  // security tracker.
  google.protobuf.Timestamp source_update_time = 6;

  // CVSS version used to populate cvss_score and severity.
  grafeas.v1.CVSSVersion cvss_version = 7;

  // The full description of the v2 CVSS for this vulnerability.
  CVSS cvss_v2 = 8;

  // Next free ID is 9.
}

// An occurrence of a severity vulnerability on a resource.
message VulnerabilityOccurrence {
  // The type of package; whether native or non native (e.g., ruby gems, node.js
  // packages, etc.).
  string type = 1;

  // Output only. The note provider assigned severity of this vulnerability.
  grafeas.v1.Severity severity = 2;

  // Output only. The CVSS score of this vulnerability. CVSS score is on a
  // scale of 0 - 10 where 0 indicates low severity and 10 indicates high
  // severity.
  float cvss_score = 3;

  // The cvss v3 score for the vulnerability.
  CVSS cvssv3 = 10;

  // Required. The set of affected locations and their fixes (if available)
  // within the associated resource.
  repeated PackageIssue package_issue = 4;

  // A detail for a distro and package this vulnerability occurrence was found
  // in and its associated fix (if one is available).
  message PackageIssue {
    // Required. The [CPE URI](https://cpe.mitre.org/specification/) this
    // vulnerability was found in.
    string affected_cpe_uri = 1;

    // Required. The package this vulnerability was found in.
    string affected_package = 2;

    // Required. The version of the package that is installed on the resource
    // affected by this vulnerability.
    grafeas.v1.Version affected_version = 3;

    // The [CPE URI](https://cpe.mitre.org/specification/) this vulnerability
    // was fixed in. It is possible for this to be different from the
    // affected_cpe_uri.
    string fixed_cpe_uri = 4;

    // The package this vulnerability was fixed in. It is possible for this to
    // be different from the affected_package.
    string fixed_package = 5;

    // Required. The version of the package this vulnerability was fixed in.
    // Setting this to VersionKind.MAXIMUM means no fix is yet available.
    grafeas.v1.Version fixed_version = 6;

    // Output only. Whether a fix is available for this package.
    bool fix_available = 7;

    // The type of package (e.g. OS, MAVEN, GO).
    string package_type = 8;

    // The distro or language system assigned severity for this vulnerability
    // when that is available and note provider assigned severity when it is not
    // available.
    grafeas.v1.Severity effective_severity = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // The location at which this package was found.
    repeated FileLocation file_location = 10;
  }

  // Output only. A one sentence description of this vulnerability.
  string short_description = 5;

  // Output only. A detailed description of this vulnerability.
  string long_description = 6;

  // Output only. URLs related to this vulnerability.
  repeated grafeas.v1.RelatedUrl related_urls = 7;

  // The distro assigned severity for this vulnerability when it is available,
  // otherwise this is the note provider assigned severity.
  //
  // When there are multiple PackageIssues for this vulnerability, they can have
  // different effective severities because some might be provided by the distro
  // while others are provided by the language ecosystem for a language pack.
  // For this reason, it is advised to use the effective severity on the
  // PackageIssue level. In the case where multiple PackageIssues have differing
  // effective severities, this field should be the highest severity for any of
  // the PackageIssues.
  grafeas.v1.Severity effective_severity = 8;

  // Output only. Whether at least one of the affected packages has a fix
  // available.
  bool fix_available = 9;

  // Output only. CVSS version used to populate cvss_score and severity.
  grafeas.v1.CVSSVersion cvss_version = 11;

  // The cvss v2 score for the vulnerability.
  CVSS cvss_v2 = 12;

  // VexAssessment provides all publisher provided Vex information that is
  // related to this vulnerability.
  message VexAssessment {
    // Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)
    // tracking number for the vulnerability.
    // Deprecated: Use vulnerability_id instead to denote CVEs.
    string cve = 1 [deprecated = true];

    // The vulnerability identifier for this Assessment. Will hold one of
    // common identifiers e.g. CVE, GHSA etc.
    string vulnerability_id = 8;

    // Holds a list of references associated with this vulnerability item and
    // assessment.
    repeated grafeas.v1.RelatedUrl related_uris = 2;

    // The VulnerabilityAssessment note from which this VexAssessment was
    // generated.
    // This will be of the form: `projects/[PROJECT_ID]/notes/[NOTE_ID]`.
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: The suffix is kept for consistency. --)
    string note_name = 3;

    // Provides the state of this Vulnerability assessment.
    grafeas.v1.VulnerabilityAssessmentNote.Assessment.State state = 4;

    // Contains information about the impact of this vulnerability,
    // this will change with time.
    repeated string impacts = 5;

    // Specifies details on how to handle (and presumably, fix) a vulnerability.
    repeated grafeas.v1.VulnerabilityAssessmentNote.Assessment.Remediation
        remediations = 6;

    // Justification provides the justification when the state of the
    // assessment if NOT_AFFECTED.
    grafeas.v1.VulnerabilityAssessmentNote.Assessment.Justification
        justification = 7;

    // Next ID: 9
  }

  VexAssessment vex_assessment = 13;

  // Occurrence-specific extra details about the vulnerability.
  string extra_details = 14;

  // Next free ID is 15.
}
