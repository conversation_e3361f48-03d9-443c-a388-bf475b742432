// Copyright 2021 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// Note provider assigned severity/impact ranking.
enum Severity {
  // Unknown.
  SEVERITY_UNSPECIFIED = 0;
  // Minimal severity.
  MINIMAL = 1;
  // Low severity.
  LOW = 2;
  // Medium severity.
  MEDIUM = 3;
  // High severity.
  HIGH = 4;
  // Critical severity.
  CRITICAL = 5;
}
