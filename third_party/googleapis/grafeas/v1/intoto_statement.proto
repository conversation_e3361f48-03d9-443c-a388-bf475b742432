// Copyright 2021 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "grafeas/v1/intoto_provenance.proto";
import "grafeas/v1/slsa_provenance.proto";
import "grafeas/v1/slsa_provenance_zero_two.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";
option java_outer_classname = "InTotoStatementProto";

// Spec defined at
// https://github.com/in-toto/attestation/tree/main/spec#statement The
// serialized InTotoStatement will be stored as Envelope.payload.
// Envelope.payloadType is always "application/vnd.in-toto+json".
message InTotoStatement {
  // Always `https://in-toto.io/Statement/v0.1`.
  string type = 1 [json_name = "_type"];
  repeated Subject subject = 2;
  // `https://slsa.dev/provenance/v0.1` for SlsaProvenance.
  string predicate_type = 3;
  oneof predicate {
    InTotoProvenance provenance = 4;
    SlsaProvenance slsa_provenance = 5;
    SlsaProvenanceZeroTwo slsa_provenance_zero_two = 6;
  }
}
message Subject {
  string name = 1;
  // `"<ALGORITHM>": "<HEX_VALUE>"`
  // Algorithms can be e.g. sha256, sha512
  // See
  // https://github.com/in-toto/attestation/blob/main/spec/field_types.md#DigestSet
  map<string, string> digest = 2;
}

message InTotoSlsaProvenanceV1 {
  // InToto spec defined at
  // https://github.com/in-toto/attestation/tree/main/spec#statement
  string type = 1 [json_name = "_type"];
  repeated Subject subject = 2;
  string predicate_type = 3;
  SlsaProvenanceV1 predicate = 4;

  // Keep in sync with schema at
  // https://github.com/slsa-framework/slsa/blob/main/docs/provenance/schema/v1/provenance.proto
  // Builder renamed to ProvenanceBuilder because of Java conflicts.
  message SlsaProvenanceV1 {
    BuildDefinition build_definition = 1;
    RunDetails run_details = 2;
  }

  message BuildDefinition {
    string build_type = 1;
    google.protobuf.Struct external_parameters = 2;
    google.protobuf.Struct internal_parameters = 3;
    repeated ResourceDescriptor resolved_dependencies = 4;
  }

  message ResourceDescriptor {
    string name = 1;
    string uri = 2;
    map<string, string> digest = 3;
    bytes content = 4;
    string download_location = 5;
    string media_type = 6;
    map<string, google.protobuf.Value> annotations = 7;
  }

  message RunDetails {
    ProvenanceBuilder builder = 1;
    BuildMetadata metadata = 2;
    repeated ResourceDescriptor byproducts = 3;
  }

  message ProvenanceBuilder {
    string id = 1;
    map<string, string> version = 2;
    repeated ResourceDescriptor builder_dependencies = 3;
  }

  message BuildMetadata {
    string invocation_id = 1;
    google.protobuf.Timestamp started_on = 2;
    google.protobuf.Timestamp finished_on = 3;
  }
}
