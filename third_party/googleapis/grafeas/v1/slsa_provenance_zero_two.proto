// Copyright 2021 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";
option java_outer_classname = "SlsaProvenanceZeroTwoProto";

message SlsaProvenanceZeroTwo {
  // See full explanation of fields at slsa.dev/provenance/v0.2.

  // Identifies the entity that executed the recipe, which is trusted to have
  // correctly performed the operation and populated this provenance.
  message SlsaBuilder {
    string id = 1;
  }

  // The collection of artifacts that influenced the build including sources,
  // dependencies, build tools, base images, and so on.
  message SlsaMaterial {
    string uri = 1;
    map<string, string> digest = 2;
  }

  // Identifies the event that kicked off the build.
  message SlsaInvocation {
    SlsaConfigSource config_source = 1;
    google.protobuf.Struct parameters = 2;
    google.protobuf.Struct environment = 3;
  }

  // Describes where the config file that kicked off the build came from.
  // This is effectively a pointer to the source where buildConfig came from.
  message SlsaConfigSource {
    string uri = 1;
    map<string, string> digest = 2;
    string entry_point = 3;
  }

  // Other properties of the build.
  message SlsaMetadata {
    string build_invocation_id = 1;
    google.protobuf.Timestamp build_started_on = 2;
    google.protobuf.Timestamp build_finished_on = 3;
    SlsaCompleteness completeness = 4;
    bool reproducible = 5;
  }

  // Indicates that the builder claims certain fields in this message to be
  // complete.
  message SlsaCompleteness {
    bool parameters = 1;
    bool environment = 2;
    bool materials = 3;
  }

  SlsaBuilder builder = 1;
  string build_type = 2;
  SlsaInvocation invocation = 3;
  google.protobuf.Struct build_config = 4;
  SlsaMetadata metadata = 5;
  repeated SlsaMaterial materials = 6;
}
