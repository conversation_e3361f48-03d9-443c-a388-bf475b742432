# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "grafeas_proto",
    srcs = [
        "attestation.proto",
        "build.proto",
        "common.proto",
        "compliance.proto",
        "cvss.proto",
        "deployment.proto",
        "discovery.proto",
        "dsse_attestation.proto",
        "grafeas.proto",
        "image.proto",
        "intoto_provenance.proto",
        "intoto_statement.proto",
        "package.proto",
        "provenance.proto",
        "sbom.proto",
        "severity.proto",
        "slsa_provenance.proto",
        "slsa_provenance_zero_two.proto",
        "upgrade.proto",
        "vex.proto",
        "vulnerability.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "grafeas_proto_with_info",
    deps = [
        ":grafeas_proto",
        "//google/cloud:common_resources_proto",
    ],
)

# The compliance_proto, common_proto and severity_proto targets were added so
# that clients can depend on those specific targets rather than grafeas_proto.
# Some clients that run code on certain VMs needed the smaller targets. Note
# that these were added by hand and were not autogenerated. Please ensure that
# these are not deleted while updating this file.
proto_library(
    name = "compliance_proto",
    srcs = [
        "compliance.proto",
    ],
    deps = [
        ":severity_proto",
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "common_proto",
    srcs = [
        "common.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "severity_proto",
    srcs = [
        "severity.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "grafeas_java_proto",
    deps = [":grafeas_proto"],
)

java_grpc_library(
    name = "grafeas_java_grpc",
    srcs = [":grafeas_proto"],
    deps = [":grafeas_java_proto"],
)

java_gapic_library(
    name = "grafeas_java_gapic",
    srcs = [":grafeas_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "grafeas_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "grafeas_v1.yaml",
    test_deps = [
        ":grafeas_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":common_proto",
        ":grafeas_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "grafeas_java_gapic_test_suite",
    test_classes = [
        "io.grafeas.v1.GrafeasClientTest",
    ],
    runtime_deps = [
        ":common_proto",
        ":grafeas_java_gapic_test",
    ],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-grafeas-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":grafeas_java_gapic",
        ":grafeas_java_grpc",
        ":grafeas_java_proto",
        ":grafeas_proto",
    ],
)

java_proto_library(
    name = "common_java_proto",
    deps = [":common_proto"],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "grafeas_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/grafeas/v1",
    protos = [":grafeas_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "grafeas_go_gapic",
    srcs = [":grafeas_proto_with_info"],
    grpc_service_config = "grafeas_grpc_service_config.json",
    importpath = "cloud.google.com/go/grafeas/apiv1;grafeas",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "grafeas_v1.yaml",
    transport = "grpc",
    deps = [
        ":grafeas_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-grafeas-v1-go",
    deps = [
        ":grafeas_go_gapic",
        ":grafeas_go_gapic_srcjar-metadata.srcjar",
        ":grafeas_go_gapic_srcjar-snippets.srcjar",
        ":grafeas_go_gapic_srcjar-test.srcjar",
        ":grafeas_go_proto",
    ],
)

# The compliance_go_proto, common_go_proto and severity_go_proto targets were
# added so that clients can depend on those specific targets rather than
# grafeas_proto. Some clients that run code on certain VMs needed the
# smaller targets. Note that these were added by hand and were not
# autogenerated. Please ensure that these are not deleted while updating this
# file.
go_proto_library(
    name = "compliance_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/grafeas/v1",
    protos = [
        ":compliance_proto",
        ":severity_proto",
    ],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_proto_library(
    name = "common_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/grafeas/v1",
    protos = [":common_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_proto_library(
    name = "severity_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/grafeas/v1",
    protos = [":severity_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",
    "py_test",
)

py_proto_library(
    name = "grafeas_py_proto",
    deps = ["grafeas_proto"],
)

py_gapic_library(
    name = "grafeas_py_gapic",
    srcs = [":grafeas_proto"],
    grpc_service_config = "grafeas_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=grafeas",
        "warehouse-package-name=grafeas",
    ],
    rest_numeric_enums = True,
    service_yaml = "grafeas_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "grafeas_py_gapic_test",
    srcs = [
        "grafeas_py_gapic_pytest.py",
        "grafeas_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":grafeas_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "grafeas-v1-py",
    deps = [
        ":grafeas_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "grafeas_php_proto",
    deps = [":grafeas_proto"],
)

php_gapic_library(
    name = "grafeas_php_gapic",
    srcs = [":grafeas_proto_with_info"],
    grpc_service_config = "grafeas_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "grafeas_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":grafeas_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-grafeas-v1-php",
    deps = [
        ":grafeas_php_gapic",
        ":grafeas_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "grafeas_nodejs_gapic",
    package_name = "@google-cloud/grafeas",
    src = ":grafeas_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "grafeas_grpc_service_config.json",
    package = "grafeas.v1",
    rest_numeric_enums = True,
    service_yaml = "grafeas_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "grafeas-v1-nodejs",
    deps = [
        ":grafeas_nodejs_gapic",
        ":grafeas_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "grafeas_ruby_proto",
    deps = [":grafeas_proto"],
)

ruby_grpc_library(
    name = "grafeas_ruby_grpc",
    srcs = [":grafeas_proto"],
    deps = [":grafeas_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "grafeas_ruby_gapic",
    srcs = [":grafeas_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-env-prefix=GRAFEAS",
        "ruby-cloud-gem-name=grafeas-v1",
        "ruby-cloud-generic-endpoint=true",
    ],
    grpc_service_config = "grafeas_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Grafeas API stores, and enables querying and retrieval of, critical metadata about all of your software artifacts.",
    ruby_cloud_title = "Grafeas V1",
    service_yaml = "grafeas_v1.yaml",
    transport = "grpc",
    deps = [
        ":grafeas_ruby_grpc",
        ":grafeas_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-grafeas-v1-ruby",
    deps = [
        ":grafeas_ruby_gapic",
        ":grafeas_ruby_grpc",
        ":grafeas_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "grafeas_csharp_proto",
    deps = [":grafeas_proto"],
)

csharp_grpc_library(
    name = "grafeas_csharp_grpc",
    srcs = [":grafeas_proto"],
    deps = [":grafeas_csharp_proto"],
)

csharp_gapic_library(
    name = "grafeas_csharp_gapic",
    srcs = [":grafeas_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "grafeas_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "grafeas_v1.yaml",
    transport = "grpc",
    deps = [
        ":grafeas_csharp_grpc",
        ":grafeas_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-grafeas-v1-csharp",
    deps = [
        ":grafeas_csharp_gapic",
        ":grafeas_csharp_grpc",
        ":grafeas_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "grafeas_cc_proto",
    deps = [":grafeas_proto"],
)

cc_grpc_library(
    name = "grafeas_cc_grpc",
    srcs = [":grafeas_proto"],
    grpc_only = True,
    deps = [":grafeas_cc_proto"],
)
