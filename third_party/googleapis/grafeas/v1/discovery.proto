// Copyright 2019 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "grafeas/v1/common.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// A note that indicates a type of analysis a provider would perform. This note
// exists in a provider's project. A `Discovery` occurrence is created in a
// consumer's project at the start of analysis.
message DiscoveryNote {
  // Required. Immutable. The kind of analysis that is handled by this
  // discovery.
  grafeas.v1.NoteKind analysis_kind = 1;
}

// Provides information about the analysis status of a discovered resource.
message DiscoveryOccurrence {
  // Whether the resource is continuously analyzed.
  enum ContinuousAnalysis {
    // Unknown.
    CONTINUOUS_ANALYSIS_UNSPECIFIED = 0;
    // The resource is continuously analyzed.
    ACTIVE = 1;
    // The resource is ignored for continuous analysis.
    INACTIVE = 2;
  }

  // Whether the resource is continuously analyzed.
  ContinuousAnalysis continuous_analysis = 1;

  // Analysis status for a resource. Currently for initial analysis only (not
  // updated in continuous analysis).
  enum AnalysisStatus {
    option allow_alias = true;

    // Unknown.
    ANALYSIS_STATUS_UNSPECIFIED = 0;
    // Resource is known but no action has been taken yet.
    PENDING = 1;
    // Resource is being analyzed.
    SCANNING = 2;
    // Analysis has finished successfully.
    FINISHED_SUCCESS = 3;
    // Analysis has completed.
    COMPLETE = 3;
    // Analysis has finished unsuccessfully, the analysis itself is in a bad
    // state.
    FINISHED_FAILED = 4;
    // The resource is known not to be supported.
    FINISHED_UNSUPPORTED = 5;
  }

  // The status of discovery for the resource.
  AnalysisStatus analysis_status = 2;

  // Indicates which analysis completed successfully. Multiple types of
  // analysis can be performed on a single resource.
  message AnalysisCompleted {
    repeated string analysis_type = 1;
  }

  AnalysisCompleted analysis_completed = 7;

  // Indicates any errors encountered during analysis of a resource. There
  // could be 0 or more of these errors.
  repeated google.rpc.Status analysis_error = 8;

  // When an error is encountered this will contain a LocalizedMessage under
  // details to show to the user. The LocalizedMessage is output only and
  // populated by the API.
  google.rpc.Status analysis_status_error = 3;

  // The CPE of the resource being scanned.
  string cpe = 4;

  // The last time this resource was scanned.
  google.protobuf.Timestamp last_scan_time = 5;

  // The time occurrences related to this discovery occurrence were archived.
  google.protobuf.Timestamp archive_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The status of an SBOM generation.
  message SBOMStatus {
    // An enum indicating the progress of the SBOM generation.
    enum SBOMState {
      // Default unknown state.
      SBOM_STATE_UNSPECIFIED = 0;
      // SBOM scanning is pending.
      PENDING = 1;
      // SBOM scanning has completed.
      COMPLETE = 2;
    }

    // The progress of the SBOM generation.
    SBOMState sbom_state = 1;
    // If there was an error generating an SBOM, this will indicate what that
    // error was.
    string error = 2;
  }

  // The status of an SBOM generation.
  SBOMStatus sbom_status = 9;

  // The status of an vulnerability attestation generation.
  message VulnerabilityAttestation {
    // An enum indicating the state of the attestation generation.
    enum VulnerabilityAttestationState {
      // Default unknown state.
      VULNERABILITY_ATTESTATION_STATE_UNSPECIFIED = 0;
      // Attestation was successfully generated and stored.
      SUCCESS = 1;
      // Attestation was unsuccessfully generated and stored.
      FAILURE = 2;
    }

    // The last time we attempted to generate an attestation.
    google.protobuf.Timestamp last_attempt_time = 1;

    // The success/failure state of the latest attestation attempt.
    VulnerabilityAttestationState state = 2;

    // If failure, the error reason for why the attestation generation failed.
    string error = 3;
  }

  // The status of an vulnerability attestation generation.
  VulnerabilityAttestation vulnerability_attestation = 10;
}
