// Copyright 2019 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// Provenance of a build. Contains all information needed to verify the full
// details about the build from source to completion.
message BuildProvenance {
  // Required. Unique identifier of the build.
  string id = 1;

  // ID of the project.
  string project_id = 2;

  // Commands requested by the build.
  repeated Command commands = 3;

  // Output of the build.
  repeated Artifact built_artifacts = 4;

  // Time at which the build was created.
  google.protobuf.Timestamp create_time = 5;

  // Time at which execution of the build was started.
  google.protobuf.Timestamp start_time = 6;

  // Time at which execution of the build was finished.
  google.protobuf.Timestamp end_time = 7;

  // E-mail address of the user who initiated this build. Note that this was the
  // user's e-mail address at the time the build was initiated; this address may
  // not represent the same end-user for all time.
  string creator = 8;

  // URI where any logs for this provenance were written.
  string logs_uri = 9;

  // Details of the Source input to the build.
  Source source_provenance = 10;

  // Trigger identifier if the build was triggered automatically; empty if not.
  string trigger_id = 11;

  // Special options applied to this build. This is a catch-all field where
  // build providers can enter any desired additional details.
  map<string, string> build_options = 12;

  // Version string of the builder at the time this build was executed.
  string builder_version = 13;
}

// Source describes the location of the source used for the build.
message Source {
  // If provided, the input binary artifacts for the build came from this
  // location.
  string artifact_storage_source_uri = 1;

  // Hash(es) of the build source, which can be used to verify that the original
  // source integrity was maintained in the build.
  //
  // The keys to this map are file paths used as build source and the values
  // contain the hash values for those files.
  //
  // If the build source came in a single package such as a gzipped tarfile
  // (.tar.gz), the FileHash will be for the single path to that file.
  map<string, FileHashes> file_hashes = 2;

  // If provided, the source code used for the build came from this location.
  SourceContext context = 3;

  // If provided, some of the source code used for the build may be found in
  // these locations, in the case where the source repository had multiple
  // remotes or submodules. This list will not include the context specified in
  // the context field.
  repeated SourceContext additional_contexts = 4;
}

// Container message for hashes of byte content of files, used in source
// messages to verify integrity of source input to the build.
message FileHashes {
  // Required. Collection of file hashes.
  repeated Hash file_hash = 1;
}

// Container message for hash values.
message Hash {
  // Required. The type of hash that was performed, e.g. "SHA-256".
  string type = 1;
  // Required. The hash value.
  bytes value = 2;
}

// Command describes a step performed as part of the build pipeline.
message Command {
  // Required. Name of the command, as presented on the command line, or if the
  // command is packaged as a Docker container, as presented to `docker pull`.
  string name = 1;

  // Environment variables set before running this command.
  repeated string env = 2;

  // Command-line arguments used when executing this command.
  repeated string args = 3;

  // Working directory (relative to project source root) used when running this
  // command.
  string dir = 4;

  // Optional unique identifier for this command, used in wait_for to reference
  // this command as a dependency.
  string id = 5;

  // The ID(s) of the command(s) that this command depends on.
  repeated string wait_for = 6;
}

// Artifact describes a build product.
message Artifact {
  // Hash or checksum value of a binary, or Docker Registry 2.0 digest of a
  // container.
  string checksum = 1;

  // Artifact ID, if any; for container images, this will be a URL by digest
  // like `gcr.io/projectID/imagename@sha256:123456`.
  string id = 2;

  // Related artifact names. This may be the path to a binary or jar file, or in
  // the case of a container build, the name used to push the container image to
  // Google Container Registry, as presented to `docker push`. Note that a
  // single Artifact ID can have multiple names, for example if two tags are
  // applied to one image.
  repeated string names = 3;
}

// A SourceContext is a reference to a tree of files. A SourceContext together
// with a path point to a unique revision of a single file or directory.
message SourceContext {
  // A SourceContext can refer any one of the following types of repositories.
  oneof context {
    // A SourceContext referring to a revision in a Google Cloud Source Repo.
    CloudRepoSourceContext cloud_repo = 1;

    // A SourceContext referring to a Gerrit project.
    GerritSourceContext gerrit = 2;

    // A SourceContext referring to any third party Git repo (e.g., GitHub).
    GitSourceContext git = 3;
  }

  // Labels with user defined metadata.
  map<string, string> labels = 4;
}

// An alias to a repo revision.
message AliasContext {
  // The type of an alias.
  enum Kind {
    // Unknown.
    KIND_UNSPECIFIED = 0;
    // Git tag.
    FIXED = 1;
    // Git branch.
    MOVABLE = 2;
    // Used to specify non-standard aliases. For example, if a Git repo has a
    // ref named "refs/foo/bar".
    OTHER = 4;
  }

  // The alias kind.
  Kind kind = 1;

  // The alias name.
  string name = 2;
}

// A CloudRepoSourceContext denotes a particular revision in a Google Cloud
// Source Repo.
message CloudRepoSourceContext {
  // The ID of the repo.
  RepoId repo_id = 1;

  // A revision in a Cloud Repo can be identified by either its revision ID or
  // its alias.
  oneof revision {
    // A revision ID.
    string revision_id = 2;

    // An alias, which may be a branch or tag.
    AliasContext alias_context = 3;
  }
}

// A SourceContext referring to a Gerrit project.
message GerritSourceContext {
  // The URI of a running Gerrit instance.
  string host_uri = 1;

  // The full project name within the host. Projects may be nested, so
  // "project/subproject" is a valid project name. The "repo name" is the
  // hostURI/project.
  string gerrit_project = 2;

  // A revision in a Gerrit project can be identified by either its revision ID
  // or its alias.
  oneof revision {
    // A revision (commit) ID.
    string revision_id = 3;

    // An alias, which may be a branch or tag.
    AliasContext alias_context = 4;
  }
}

// A GitSourceContext denotes a particular revision in a third party Git
// repository (e.g., GitHub).
message GitSourceContext {
  // Git repository URL.
  string url = 1;

  // Git commit hash.
  string revision_id = 2;
}

// A unique identifier for a Cloud Repo.
message RepoId {
  // A cloud repo can be identified by either its project ID and repository name
  // combination, or its globally unique identifier.
  oneof id {
    // A combination of a project ID and a repo name.
    ProjectRepoId project_repo_id = 1;

    // A server-assigned, globally unique identifier.
    string uid = 2;
  }
}

// Selects a repo using a Google Cloud Platform project ID (e.g.,
// winged-cargo-31) and a repo name within that project.
message ProjectRepoId {
  // The ID of the project.
  string project_id = 1;

  // The name of the repo. Leave empty for the default repo.
  string repo_name = 2;
}
