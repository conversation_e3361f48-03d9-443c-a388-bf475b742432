// Copyright 2021 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
syntax = "proto3";

package grafeas.v1;

import "grafeas/v1/common.proto";
import "grafeas/v1/intoto_statement.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

message DSSEAttestationNote {
  // This submessage provides human-readable hints about the purpose of the
  // authority. Because the name of a note acts as its resource reference, it is
  // important to disambiguate the canonical name of the Note (which might be a
  // UUID for security purposes) from "readable" names more suitable for debug
  // output. Note that these hints should not be used to look up authorities in
  // security sensitive contexts, such as when looking up attestations to
  // verify.
  message DSSEHint {
    // Required. The human readable name of this attestation authority, for
    // example "cloudbuild-prod".
    string human_readable_name = 1;
  }
  // DSSEHint hints at the purpose of the attestation authority.
  DSSEHint hint = 1;
}

// Deprecated. Prefer to use a regular Occurrence, and populate the
// Envelope at the top level of the Occurrence.
message DSSEAttestationOccurrence {
  // If doing something security critical, make sure to verify the signatures in
  // this metadata.
  Envelope envelope = 1;
  oneof decoded_payload {
    InTotoStatement statement = 2;
  }
}
