// Copyright 2019 The Grafeas Authors. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package grafeas.v1;

import "google/protobuf/timestamp.proto";
import "grafeas/v1/package.proto";

option go_package = "google.golang.org/genproto/googleapis/grafeas/v1;grafeas";
option java_multiple_files = true;
option java_package = "io.grafeas.v1";
option objc_class_prefix = "GRA";

// An Upgrade Note represents a potential upgrade of a package to a given
// version. For each package version combination (i.e. bash 4.0, bash 4.1,
// bash 4.1.2), there will be an Upgrade Note. For Windows, windows_update field
// represents the information related to the update.
message UpgradeNote {
  // Required for non-Windows OS. The package this Upgrade is for.
  string package = 1;
  // Required for non-Windows OS. The version of the package in machine + human
  // readable form.
  grafeas.v1.Version version = 2;
  // Metadata about the upgrade for each specific operating system.
  repeated UpgradeDistribution distributions = 3;
  // Required for Windows OS. Represents the metadata about the Windows update.
  WindowsUpdate windows_update = 4;
}

// The Upgrade Distribution represents metadata about the Upgrade for each
// operating system (CPE). Some distributions have additional metadata around
// updates, classifying them into various categories and severities.
message UpgradeDistribution {
  // Required - The specific operating system this metadata applies to. See
  // https://cpe.mitre.org/specification/.
  string cpe_uri = 1;
  // The operating system classification of this Upgrade, as specified by the
  // upstream operating system upgrade feed. For Windows the classification is
  // one of the category_ids listed at
  // https://docs.microsoft.com/en-us/previous-versions/windows/desktop/ff357803(v=vs.85)
  string classification = 2;
  // The severity as specified by the upstream operating system.
  string severity = 3;
  // The cve tied to this Upgrade.
  repeated string cve = 4;
}

// Windows Update represents the metadata about the update for the Windows
// operating system. The fields in this message come from the Windows Update API
// documented at
// https://docs.microsoft.com/en-us/windows/win32/api/wuapi/nn-wuapi-iupdate.
message WindowsUpdate {
  // The unique identifier of the update.
  message Identity {
    // The revision independent identifier of the update.
    string update_id = 1;
    // The revision number of the update.
    int32 revision = 2;
  }
  // Required - The unique identifier for the update.
  Identity identity = 1;
  // The localized title of the update.
  string title = 2;
  // The localized description of the update.
  string description = 3;
  // The category to which the update belongs.
  message Category {
    // The identifier of the category.
    string category_id = 1;
    // The localized name of the category.
    string name = 2;
  }
  // The list of categories to which the update belongs.
  repeated Category categories = 4;
  // The Microsoft Knowledge Base article IDs that are associated with the
  // update.
  repeated string kb_article_ids = 5;
  // The hyperlink to the support information for the update.
  string support_url = 6;
  // The last published timestamp of the update.
  google.protobuf.Timestamp last_published_timestamp = 7;
}

// An Upgrade Occurrence represents that a specific resource_url could install a
// specific upgrade. This presence is supplied via local sources (i.e. it is
// present in the mirror and the running system has noticed its availability).
// For Windows, both distribution and windows_update contain information for the
// Windows update.
message UpgradeOccurrence {
  // Required for non-Windows OS. The package this Upgrade is for.
  string package = 1;
  // Required for non-Windows OS. The version of the package in a machine +
  // human readable form.
  grafeas.v1.Version parsed_version = 3;
  // Metadata about the upgrade for available for the specific operating system
  // for the resource_url. This allows efficient filtering, as well as
  // making it easier to use the occurrence.
  UpgradeDistribution distribution = 4;
  // Required for Windows OS. Represents the metadata about the Windows update.
  WindowsUpdate windows_update = 5;
}
