# To make proto_library rules to include source info in the descriptor
build --protocopt=--include_source_info
build --protocopt=--experimental_allow_proto3_optional
# New boringssl requires C++14
build --repo_env=BAZEL_CXXOPTS="-std=c++14"


# This is to avoid JVM SIGBUS crashes on highly parallel builds,
# see https://github.com/bazelbuild/bazel/issues/3236 for more details
build --enable_platform_specific_config
build:linux --sandbox_tmpfs_path=/tmp

# Do not create bazel-* symlinks in the workspace directory
build:linux --experimental_convenience_symlinks=ignore
test:linux --experimental_convenience_symlinks=ignore
run:linux --experimental_convenience_symlinks=ignore
