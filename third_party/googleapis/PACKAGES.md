# Auto-Generated Packages for Google Cloud Platform

This indexes the gRPC and GAPIC packages delivered to the [Google Cloud
Platform](https://github.com/GoogleCloudPlatform) teams and where they can be
found. Currently, package delivery is only being done for Python.

## [Python](https://github.com/GoogleCloudPlatform/gcloud-python)

### Common
- [GAX core library](https://pypi.python.org/pypi/google-gax) ([source](https://github.com/googleapis/gax-python))
- [gRPC for common protos](https://pypi.python.org/pypi/googleapis-common-protos) ([source](https://github.com/googleapis/googleapis))
- [Long-running operations](https://github.com/googleapis/googleapis/blob/master/google/longrunning/operations.proto)
  - [GAPIC](https://pypi.python.org/pypi/gapic-google-longrunning)
  - gRPC: Part of [gRPC for common protos](https://pypi.python.org/pypi/googleapis-common-protos)
  - Documentation: TBD

### [Cloud Bigtable](https://cloud.google.com/bigtable/)
- GAPIC: TBD
- gRPC: TBD
- Documentation: TBD

### [Cloud Datastore](https://cloud.google.com/datastore/)
- [GAPIC](https://pypi.python.org/pypi/gapic-google-cloud-datastore-v1)
- [gRPC](https://pypi.python.org/pypi/grpc-google-cloud-datastore-v1)
- Documentation: TBD

### [Cloud Identity and Access Management (IAM)](https://cloud.google.com/iam/)
- Admin API
  - [GAPIC](https://pypi.python.org/pypi/gapic-google-iam-admin-v1)
  - [gRPC](https://pypi.python.org/pypi/grpc-google-iam-admin-v1)
  - Documentation: TBD
- Policy Mixin (used by Pub/Sub and others)
  - [gRPC](https://pypi.python.org/pypi/grpc-google-iam-v1)

### [Cloud Natural Language](https://cloud.google.com/natural-language/)
- [GAPIC](https://pypi.python.org/pypi/gapic-google-cloud-language-v1beta1)
- [gRPC](https://pypi.python.org/pypi/grpc-google-cloud-language-v1beta1)
- Documentation: TBD

### [Cloud Pub/Sub](https://cloud.google.com/pubsub/)
- [GAPIC](https://pypi.python.org/pypi/gapic-google-cloud-pubsub-v1)
- [gRPC](https://pypi.python.org/pypi/grpc-google-cloud-pubsub-v1)
- [Documentation](http://pythonhosted.org/gax-google-pubsub-v1/):
  For old `gax` namespace... will be updated soon for `gapic` namespace.

### [Cloud Speech](https://cloud.google.com/speech/)
- [GAPIC](https://pypi.python.org/pypi/gapic-google-cloud-speech-v1beta1)
- [gRPC](https://pypi.python.org/pypi/grpc-google-cloud-speech-v1beta1)
- Documentation: TBD

### [Cloud Vision](https://cloud.google.com/vision/)
- [GAPIC](https://pypi.python.org/pypi/gapic-google-cloud-vision-v1)
- [gRPC](https://pypi.python.org/pypi/grpc-google-cloud-vision-v1)
- [Documentation](http://pythonhosted.org/gax-google-cloud-vision-v1/):
  For old `gax` namespace... will be updated soon for `gapic` namespace.

### [Stackdriver Debugger](https://cloud.google.com/debugger/)
- GAPIC: TBD
- gRPC: TBD
- Documentation: TBD

### [Stackdriver Error Reporting](https://cloud.google.com/error-reporting/)
- [GAPIC](https://pypi.python.org/pypi/gapic-google-cloud-error-reporting-v1beta1)
- [gRPC](https://pypi.python.org/pypi/grpc-google-cloud-error-reporting-v1beta1)
- Documentation: TBD

### [Stackdriver Logging](https://cloud.google.com/logging/)
- [GAPIC](https://pypi.python.org/pypi/gapic-google-cloud-logging-v2)
- [gRPC](https://pypi.python.org/pypi/grpc-google-cloud-logging-v2)
- [Documentation](http://gapic-google-cloud-logging-v2.readthedocs.io)

### [Stackdriver Monitoring](https://cloud.google.com/monitoring/)
- [GAPIC](https://pypi.python.org/pypi/gapic-google-monitoring-v3)
- [gRPC](https://pypi.python.org/pypi/grpc-google-monitoring-v3)
- Documentation: TBD

### [Stackdriver Trace](https://cloud.google.com/trace/)
- GAPIC: TBD
- gRPC: TBD
- Documentation: TBD
