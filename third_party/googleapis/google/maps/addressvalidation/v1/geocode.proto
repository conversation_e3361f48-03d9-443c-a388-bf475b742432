// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.addressvalidation.v1;

import "google/geo/type/viewport.proto";
import "google/type/latlng.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.AddressValidation.V1";
option go_package = "cloud.google.com/go/maps/addressvalidation/apiv1/addressvalidationpb;addressvalidationpb";
option java_multiple_files = true;
option java_outer_classname = "GeocodeProto";
option java_package = "com.google.maps.addressvalidation.v1";
option objc_class_prefix = "GMPAVV1";
option php_namespace = "Google\\Maps\\AddressValidation\\V1";
option ruby_package = "Google::Maps::AddressValidation::V1";

// Contains information about the place the input was geocoded to.
message Geocode {
  // The geocoded location of the input.
  //
  // Using place IDs is preferred over using addresses,
  // latitude/longitude coordinates, or plus codes. Using coordinates when
  // routing or calculating driving directions will always result in the point
  // being snapped to the road nearest to those coordinates. This may not be a
  // road that will quickly or safely lead to the destination and may not be
  // near an access point to the property. Additionally, when a location is
  // reverse geocoded, there is no guarantee that the returned address will
  // match the original.
  google.type.LatLng location = 1;

  // The plus code corresponding to the `location`.
  PlusCode plus_code = 2;

  // The bounds of the geocoded place.
  google.geo.type.Viewport bounds = 4;

  // The size of the geocoded place, in meters. This is another measure of the
  // coarseness of the geocoded location, but in physical size rather than in
  // semantic meaning.
  float feature_size_meters = 5;

  // The PlaceID of the place this input geocodes to.
  //
  // For more information about Place IDs see
  // [here](https://developers.google.com/maps/documentation/places/web-service/place-id).
  string place_id = 6;

  // The type(s) of place that the input geocoded to. For example,
  // `['locality', 'political']`. The full list of types can be found
  // [here](https://developers.google.com/maps/documentation/geocoding/requests-geocoding#Types).
  repeated string place_types = 7;
}

// Plus code (http://plus.codes) is a location reference with two formats:
// global code defining a 14mx14m (1/8000th of a degree) or smaller rectangle,
// and compound code, replacing the prefix with a reference location.
message PlusCode {
  // Place's global (full) code, such as "9FWM33GV+HQ", representing an
  // 1/8000 by 1/8000 degree area (~14 by 14 meters).
  string global_code = 1;

  // Place's compound code, such as "33GV+HQ, Ramberg, Norway", containing
  // the suffix of the global code and replacing the prefix with a formatted
  // name of a reference entity.
  string compound_code = 2;
}
