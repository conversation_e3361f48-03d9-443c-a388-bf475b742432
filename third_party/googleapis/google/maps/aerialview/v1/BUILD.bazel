# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "aerialview_proto",
    srcs = [
        "aerial_view.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:duration_proto",
    ],
)

proto_library_with_info(
    name = "aerialview_proto_with_info",
    deps = [
        ":aerialview_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "aerialview_java_proto",
    deps = [":aerialview_proto"],
)

java_grpc_library(
    name = "aerialview_java_grpc",
    srcs = [":aerialview_proto"],
    deps = [":aerialview_java_proto"],
)

java_gapic_library(
    name = "aerialview_java_gapic",
    srcs = [":aerialview_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "aerialview_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "aerialview_v1.yaml",
    test_deps = [
        ":aerialview_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":aerialview_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "aerialview_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.aerialview.v1.AerialViewClientHttpJsonTest",
        "com.google.maps.aerialview.v1.AerialViewClientTest",
    ],
    runtime_deps = [":aerialview_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-maps-aerialview-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":aerialview_java_gapic",
        ":aerialview_java_grpc",
        ":aerialview_java_proto",
        ":aerialview_proto",
    ],
)

go_proto_library(
    name = "aerialview_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/aerialview/apiv1/aerialviewpb",
    protos = [":aerialview_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "aerialview_go_gapic",
    srcs = [":aerialview_proto_with_info"],
    grpc_service_config = "aerialview_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/aerialview/apiv1;aerialview",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "aerialview_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":aerialview_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-maps-aerialview-v1-go",
    deps = [
        ":aerialview_go_gapic",
        ":aerialview_go_gapic_srcjar-metadata.srcjar",
        ":aerialview_go_gapic_srcjar-snippets.srcjar",
        ":aerialview_go_gapic_srcjar-test.srcjar",
        ":aerialview_go_proto",
    ],
)

py_gapic_library(
    name = "aerialview_py_gapic",
    srcs = [":aerialview_proto"],
    grpc_service_config = "aerialview_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "aerialview_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "aerialview_py_gapic_test",
    srcs = [
        "aerialview_py_gapic_pytest.py",
        "aerialview_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":aerialview_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-aerialview-v1-py",
    deps = [
        ":aerialview_py_gapic",
    ],
)

php_proto_library(
    name = "aerialview_php_proto",
    deps = [":aerialview_proto"],
)

php_gapic_library(
    name = "aerialview_php_gapic",
    srcs = [":aerialview_proto_with_info"],
    grpc_service_config = "aerialview_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "aerialview_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":aerialview_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-maps-aerialview-v1-php",
    deps = [
        ":aerialview_php_gapic",
        ":aerialview_php_proto",
    ],
)

nodejs_gapic_library(
    name = "aerialview_nodejs_gapic",
    package_name = "@google-cloud/aerialview",
    src = ":aerialview_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "aerialview_grpc_service_config.json",
    package = "google.maps.aerialview.v1",
    rest_numeric_enums = True,
    service_yaml = "aerialview_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-aerialview-v1-nodejs",
    deps = [
        ":aerialview_nodejs_gapic",
        ":aerialview_proto",
    ],
)

ruby_proto_library(
    name = "aerialview_ruby_proto",
    deps = [":aerialview_proto"],
)

ruby_grpc_library(
    name = "aerialview_ruby_grpc",
    srcs = [":aerialview_proto"],
    deps = [":aerialview_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "aerialview_ruby_gapic",
    srcs = [":aerialview_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-maps-aerialview-v1",
    ],
    grpc_service_config = "aerialview_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "aerialview_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":aerialview_ruby_grpc",
        ":aerialview_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-maps-aerialview-v1-ruby",
    deps = [
        ":aerialview_ruby_gapic",
        ":aerialview_ruby_grpc",
        ":aerialview_ruby_proto",
    ],
)

csharp_proto_library(
    name = "aerialview_csharp_proto",
    extra_opts = [],
    deps = [":aerialview_proto"],
)

csharp_grpc_library(
    name = "aerialview_csharp_grpc",
    srcs = [":aerialview_proto"],
    deps = [":aerialview_csharp_proto"],
)

csharp_gapic_library(
    name = "aerialview_csharp_gapic",
    srcs = [":aerialview_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "aerialview_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "aerialview_v1.yaml",
    deps = [
        ":aerialview_csharp_grpc",
        ":aerialview_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-maps-aerialview-v1-csharp",
    deps = [
        ":aerialview_csharp_gapic",
        ":aerialview_csharp_grpc",
        ":aerialview_csharp_proto",
    ],
)

cc_proto_library(
    name = "aerialview_cc_proto",
    deps = [":aerialview_proto"],
)

cc_grpc_library(
    name = "aerialview_cc_grpc",
    srcs = [":aerialview_proto"],
    grpc_only = True,
    deps = [":aerialview_cc_proto"],
)
