// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/maps/places/v1/photo.proto";
import "google/maps/places/v1/review.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "ContextualContentProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Experimental: See
// https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
// for more details.
//
// Content that is contextual to the place query.
message ContextualContent {
  // Experimental: See
  // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
  // for more details.
  //
  // Justifications for the place. Justifications answers the question of why a
  // place could interest an end user.
  message Justification {
    // Experimental: See
    // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
    // for more details.
    //
    // User review justifications. This highlights a section of the user review
    // that would interest an end user. For instance, if the search query is
    // "firewood pizza", the review justification highlights the text relevant
    // to the search query.
    message ReviewJustification {
      // The text highlighted by the justification. This is a subset of the
      // review itself. The exact word to highlight is marked by the
      // HighlightedTextRange. There could be several words in the text being
      // highlighted.
      message HighlightedText {
        // The range of highlighted text.
        message HighlightedTextRange {
          int32 start_index = 1;

          int32 end_index = 2;
        }

        string text = 1;

        // The list of the ranges of the highlighted text.
        repeated HighlightedTextRange highlighted_text_ranges = 2;
      }

      HighlightedText highlighted_text = 1;

      // The review that the highlighted text is generated from.
      Review review = 2;
    }

    // Experimental: See
    // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
    // for more details.
    // BusinessAvailabilityAttributes justifications. This shows some attributes
    // a business has that could interest an end user.
    message BusinessAvailabilityAttributesJustification {
      // If a place provides takeout.
      bool takeout = 1;

      // If a place provides delivery.
      bool delivery = 2;

      // If a place provides dine-in.
      bool dine_in = 3;
    }

    oneof justification {
      // Experimental: See
      // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
      // for more details.
      ReviewJustification review_justification = 1;

      // Experimental: See
      // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
      // for more details.
      BusinessAvailabilityAttributesJustification
          business_availability_attributes_justification = 2;
    }
  }

  // List of reviews about this place, contexual to the place query.
  repeated Review reviews = 1;

  // Information (including references) about photos of this place, contexual to
  // the place query.
  repeated Photo photos = 2;

  // Experimental: See
  // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
  // for more details.
  //
  // Justifications for the place.
  repeated Justification justifications = 3;
}
