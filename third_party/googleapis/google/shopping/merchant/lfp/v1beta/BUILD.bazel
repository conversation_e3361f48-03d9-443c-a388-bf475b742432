# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "lfp_proto",
    srcs = [
        "lfpinventory.proto",
        "lfpsale.proto",
        "lfpstore.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/shopping/type:type_proto",  # Manual fix. Original :types_proto
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "lfp_proto_with_info",
    deps = [
        ":lfp_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "lfp_java_proto",
    deps = [":lfp_proto"],
)

java_grpc_library(
    name = "lfp_java_grpc",
    srcs = [":lfp_proto"],
    deps = [":lfp_java_proto"],
)

java_gapic_library(
    name = "lfp_java_gapic",
    srcs = [":lfp_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "lfp_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    test_deps = [
        ":lfp_java_grpc",
        "//google/shopping/type:type_proto"
    ],
    transport = "grpc+rest",
    deps = [
        ":lfp_java_proto",
        "//google/shopping/type:type_java_proto", # Added manually
    ],
)

java_gapic_test(
    name = "lfp_java_gapic_test_suite",
    test_classes = [
        "com.google.shopping.merchant.lfp.v1beta.LfpInventoryServiceClientHttpJsonTest",
        "com.google.shopping.merchant.lfp.v1beta.LfpInventoryServiceClientTest",
        "com.google.shopping.merchant.lfp.v1beta.LfpSaleServiceClientHttpJsonTest",
        "com.google.shopping.merchant.lfp.v1beta.LfpSaleServiceClientTest",
        "com.google.shopping.merchant.lfp.v1beta.LfpStoreServiceClientHttpJsonTest",
        "com.google.shopping.merchant.lfp.v1beta.LfpStoreServiceClientTest",
    ],
    runtime_deps = [":lfp_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-merchant-lfp-v1beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":lfp_java_gapic",
        ":lfp_java_grpc",
        ":lfp_java_proto",
        ":lfp_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "lfp_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/shopping/merchant/lfp/apiv1beta/lfppb",
    protos = [":lfp_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/shopping/type:type_go_proto",  # Manual fix. Original :types_go_proto
    ],
)

go_gapic_library(
    name = "lfp_go_gapic",
    srcs = [":lfp_proto_with_info"],
    grpc_service_config = "lfp_grpc_service_config.json",
    importpath = "cloud.google.com/go/shopping/merchant/lfp/apiv1beta;lfp",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":lfp_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-merchant-lfp-v1beta-go",
    deps = [
        ":lfp_go_gapic",
        ":lfp_go_gapic_srcjar-metadata.srcjar",
        ":lfp_go_gapic_srcjar-snippets.srcjar",
        ":lfp_go_gapic_srcjar-test.srcjar",
        ":lfp_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
)

py_import(
    name = "shopping_type",
    srcs = [
        "//google/shopping/type:type_py_gapic",
],
)

py_gapic_library(
    name = "lfp_py_gapic",
    srcs = [":lfp_proto"],
    grpc_service_config = "lfp_grpc_service_config.json",
    opt_args = [
        "proto-plus-deps=google.shopping.type", # Added manually
        "python-gapic-namespace=google.shopping",
        "python-gapic-name=merchant_lfp",
    ],
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
      ":shopping_type", # Added manually
    ],
)

py_test(
    name = "lfp_py_gapic_test",
    srcs = [
        "lfp_py_gapic_pytest.py",
        "lfp_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":lfp_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "merchant-lfp-v1beta-py",
    deps = [
        ":lfp_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "lfp_php_proto",
    deps = [":lfp_proto"],
)

php_gapic_library(
    name = "lfp_php_gapic",
    srcs = [":lfp_proto_with_info"],
    grpc_service_config = "lfp_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":lfp_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-merchant-lfp-v1beta-php",
    deps = [
        ":lfp_php_gapic",
        ":lfp_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "lfp_nodejs_gapic",
    package_name = "@google-shopping/lfp",
    src = ":lfp_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "lfp_grpc_service_config.json",
    package = "google.shopping.merchant.lfp.v1beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "merchant-lfp-v1beta-nodejs",
    deps = [
        ":lfp_nodejs_gapic",
        ":lfp_proto",
        "//google/shopping/type:type_proto" # Added manually
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "lfp_ruby_proto",
    deps = [":lfp_proto"],
)

ruby_grpc_library(
    name = "lfp_ruby_grpc",
    srcs = [":lfp_proto"],
    deps = [":lfp_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "lfp_ruby_gapic",
    srcs = [":lfp_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-shopping-merchant-lfp-v1beta",
        "ruby-cloud-extra-dependencies=google-shopping-type=>0.0+<2.a",
    ],
    grpc_service_config = "lfp_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":lfp_ruby_grpc",
        ":lfp_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-shopping-merchant-lfp-v1beta-ruby",
    deps = [
        ":lfp_ruby_gapic",
        ":lfp_ruby_grpc",
        ":lfp_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "lfp_csharp_proto",
    extra_opts = [],
    deps = [":lfp_proto"],
)

csharp_grpc_library(
    name = "lfp_csharp_grpc",
    srcs = [":lfp_proto"],
    deps = [":lfp_csharp_proto"],
)

csharp_gapic_library(
    name = "lfp_csharp_gapic",
    srcs = [":lfp_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "lfp_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":lfp_csharp_grpc",
        ":lfp_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-merchant-lfp-v1beta-csharp",
    deps = [
        ":lfp_csharp_gapic",
        ":lfp_csharp_grpc",
        ":lfp_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "lfp_cc_proto",
    deps = [":lfp_proto"],
)

cc_grpc_library(
    name = "lfp_cc_grpc",
    srcs = [":lfp_proto"],
    grpc_only = True,
    deps = [":lfp_cc_proto"],
)
