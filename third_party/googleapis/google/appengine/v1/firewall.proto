// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1;

option csharp_namespace = "Google.Cloud.AppEngine.V1";
option go_package = "cloud.google.com/go/appengine/apiv1/appenginepb;appenginepb";
option java_multiple_files = true;
option java_outer_classname = "FirewallProto";
option java_package = "com.google.appengine.v1.firewall";
option php_namespace = "Google\\Cloud\\AppEngine\\V1";
option ruby_package = "Google::Cloud::AppEngine::V1";

// A single firewall rule that is evaluated against incoming traffic
// and provides an action to take on matched requests.
message FirewallRule {
  // Available actions to take on matching requests.
  enum Action {
    UNSPECIFIED_ACTION = 0;

    // Matching requests are allowed.
    ALLOW = 1;

    // Matching requests are denied.
    DENY = 2;
  }

  // A positive integer between [1, Int32.MaxValue-1] that defines the order of
  // rule evaluation. Rules with the lowest priority are evaluated first.
  //
  // A default rule at priority Int32.MaxValue matches all IPv4 and IPv6 traffic
  // when no previous rule matches. Only the action of this rule can be modified
  // by the user.
  int32 priority = 1;

  // The action to take on matched requests.
  Action action = 2;

  // IP address or range, defined using CIDR notation, of requests that this
  // rule applies to. You can use the wildcard character "*" to match all IPs
  // equivalent to "0/0" and "::/0" together.
  // Examples: `***********` or `***********/16` or `2001:db8::/32`
  //           or `2001:0db8:0000:0042:0000:8a2e:0370:7334`.
  //
  //
  // <p>Truncation will be silently performed on addresses which are not
  // properly truncated. For example, `*******/24` is accepted as the same
  // address as `*******/24`. Similarly, for IPv6, `2001:db8::1/32` is accepted
  // as the same address as `2001:db8::/32`.
  string source_range = 3;

  // An optional string description of this rule.
  // This field has a maximum length of 100 characters.
  string description = 4;
}
