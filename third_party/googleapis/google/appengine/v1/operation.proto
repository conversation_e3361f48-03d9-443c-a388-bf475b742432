// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AppEngine.V1";
option go_package = "cloud.google.com/go/appengine/apiv1/appenginepb;appenginepb";
option java_multiple_files = true;
option java_outer_classname = "OperationProto";
option java_package = "com.google.appengine.v1";
option php_namespace = "Google\\Cloud\\AppEngine\\V1";
option ruby_package = "Google::Cloud::AppEngine::V1";

// Metadata for the given [google.longrunning.Operation][google.longrunning.Operation].
message OperationMetadataV1 {
  // API method that initiated this operation. Example:
  // `google.appengine.v1.Versions.CreateVersion`.
  //
  // @OutputOnly
  string method = 1;

  // Time that this operation was created.
  //
  // @OutputOnly
  google.protobuf.Timestamp insert_time = 2;

  // Time that this operation completed.
  //
  // @OutputOnly
  google.protobuf.Timestamp end_time = 3;

  // User who requested this operation.
  //
  // @OutputOnly
  string user = 4;

  // Name of the resource that this operation is acting on. Example:
  // `apps/myapp/services/default`.
  //
  // @OutputOnly
  string target = 5;

  // Ephemeral message that may change every time the operation is polled.
  // @OutputOnly
  string ephemeral_message = 6;

  // Durable messages that persist on every operation poll.
  // @OutputOnly
  repeated string warning = 7;

  // Metadata specific to the type of operation in progress.
  // @OutputOnly
  oneof method_metadata {
    CreateVersionMetadataV1 create_version_metadata = 8;
  }
}

// Metadata for the given [google.longrunning.Operation][google.longrunning.Operation] during a
// [google.appengine.v1.CreateVersionRequest][google.appengine.v1.CreateVersionRequest].
message CreateVersionMetadataV1 {
  // The Cloud Build ID if one was created as part of the version create.
  // @OutputOnly
  string cloud_build_id = 1;
}
