// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1;

option csharp_namespace = "Google.Cloud.AppEngine.V1";
option go_package = "cloud.google.com/go/appengine/apiv1/appenginepb;appenginepb";
option java_multiple_files = true;
option java_outer_classname = "NetworkSettingsProto";
option java_package = "com.google.appengine.v1";
option php_namespace = "Google\\Cloud\\AppEngine\\V1";
option ruby_package = "Google::Cloud::AppEngine::V1";

// A NetworkSettings resource is a container for ingress settings for a version
// or service.
message NetworkSettings {
  // If unspecified, INGRESS_TRAFFIC_ALLOWED_ALL will be used.
  enum IngressTrafficAllowed {
    // Unspecified
    INGRESS_TRAFFIC_ALLOWED_UNSPECIFIED = 0;

    // Allow HTTP traffic from public and private sources.
    INGRESS_TRAFFIC_ALLOWED_ALL = 1;

    // Allow HTTP traffic from only private VPC sources.
    INGRESS_TRAFFIC_ALLOWED_INTERNAL_ONLY = 2;

    // Allow HTTP traffic from private VPC sources and through load balancers.
    INGRESS_TRAFFIC_ALLOWED_INTERNAL_AND_LB = 3;
  }

  // The ingress settings for version or service.
  IngressTrafficAllowed ingress_traffic_allowed = 1;
}
