// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AppEngine.V1";
option go_package = "cloud.google.com/go/appengine/apiv1/appenginepb;appenginepb";
option java_multiple_files = true;
option java_outer_classname = "CertificateProto";
option java_package = "com.google.appengine.v1";
option php_namespace = "Google\\Cloud\\AppEngine\\V1";
option ruby_package = "Google::Cloud::AppEngine::V1";

// An SSL certificate that a user has been authorized to administer. A user
// is authorized to administer any certificate that applies to one of their
// authorized domains.
message AuthorizedCertificate {
  // Full path to the `AuthorizedCertificate` resource in the API. Example:
  // `apps/myapp/authorizedCertificates/12345`.
  //
  // @OutputOnly
  string name = 1;

  // Relative name of the certificate. This is a unique value autogenerated
  // on `AuthorizedCertificate` resource creation. Example: `12345`.
  //
  // @OutputOnly
  string id = 2;

  // The user-specified display name of the certificate. This is not
  // guaranteed to be unique. Example: `My Certificate`.
  string display_name = 3;

  // Topmost applicable domains of this certificate. This certificate
  // applies to these domains and their subdomains. Example: `example.com`.
  //
  // @OutputOnly
  repeated string domain_names = 4;

  // The time when this certificate expires. To update the renewal time on this
  // certificate, upload an SSL certificate with a different expiration time
  // using [`AuthorizedCertificates.UpdateAuthorizedCertificate`]().
  //
  // @OutputOnly
  google.protobuf.Timestamp expire_time = 5;

  // The SSL certificate serving the `AuthorizedCertificate` resource. This
  // must be obtained independently from a certificate authority.
  CertificateRawData certificate_raw_data = 6;

  // Only applicable if this certificate is managed by App Engine. Managed
  // certificates are tied to the lifecycle of a `DomainMapping` and cannot be
  // updated or deleted via the `AuthorizedCertificates` API. If this
  // certificate is manually administered by the user, this field will be empty.
  //
  // @OutputOnly
  ManagedCertificate managed_certificate = 7;

  // The full paths to user visible Domain Mapping resources that have this
  // certificate mapped. Example: `apps/myapp/domainMappings/example.com`.
  //
  // This may not represent the full list of mapped domain mappings if the user
  // does not have `VIEWER` permissions on all of the applications that have
  // this certificate mapped. See `domain_mappings_count` for a complete count.
  //
  // Only returned by `GET` or `LIST` requests when specifically requested by
  // the `view=FULL_CERTIFICATE` option.
  //
  // @OutputOnly
  repeated string visible_domain_mappings = 8;

  // Aggregate count of the domain mappings with this certificate mapped. This
  // count includes domain mappings on applications for which the user does not
  // have `VIEWER` permissions.
  //
  // Only returned by `GET` or `LIST` requests when specifically requested by
  // the `view=FULL_CERTIFICATE` option.
  //
  // @OutputOnly
  int32 domain_mappings_count = 9;
}

// An SSL certificate obtained from a certificate authority.
message CertificateRawData {
  // PEM encoded x.509 public key certificate. This field is set once on
  // certificate creation. Must include the header and footer. Example:
  // <pre>
  // -----BEGIN CERTIFICATE-----
  // <certificate_value>
  // -----END CERTIFICATE-----
  // </pre>
  string public_certificate = 1;

  // Unencrypted PEM encoded RSA private key. This field is set once on
  // certificate creation and then encrypted. The key size must be 2048
  // bits or fewer. Must include the header and footer. Example:
  // <pre>
  // -----BEGIN RSA PRIVATE KEY-----
  // <unencrypted_key_value>
  // -----END RSA PRIVATE KEY-----
  // </pre>
  // @InputOnly
  string private_key = 2;
}

// State of certificate management. Refers to the most recent certificate
// acquisition or renewal attempt.
enum ManagementStatus {
  MANAGEMENT_STATUS_UNSPECIFIED = 0;

  // Certificate was successfully obtained and inserted into the serving
  // system.
  OK = 1;

  // Certificate is under active attempts to acquire or renew.
  PENDING = 2;

  // Most recent renewal failed due to an invalid DNS setup and will be
  // retried. Renewal attempts will continue to fail until the certificate
  // domain's DNS configuration is fixed. The last successfully provisioned
  // certificate may still be serving.
  FAILED_RETRYING_NOT_VISIBLE = 4;

  // All renewal attempts have been exhausted, likely due to an invalid DNS
  // setup.
  FAILED_PERMANENT = 6;

  // Most recent renewal failed due to an explicit CAA record that does not
  // include one of the in-use CAs (Google CA and Let's Encrypt). Renewals will
  // continue to fail until the CAA is reconfigured. The last successfully
  // provisioned certificate may still be serving.
  FAILED_RETRYING_CAA_FORBIDDEN = 7;

  // Most recent renewal failed due to a CAA retrieval failure. This means that
  // the domain's DNS provider does not properly handle CAA records, failing
  // requests for CAA records when no CAA records are defined. Renewals will
  // continue to fail until the DNS provider is changed or a CAA record is
  // added for the given domain. The last successfully provisioned certificate
  // may still be serving.
  FAILED_RETRYING_CAA_CHECKING = 8;
}

// A certificate managed by App Engine.
message ManagedCertificate {
  // Time at which the certificate was last renewed. The renewal process is
  // fully managed. Certificate renewal will automatically occur before the
  // certificate expires. Renewal errors can be tracked via `ManagementStatus`.
  //
  // @OutputOnly
  google.protobuf.Timestamp last_renewal_time = 1;

  // Status of certificate management. Refers to the most recent certificate
  // acquisition or renewal attempt.
  //
  // @OutputOnly
  ManagementStatus status = 2;
}
