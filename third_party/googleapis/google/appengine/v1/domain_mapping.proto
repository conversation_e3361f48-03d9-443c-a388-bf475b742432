// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1;

option csharp_namespace = "Google.Cloud.AppEngine.V1";
option go_package = "cloud.google.com/go/appengine/apiv1/appenginepb;appenginepb";
option java_multiple_files = true;
option java_outer_classname = "DomainMappingProto";
option java_package = "com.google.appengine.v1";
option php_namespace = "Google\\Cloud\\AppEngine\\V1";
option ruby_package = "Google::Cloud::AppEngine::V1";

// A domain serving an App Engine application.
message DomainMapping {
  // Full path to the `DomainMapping` resource in the API. Example:
  // `apps/myapp/domainMapping/example.com`.
  //
  // @OutputOnly
  string name = 1;

  // Relative name of the domain serving the application. Example:
  // `example.com`.
  string id = 2;

  // SSL configuration for this domain. If unconfigured, this domain will not
  // serve with SSL.
  SslSettings ssl_settings = 3;

  // The resource records required to configure this domain mapping. These
  // records must be added to the domain's DNS configuration in order to
  // serve the application via this domain mapping.
  //
  // @OutputOnly
  repeated ResourceRecord resource_records = 4;
}

// SSL configuration for a `DomainMapping` resource.
message SslSettings {
  // The SSL management type for this domain.
  enum SslManagementType {
    // Defaults to `AUTOMATIC`.
    SSL_MANAGEMENT_TYPE_UNSPECIFIED = 0;

    // SSL support for this domain is configured automatically. The mapped SSL
    // certificate will be automatically renewed.
    AUTOMATIC = 1;

    // SSL support for this domain is configured manually by the user. Either
    // the domain has no SSL support or a user-obtained SSL certificate has been
    // explictly mapped to this domain.
    MANUAL = 2;
  }

  // ID of the `AuthorizedCertificate` resource configuring SSL for the
  // application. Clearing this field will remove SSL support.
  //
  // By default, a managed certificate is automatically created for every
  // domain mapping. To omit SSL support or to configure SSL manually, specify
  // `SslManagementType.MANUAL` on a `CREATE` or `UPDATE` request. You must
  // be authorized to administer the `AuthorizedCertificate` resource to
  // manually map it to a `DomainMapping` resource.
  // Example: `12345`.
  string certificate_id = 1;

  // SSL management type for this domain. If `AUTOMATIC`, a managed certificate
  // is automatically provisioned. If `MANUAL`, `certificate_id` must be
  // manually specified in order to configure SSL for this domain.
  SslManagementType ssl_management_type = 3;

  // ID of the managed `AuthorizedCertificate` resource currently being
  // provisioned, if applicable. Until the new managed certificate has been
  // successfully provisioned, the previous SSL state will be preserved. Once
  // the provisioning process completes, the `certificate_id` field will reflect
  // the new managed certificate and this field will be left empty. To remove
  // SSL support while there is still a pending managed certificate, clear the
  // `certificate_id` field with an `UpdateDomainMappingRequest`.
  //
  // @OutputOnly
  string pending_managed_certificate_id = 4;
}

// A DNS resource record.
message ResourceRecord {
  // A resource record type.
  enum RecordType {
    // An unknown resource record.
    RECORD_TYPE_UNSPECIFIED = 0;

    // An A resource record. Data is an IPv4 address.
    A = 1;

    // An AAAA resource record. Data is an IPv6 address.
    AAAA = 2;

    // A CNAME resource record. Data is a domain name to be aliased.
    CNAME = 3;
  }

  // Relative name of the object affected by this record. Only applicable for
  // `CNAME` records. Example: 'www'.
  string name = 1;

  // Data for this record. Values vary by record type, as defined in RFC 1035
  // (section 5) and RFC 1034 (section 3.6.1).
  string rrdata = 2;

  // Resource record type. Example: `AAAA`.
  RecordType type = 3;
}
