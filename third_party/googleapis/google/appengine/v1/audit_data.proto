// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1;

import "google/appengine/v1/appengine.proto";

option csharp_namespace = "Google.Cloud.AppEngine.V1";
option go_package = "cloud.google.com/go/appengine/apiv1/appenginepb;appenginepb";
option java_multiple_files = true;
option java_outer_classname = "AuditDataProto";
option java_package = "com.google.appengine.v1";
option php_namespace = "Google\\Cloud\\AppEngine\\V1";
option ruby_package = "Google::Cloud::AppEngine::V1";

// App Engine admin service audit log.
message AuditData {
  // Detailed information about methods that require it. Does not include
  // simple Get, List or Delete methods because all significant information
  // (resource name, number of returned elements for List operations) is already
  // included in parent audit log message.
  oneof method {
    // Detailed information about UpdateService call.
    UpdateServiceMethod update_service = 1;

    // Detailed information about CreateVersion call.
    CreateVersionMethod create_version = 2;
  }
}

// Detailed information about UpdateService call.
message UpdateServiceMethod {
  // Update service request.
  UpdateServiceRequest request = 1;
}

// Detailed information about CreateVersion call.
message CreateVersionMethod {
  // Create version request.
  CreateVersionRequest request = 1;
}
