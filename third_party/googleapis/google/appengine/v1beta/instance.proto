// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AppEngine.V1Beta";
option go_package = "google.golang.org/genproto/googleapis/appengine/v1beta;appengine";
option java_multiple_files = true;
option java_outer_classname = "InstanceProto";
option java_package = "com.google.appengine.v1beta";
option php_namespace = "Google\\Cloud\\AppEngine\\V1beta";
option ruby_package = "Google::Cloud::AppEngine::V1beta";

// An Instance resource is the computing unit that App Engine uses to
// automatically scale an application.
message Instance {
  option (google.api.resource) = {
    type: "appengine.googleapis.com/Instance"
    pattern: "apps/{app}/services/{service}/versions/{version}/instances/{instance}"
  };

  // Wrapper for LivenessState enum.
  message Liveness {
    // Liveness health check status for Flex instances.
    enum LivenessState {
      // There is no liveness health check for the instance. Only applicable for
      // instances in App Engine standard environment.
      LIVENESS_STATE_UNSPECIFIED = 0;

      // The health checking system is aware of the instance but its health is
      // not known at the moment.
      UNKNOWN = 1;

      // The instance is reachable i.e. a connection to the application health
      // checking endpoint can be established, and conforms to the requirements
      // defined by the health check.
      HEALTHY = 2;

      // The instance is reachable, but does not conform to the requirements
      // defined by the health check.
      UNHEALTHY = 3;

      // The instance is being drained. The existing connections to the instance
      // have time to complete, but the new ones are being refused.
      DRAINING = 4;

      // The instance is unreachable i.e. a connection to the application health
      // checking endpoint cannot be established, or the server does not respond
      // within the specified timeout.
      TIMEOUT = 5;
    }


  }

  // Availability of the instance.
  enum Availability {
    UNSPECIFIED = 0;

    RESIDENT = 1;

    DYNAMIC = 2;
  }

  // Output only. Full path to the Instance resource in the API.
  // Example: `apps/myapp/services/default/versions/v1/instances/instance-1`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Relative name of the instance within the version.
  // Example: `instance-1`.
  string id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. App Engine release this instance is running on.
  string app_engine_release = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Availability of the instance.
  Availability availability = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the virtual machine where this instance lives. Only applicable
  // for instances in App Engine flexible environment.
  string vm_name = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Zone where the virtual machine is located. Only applicable for instances
  // in App Engine flexible environment.
  string vm_zone_name = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Virtual machine ID of this instance. Only applicable for instances in
  // App Engine flexible environment.
  string vm_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time that this instance was started.
  //
  // @OutputOnly
  google.protobuf.Timestamp start_time = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of requests since this instance was started.
  int32 requests = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of errors since this instance was started.
  int32 errors = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Average queries per second (QPS) over the last minute.
  float qps = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Average latency (ms) over the last minute.
  int32 average_latency = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total memory in use (bytes).
  int64 memory_usage = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of the virtual machine where this instance lives. Only applicable
  // for instances in App Engine flexible environment.
  string vm_status = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether this instance is in debug mode. Only applicable for instances in
  // App Engine flexible environment.
  bool vm_debug_enabled = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The IP address of this instance. Only applicable for instances in App
  // Engine flexible environment.
  string vm_ip = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The liveness health check of this instance. Only applicable for instances
  // in App Engine flexible environment.
  Liveness.LivenessState vm_liveness = 17 [(google.api.field_behavior) = OUTPUT_ONLY];
}
