// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1beta;

import "google/appengine/v1beta/application.proto";
import "google/appengine/v1beta/certificate.proto";
import "google/appengine/v1beta/domain.proto";
import "google/appengine/v1beta/domain_mapping.proto";
import "google/appengine/v1beta/firewall.proto";
import "google/appengine/v1beta/instance.proto";
import "google/appengine/v1beta/version.proto";
import "google/appengine/v1beta/service.proto";
import "google/api/annotations.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/api/client.proto";

option csharp_namespace = "Google.Cloud.AppEngine.V1Beta";
option go_package = "google.golang.org/genproto/googleapis/appengine/v1beta;appengine";
option java_multiple_files = true;
option java_outer_classname = "AppengineProto";
option java_package = "com.google.appengine.v1beta";
option php_namespace = "Google\\Cloud\\AppEngine\\V1beta";
option ruby_package = "Google::Cloud::AppEngine::V1beta";

// Manages App Engine applications.
service Applications {
  option (google.api.default_host) = "appengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/appengine.admin,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Gets information about an application.
  rpc GetApplication(GetApplicationRequest) returns (Application) {
    option (google.api.http) = {
      get: "/v1beta/{name=apps/*}"
    };
  }

  // Creates an App Engine application for a Google Cloud Platform project.
  // Required fields:
  //
  // * `id` - The ID of the target Cloud Platform project.
  // * *location* - The [region](https://cloud.google.com/appengine/docs/locations) where you want the App Engine application located.
  //
  // For more information about App Engine applications, see [Managing Projects, Applications, and Billing](https://cloud.google.com/appengine/docs/standard/python/console/).
  rpc CreateApplication(CreateApplicationRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/apps"
      body: "application"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Application"
      metadata_type: "OperationMetadataV1Beta"
    };
  }

  // Updates the specified Application resource.
  // You can update the following fields:
  //
  // * `auth_domain` - Google authentication domain for controlling user access to the application.
  // * `default_cookie_expiration` - Cookie expiration policy for the application.
  rpc UpdateApplication(UpdateApplicationRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{name=apps/*}"
      body: "application"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Application"
      metadata_type: "OperationMetadataV1Beta"
    };
  }

  // Recreates the required App Engine features for the specified App Engine
  // application, for example a Cloud Storage bucket or App Engine service
  // account.
  // Use this method if you receive an error message about a missing feature,
  // for example, *Error retrieving the App Engine service account*.
  // If you have deleted your App Engine service account, this will
  // not be able to recreate it. Instead, you should attempt to use the
  // IAM undelete API if possible at https://cloud.google.com/iam/reference/rest/v1/projects.serviceAccounts/undelete?apix_params=%7B"name"%3A"projects%2F-%2FserviceAccounts%2Funique_id"%2C"resource"%3A%7B%7D%7D .
  // If the deletion was recent, the numeric ID can be found in the Cloud
  // Console Activity Log.
  rpc RepairApplication(RepairApplicationRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{name=apps/*}:repair"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Application"
      metadata_type: "OperationMetadataV1Beta"
    };
  }
}

// Request message for `Applications.GetApplication`.
message GetApplicationRequest {
  // Name of the Application resource to get. Example: `apps/myapp`.
  string name = 1;
}

// Request message for `Applications.CreateApplication`.
message CreateApplicationRequest {
  // Application configuration.
  Application application = 2;
}

// Request message for `Applications.UpdateApplication`.
message UpdateApplicationRequest {
  // Name of the Application resource to update. Example: `apps/myapp`.
  string name = 1;

  // An Application containing the updated resource.
  Application application = 2;

  // Standard field mask for the set of fields to be updated.
  google.protobuf.FieldMask update_mask = 3;
}

// Request message for 'Applications.RepairApplication'.
message RepairApplicationRequest {
  // Name of the application to repair. Example: `apps/myapp`
  string name = 1;
}

// Manages services of an application.
service Services {
  option (google.api.default_host) = "appengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/appengine.admin,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists all the services in the application.
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=apps/*}/services"
    };
  }

  // Gets the current configuration of the specified service.
  rpc GetService(GetServiceRequest) returns (Service) {
    option (google.api.http) = {
      get: "/v1beta/{name=apps/*/services/*}"
    };
  }

  // Updates the configuration of the specified service.
  rpc UpdateService(UpdateServiceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{name=apps/*/services/*}"
      body: "service"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Service"
      metadata_type: "OperationMetadataV1Beta"
    };
  }

  // Deletes the specified service and all enclosed versions.
  rpc DeleteService(DeleteServiceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=apps/*/services/*}"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadataV1Beta"
    };
  }
}

// Request message for `Services.ListServices`.
message ListServicesRequest {
  // Name of the parent Application resource. Example: `apps/myapp`.
  string parent = 1;

  // Maximum results to return per page.
  int32 page_size = 2;

  // Continuation token for fetching the next page of results.
  string page_token = 3;
}

// Response message for `Services.ListServices`.
message ListServicesResponse {
  // The services belonging to the requested application.
  repeated Service services = 1;

  // Continuation token for fetching the next page of results.
  string next_page_token = 2;
}

// Request message for `Services.GetService`.
message GetServiceRequest {
  // Name of the resource requested. Example: `apps/myapp/services/default`.
  string name = 1;
}

// Request message for `Services.UpdateService`.
message UpdateServiceRequest {
  // Name of the resource to update. Example: `apps/myapp/services/default`.
  string name = 1;

  // A Service resource containing the updated service. Only fields set in the
  // field mask will be updated.
  Service service = 2;

  // Standard field mask for the set of fields to be updated.
  google.protobuf.FieldMask update_mask = 3;

  // Set to `true` to gradually shift traffic to one or more versions that you
  // specify. By default, traffic is shifted immediately.
  // For gradual traffic migration, the target versions
  // must be located within instances that are configured for both
  // [warmup requests](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#InboundServiceType)
  // and
  // [automatic scaling](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#AutomaticScaling).
  // You must specify the
  // [`shardBy`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services#ShardBy)
  // field in the Service resource. Gradual traffic migration is not
  // supported in the App Engine flexible environment. For examples, see
  // [Migrating and Splitting Traffic](https://cloud.google.com/appengine/docs/admin-api/migrating-splitting-traffic).
  bool migrate_traffic = 4;
}

// Request message for `Services.DeleteService`.
message DeleteServiceRequest {
  // Name of the resource requested. Example: `apps/myapp/services/default`.
  string name = 1;
}

// Manages versions of a service.
service Versions {
  option (google.api.default_host) = "appengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/appengine.admin,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists the versions of a service.
  rpc ListVersions(ListVersionsRequest) returns (ListVersionsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=apps/*/services/*}/versions"
    };
  }

  // Gets the specified Version resource.
  // By default, only a `BASIC_VIEW` will be returned.
  // Specify the `FULL_VIEW` parameter to get the full resource.
  rpc GetVersion(GetVersionRequest) returns (Version) {
    option (google.api.http) = {
      get: "/v1beta/{name=apps/*/services/*/versions/*}"
    };
  }

  // Deploys code and resource files to a new version.
  rpc CreateVersion(CreateVersionRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=apps/*/services/*}/versions"
      body: "version"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Version"
      metadata_type: "CreateVersionMetadataV1Beta"
    };
  }

  // Updates the specified Version resource.
  // You can specify the following fields depending on the App Engine
  // environment and type of scaling that the version resource uses:
  //
  // **Standard environment**
  //
  // * [`instance_class`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.instance_class)
  //
  // *automatic scaling* in the standard environment:
  //
  // * [`automatic_scaling.min_idle_instances`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling)
  // * [`automatic_scaling.max_idle_instances`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling)
  // * [`automaticScaling.standard_scheduler_settings.max_instances`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings)
  // * [`automaticScaling.standard_scheduler_settings.min_instances`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings)
  // * [`automaticScaling.standard_scheduler_settings.target_cpu_utilization`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings)
  // * [`automaticScaling.standard_scheduler_settings.target_throughput_utilization`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings)
  //
  // *basic scaling* or *manual scaling* in the standard environment:
  //
  // * [`serving_status`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.serving_status)
  // * [`manual_scaling.instances`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#manualscaling)
  //
  // **Flexible environment**
  //
  // * [`serving_status`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.serving_status)
  //
  // *automatic scaling* in the flexible environment:
  //
  // * [`automatic_scaling.min_total_instances`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling)
  // * [`automatic_scaling.max_total_instances`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling)
  // * [`automatic_scaling.cool_down_period_sec`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling)
  // * [`automatic_scaling.cpu_utilization.target_utilization`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling)
  //
  // *manual scaling* in the flexible environment:
  //
  // * [`manual_scaling.instances`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#manualscaling)
  rpc UpdateVersion(UpdateVersionRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{name=apps/*/services/*/versions/*}"
      body: "version"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Version"
      metadata_type: "OperationMetadataV1Beta"
    };
  }

  // Deletes an existing Version resource.
  rpc DeleteVersion(DeleteVersionRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=apps/*/services/*/versions/*}"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadataV1Beta"
    };
  }
}

// Request message for `Versions.ListVersions`.
message ListVersionsRequest {
  // Name of the parent Service resource. Example:
  // `apps/myapp/services/default`.
  string parent = 1;

  // Controls the set of fields returned in the `List` response.
  VersionView view = 2;

  // Maximum results to return per page.
  int32 page_size = 3;

  // Continuation token for fetching the next page of results.
  string page_token = 4;
}

// Response message for `Versions.ListVersions`.
message ListVersionsResponse {
  // The versions belonging to the requested service.
  repeated Version versions = 1;

  // Continuation token for fetching the next page of results.
  string next_page_token = 2;
}

// Request message for `Versions.GetVersion`.
message GetVersionRequest {
  // Name of the resource requested. Example:
  // `apps/myapp/services/default/versions/v1`.
  string name = 1;

  // Controls the set of fields returned in the `Get` response.
  VersionView view = 2;
}

// Request message for `Versions.CreateVersion`.
message CreateVersionRequest {
  // Name of the parent resource to create this version under. Example:
  // `apps/myapp/services/default`.
  string parent = 1;

  // Application deployment configuration.
  Version version = 2;
}

// Request message for `Versions.UpdateVersion`.
message UpdateVersionRequest {
  // Name of the resource to update. Example:
  // `apps/myapp/services/default/versions/1`.
  string name = 1;

  // A Version containing the updated resource. Only fields set in the field
  // mask will be updated.
  Version version = 2;

  // Standard field mask for the set of fields to be updated.
  google.protobuf.FieldMask update_mask = 3;
}

// Fields that should be returned when [Version][google.appengine.v1beta.Version] resources
// are retrieved.
enum VersionView {
  // Basic version information including scaling and inbound services,
  // but not detailed deployment information.
  BASIC = 0;

  // The information from `BASIC`, plus detailed information about the
  // deployment. This format is required when creating resources, but
  // is not returned in `Get` or `List` by default.
  FULL = 1;
}

// Request message for `Versions.DeleteVersion`.
message DeleteVersionRequest {
  // Name of the resource requested. Example:
  // `apps/myapp/services/default/versions/v1`.
  string name = 1;
}

// Request message for `Instances.ListInstances`.
message ListInstancesRequest {
  // Name of the parent Version resource. Example:
  // `apps/myapp/services/default/versions/v1`.
  string parent = 1;

  // Maximum results to return per page.
  int32 page_size = 2;

  // Continuation token for fetching the next page of results.
  string page_token = 3;
}

// Response message for `Instances.ListInstances`.
message ListInstancesResponse {
  // The instances belonging to the requested version.
  repeated Instance instances = 1;

  // Continuation token for fetching the next page of results.
  string next_page_token = 2;
}

// Request message for `Instances.GetInstance`.
message GetInstanceRequest {
  // Name of the resource requested. Example:
  // `apps/myapp/services/default/versions/v1/instances/instance-1`.
  string name = 1;
}

// Request message for `Instances.DeleteInstance`.
message DeleteInstanceRequest {
  // Name of the resource requested. Example:
  // `apps/myapp/services/default/versions/v1/instances/instance-1`.
  string name = 1;
}

// Request message for `Instances.DebugInstance`.
message DebugInstanceRequest {
  // Name of the resource requested. Example:
  // `apps/myapp/services/default/versions/v1/instances/instance-1`.
  string name = 1;

  // Public SSH key to add to the instance. Examples:
  //
  // * `[USERNAME]:ssh-rsa [KEY_VALUE] [USERNAME]`
  // * `[USERNAME]:ssh-rsa [KEY_VALUE] google-ssh {"userName":"[USERNAME]","expireOn":"[EXPIRE_TIME]"}`
  //
  // For more information, see
  // [Adding and Removing SSH Keys](https://cloud.google.com/compute/docs/instances/adding-removing-ssh-keys).
  string ssh_key = 2;
}

// Request message for `Firewall.ListIngressRules`.
message ListIngressRulesRequest {
  // Name of the Firewall collection to retrieve.
  // Example: `apps/myapp/firewall/ingressRules`.
  string parent = 1;

  // Maximum results to return per page.
  int32 page_size = 2;

  // Continuation token for fetching the next page of results.
  string page_token = 3;

  // A valid IP Address. If set, only rules matching this address will be
  // returned. The first returned rule will be the rule that fires on requests
  // from this IP.
  string matching_address = 4;
}

// Manages instances of a version.
service Instances {
  option (google.api.default_host) = "appengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/appengine.admin,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists the instances of a version.
  //
  // Tip: To aggregate details about instances over time, see the
  // [Stackdriver Monitoring API](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/list).
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=apps/*/services/*/versions/*}/instances"
    };
  }

  // Gets instance information.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1beta/{name=apps/*/services/*/versions/*/instances/*}"
    };
  }

  // Stops a running instance.
  //
  // The instance might be automatically recreated based on the scaling settings
  // of the version. For more information, see "How Instances are Managed"
  // ([standard environment](https://cloud.google.com/appengine/docs/standard/python/how-instances-are-managed) |
  // [flexible environment](https://cloud.google.com/appengine/docs/flexible/python/how-instances-are-managed)).
  //
  // To ensure that instances are not re-created and avoid getting billed, you
  // can stop all instances within the target version by changing the serving
  // status of the version to `STOPPED` with the
  // [`apps.services.versions.patch`](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1/apps.services.versions/patch)
  // method.
  rpc DeleteInstance(DeleteInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=apps/*/services/*/versions/*/instances/*}"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadataV1Beta"
    };
  }

  // Enables debugging on a VM instance. This allows you to use the SSH
  // command to connect to the virtual machine where the instance lives.
  // While in "debug mode", the instance continues to serve live traffic.
  // You should delete the instance when you are done debugging and then
  // allow the system to take over and determine if another instance
  // should be started.
  //
  // Only applicable for instances in App Engine flexible environment.
  rpc DebugInstance(DebugInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{name=apps/*/services/*/versions/*/instances/*}:debug"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadataV1Beta"
    };
  }
}

// Response message for `Firewall.ListIngressRules`.
message ListIngressRulesResponse {
  // The ingress FirewallRules for this application.
  repeated FirewallRule ingress_rules = 1;

  // Continuation token for fetching the next page of results.
  string next_page_token = 2;
}

// Request message for `Firewall.BatchUpdateIngressRules`.
message BatchUpdateIngressRulesRequest {
  // Name of the Firewall collection to set.
  // Example: `apps/myapp/firewall/ingressRules`.
  string name = 1;

  // A list of FirewallRules to replace the existing set.
  repeated FirewallRule ingress_rules = 2;
}

// Response message for `Firewall.UpdateAllIngressRules`.
message BatchUpdateIngressRulesResponse {
  // The full list of ingress FirewallRules for this application.
  repeated FirewallRule ingress_rules = 1;
}

// Request message for `Firewall.CreateIngressRule`.
message CreateIngressRuleRequest {
  // Name of the parent Firewall collection in which to create a new rule.
  // Example: `apps/myapp/firewall/ingressRules`.
  string parent = 1;

  // A FirewallRule containing the new resource.
  //
  // The user may optionally provide a position at which the new rule will be
  // placed. The positions define a sequential list starting at 1. If a rule
  // already exists at the given position, rules greater than the provided
  // position will be moved forward by one.
  //
  // If no position is provided, the server will place the rule as the second to
  // last rule in the sequence before the required default allow-all or deny-all
  // rule.
  FirewallRule rule = 2;
}

// Request message for `Firewall.GetIngressRule`.
message GetIngressRuleRequest {
  // Name of the Firewall resource to retrieve.
  // Example: `apps/myapp/firewall/ingressRules/100`.
  string name = 1;
}

// Request message for `Firewall.UpdateIngressRule`.
message UpdateIngressRuleRequest {
  // Name of the Firewall resource to update.
  // Example: `apps/myapp/firewall/ingressRules/100`.
  string name = 1;

  // A FirewallRule containing the updated resource
  FirewallRule rule = 2;

  // Standard field mask for the set of fields to be updated.
  google.protobuf.FieldMask update_mask = 3;
}

// Request message for `Firewall.DeleteIngressRule`.
message DeleteIngressRuleRequest {
  // Name of the Firewall resource to delete.
  // Example: `apps/myapp/firewall/ingressRules/100`.
  string name = 1;
}

// Request message for `AuthorizedDomains.ListAuthorizedDomains`.
message ListAuthorizedDomainsRequest {
  // Name of the parent Application resource. Example: `apps/myapp`.
  string parent = 1;

  // Maximum results to return per page.
  int32 page_size = 2;

  // Continuation token for fetching the next page of results.
  string page_token = 3;
}

// Response message for `AuthorizedDomains.ListAuthorizedDomains`.
message ListAuthorizedDomainsResponse {
  // The authorized domains belonging to the user.
  repeated AuthorizedDomain domains = 1;

  // Continuation token for fetching the next page of results.
  string next_page_token = 2;
}

// Request message for `AuthorizedCertificates.ListAuthorizedCertificates`.
message ListAuthorizedCertificatesRequest {
  // Name of the parent `Application` resource. Example: `apps/myapp`.
  string parent = 1;

  // Controls the set of fields returned in the `LIST` response.
  AuthorizedCertificateView view = 4;

  // Maximum results to return per page.
  int32 page_size = 2;

  // Continuation token for fetching the next page of results.
  string page_token = 3;
}

// Response message for `AuthorizedCertificates.ListAuthorizedCertificates`.
message ListAuthorizedCertificatesResponse {
  // The SSL certificates the user is authorized to administer.
  repeated AuthorizedCertificate certificates = 1;

  // Continuation token for fetching the next page of results.
  string next_page_token = 2;
}

// Firewall resources are used to define a collection of access control rules
// for an Application. Each rule is defined with a position which specifies
// the rule's order in the sequence of rules, an IP range to be matched against
// requests, and an action to take upon matching requests.
//
// Every request is evaluated against the Firewall rules in priority order.
// Processesing stops at the first rule which matches the request's IP address.
// A final rule always specifies an action that applies to all remaining
// IP addresses. The default final rule for a newly-created application will be
// set to "allow" if not otherwise specified by the user.
service Firewall {
  option (google.api.default_host) = "appengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/appengine.admin,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists the firewall rules of an application.
  rpc ListIngressRules(ListIngressRulesRequest) returns (ListIngressRulesResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=apps/*}/firewall/ingressRules"
    };
  }

  // Replaces the entire firewall ruleset in one bulk operation. This overrides
  // and replaces the rules of an existing firewall with the new rules.
  //
  // If the final rule does not match traffic with the '*' wildcard IP range,
  // then an "allow all" rule is explicitly added to the end of the list.
  rpc BatchUpdateIngressRules(BatchUpdateIngressRulesRequest) returns (BatchUpdateIngressRulesResponse) {
    option (google.api.http) = {
      post: "/v1beta/{name=apps/*/firewall/ingressRules}:batchUpdate"
      body: "*"
    };
  }

  // Creates a firewall rule for the application.
  rpc CreateIngressRule(CreateIngressRuleRequest) returns (FirewallRule) {
    option (google.api.http) = {
      post: "/v1beta/{parent=apps/*}/firewall/ingressRules"
      body: "rule"
    };
  }

  // Gets the specified firewall rule.
  rpc GetIngressRule(GetIngressRuleRequest) returns (FirewallRule) {
    option (google.api.http) = {
      get: "/v1beta/{name=apps/*/firewall/ingressRules/*}"
    };
  }

  // Updates the specified firewall rule.
  rpc UpdateIngressRule(UpdateIngressRuleRequest) returns (FirewallRule) {
    option (google.api.http) = {
      patch: "/v1beta/{name=apps/*/firewall/ingressRules/*}"
      body: "rule"
    };
  }

  // Deletes the specified firewall rule.
  rpc DeleteIngressRule(DeleteIngressRuleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=apps/*/firewall/ingressRules/*}"
    };
  }
}

// Request message for `AuthorizedCertificates.GetAuthorizedCertificate`.
message GetAuthorizedCertificateRequest {
  // Name of the resource requested. Example:
  // `apps/myapp/authorizedCertificates/12345`.
  string name = 1;

  // Controls the set of fields returned in the `GET` response.
  AuthorizedCertificateView view = 2;
}

// Request message for `AuthorizedCertificates.CreateAuthorizedCertificate`.
message CreateAuthorizedCertificateRequest {
  // Name of the parent `Application` resource. Example: `apps/myapp`.
  string parent = 1;

  // SSL certificate data.
  AuthorizedCertificate certificate = 2;
}

// Request message for `AuthorizedCertificates.UpdateAuthorizedCertificate`.
message UpdateAuthorizedCertificateRequest {
  // Name of the resource to update. Example:
  // `apps/myapp/authorizedCertificates/12345`.
  string name = 1;

  // An `AuthorizedCertificate` containing the updated resource. Only fields set
  // in the field mask will be updated.
  AuthorizedCertificate certificate = 2;

  // Standard field mask for the set of fields to be updated. Updates are only
  // supported on the `certificate_raw_data` and `display_name` fields.
  google.protobuf.FieldMask update_mask = 3;
}

// Request message for `AuthorizedCertificates.DeleteAuthorizedCertificate`.
message DeleteAuthorizedCertificateRequest {
  // Name of the resource to delete. Example:
  // `apps/myapp/authorizedCertificates/12345`.
  string name = 1;
}

// Request message for `DomainMappings.ListDomainMappings`.
message ListDomainMappingsRequest {
  // Name of the parent Application resource. Example: `apps/myapp`.
  string parent = 1;

  // Maximum results to return per page.
  int32 page_size = 2;

  // Continuation token for fetching the next page of results.
  string page_token = 3;
}

// Response message for `DomainMappings.ListDomainMappings`.
message ListDomainMappingsResponse {
  // The domain mappings for the application.
  repeated DomainMapping domain_mappings = 1;

  // Continuation token for fetching the next page of results.
  string next_page_token = 2;
}

// Request message for `DomainMappings.GetDomainMapping`.
message GetDomainMappingRequest {
  // Name of the resource requested. Example:
  // `apps/myapp/domainMappings/example.com`.
  string name = 1;
}

// Request message for `DomainMappings.CreateDomainMapping`.
message CreateDomainMappingRequest {
  // Name of the parent Application resource. Example: `apps/myapp`.
  string parent = 1;

  // Domain mapping configuration.
  DomainMapping domain_mapping = 2;

  // Whether the domain creation should override any existing mappings for this
  // domain. By default, overrides are rejected.
  DomainOverrideStrategy override_strategy = 4;
}

// Manages domains a user is authorized to administer. To authorize use of a
// domain, verify ownership via
// [Webmaster Central](https://www.google.com/webmasters/verification/home).
service AuthorizedDomains {
  option (google.api.default_host) = "appengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/appengine.admin,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists all domains the user is authorized to administer.
  rpc ListAuthorizedDomains(ListAuthorizedDomainsRequest) returns (ListAuthorizedDomainsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=apps/*}/authorizedDomains"
    };
  }
}

// Request message for `DomainMappings.UpdateDomainMapping`.
message UpdateDomainMappingRequest {
  // Name of the resource to update. Example:
  // `apps/myapp/domainMappings/example.com`.
  string name = 1;

  // A domain mapping containing the updated resource. Only fields set
  // in the field mask will be updated.
  DomainMapping domain_mapping = 2;

  // Standard field mask for the set of fields to be updated.
  google.protobuf.FieldMask update_mask = 3;
}

// Request message for `DomainMappings.DeleteDomainMapping`.
message DeleteDomainMappingRequest {
  // Name of the resource to delete. Example:
  // `apps/myapp/domainMappings/example.com`.
  string name = 1;
}

// Manages SSL certificates a user is authorized to administer. A user can
// administer any SSL certificates applicable to their authorized domains.
service AuthorizedCertificates {
  option (google.api.default_host) = "appengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/appengine.admin,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists all SSL certificates the user is authorized to administer.
  rpc ListAuthorizedCertificates(ListAuthorizedCertificatesRequest) returns (ListAuthorizedCertificatesResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=apps/*}/authorizedCertificates"
    };
  }

  // Gets the specified SSL certificate.
  rpc GetAuthorizedCertificate(GetAuthorizedCertificateRequest) returns (AuthorizedCertificate) {
    option (google.api.http) = {
      get: "/v1beta/{name=apps/*/authorizedCertificates/*}"
    };
  }

  // Uploads the specified SSL certificate.
  rpc CreateAuthorizedCertificate(CreateAuthorizedCertificateRequest) returns (AuthorizedCertificate) {
    option (google.api.http) = {
      post: "/v1beta/{parent=apps/*}/authorizedCertificates"
      body: "certificate"
    };
  }

  // Updates the specified SSL certificate. To renew a certificate and maintain
  // its existing domain mappings, update `certificate_data` with a new
  // certificate. The new certificate must be applicable to the same domains as
  // the original certificate. The certificate `display_name` may also be
  // updated.
  rpc UpdateAuthorizedCertificate(UpdateAuthorizedCertificateRequest) returns (AuthorizedCertificate) {
    option (google.api.http) = {
      patch: "/v1beta/{name=apps/*/authorizedCertificates/*}"
      body: "certificate"
    };
  }

  // Deletes the specified SSL certificate.
  rpc DeleteAuthorizedCertificate(DeleteAuthorizedCertificateRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=apps/*/authorizedCertificates/*}"
    };
  }
}

// Fields that should be returned when an AuthorizedCertificate resource is
// retrieved.
enum AuthorizedCertificateView {
  // Basic certificate information, including applicable domains and expiration
  // date.
  BASIC_CERTIFICATE = 0;

  // The information from `BASIC_CERTIFICATE`, plus detailed information on the
  // domain mappings that have this certificate mapped.
  FULL_CERTIFICATE = 1;
}

// Override strategy for mutating an existing mapping.
enum DomainOverrideStrategy {
  // Strategy unspecified. Defaults to `STRICT`.
  UNSPECIFIED_DOMAIN_OVERRIDE_STRATEGY = 0;

  // Overrides not allowed. If a mapping already exists for the
  // specified domain, the request will return an ALREADY_EXISTS (409).
  STRICT = 1;

  // Overrides allowed. If a mapping already exists for the specified domain,
  // the request will overwrite it. Note that this might stop another
  // Google product from serving. For example, if the domain is
  // mapped to another App Engine application, that app will no
  // longer serve from that domain.
  OVERRIDE = 2;
}

// Manages domains serving an application.
service DomainMappings {
  option (google.api.default_host) = "appengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/appengine.admin,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Lists the domain mappings on an application.
  rpc ListDomainMappings(ListDomainMappingsRequest) returns (ListDomainMappingsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=apps/*}/domainMappings"
    };
  }

  // Gets the specified domain mapping.
  rpc GetDomainMapping(GetDomainMappingRequest) returns (DomainMapping) {
    option (google.api.http) = {
      get: "/v1beta/{name=apps/*/domainMappings/*}"
    };
  }

  // Maps a domain to an application. A user must be authorized to administer a
  // domain in order to map it to an application. For a list of available
  // authorized domains, see [`AuthorizedDomains.ListAuthorizedDomains`]().
  rpc CreateDomainMapping(CreateDomainMappingRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=apps/*}/domainMappings"
      body: "domain_mapping"
    };
    option (google.longrunning.operation_info) = {
      response_type: "DomainMapping"
      metadata_type: "OperationMetadataV1Beta"
    };
  }

  // Updates the specified domain mapping. To map an SSL certificate to a
  // domain mapping, update `certificate_id` to point to an `AuthorizedCertificate`
  // resource. A user must be authorized to administer the associated domain
  // in order to update a `DomainMapping` resource.
  rpc UpdateDomainMapping(UpdateDomainMappingRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{name=apps/*/domainMappings/*}"
      body: "domain_mapping"
    };
    option (google.longrunning.operation_info) = {
      response_type: "DomainMapping"
      metadata_type: "OperationMetadataV1Beta"
    };
  }

  // Deletes the specified domain mapping. A user must be authorized to
  // administer the associated domain in order to delete a `DomainMapping`
  // resource.
  rpc DeleteDomainMapping(DeleteDomainMappingRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=apps/*/domainMappings/*}"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadataV1Beta"
    };
  }
}
