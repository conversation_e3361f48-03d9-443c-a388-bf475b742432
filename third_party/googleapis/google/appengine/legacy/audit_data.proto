// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.legacy;

option go_package = "google.golang.org/genproto/googleapis/appengine/legacy;legacy";
option java_multiple_files = true;
option java_outer_classname = "AuditDataProto";
option java_package = "com.google.appengine.legacy";

// Admin Console legacy audit log.
message AuditData {
  // Text description of the admin event.
  // This is the "Event" column in Admin Console's Admin Logs.
  string event_message = 1;

  // Arbitrary event data.
  // This is the "Result" column in Admin Console's Admin Logs.
  map<string, string> event_data = 2;
}
