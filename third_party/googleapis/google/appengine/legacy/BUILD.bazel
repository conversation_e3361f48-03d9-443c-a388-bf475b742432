# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "legacy_proto",
    srcs = [
        "audit_data.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "legacy_java_proto",
    deps = [":legacy_proto"],
)

java_grpc_library(
    name = "legacy_java_grpc",
    srcs = [":legacy_proto"],
    deps = [":legacy_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "legacy_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/appengine/legacy",
    protos = [":legacy_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "legacy_moved_proto",
    srcs = [":legacy_proto"],
    deps = [
        "//google/api:annotations_proto",
    ],
)

py_proto_library(
    name = "legacy_py_proto",
    deps = [":legacy_moved_proto"],
)

py_grpc_library(
    name = "legacy_py_grpc",
    srcs = [":legacy_moved_proto"],
    deps = [":legacy_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "legacy_php_proto",
    deps = [":legacy_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "legacy_ruby_proto",
    deps = [":legacy_proto"],
)

ruby_grpc_library(
    name = "legacy_ruby_grpc",
    srcs = [":legacy_proto"],
    deps = [":legacy_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "legacy_csharp_proto",
    deps = [":legacy_proto"],
)

csharp_grpc_library(
    name = "legacy_csharp_grpc",
    srcs = [":legacy_proto"],
    deps = [":legacy_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "legacy_cc_proto",
    deps = [":legacy_proto"],
)

cc_grpc_library(
    name = "legacy_cc_grpc",
    srcs = [":legacy_proto"],
    grpc_only = True,
    deps = [":legacy_cc_proto"],
)
