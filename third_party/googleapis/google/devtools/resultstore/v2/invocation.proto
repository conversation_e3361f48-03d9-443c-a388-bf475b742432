// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.resultstore.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/devtools/resultstore/v2/common.proto";
import "google/devtools/resultstore/v2/coverage.proto";
import "google/devtools/resultstore/v2/coverage_summary.proto";
import "google/devtools/resultstore/v2/file.proto";
import "google/devtools/resultstore/v2/file_processing_error.proto";

option go_package = "google.golang.org/genproto/googleapis/devtools/resultstore/v2;resultstore";
option java_multiple_files = true;
option java_outer_classname = "InvocationProto";
option java_package = "com.google.devtools.resultstore.v2";

// An Invocation typically represents the result of running a tool. Each has a
// unique ID, typically generated by the server. Target resources under each
// Invocation contain the bulk of the data.
message Invocation {
  option (google.api.resource) = {
    type: "resultstore.googleapis.com/Invocation"
    pattern: "invocations/{invocation}"
  };

  // The resource ID components that identify the Invocation.
  message Id {
    // The Invocation ID.
    string invocation_id = 1;
  }

  // The resource name.  Its format must be:
  // invocations/${INVOCATION_ID}
  string name = 1;

  // The resource ID components that identify the Invocation. They must match
  // the resource name after proper encoding.
  Id id = 2;

  // The aggregate status of the invocation.
  StatusAttributes status_attributes = 3;

  // When this invocation started and its duration.
  Timing timing = 4;

  // Attributes of this invocation.
  InvocationAttributes invocation_attributes = 5;

  // The workspace the tool was run in.
  WorkspaceInfo workspace_info = 6;

  // Arbitrary name-value pairs.
  // This is implemented as a multi-map. Multiple properties are allowed with
  // the same key. Properties will be returned in lexicographical order by key.
  repeated Property properties = 7;

  // A list of file references for invocation level files.
  // The file IDs must be unique within this list. Duplicate file IDs will
  // result in an error. Files will be returned in lexicographical order by ID.
  // Use this field to specify build logs, and other invocation level logs.
  //
  // Files with the following reserved file IDs cause specific post-processing
  // or have special handling. These files must be immediately available to
  // ResultStore for processing when the reference is uploaded.
  //
  // build.log: The primary log for the Invocation.
  // coverage_report.lcov: Aggregate coverage report for the invocation.
  repeated File files = 8;

  // Summary of aggregate coverage across all Actions in this Invocation.
  // If missing, this data will be populated by the server from the
  // coverage_report.lcov file or the union of all ActionCoverages under this
  // invocation (in that order).
  repeated LanguageCoverageSummary coverage_summaries = 9;

  // Aggregate code coverage for all build and test Actions within this
  // Invocation. If missing, this data will be populated by the server
  // from the coverage_report.lcov file or the union of all ActionCoverages
  // under this invocation (in that order).
  AggregateCoverage aggregate_coverage = 10;

  // NOT IMPLEMENTED.
  // ResultStore will read and parse Files with reserved IDs listed above. Read
  // and parse errors for all these Files are reported here.
  // This is implemented as a map, with one FileProcessingErrors for each file.
  // Typically produced when parsing Files, but may also be provided directly
  // by clients.
  repeated FileProcessingErrors file_processing_errors = 11;
}

// If known, represents the state of the user/build-system workspace.
message WorkspaceContext {}

// Describes the workspace under which the tool was invoked, this includes
// information that was fed into the command, the source code referenced, and
// the tool itself.
message WorkspaceInfo {
  // Data about the workspace that might be useful for debugging.
  WorkspaceContext workspace_context = 1;

  // Where the tool was invoked
  string hostname = 3;

  // The client's working directory where the build/test was run from.
  string working_directory = 4;

  // Tools should set tool_tag to the name of the tool or use case.
  string tool_tag = 5;

  // The command lines invoked. The first command line is the one typed by the
  // user, then each one after that should be an expansion of the previous
  // command line.
  repeated CommandLine command_lines = 7;
}

// The command and arguments that produced this Invocation.
message CommandLine {
  // A label describing this command line.
  string label = 1;

  // The command-line tool that is run: argv[0].
  string tool = 2;

  // The arguments to the above tool: argv[1]...argv[N].
  repeated string args = 3;

  // The subcommand that was run with the tool, usually "build" or "test".
  // For example, in the Bazel command "bazel build //foo", this would be set
  // to "build". Omit if the tool doesn't accept a subcommand.  This is must
  // be a reference to one of values in args.
  string command = 4;
}

// Attributes that apply to all invocations.
message InvocationAttributes {
  // Immutable. The Cloud Project that owns this invocation (this is different
  // than the Consumer Cloud Project that calls this API). This must be set in
  // the CreateInvocation call, and can't be changed. As input, callers can set
  // this field to a project id (string) or a stringified int64 project number.
  // As output, the API populates this field with the stringified int64 project
  // number (per https://google.aip.dev/cloud/2510).
  string project_id = 1 [(google.api.field_behavior) = IMMUTABLE];

  // The list of users in the command chain.  The first user in this sequence
  // is the one who instigated the first command in the chain. For example,
  // this might contain just the user that ran a Bazel command, or a robot
  // that tested a change as part of a CI system. It could also contain the user
  // that manually triggered a CI test, then the robot that ran the test.
  repeated string users = 2;

  // Labels to categorize this invocation.
  // This is implemented as a set. All labels will be unique. Any duplicate
  // labels added will be ignored. Labels will be returned in lexicographical
  // order. Labels should be a list of words describing the Invocation. Labels
  // should be short, easy to read, and you shouldn't have more than a handful.
  // Labels should not be used for unique properties such as unique IDs. Use
  // properties in cases that don't meet these conditions.
  repeated string labels = 3;

  // This field describes the overall context or purpose of this invocation.
  // It will be used in the UI to give users more information about
  // how or why this invocation was run.
  string description = 4;

  // If this Invocation was run in the context of a larger Continuous
  // Integration build or other automated system, this field may contain more
  // information about the greater context.
  repeated InvocationContext invocation_contexts = 6;

  // Exit code of the process that ran the invocation. A non-zero value
  // means failure. For example, the exit code of a "bazel test" command.
  int32 exit_code = 7;
}

// Describes the invocation context which includes a display name and URL.
message InvocationContext {
  // A human readable name for the context under which this Invocation was run.
  string display_name = 1;

  // A URL pointing to a UI containing more information
  string url = 2;
}
