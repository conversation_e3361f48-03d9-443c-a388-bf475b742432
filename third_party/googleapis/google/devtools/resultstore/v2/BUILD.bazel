# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "resultstore_proto",
    srcs = [
        "action.proto",
        "common.proto",
        "configuration.proto",
        "configured_target.proto",
        "coverage.proto",
        "coverage_summary.proto",
        "download_metadata.proto",
        "file.proto",
        "file_processing_error.proto",
        "file_set.proto",
        "invocation.proto",
        "resultstore_download.proto",
        "resultstore_file_download.proto",
        "resultstore_upload.proto",
        "target.proto",
        "test_suite.proto",
        "upload_metadata.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "resultstore_java_proto",
    deps = [":resultstore_proto"],
)

java_grpc_library(
    name = "resultstore_java_grpc",
    srcs = [":resultstore_proto"],
    deps = [":resultstore_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "resultstore_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/devtools/resultstore/v2",
    protos = [":resultstore_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "resultstore_moved_proto",
    srcs = [":resultstore_proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

py_proto_library(
    name = "resultstore_py_proto",
    deps = [":resultstore_moved_proto"],
)

py_grpc_library(
    name = "resultstore_py_grpc",
    srcs = [":resultstore_moved_proto"],
    deps = [":resultstore_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "resultstore_php_proto",
    deps = [":resultstore_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "resultstore_ruby_proto",
    deps = [":resultstore_proto"],
)

ruby_grpc_library(
    name = "resultstore_ruby_grpc",
    srcs = [":resultstore_proto"],
    deps = [":resultstore_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "resultstore_csharp_proto",
    deps = [":resultstore_proto"],
)

csharp_grpc_library(
    name = "resultstore_csharp_grpc",
    srcs = [":resultstore_proto"],
    deps = [":resultstore_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "resultstore_cc_proto",
    deps = [":resultstore_proto"],
)

cc_grpc_library(
    name = "resultstore_cc_grpc",
    srcs = [":resultstore_proto"],
    grpc_only = True,
    deps = [":resultstore_cc_proto"],
)
