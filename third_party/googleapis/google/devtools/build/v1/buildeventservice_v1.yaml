type: google.api.Service
config_version: 3
name: buildeventservice.googleapis.com
title: Build Event Service Backend API

apis:
- name: google.devtools.build.v1.PublishBuildEvent

documentation:
  summary: <PERSON><PERSON> build events from tools such as bazel.

authentication:
  rules:
  - selector: google.devtools.build.v1.PublishBuildEvent.PublishBuildToolEventStream
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.devtools.build.v1.PublishBuildEvent.PublishLifecycleEvent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
