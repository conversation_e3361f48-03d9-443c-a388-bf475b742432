// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.cloudtrace.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/devtools/cloudtrace/v2/trace.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.Trace.V2";
option go_package = "cloud.google.com/go/trace/apiv2/tracepb;tracepb";
option java_multiple_files = true;
option java_outer_classname = "TracingProto";
option java_package = "com.google.devtools.cloudtrace.v2";
option php_namespace = "Google\\Cloud\\Trace\\V2";
option ruby_package = "Google::Cloud::Trace::V2";

// Service for collecting and viewing traces and spans within a trace.
//
// A trace is a collection of spans corresponding to a single
// operation or a set of operations in an application.
//
// A span is an individual timed event which forms a node of the trace tree.
// A single trace can contain spans from multiple services.
service TraceService {
  option (google.api.default_host) = "cloudtrace.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/trace.append";

  // Batch writes new spans to new or existing traces. You cannot update
  // existing spans.
  rpc BatchWriteSpans(BatchWriteSpansRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*}/traces:batchWrite"
      body: "*"
    };
    option (google.api.method_signature) = "name,spans";
  }

  // Creates a new span.
  rpc CreateSpan(Span) returns (Span) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/traces/*/spans/*}"
      body: "*"
    };
  }
}

// The request message for the `BatchWriteSpans` method.
message BatchWriteSpansRequest {
  // Required. The name of the project where the spans belong. The format is
  // `projects/[PROJECT_ID]`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. A list of new spans. The span names must not match existing
  // spans, otherwise the results are undefined.
  repeated Span spans = 2 [(google.api.field_behavior) = REQUIRED];
}
