type: google.api.Service
config_version: 3
name: cloudtrace.googleapis.com
title: Stackdriver Trace API

apis:
- name: google.devtools.cloudtrace.v2.TraceService

documentation:
  summary: |-
    Sends application trace data to Stackdriver Trace for viewing. Trace data
    is collected for all App Engine applications by default. Trace data from
    other applications can be provided using this API. This library is used to
    interact with the Trace API directly. If you are looking to instrument
    your application for Stackdriver Trace, we recommend using OpenTelemetry.

backend:
  rules:
  - selector: google.devtools.cloudtrace.v2.TraceService.BatchWriteSpans
    deadline: 60.0
  - selector: google.devtools.cloudtrace.v2.TraceService.CreateSpan
    deadline: 60.0

authentication:
  rules:
  - selector: google.devtools.cloudtrace.v2.TraceService.BatchWriteSpans
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/trace.append
  - selector: google.devtools.cloudtrace.v2.TraceService.CreateSpan
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/trace.append
