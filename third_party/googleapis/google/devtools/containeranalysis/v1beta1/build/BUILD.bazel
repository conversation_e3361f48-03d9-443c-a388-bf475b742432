load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "build_proto",
    srcs = [
        "build.proto",
    ],
    deps = [
        "//google/devtools/containeranalysis/v1beta1/provenance:provenance_proto",
    ],
)

proto_library_with_info(
    name = "build_proto_with_info",
    deps = [":build_proto"],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
)

java_proto_library(
    name = "build_java_proto",
    deps = [":build_proto"],
)

java_grpc_library(
    name = "build_java_grpc",
    srcs = [":build_proto"],
    deps = [":build_java_proto"],
)

##############################################################################
# Go
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "go_proto_library")

go_proto_library(
    name = "build_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/devtools/containeranalysis/v1beta1/build",
    protos = [":build_proto"],
    deps = [
        "//google/devtools/containeranalysis/v1beta1/provenance:provenance_go_proto",
    ],
)
