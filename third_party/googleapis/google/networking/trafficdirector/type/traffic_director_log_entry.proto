// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.networking.trafficdirector.type;

option csharp_namespace = "Google.Cloud.Networking.TrafficDirector.Type";
option go_package = "google.golang.org/genproto/googleapis/networking/trafficdirector/type";
option java_multiple_files = true;
option java_outer_classname = "TrafficDirectorLogEntryProto";
option java_package = "com.google.networking.trafficdirector.type";
option objc_class_prefix = "TRFCD";
option php_namespace = "Google\\Cloud\\Networking\\TrafficDirector\\Type";
option ruby_package = "Google::Cloud::Networking::TrafficDirector::Type";

// A common proto for describing how the Traffic Director handles
// xDS-connections/requests/responses.
message TrafficDirectorLogEntry {
  // Defines possible values of client type.
  enum ClientType {
    // Unspecified.
    CLIENT_TYPE_UNSPECIFIED = 0;

    // Envoy client.
    ENVOY = 1;

    // gRPC Java client.
    GRPC_JAVA = 2;

    // gRPC C++ client.
    GRPC_CPP = 3;

    // gRPC Python client.
    GRPC_PYTHON = 4;

    // gRPC Go client.
    GRPC_GO = 5;

    // gRPC Ruby client.
    GRPC_RUBY = 6;

    // gRPC Ruby client.
    GRPC_PHP = 7;

    // gRPC Node client.
    GRPC_NODE = 8;

    // gRPC CSharp client.
    GRPC_CSHARP = 9;

    // unknown client type.
    UNKNOWN = 10;
  }

  // Defines possible values of API version.
  enum TransportApiVersion {
    // Unspecified.
    TRANSPORT_API_VERSION_UNSPECIFIED = 0;

    // v2 xDS version.
    V2 = 1;

    // v3 xDS version.
    V3 = 2;
  }

  // An ID of xDS-client connecting to the Traffic Director.
  string node_id = 1;

  // The string representation of IPv4 or IPv6 address of xDS-client
  // connecting to the Traffic Director.
  // IPv4 address must be in the format defined in RFC791, four octets separated
  // by a period. Size of a string is between 7-15 characters. Example: *******
  // IPv6 address must be in one of the formats defined in RFC4291. Size of a
  // string is between 7-39 characters. Example: 2001:DB8:0:0:8:800:200C:417A
  string node_ip = 2;

  // A free text describing details of the event.
  string description = 4;

  // Type of xDS-client connecting to Traffic Director
  ClientType client_type = 5;

  // The version of xDS-client connecting to Traffic Director.
  string client_version = 6;

  // The xDS API version used by xDS clients connecting to Traffic Director.
  TransportApiVersion transport_api_version = 7;
}
