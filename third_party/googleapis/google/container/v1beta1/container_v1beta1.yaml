type: google.api.Service
config_version: 3
name: container.googleapis.com
title: Kubernetes Engine API

apis:
- name: google.container.v1beta1.ClusterManager

types:
- name: google.container.v1beta1.GetOpenIDConfigRequest
- name: google.container.v1beta1.GetOpenIDConfigResponse
- name: google.container.v1beta1.SecurityBulletinEvent
- name: google.container.v1beta1.UpgradeAvailableEvent
- name: google.container.v1beta1.UpgradeEvent

documentation:
  summary: |-
    Builds and manages container-based applications, powered by the open source
    Kubernetes technology.

authentication:
  rules:
  - selector: 'google.container.v1beta1.ClusterManager.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
