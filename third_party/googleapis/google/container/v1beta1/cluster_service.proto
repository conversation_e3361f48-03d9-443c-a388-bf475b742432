// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.container.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/rpc/code.proto";
import "google/rpc/status.proto";
import "google/type/date.proto";

option csharp_namespace = "Google.Cloud.Container.V1Beta1";
option go_package = "cloud.google.com/go/container/apiv1beta1/containerpb;containerpb";
option java_multiple_files = true;
option java_outer_classname = "ClusterServiceProto";
option java_package = "com.google.container.v1beta1";
option php_namespace = "Google\\Cloud\\Container\\V1beta1";
option ruby_package = "Google::Cloud::Container::V1beta1";
option (google.api.resource_definition) = {
  type: "pubsub.googleapis.com/Topic"
  pattern: "projects/{project}/topics/{topic}"
};

// Google Kubernetes Engine Cluster Manager v1beta1
service ClusterManager {
  option (google.api.default_host) = "container.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all clusters owned by a project in either the specified zone or all
  // zones.
  rpc ListClusters(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/clusters"
      additional_bindings {
        get: "/v1beta1/projects/{project_id}/zones/{zone}/clusters"
      }
    };
    option (google.api.method_signature) = "project_id,zone";
  }

  // Gets the details for a specific cluster.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/clusters/*}"
      additional_bindings {
        get: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}"
      }
    };
    option (google.api.method_signature) = "project_id,zone,cluster_id";
  }

  // Creates a cluster, consisting of the specified number and type of Google
  // Compute Engine instances.
  //
  // By default, the cluster is created in the project's
  // [default
  // network](https://cloud.google.com/compute/docs/networks-and-firewalls#networks).
  //
  // One firewall is added for the cluster. After cluster creation,
  // the Kubelet creates routes for each node to allow the containers
  // on that node to communicate with all other instances in the
  // cluster.
  //
  // Finally, an entry is added to the project's global metadata indicating
  // which CIDR range the cluster is using.
  rpc CreateCluster(CreateClusterRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/clusters"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters"
        body: "*"
      }
    };
    option (google.api.method_signature) = "project_id,zone,cluster";
  }

  // Updates the settings for a specific cluster.
  rpc UpdateCluster(UpdateClusterRequest) returns (Operation) {
    option (google.api.http) = {
      put: "/v1beta1/{name=projects/*/locations/*/clusters/*}"
      body: "*"
      additional_bindings {
        put: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}"
        body: "*"
      }
    };
    option (google.api.method_signature) = "project_id,zone,cluster_id,update";
  }

  // Updates the version and/or image type of a specific node pool.
  rpc UpdateNodePool(UpdateNodePoolRequest) returns (Operation) {
    option (google.api.http) = {
      put: "/v1beta1/{name=projects/*/locations/*/clusters/*/nodePools/*}"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools/{node_pool_id}/update"
        body: "*"
      }
    };
  }

  // Sets the autoscaling settings of a specific node pool.
  rpc SetNodePoolAutoscaling(SetNodePoolAutoscalingRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*/nodePools/*}:setAutoscaling"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools/{node_pool_id}/autoscaling"
        body: "*"
      }
    };
  }

  // Sets the logging service for a specific cluster.
  rpc SetLoggingService(SetLoggingServiceRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setLogging"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/logging"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,logging_service";
  }

  // Sets the monitoring service for a specific cluster.
  rpc SetMonitoringService(SetMonitoringServiceRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setMonitoring"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/monitoring"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,monitoring_service";
  }

  // Sets the addons for a specific cluster.
  rpc SetAddonsConfig(SetAddonsConfigRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setAddons"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/addons"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,addons_config";
  }

  // Sets the locations for a specific cluster.
  // Deprecated. Use
  // [projects.locations.clusters.update](https://cloud.google.com/kubernetes-engine/docs/reference/rest/v1beta1/projects.locations.clusters/update)
  // instead.
  rpc SetLocations(SetLocationsRequest) returns (Operation) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setLocations"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/locations"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,locations";
  }

  // Updates the master for a specific cluster.
  rpc UpdateMaster(UpdateMasterRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:updateMaster"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/master"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,master_version";
  }

  // Sets master auth materials. Currently supports changing the admin password
  // or a specific cluster, either via password generation or explicitly setting
  // the password.
  rpc SetMasterAuth(SetMasterAuthRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setMasterAuth"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}:setMasterAuth"
        body: "*"
      }
    };
  }

  // Deletes the cluster, including the Kubernetes endpoint and all worker
  // nodes.
  //
  // Firewalls and routes that were configured during cluster creation
  // are also deleted.
  //
  // Other Google Compute Engine resources that might be in use by the cluster,
  // such as load balancer resources, are not deleted if they weren't present
  // when the cluster was initially created.
  rpc DeleteCluster(DeleteClusterRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/clusters/*}"
      additional_bindings {
        delete: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}"
      }
    };
    option (google.api.method_signature) = "project_id,zone,cluster_id";
  }

  // Lists all operations in a project in the specified zone or all zones.
  rpc ListOperations(ListOperationsRequest) returns (ListOperationsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/operations"
      additional_bindings {
        get: "/v1beta1/projects/{project_id}/zones/{zone}/operations"
      }
    };
    option (google.api.method_signature) = "project_id,zone";
  }

  // Gets the specified operation.
  rpc GetOperation(GetOperationRequest) returns (Operation) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/operations/*}"
      additional_bindings {
        get: "/v1beta1/projects/{project_id}/zones/{zone}/operations/{operation_id}"
      }
    };
    option (google.api.method_signature) = "project_id,zone,operation_id";
  }

  // Cancels the specified operation.
  rpc CancelOperation(CancelOperationRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/operations/*}:cancel"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/operations/{operation_id}:cancel"
        body: "*"
      }
    };
    option (google.api.method_signature) = "project_id,zone,operation_id";
  }

  // Returns configuration info about the Google Kubernetes Engine service.
  rpc GetServerConfig(GetServerConfigRequest) returns (ServerConfig) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*}/serverConfig"
      additional_bindings {
        get: "/v1beta1/projects/{project_id}/zones/{zone}/serverconfig"
      }
    };
    option (google.api.method_signature) = "project_id,zone";
  }

  // Gets the public component of the cluster signing keys in
  // JSON Web Key format.
  rpc GetJSONWebKeys(GetJSONWebKeysRequest) returns (GetJSONWebKeysResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/clusters/*}/jwks"
    };
  }

  // Lists the node pools for a cluster.
  rpc ListNodePools(ListNodePoolsRequest) returns (ListNodePoolsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/clusters/*}/nodePools"
      additional_bindings {
        get: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools"
      }
    };
    option (google.api.method_signature) = "project_id,zone,cluster_id";
  }

  // Retrieves the requested node pool.
  rpc GetNodePool(GetNodePoolRequest) returns (NodePool) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/clusters/*/nodePools/*}"
      additional_bindings {
        get: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools/{node_pool_id}"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,node_pool_id";
  }

  // Creates a node pool for a cluster.
  rpc CreateNodePool(CreateNodePoolRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/clusters/*}/nodePools"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,node_pool";
  }

  // Deletes a node pool from a cluster.
  rpc DeleteNodePool(DeleteNodePoolRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/clusters/*/nodePools/*}"
      additional_bindings {
        delete: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools/{node_pool_id}"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,node_pool_id";
  }

  // CompleteNodePoolUpgrade will signal an on-going node pool upgrade to
  // complete.
  rpc CompleteNodePoolUpgrade(CompleteNodePoolUpgradeRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*/nodePools/*}:completeUpgrade"
      body: "*"
    };
  }

  // Rolls back a previously Aborted or Failed NodePool upgrade.
  // This makes no changes if the last upgrade successfully completed.
  rpc RollbackNodePoolUpgrade(RollbackNodePoolUpgradeRequest)
      returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*/nodePools/*}:rollback"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools/{node_pool_id}:rollback"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,node_pool_id";
  }

  // Sets the NodeManagement options for a node pool.
  rpc SetNodePoolManagement(SetNodePoolManagementRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*/nodePools/*}:setManagement"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools/{node_pool_id}/setManagement"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,node_pool_id,management";
  }

  // Sets labels on a cluster.
  rpc SetLabels(SetLabelsRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setResourceLabels"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/resourceLabels"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,resource_labels,label_fingerprint";
  }

  // Enables or disables the ABAC authorization mechanism on a cluster.
  rpc SetLegacyAbac(SetLegacyAbacRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setLegacyAbac"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/legacyAbac"
        body: "*"
      }
    };
    option (google.api.method_signature) = "project_id,zone,cluster_id,enabled";
  }

  // Starts master IP rotation.
  rpc StartIPRotation(StartIPRotationRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:startIpRotation"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}:startIpRotation"
        body: "*"
      }
    };
    option (google.api.method_signature) = "project_id,zone,cluster_id";
  }

  // Completes master IP rotation.
  rpc CompleteIPRotation(CompleteIPRotationRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:completeIpRotation"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}:completeIpRotation"
        body: "*"
      }
    };
    option (google.api.method_signature) = "project_id,zone,cluster_id";
  }

  // SetNodePoolSizeRequest sets the size of a node pool. The new size will be
  // used for all replicas, including future replicas created by modifying
  // [NodePool.locations][google.container.v1beta1.NodePool.locations].
  rpc SetNodePoolSize(SetNodePoolSizeRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*/nodePools/*}:setSize"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}/nodePools/{node_pool_id}/setSize"
        body: "*"
      }
    };
  }

  // Enables or disables Network Policy for a cluster.
  rpc SetNetworkPolicy(SetNetworkPolicyRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setNetworkPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}:setNetworkPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,network_policy";
  }

  // Sets the maintenance policy for a cluster.
  rpc SetMaintenancePolicy(SetMaintenancePolicyRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/clusters/*}:setMaintenancePolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/projects/{project_id}/zones/{zone}/clusters/{cluster_id}:setMaintenancePolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) =
        "project_id,zone,cluster_id,maintenance_policy";
  }

  // Lists subnetworks that can be used for creating clusters in a project.
  rpc ListUsableSubnetworks(ListUsableSubnetworksRequest)
      returns (ListUsableSubnetworksResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*}/aggregated/usableSubnetworks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Checks the cluster compatibility with Autopilot mode, and returns a list of
  // compatibility issues.
  rpc CheckAutopilotCompatibility(CheckAutopilotCompatibilityRequest)
      returns (CheckAutopilotCompatibilityResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/clusters/*}:checkAutopilotCompatibility"
    };
  }

  // Fetches locations that offer Google Kubernetes Engine.
  rpc ListLocations(ListLocationsRequest) returns (ListLocationsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*}/locations"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Parameters that can be configured on Linux nodes.
message LinuxNodeConfig {
  // Possible cgroup modes that can be used.
  enum CgroupMode {
    // CGROUP_MODE_UNSPECIFIED is when unspecified cgroup configuration is used.
    // The default for the GKE node OS image will be used.
    CGROUP_MODE_UNSPECIFIED = 0;

    // CGROUP_MODE_V1 specifies to use cgroupv1 for the cgroup configuration on
    // the node image.
    CGROUP_MODE_V1 = 1;

    // CGROUP_MODE_V2 specifies to use cgroupv2 for the cgroup configuration on
    // the node image.
    CGROUP_MODE_V2 = 2;
  }

  // Hugepages amount in both 2m and 1g size
  message HugepagesConfig {
    // Optional. Amount of 2M hugepages
    optional int32 hugepage_size2m = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Amount of 1G hugepages
    optional int32 hugepage_size1g = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // The Linux kernel parameters to be applied to the nodes and all pods running
  // on the nodes.
  //
  // The following parameters are supported.
  //
  // net.core.busy_poll
  // net.core.busy_read
  // net.core.netdev_max_backlog
  // net.core.rmem_max
  // net.core.wmem_default
  // net.core.wmem_max
  // net.core.optmem_max
  // net.core.somaxconn
  // net.ipv4.tcp_rmem
  // net.ipv4.tcp_wmem
  // net.ipv4.tcp_tw_reuse
  map<string, string> sysctls = 1;

  // cgroup_mode specifies the cgroup mode to be used on the node.
  CgroupMode cgroup_mode = 2;

  // Optional. Amounts for 2M and 1G hugepages
  optional HugepagesConfig hugepages = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Parameters that can be configured on Windows nodes.
// Windows Node Config that define the parameters that will be used to
// configure the Windows node pool settings
message WindowsNodeConfig {
  // Possible OS version that can be used.
  enum OSVersion {
    // When OSVersion is not specified
    OS_VERSION_UNSPECIFIED = 0;

    // LTSC2019 specifies to use LTSC2019 as the Windows Servercore Base Image
    OS_VERSION_LTSC2019 = 1;

    // LTSC2022 specifies to use LTSC2022 as the Windows Servercore Base Image
    OS_VERSION_LTSC2022 = 2;
  }

  // OSVersion specifies the Windows node config to be used on the node
  OSVersion os_version = 1;
}

// Node kubelet configs.
message NodeKubeletConfig {
  // Control the CPU management policy on the node.
  // See
  // https://kubernetes.io/docs/tasks/administer-cluster/cpu-management-policies/
  //
  // The following values are allowed.
  // * "none": the default, which represents the existing scheduling behavior.
  // * "static": allows pods with certain resource characteristics to be granted
  // increased CPU affinity and exclusivity on the node.
  // The default value is 'none' if unspecified.
  string cpu_manager_policy = 1;

  // Enable CPU CFS quota enforcement for containers that specify CPU limits.
  //
  // This option is enabled by default which makes kubelet use CFS quota
  // (https://www.kernel.org/doc/Documentation/scheduler/sched-bwc.txt) to
  // enforce container CPU limits. Otherwise, CPU limits will not be enforced at
  // all.
  //
  // Disable this option to mitigate CPU throttling problems while still having
  // your pods to be in Guaranteed QoS class by specifying the CPU limits.
  //
  // The default value is 'true' if unspecified.
  google.protobuf.BoolValue cpu_cfs_quota = 2;

  // Set the CPU CFS quota period value 'cpu.cfs_period_us'.
  //
  // The string must be a sequence of decimal numbers, each with optional
  // fraction and a unit suffix, such as "300ms".
  // Valid time units are "ns", "us" (or "µs"), "ms", "s", "m", "h".
  // The value must be a positive duration.
  string cpu_cfs_quota_period = 3;

  // Set the Pod PID limits. See
  // https://kubernetes.io/docs/concepts/policy/pid-limiting/#pod-pid-limits
  //
  // Controls the maximum number of processes allowed to run in a pod. The value
  // must be greater than or equal to 1024 and less than 4194304.
  int64 pod_pids_limit = 4;

  // Enable or disable Kubelet read only port.
  optional bool insecure_kubelet_readonly_port_enabled = 7;
}

// Parameters that describe the nodes in a cluster.
//
// GKE Autopilot clusters do not
// recognize parameters in `NodeConfig`. Use
// [AutoprovisioningNodePoolDefaults][google.container.v1beta1.AutoprovisioningNodePoolDefaults]
// instead.
message NodeConfig {
  // The name of a Google Compute Engine [machine
  // type](https://cloud.google.com/compute/docs/machine-types).
  //
  // If unspecified, the default machine type is
  // `e2-medium`.
  string machine_type = 1;

  // Size of the disk attached to each node, specified in GB.
  // The smallest allowed disk size is 10GB.
  //
  // If unspecified, the default disk size is 100GB.
  int32 disk_size_gb = 2;

  // The set of Google API scopes to be made available on all of the
  // node VMs under the "default" service account.
  //
  // The following scopes are recommended, but not required, and by default are
  // not included:
  //
  // * `https://www.googleapis.com/auth/compute` is required for mounting
  // persistent storage on your nodes.
  // * `https://www.googleapis.com/auth/devstorage.read_only` is required for
  // communicating with **gcr.io**
  // (the [Google Container
  // Registry](https://cloud.google.com/container-registry/)).
  //
  // If unspecified, no scopes are added, unless Cloud Logging or Cloud
  // Monitoring are enabled, in which case their required scopes will be added.
  repeated string oauth_scopes = 3;

  // The Google Cloud Platform Service Account to be used by the node VMs.
  // Specify the email address of the Service Account; otherwise, if no Service
  // Account is specified, the "default" service account is used.
  string service_account = 9;

  // The metadata key/value pairs assigned to instances in the cluster.
  //
  // Keys must conform to the regexp `[a-zA-Z0-9-_]+` and be less than 128 bytes
  // in length. These are reflected as part of a URL in the metadata server.
  // Additionally, to avoid ambiguity, keys must not conflict with any other
  // metadata keys for the project or be one of the reserved keys:
  //
  //  - "cluster-location"
  //  - "cluster-name"
  //  - "cluster-uid"
  //  - "configure-sh"
  //  - "containerd-configure-sh"
  //  - "enable-oslogin"
  //  - "gci-ensure-gke-docker"
  //  - "gci-metrics-enabled"
  //  - "gci-update-strategy"
  //  - "instance-template"
  //  - "kube-env"
  //  - "startup-script"
  //  - "user-data"
  //  - "disable-address-manager"
  //  - "windows-startup-script-ps1"
  //  - "common-psm1"
  //  - "k8s-node-setup-psm1"
  //  - "install-ssh-psm1"
  //  - "user-profile-psm1"
  //
  // Values are free-form strings, and only have meaning as interpreted by
  // the image running in the instance. The only restriction placed on them is
  // that each value's size must be less than or equal to 32 KB.
  //
  // The total size of all keys and values must be less than 512 KB.
  map<string, string> metadata = 4;

  // The image type to use for this node. Note that for a given image type,
  // the latest version of it will be used. Please see
  // https://cloud.google.com/kubernetes-engine/docs/concepts/node-images for
  // available image types.
  string image_type = 5;

  // The map of Kubernetes labels (key/value pairs) to be applied to each node.
  // These will added in addition to any default label(s) that
  // Kubernetes may apply to the node.
  // In case of conflict in label keys, the applied set may differ depending on
  // the Kubernetes version -- it's best to assume the behavior is undefined
  // and conflicts should be avoided.
  // For more information, including usage and the valid values, see:
  // https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  map<string, string> labels = 6;

  // The number of local SSD disks to be attached to the node.
  //
  // The limit for this value is dependent upon the maximum number of
  // disks available on a machine per zone. See:
  // https://cloud.google.com/compute/docs/disks/local-ssd
  // for more information.
  int32 local_ssd_count = 7;

  // The list of instance tags applied to all nodes. Tags are used to identify
  // valid sources or targets for network firewalls and are specified by
  // the client during cluster or node pool creation. Each tag within the list
  // must comply with RFC1035.
  repeated string tags = 8;

  // Whether the nodes are created as preemptible VM instances. See:
  // https://cloud.google.com/compute/docs/instances/preemptible for more
  // information about preemptible VM instances.
  bool preemptible = 10;

  // A list of hardware accelerators to be attached to each node.
  // See https://cloud.google.com/compute/docs/gpus for more information about
  // support for GPUs.
  repeated AcceleratorConfig accelerators = 11;

  // Sandbox configuration for this node.
  SandboxConfig sandbox_config = 17;

  // Setting this field will assign instances of this
  // pool to run on the specified node group. This is useful for running
  // workloads on [sole tenant
  // nodes](https://cloud.google.com/compute/docs/nodes/sole-tenant-nodes).
  string node_group = 18;

  // The optional reservation affinity. Setting this field will apply
  // the specified [Zonal Compute
  // Reservation](https://cloud.google.com/compute/docs/instances/reserving-zonal-resources)
  // to this node pool.
  ReservationAffinity reservation_affinity = 19;

  // Type of the disk attached to each node (e.g. 'pd-standard', 'pd-ssd' or
  // 'pd-balanced')
  //
  // If unspecified, the default disk type is 'pd-standard'
  string disk_type = 12;

  // Minimum CPU platform to be used by this instance. The instance may be
  // scheduled on the specified or newer CPU platform. Applicable values are the
  // friendly names of CPU platforms, such as
  // `minCpuPlatform: "Intel Haswell"` or
  // `minCpuPlatform: "Intel Sandy Bridge"`. For more
  // information, read [how to specify min CPU
  // platform](https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform).
  string min_cpu_platform = 13;

  // The workload metadata configuration for this node.
  WorkloadMetadataConfig workload_metadata_config = 14;

  // List of kubernetes taints to be applied to each node.
  //
  // For more information, including usage and the valid values, see:
  // https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  repeated NodeTaint taints = 15;

  //
  // The Customer Managed Encryption Key used to encrypt the boot disk attached
  // to each node in the node pool. This should be of the form
  // projects/[KEY_PROJECT_ID]/locations/[LOCATION]/keyRings/[RING_NAME]/cryptoKeys/[KEY_NAME].
  // For more information about protecting resources with Cloud KMS Keys please
  // see:
  // https://cloud.google.com/compute/docs/disks/customer-managed-encryption
  string boot_disk_kms_key = 23;

  // Shielded Instance options.
  ShieldedInstanceConfig shielded_instance_config = 20;

  // Parameters that can be configured on Linux nodes.
  LinuxNodeConfig linux_node_config = 21;

  // Node kubelet configs.
  NodeKubeletConfig kubelet_config = 22;

  // Parameters for the ephemeral storage filesystem.
  // If unspecified, ephemeral storage is backed by the boot disk.
  EphemeralStorageConfig ephemeral_storage_config = 24;

  // GCFS (Google Container File System) configs.
  GcfsConfig gcfs_config = 25;

  // Advanced features for the Compute Engine VM.
  AdvancedMachineFeatures advanced_machine_features = 26;

  // Enable or disable gvnic on the node pool.
  VirtualNIC gvnic = 29;

  // Spot flag for enabling Spot VM, which is a rebrand of
  // the existing preemptible flag.
  bool spot = 32;

  // Confidential nodes config.
  // All the nodes in the node pool will be Confidential VM once enabled.
  ConfidentialNodes confidential_nodes = 35;

  // Enable or disable NCCL fast socket for the node pool.
  optional FastSocket fast_socket = 36;

  // The resource labels for the node pool to use to annotate any related
  // Google Compute Engine resources.
  map<string, string> resource_labels = 37;

  // Logging configuration.
  NodePoolLoggingConfig logging_config = 38;

  // Parameters that can be configured on Windows nodes.
  WindowsNodeConfig windows_node_config = 39;

  // Parameters for using raw-block Local NVMe SSDs.
  LocalNvmeSsdBlockConfig local_nvme_ssd_block_config = 40;

  // Parameters for the node ephemeral storage using Local SSDs.
  // If unspecified, ephemeral storage is backed by the boot disk.
  // This field is functionally equivalent to the ephemeral_storage_config
  EphemeralStorageLocalSsdConfig ephemeral_storage_local_ssd_config = 41;

  // Parameters for node pools to be backed by shared sole tenant node groups.
  SoleTenantConfig sole_tenant_config = 42;

  // Parameters for containerd customization.
  ContainerdConfig containerd_config = 43;

  // HostMaintenancePolicy contains the desired maintenance policy for the
  // Google Compute Engine hosts.
  HostMaintenancePolicy host_maintenance_policy = 44;

  // A map of resource manager tag keys and values to be attached to the nodes.
  ResourceManagerTags resource_manager_tags = 45;

  // Optional. Reserved for future use.
  bool enable_confidential_storage = 46
      [(google.api.field_behavior) = OPTIONAL];

  // List of secondary boot disks attached to the nodes.
  repeated SecondaryBootDisk secondary_boot_disks = 48;

  // Secondary boot disk update strategy.
  optional SecondaryBootDiskUpdateStrategy secondary_boot_disk_update_strategy =
      50;
}

// Specifies options for controlling advanced machine features.
message AdvancedMachineFeatures {
  // The number of threads per physical core. To disable simultaneous
  // multithreading (SMT) set this to 1. If unset, the maximum number of threads
  // supported per core by the underlying processor is assumed.
  optional int64 threads_per_core = 1;

  // Whether or not to enable nested virtualization (defaults to false).
  optional bool enable_nested_virtualization = 2;
}

// Parameters for node pool-level network config.
message NodeNetworkConfig {
  // Configuration of all network bandwidth tiers
  message NetworkPerformanceConfig {
    // Node network tier
    enum Tier {
      // Default value
      TIER_UNSPECIFIED = 0;

      // Higher bandwidth, actual values based on VM size.
      TIER_1 = 1;
    }

    // Specifies the total network bandwidth tier for the NodePool.
    optional Tier total_egress_bandwidth_tier = 1;

    // Specifies the network bandwidth tier for the NodePool for traffic to
    // external/public IP addresses.
    optional Tier external_ip_egress_bandwidth_tier = 2;
  }

  // Input only. Whether to create a new range for pod IPs in this node pool.
  // Defaults are provided for `pod_range` and `pod_ipv4_cidr_block` if they
  // are not specified.
  //
  // If neither `create_pod_range` or `pod_range` are specified, the
  // cluster-level default (`ip_allocation_policy.cluster_ipv4_cidr_block`) is
  // used.
  //
  // Only applicable if `ip_allocation_policy.use_ip_aliases` is true.
  //
  // This field cannot be changed after the node pool has been created.
  bool create_pod_range = 4 [(google.api.field_behavior) = INPUT_ONLY];

  // The ID of the secondary range for pod IPs.
  // If `create_pod_range` is true, this ID is used for the new range.
  // If `create_pod_range` is false, uses an existing secondary range with this
  // ID.
  //
  // Only applicable if `ip_allocation_policy.use_ip_aliases` is true.
  //
  // This field cannot be changed after the node pool has been created.
  string pod_range = 5;

  // The IP address range for pod IPs in this node pool.
  //
  // Only applicable if `create_pod_range` is true.
  //
  // Set to blank to have a range chosen with the default size.
  //
  // Set to /netmask (e.g. `/14`) to have a range chosen with a specific
  // netmask.
  //
  // Set to a
  // [CIDR](https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
  // notation (e.g. `*********/14`) to pick a specific range to use.
  //
  // Only applicable if `ip_allocation_policy.use_ip_aliases` is true.
  //
  // This field cannot be changed after the node pool has been created.
  string pod_ipv4_cidr_block = 6;

  // Whether nodes have internal IP addresses only.
  // If enable_private_nodes is not specified, then the value is derived from
  // [cluster.privateClusterConfig.enablePrivateNodes][google.container.v1beta1.PrivateClusterConfig.enablePrivateNodes]
  optional bool enable_private_nodes = 9;

  // Network bandwidth tier configuration.
  optional NetworkPerformanceConfig network_performance_config = 11;

  // [PRIVATE FIELD]
  // Pod CIDR size overprovisioning config for the nodepool.
  //
  // Pod CIDR size per node depends on max_pods_per_node. By default, the value
  // of max_pods_per_node is rounded off to next power of 2 and we then double
  // that to get the size of pod CIDR block per node.
  // Example: max_pods_per_node of 30 would result in 64 IPs (/26).
  //
  // This config can disable the doubling of IPs (we still round off to next
  // power of 2)
  // Example: max_pods_per_node of 30 will result in 32 IPs (/27) when
  // overprovisioning is disabled.
  PodCIDROverprovisionConfig pod_cidr_overprovision_config = 13;

  // We specify the additional node networks for this node pool using this list.
  // Each node network corresponds to an additional interface
  repeated AdditionalNodeNetworkConfig additional_node_network_configs = 14;

  // We specify the additional pod networks for this node pool using this list.
  // Each pod network corresponds to an additional alias IP range for the node
  repeated AdditionalPodNetworkConfig additional_pod_network_configs = 15;

  // Output only. The utilization of the IPv4 range for the pod.
  // The ratio is Usage/[Total number of IPs in the secondary range],
  // Usage=numNodes*numZones*podIPsPerNode.
  double pod_ipv4_range_utilization = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// AdditionalNodeNetworkConfig is the configuration for additional node networks
// within the NodeNetworkConfig message
message AdditionalNodeNetworkConfig {
  // Name of the VPC where the additional interface belongs
  string network = 1;

  // Name of the subnetwork where the additional interface belongs
  string subnetwork = 2;
}

// AdditionalPodNetworkConfig is the configuration for additional pod networks
// within the NodeNetworkConfig message
message AdditionalPodNetworkConfig {
  // Name of the subnetwork where the additional pod network belongs.
  string subnetwork = 1;

  // The name of the secondary range on the subnet which provides IP address for
  // this pod range.
  string secondary_pod_range = 2;

  // The maximum number of pods per node which use this pod network.
  optional MaxPodsConstraint max_pods_per_node = 3;
}

// A set of Shielded Instance options.
message ShieldedInstanceConfig {
  // Defines whether the instance has Secure Boot enabled.
  //
  // Secure Boot helps ensure that the system only runs authentic software by
  // verifying the digital signature of all boot components, and halting the
  // boot process if signature verification fails.
  bool enable_secure_boot = 1;

  // Defines whether the instance has integrity monitoring enabled.
  //
  // Enables monitoring and attestation of the boot integrity of the instance.
  // The attestation is performed against the integrity policy baseline. This
  // baseline is initially derived from the implicitly trusted boot image when
  // the instance is created.
  bool enable_integrity_monitoring = 2;
}

// SandboxConfig contains configurations of the sandbox to use for the node.
message SandboxConfig {
  // Possible types of sandboxes.
  enum Type {
    // Default value. This should not be used.
    UNSPECIFIED = 0;

    // Run sandbox using gvisor.
    GVISOR = 1;
  }

  // Type of the sandbox to use for the node (e.g. 'gvisor')
  string sandbox_type = 1 [deprecated = true];

  // Type of the sandbox to use for the node.
  Type type = 2;
}

// EphemeralStorageConfig contains configuration for the ephemeral storage
// filesystem.
message EphemeralStorageConfig {
  // Number of local SSDs to use to back ephemeral storage. Uses NVMe
  // interfaces. The limit for this value is dependent upon the maximum number
  // of disk available on a machine per zone. See:
  // https://cloud.google.com/compute/docs/disks/local-ssd
  // for more information.
  //
  // A zero (or unset) value has different meanings depending on machine type
  // being used:
  // 1. For pre-Gen3 machines, which support flexible numbers of local ssds,
  // zero (or unset) means to disable using local SSDs as ephemeral storage.
  // 2. For Gen3 machines which dictate a specific number of local ssds, zero
  // (or unset) means to use the default number of local ssds that goes with
  // that machine type. For example, for a c3-standard-8-lssd machine, 2 local
  // ssds would be provisioned. For c3-standard-8 (which doesn't support local
  // ssds), 0 will be provisioned. See
  // https://cloud.google.com/compute/docs/disks/local-ssd#choose_number_local_ssds
  // for more info.
  int32 local_ssd_count = 1;
}

// LocalNvmeSsdBlockConfig contains configuration for using raw-block local
// NVMe SSDs
message LocalNvmeSsdBlockConfig {
  // Number of local NVMe SSDs to use.  The limit for this value is dependent
  // upon the maximum number of disk available on a machine per zone. See:
  // https://cloud.google.com/compute/docs/disks/local-ssd
  // for more information.
  //
  // A zero (or unset) value has different meanings depending on machine type
  // being used:
  // 1. For pre-Gen3 machines, which support flexible numbers of local ssds,
  // zero (or unset) means to disable using local SSDs as ephemeral storage.
  // 2. For Gen3 machines which dictate a specific number of local ssds, zero
  // (or unset) means to use the default number of local ssds that goes with
  // that machine type. For example, for a c3-standard-8-lssd machine, 2 local
  // ssds would be provisioned. For c3-standard-8 (which doesn't support local
  // ssds), 0 will be provisioned. See
  // https://cloud.google.com/compute/docs/disks/local-ssd#choose_number_local_ssds
  // for more info.
  int32 local_ssd_count = 1;
}

// EphemeralStorageLocalSsdConfig contains configuration for the node ephemeral
// storage using Local SSDs.
message EphemeralStorageLocalSsdConfig {
  // Number of local SSDs to use to back ephemeral storage. Uses NVMe
  // interfaces.
  //
  // A zero (or unset) value has different meanings depending on machine type
  // being used:
  // 1. For pre-Gen3 machines, which support flexible numbers of local ssds,
  // zero (or unset) means to disable using local SSDs as ephemeral storage. The
  // limit for this value is dependent upon the maximum number of disk
  // available on a machine per zone. See:
  // https://cloud.google.com/compute/docs/disks/local-ssd
  // for more information.
  // 2. For Gen3 machines which dictate a specific number of local ssds, zero
  // (or unset) means to use the default number of local ssds that goes with
  // that machine type. For example, for a c3-standard-8-lssd machine, 2 local
  // ssds would be provisioned. For c3-standard-8 (which doesn't support local
  // ssds), 0 will be provisioned. See
  // https://cloud.google.com/compute/docs/disks/local-ssd#choose_number_local_ssds
  // for more info.
  int32 local_ssd_count = 1;
}

// GcfsConfig contains configurations of Google Container File System.
message GcfsConfig {
  // Whether to use GCFS.
  bool enabled = 1;
}

// [ReservationAffinity](https://cloud.google.com/compute/docs/instances/reserving-zonal-resources)
// is the configuration of desired reservation which instances could take
// capacity from.
message ReservationAffinity {
  // Indicates whether to consume capacity from a reservation or not.
  enum Type {
    // Default value. This should not be used.
    UNSPECIFIED = 0;

    // Do not consume from any reserved capacity.
    NO_RESERVATION = 1;

    // Consume any reservation available.
    ANY_RESERVATION = 2;

    // Must consume from a specific reservation. Must specify key value fields
    // for specifying the reservations.
    SPECIFIC_RESERVATION = 3;
  }

  // Corresponds to the type of reservation consumption.
  Type consume_reservation_type = 1;

  // Corresponds to the label key of a reservation resource. To target a
  // SPECIFIC_RESERVATION by name, specify
  // "compute.googleapis.com/reservation-name" as the key and specify the name
  // of your reservation as its value.
  string key = 2;

  // Corresponds to the label value(s) of reservation resource(s).
  repeated string values = 3;
}

// SoleTenantConfig contains the NodeAffinities to specify what shared sole
// tenant node groups should back the node pool.
message SoleTenantConfig {
  // Specifies the NodeAffinity key, values, and affinity operator according to
  // [shared sole tenant node group
  // affinities](https://cloud.google.com/compute/docs/nodes/sole-tenant-nodes#node_affinity_and_anti-affinity).
  message NodeAffinity {
    // Operator allows user to specify affinity or anti-affinity for the
    // given key values.
    enum Operator {
      // Invalid or unspecified affinity operator.
      OPERATOR_UNSPECIFIED = 0;

      // Affinity operator.
      IN = 1;

      // Anti-affinity operator.
      NOT_IN = 2;
    }

    // Key for NodeAffinity.
    string key = 1;

    // Operator for NodeAffinity.
    Operator operator = 2;

    // Values for NodeAffinity.
    repeated string values = 3;
  }

  // NodeAffinities used to match to a shared sole tenant node group.
  repeated NodeAffinity node_affinities = 1;
}

// ContainerdConfig contains configuration to customize containerd.
message ContainerdConfig {
  // PrivateRegistryAccessConfig contains access configuration for
  // private container registries.
  message PrivateRegistryAccessConfig {
    // CertificateAuthorityDomainConfig configures one or more fully qualified
    // domain names (FQDN) to a specific certificate.
    message CertificateAuthorityDomainConfig {
      // GCPSecretManagerCertificateConfig configures a secret from
      // [Google Secret Manager](https://cloud.google.com/secret-manager).
      message GCPSecretManagerCertificateConfig {
        // Secret URI, in the form
        // "projects/$PROJECT_ID/secrets/$SECRET_NAME/versions/$VERSION".
        // Version can be fixed (e.g. "2") or "latest"
        string secret_uri = 1;
      }

      // List of fully qualified domain names (FQDN).
      // Specifying port is supported.
      // Wilcards are NOT supported.
      // Examples:
      // - my.customdomain.com
      // - 10.0.1.2:5000
      repeated string fqdns = 1;

      // Certificate access config. The following are supported:
      // - GCPSecretManagerCertificateConfig
      oneof certificate_config {
        // Google Secret Manager (GCP) certificate configuration.
        GCPSecretManagerCertificateConfig
            gcp_secret_manager_certificate_config = 2;
      }
    }

    // Private registry access is enabled.
    bool enabled = 1;

    // Private registry access configuration.
    repeated CertificateAuthorityDomainConfig
        certificate_authority_domain_config = 2;
  }

  // PrivateRegistryAccessConfig is used to configure access configuration
  // for private container registries.
  PrivateRegistryAccessConfig private_registry_access_config = 1;
}

// HostMaintenancePolicy contains the maintenance policy for the hosts on which
// the GKE VMs run on.
message HostMaintenancePolicy {
  // Allows selecting how infrastructure upgrades should be applied to the
  // cluster or node pool.
  enum MaintenanceInterval {
    // The maintenance interval is not explicitly specified.
    MAINTENANCE_INTERVAL_UNSPECIFIED = 0;

    // Nodes are eligible to receive infrastructure and hypervisor updates as
    // they become available.  This may result in more maintenance operations
    // (live migrations or terminations) for the node than the PERIODIC option.
    AS_NEEDED = 1;

    // Nodes receive infrastructure and hypervisor updates on a periodic basis,
    // minimizing the number of maintenance operations (live migrations or
    // terminations) on an individual VM.  This may mean underlying VMs will
    // take longer to receive an update than if it was configured for
    // AS_NEEDED.  Security updates will still be applied as soon
    // as they are available.
    PERIODIC = 2;
  }

  // Strategy that will trigger maintenance on behalf of the customer.
  message OpportunisticMaintenanceStrategy {
    // The amount of time that a node can remain idle (no customer owned
    // workloads running), before triggering maintenance.
    optional google.protobuf.Duration node_idle_time_window = 1;

    // The window of time that opportunistic maintenance can run. Example: A
    // setting of 14 days implies that opportunistic maintenance can only be ran
    // in the 2 weeks leading up to the scheduled maintenance date. Setting 28
    // days allows opportunistic maintenance to run at any time in the scheduled
    // maintenance window (all `PERIODIC` maintenance is set 28 days in
    // advance).
    optional google.protobuf.Duration maintenance_availability_window = 2;

    // The minimum nodes required to be available in a pool. Blocks maintenance
    // if it would cause the number of running nodes to dip below this value.
    optional int64 min_nodes_per_pool = 3;
  }

  // Specifies the frequency of planned maintenance events.
  optional MaintenanceInterval maintenance_interval = 1;

  // Set of host maintenance strategies available to the customer, all require
  // the maintenance_interval to be PERIODIC. If no strategy is set, and the
  // interval is periodic, customer will be expected to trigger maintenance
  // manually or let maintenance trigger at its initial scheduled time.
  oneof maintenance_strategy {
    // Strategy that will trigger maintenance on behalf of the customer.
    OpportunisticMaintenanceStrategy opportunistic_maintenance_strategy = 2;
  }
}

// Kubernetes taint is composed of three fields: key, value, and effect. Effect
// can only be one of three types:  NoSchedule, PreferNoSchedule or NoExecute.
//
// See
// [here](https://kubernetes.io/docs/concepts/configuration/taint-and-toleration)
// for more information, including usage and the valid values.
message NodeTaint {
  // Possible values for Effect in taint.
  enum Effect {
    // Not set
    EFFECT_UNSPECIFIED = 0;

    // NoSchedule
    NO_SCHEDULE = 1;

    // PreferNoSchedule
    PREFER_NO_SCHEDULE = 2;

    // NoExecute
    NO_EXECUTE = 3;
  }

  // Key for taint.
  string key = 1;

  // Value for taint.
  string value = 2;

  // Effect for taint.
  Effect effect = 3;
}

// Collection of Kubernetes [node
// taints](https://kubernetes.io/docs/concepts/configuration/taint-and-toleration).
message NodeTaints {
  // List of node taints.
  repeated NodeTaint taints = 1;
}

// Collection of node-level [Kubernetes
// labels](https://kubernetes.io/docs/concepts/overview/working-with-objects/labels).
message NodeLabels {
  // Map of node label keys and node label values.
  map<string, string> labels = 1;
}

// Collection of [GCP
// labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels).
message ResourceLabels {
  // Map of node label keys and node label values.
  map<string, string> labels = 1;
}

// Collection of Compute Engine network tags that can be applied to a node's
// underlying VM instance. (See `tags` field in
// [`NodeConfig`](/kubernetes-engine/docs/reference/rest/v1/NodeConfig)).
message NetworkTags {
  // List of network tags.
  repeated string tags = 1;
}

// The authentication information for accessing the master endpoint.
// Authentication can be done using HTTP basic auth or using client
// certificates.
message MasterAuth {
  // The username to use for HTTP basic authentication to the master endpoint.
  // For clusters v1.6.0 and later, basic authentication can be disabled by
  // leaving username unspecified (or setting it to the empty string).
  //
  // Warning: basic authentication is deprecated, and will be removed in GKE
  // control plane versions 1.19 and newer. For a list of recommended
  // authentication methods, see:
  // https://cloud.google.com/kubernetes-engine/docs/how-to/api-server-authentication
  string username = 1 [deprecated = true];

  // The password to use for HTTP basic authentication to the master endpoint.
  // Because the master endpoint is open to the Internet, you should create a
  // strong password.  If a password is provided for cluster creation, username
  // must be non-empty.
  //
  // Warning: basic authentication is deprecated, and will be removed in GKE
  // control plane versions 1.19 and newer. For a list of recommended
  // authentication methods, see:
  // https://cloud.google.com/kubernetes-engine/docs/how-to/api-server-authentication
  string password = 2 [deprecated = true];

  // Configuration for client certificate authentication on the cluster. For
  // clusters before v1.12, if no configuration is specified, a client
  // certificate is issued.
  ClientCertificateConfig client_certificate_config = 3;

  // Output only. Base64-encoded public certificate that is the root of
  // trust for the cluster.
  string cluster_ca_certificate = 100
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Base64-encoded public certificate used by clients to
  // authenticate to the cluster endpoint.
  string client_certificate = 101 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Base64-encoded private key used by clients to authenticate
  // to the cluster endpoint.
  string client_key = 102 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for client certificates on the cluster.
message ClientCertificateConfig {
  // Issue a client certificate.
  bool issue_client_certificate = 1;
}

// Configuration for the addons that can be automatically spun up in the
// cluster, enabling additional functionality.
message AddonsConfig {
  // Configuration for the HTTP (L7) load balancing controller addon, which
  // makes it easy to set up HTTP load balancers for services in a cluster.
  HttpLoadBalancing http_load_balancing = 1;

  // Configuration for the horizontal pod autoscaling feature, which
  // increases or decreases the number of replica pods a replication controller
  // has based on the resource usage of the existing pods.
  HorizontalPodAutoscaling horizontal_pod_autoscaling = 2;

  // Configuration for the Kubernetes Dashboard.
  // This addon is deprecated, and will be disabled in 1.15. It is recommended
  // to use the Cloud Console to manage and monitor your Kubernetes clusters,
  // workloads and applications. For more information, see:
  // https://cloud.google.com/kubernetes-engine/docs/concepts/dashboards
  KubernetesDashboard kubernetes_dashboard = 3 [deprecated = true];

  // Configuration for NetworkPolicy. This only tracks whether the addon
  // is enabled or not on the Master, it does not track whether network policy
  // is enabled for the nodes.
  NetworkPolicyConfig network_policy_config = 4;

  // Configuration for Istio, an open platform to connect, manage, and secure
  // microservices.
  IstioConfig istio_config = 5 [deprecated = true];

  // Configuration for the Cloud Run addon. The `IstioConfig` addon must be
  // enabled in order to enable Cloud Run addon. This option can only be enabled
  // at cluster creation time.
  CloudRunConfig cloud_run_config = 7;

  // Configuration for NodeLocalDNS, a dns cache running on cluster nodes
  DnsCacheConfig dns_cache_config = 8;

  // Configuration for the ConfigConnector add-on, a Kubernetes
  // extension to manage hosted GCP services through the Kubernetes API
  ConfigConnectorConfig config_connector_config = 10;

  // Configuration for the Compute Engine Persistent Disk CSI driver.
  GcePersistentDiskCsiDriverConfig gce_persistent_disk_csi_driver_config = 11;

  // Configuration for the KALM addon, which manages the lifecycle of k8s
  // applications.
  KalmConfig kalm_config = 12 [deprecated = true];

  // Configuration for the GCP Filestore CSI driver.
  GcpFilestoreCsiDriverConfig gcp_filestore_csi_driver_config = 14;

  // Configuration for the Backup for GKE agent addon.
  GkeBackupAgentConfig gke_backup_agent_config = 16;

  // Configuration for the Cloud Storage Fuse CSI driver.
  GcsFuseCsiDriverConfig gcs_fuse_csi_driver_config = 17;

  // Optional. Configuration for the StatefulHA add-on.
  StatefulHAConfig stateful_ha_config = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration for Ray Operator addon.
  RayOperatorConfig ray_operator_config = 21
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration options for the HTTP (L7) load balancing controller addon,
// which makes it easy to set up HTTP load balancers for services in a cluster.
message HttpLoadBalancing {
  // Whether the HTTP Load Balancing controller is enabled in the cluster.
  // When enabled, it runs a small pod in the cluster that manages the load
  // balancers.
  bool disabled = 1;
}

// Configuration options for the horizontal pod autoscaling feature, which
// increases or decreases the number of replica pods a replication controller
// has based on the resource usage of the existing pods.
message HorizontalPodAutoscaling {
  // Whether the Horizontal Pod Autoscaling feature is enabled in the cluster.
  // When enabled, it ensures that metrics are collected into Stackdriver
  // Monitoring.
  bool disabled = 1;
}

// Configuration for the Kubernetes Dashboard.
message KubernetesDashboard {
  // Whether the Kubernetes Dashboard is enabled for this cluster.
  bool disabled = 1;
}

// Configuration for NetworkPolicy. This only tracks whether the addon
// is enabled or not on the Master, it does not track whether network policy
// is enabled for the nodes.
message NetworkPolicyConfig {
  // Whether NetworkPolicy is enabled for this cluster.
  bool disabled = 1;
}

// Configuration for NodeLocal DNSCache
message DnsCacheConfig {
  // Whether NodeLocal DNSCache is enabled for this cluster.
  bool enabled = 1;
}

// Configuration options for the KALM addon.
message KalmConfig {
  // Whether KALM is enabled for this cluster.
  bool enabled = 1 [deprecated = true];
}

// Configuration for the Backup for GKE Agent.
message GkeBackupAgentConfig {
  // Whether the Backup for GKE agent is enabled for this cluster.
  bool enabled = 1;
}

// Configuration for the Stateful HA add-on.
message StatefulHAConfig {
  // Whether the Stateful HA add-on is enabled for this cluster.
  bool enabled = 1;
}

// Configuration options for the Config Connector add-on.
message ConfigConnectorConfig {
  // Whether Cloud Connector is enabled for this cluster.
  bool enabled = 1;
}

// Configuration for the Compute Engine PD CSI driver.
message GcePersistentDiskCsiDriverConfig {
  // Whether the Compute Engine PD CSI driver is enabled for this cluster.
  bool enabled = 1;
}

// Configuration for the GCP Filestore CSI driver.
message GcpFilestoreCsiDriverConfig {
  // Whether the GCP Filestore CSI driver is enabled for this cluster.
  bool enabled = 1;
}

// Configuration for the Cloud Storage Fuse CSI driver.
message GcsFuseCsiDriverConfig {
  // Whether the Cloud Storage Fuse CSI driver is enabled for this cluster.
  bool enabled = 1;
}

// Configuration options for the Ray Operator add-on.
message RayOperatorConfig {
  // Whether the Ray addon is enabled for this cluster.
  bool enabled = 1;

  // Optional. Logging configuration for Ray clusters.
  RayClusterLoggingConfig ray_cluster_logging_config = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Monitoring configuration for Ray clusters.
  RayClusterMonitoringConfig ray_cluster_monitoring_config = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for controlling master global access settings.
message PrivateClusterMasterGlobalAccessConfig {
  // Whenever master is accessible globally or not.
  bool enabled = 1;
}

// Configuration options for private clusters.
message PrivateClusterConfig {
  // Whether nodes have internal IP addresses only. If enabled, all nodes are
  // given only RFC 1918 private addresses and communicate with the master via
  // private networking.
  bool enable_private_nodes = 1;

  // Whether the master's internal IP address is used as the cluster endpoint.
  bool enable_private_endpoint = 2;

  // The IP range in CIDR notation to use for the hosted master network. This
  // range will be used for assigning internal IP addresses to the master or
  // set of masters, as well as the ILB VIP. This range must not overlap with
  // any other ranges in use within the cluster's network.
  string master_ipv4_cidr_block = 3;

  // Output only. The internal IP address of this cluster's master endpoint.
  string private_endpoint = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The external IP address of this cluster's master endpoint.
  string public_endpoint = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The peering name in the customer VPC used by this cluster.
  string peering_name = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Controls master global access settings.
  PrivateClusterMasterGlobalAccessConfig master_global_access_config = 8;

  // Subnet to provision the master's private endpoint during cluster creation.
  // Specified in projects/*/regions/*/subnetworks/* format.
  string private_endpoint_subnetwork = 10;
}

// Configuration options for Istio addon.
message IstioConfig {
  // Istio auth mode, https://istio.io/docs/concepts/security/mutual-tls.html
  enum IstioAuthMode {
    // auth not enabled
    AUTH_NONE = 0;

    // auth mutual TLS enabled
    AUTH_MUTUAL_TLS = 1;
  }

  // Whether Istio is enabled for this cluster.
  bool disabled = 1 [deprecated = true];

  // The specified Istio auth mode, either none, or mutual TLS.
  IstioAuthMode auth = 2 [deprecated = true];
}

// Configuration options for the Cloud Run feature.
message CloudRunConfig {
  // Load balancer type of ingress service of Cloud Run.
  enum LoadBalancerType {
    // Load balancer type for Cloud Run is unspecified.
    LOAD_BALANCER_TYPE_UNSPECIFIED = 0;

    // Install external load balancer for Cloud Run.
    LOAD_BALANCER_TYPE_EXTERNAL = 1;

    // Install internal load balancer for Cloud Run.
    LOAD_BALANCER_TYPE_INTERNAL = 2;
  }

  // Whether Cloud Run addon is enabled for this cluster.
  bool disabled = 1;

  // Which load balancer type is installed for Cloud Run.
  LoadBalancerType load_balancer_type = 3;
}

// Configuration options for the master authorized networks feature. Enabled
// master authorized networks will disallow all external traffic to access
// Kubernetes master through HTTPS except traffic from the given CIDR blocks,
// Google Compute Engine Public IPs and Google Prod IPs.
message MasterAuthorizedNetworksConfig {
  // CidrBlock contains an optional name and one CIDR block.
  message CidrBlock {
    // display_name is an optional field for users to identify CIDR blocks.
    string display_name = 1;

    // cidr_block must be specified in CIDR notation.
    string cidr_block = 2;
  }

  // Whether or not master authorized networks is enabled.
  bool enabled = 1;

  // cidr_blocks define up to 10 external networks that could access
  // Kubernetes master through HTTPS.
  repeated CidrBlock cidr_blocks = 2;

  // Whether master is accessbile via Google Compute Engine Public IP addresses.
  optional bool gcp_public_cidrs_access_enabled = 3;
}

// Configuration for the legacy Attribute Based Access Control authorization
// mode.
message LegacyAbac {
  // Whether the ABAC authorizer is enabled for this cluster. When enabled,
  // identities in the system, including service accounts, nodes, and
  // controllers, will have statically granted permissions beyond those
  // provided by the RBAC configuration or IAM.
  bool enabled = 1;
}

// Configuration options for the NetworkPolicy feature.
// https://kubernetes.io/docs/concepts/services-networking/networkpolicies/
message NetworkPolicy {
  // Allowed Network Policy providers.
  enum Provider {
    // Not set
    PROVIDER_UNSPECIFIED = 0;

    // Tigera (Calico Felix).
    CALICO = 1;
  }

  // The selected network policy provider.
  Provider provider = 1;

  // Whether network policy is enabled on the cluster.
  bool enabled = 2;
}

// [PRIVATE FIELD]
// Config for pod CIDR size overprovisioning.
message PodCIDROverprovisionConfig {
  // Whether Pod CIDR overprovisioning is disabled.
  // Note: Pod CIDR overprovisioning is enabled by default.
  bool disable = 1;
}

// Configuration for controlling how IPs are allocated in the cluster.
message IPAllocationPolicy {
  // Possible values for IP stack type
  enum StackType {
    // By default, the clusters will be IPV4 only
    STACK_TYPE_UNSPECIFIED = 0;

    // The value used if the cluster is a IPV4 only
    IPV4 = 1;

    // The value used if the cluster is a dual stack cluster
    IPV4_IPV6 = 2;
  }

  // IPv6 access type
  enum IPv6AccessType {
    // Default value, will be defaulted as type external.
    IPV6_ACCESS_TYPE_UNSPECIFIED = 0;

    // Access type internal (all v6 addresses are internal IPs)
    INTERNAL = 1;

    // Access type external (all v6 addresses are external IPs)
    EXTERNAL = 2;
  }

  // Whether alias IPs will be used for pod IPs in the cluster.
  // This is used in conjunction with use_routes. It cannot
  // be true if use_routes is true. If both use_ip_aliases and use_routes are
  // false, then the server picks the default IP allocation mode
  bool use_ip_aliases = 1;

  // Whether a new subnetwork will be created automatically for the cluster.
  //
  // This field is only applicable when `use_ip_aliases` is true.
  bool create_subnetwork = 2;

  // A custom subnetwork name to be used if `create_subnetwork` is true.  If
  // this field is empty, then an automatic name will be chosen for the new
  // subnetwork.
  string subnetwork_name = 3;

  // This field is deprecated, use cluster_ipv4_cidr_block.
  string cluster_ipv4_cidr = 4 [deprecated = true];

  // This field is deprecated, use node_ipv4_cidr_block.
  string node_ipv4_cidr = 5 [deprecated = true];

  // This field is deprecated, use services_ipv4_cidr_block.
  string services_ipv4_cidr = 6 [deprecated = true];

  // The name of the secondary range to be used for the cluster CIDR
  // block.  The secondary range will be used for pod IP
  // addresses. This must be an existing secondary range associated
  // with the cluster subnetwork.
  //
  // This field is only applicable with use_ip_aliases and
  // create_subnetwork is false.
  string cluster_secondary_range_name = 7;

  // The name of the secondary range to be used as for the services
  // CIDR block.  The secondary range will be used for service
  // ClusterIPs. This must be an existing secondary range associated
  // with the cluster subnetwork.
  //
  // This field is only applicable with use_ip_aliases and
  // create_subnetwork is false.
  string services_secondary_range_name = 8;

  // The IP address range for the cluster pod IPs. If this field is set, then
  // `cluster.cluster_ipv4_cidr` must be left blank.
  //
  // This field is only applicable when `use_ip_aliases` is true.
  //
  // Set to blank to have a range chosen with the default size.
  //
  // Set to /netmask (e.g. `/14`) to have a range chosen with a specific
  // netmask.
  //
  // Set to a
  // [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
  // notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
  // `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific range
  // to use.
  string cluster_ipv4_cidr_block = 9;

  // The IP address range of the instance IPs in this cluster.
  //
  // This is applicable only if `create_subnetwork` is true.
  //
  // Set to blank to have a range chosen with the default size.
  //
  // Set to /netmask (e.g. `/14`) to have a range chosen with a specific
  // netmask.
  //
  // Set to a
  // [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
  // notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
  // `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific range
  // to use.
  string node_ipv4_cidr_block = 10;

  // The IP address range of the services IPs in this cluster. If blank, a range
  // will be automatically chosen with the default size.
  //
  // This field is only applicable when `use_ip_aliases` is true.
  //
  // Set to blank to have a range chosen with the default size.
  //
  // Set to /netmask (e.g. `/14`) to have a range chosen with a specific
  // netmask.
  //
  // Set to a
  // [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
  // notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
  // `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific range
  // to use.
  string services_ipv4_cidr_block = 11;

  // If true, allow allocation of cluster CIDR ranges that overlap with certain
  // kinds of network routes. By default we do not allow cluster CIDR ranges to
  // intersect with any user declared routes. With allow_route_overlap == true,
  // we allow overlapping with CIDR ranges that are larger than the cluster CIDR
  // range.
  //
  // If this field is set to true, then cluster and services CIDRs must be
  // fully-specified (e.g. `*********/14`, but not `/14`), which means:
  // 1) When `use_ip_aliases` is true, `cluster_ipv4_cidr_block` and
  //    `services_ipv4_cidr_block` must be fully-specified.
  // 2) When `use_ip_aliases` is false, `cluster.cluster_ipv4_cidr` muse be
  //    fully-specified.
  bool allow_route_overlap = 12;

  // The IP address range of the Cloud TPUs in this cluster. If unspecified, a
  // range will be automatically chosen with the default size.
  //
  // This field is only applicable when `use_ip_aliases` is true.
  //
  // If unspecified, the range will use the default size.
  //
  // Set to /netmask (e.g. `/14`) to have a range chosen with a specific
  // netmask.
  //
  // Set to a
  // [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
  // notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
  // `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific range
  // to use.
  // This field is deprecated, use cluster.tpu_config.ipv4_cidr_block instead.
  string tpu_ipv4_cidr_block = 13;

  // Whether routes will be used for pod IPs in the cluster.
  // This is used in conjunction with use_ip_aliases. It cannot be true if
  // use_ip_aliases is true. If both use_ip_aliases and use_routes are false,
  // then the server picks the default IP allocation mode
  bool use_routes = 15;

  // IP stack type
  StackType stack_type = 16;

  // The ipv6 access type (internal or external) when create_subnetwork is true
  IPv6AccessType ipv6_access_type = 17;

  // [PRIVATE FIELD]
  // Pod CIDR size overprovisioning config for the cluster.
  //
  // Pod CIDR size per node depends on max_pods_per_node. By default, the value
  // of max_pods_per_node is doubled and then rounded off to next power of 2 to
  // get the size of pod CIDR block per node.
  // Example: max_pods_per_node of 30 would result in 64 IPs (/26).
  //
  // This config can disable the doubling of IPs (we still round off to next
  // power of 2)
  // Example: max_pods_per_node of 30 will result in 32 IPs (/27) when
  // overprovisioning is disabled.
  PodCIDROverprovisionConfig pod_cidr_overprovision_config = 21;

  // Output only. The subnet's IPv6 CIDR block used by nodes and pods.
  string subnet_ipv6_cidr_block = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The services IPv6 CIDR block for the cluster.
  string services_ipv6_cidr_block = 23
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The additional pod ranges that are added to the cluster.
  // These pod ranges can be used by new node pools to allocate pod IPs
  // automatically. Once the range is removed it will not show up in
  // IPAllocationPolicy.
  AdditionalPodRangesConfig additional_pod_ranges_config = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The utilization of the cluster default IPv4 range for the
  // pod. The ratio is Usage/[Total number of IPs in the secondary range],
  // Usage=numNodes*numZones*podIPsPerNode.
  double default_pod_ipv4_range_utilization = 25
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for Binary Authorization.
message BinaryAuthorization {
  // Binary Authorization mode of operation.
  enum EvaluationMode {
    // Default value
    EVALUATION_MODE_UNSPECIFIED = 0;

    // Disable BinaryAuthorization
    DISABLED = 1;

    // Enforce Kubernetes admission requests with BinaryAuthorization using the
    // project's singleton policy. This is equivalent to setting the
    // enabled boolean to true.
    PROJECT_SINGLETON_POLICY_ENFORCE = 2;

    // Use Binary Authorization Continuous Validation with the policies
    // specified in policy_bindings.
    POLICY_BINDINGS = 5;

    // Use Binary Authorization Continuous Validation with the policies
    // specified in policy_bindings and enforce Kubernetes admission requests
    // with Binary Authorization using the project's singleton policy.
    POLICY_BINDINGS_AND_PROJECT_SINGLETON_POLICY_ENFORCE = 6;
  }

  // Binauthz policy that applies to this cluster.
  message PolicyBinding {
    // The relative resource name of the binauthz platform policy to evaluate.
    // GKE platform policies have the following format:
    // `projects/{project_number}/platforms/gke/policies/{policy_id}`.
    optional string name = 1;
  }

  // This field is deprecated. Leave this unset and instead configure
  // BinaryAuthorization using evaluation_mode. If evaluation_mode is set to
  // anything other than EVALUATION_MODE_UNSPECIFIED, this field is ignored.
  bool enabled = 1 [deprecated = true];

  // Mode of operation for binauthz policy evaluation. If unspecified, defaults
  // to DISABLED.
  EvaluationMode evaluation_mode = 2;

  // Optional. Binauthz policies that apply to this cluster.
  repeated PolicyBinding policy_bindings = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for the PodSecurityPolicy feature.
message PodSecurityPolicyConfig {
  // Enable the PodSecurityPolicy controller for this cluster. If enabled, pods
  // must be valid under a PodSecurityPolicy to be created.
  bool enabled = 1;
}

// Configuration for returning group information from authenticators.
message AuthenticatorGroupsConfig {
  // Whether this cluster should return group membership lookups
  // during authentication using a group of security groups.
  bool enabled = 1;

  // The name of the security group-of-groups to be used. Only relevant
  // if enabled = true.
  string security_group = 2;
}

// Telemetry integration for the cluster.
message ClusterTelemetry {
  // Type of the integration.
  enum Type {
    // Not set.
    UNSPECIFIED = 0;

    // Monitoring integration is disabled.
    DISABLED = 1;

    // Monitoring integration is enabled.
    ENABLED = 2;

    // Only system components are monitored and logged.
    SYSTEM_ONLY = 3;
  }

  // Type of the integration.
  Type type = 1;
}

// A Google Kubernetes Engine cluster.
message Cluster {
  // The current status of the cluster.
  enum Status {
    // Not set.
    STATUS_UNSPECIFIED = 0;

    // The PROVISIONING state indicates the cluster is being created.
    PROVISIONING = 1;

    // The RUNNING state indicates the cluster has been created and is fully
    // usable.
    RUNNING = 2;

    // The RECONCILING state indicates that some work is actively being done on
    // the cluster, such as upgrading the master or node software. Details can
    // be found in the `statusMessage` field.
    RECONCILING = 3;

    // The STOPPING state indicates the cluster is being deleted.
    STOPPING = 4;

    // The ERROR state indicates the cluster may be unusable. Details
    // can be found in the `statusMessage` field.
    ERROR = 5;

    // The DEGRADED state indicates the cluster requires user action to restore
    // full functionality. Details can be found in the `statusMessage` field.
    DEGRADED = 6;
  }

  // The name of this cluster. The name must be unique within this project
  // and location (e.g. zone or region), and can be up to 40 characters with
  // the following restrictions:
  //
  // * Lowercase letters, numbers, and hyphens only.
  // * Must start with a letter.
  // * Must end with a number or a letter.
  string name = 1;

  // An optional description of this cluster.
  string description = 2;

  // The number of nodes to create in this cluster. You must ensure that your
  // Compute Engine [resource quota](https://cloud.google.com/compute/quotas)
  // is sufficient for this number of instances. You must also have available
  // firewall and routes quota.
  // For requests, this field should only be used in lieu of a
  // "node_pool" object, since this configuration (along with the
  // "node_config") will be used to create a "NodePool" object with an
  // auto-generated name. Do not use this and a node_pool at the same time.
  //
  // This field is deprecated, use node_pool.initial_node_count instead.
  int32 initial_node_count = 3 [deprecated = true];

  // Parameters used in creating the cluster's nodes.
  // For requests, this field should only be used in lieu of a
  // "node_pool" object, since this configuration (along with the
  // "initial_node_count") will be used to create a "NodePool" object with an
  // auto-generated name. Do not use this and a node_pool at the same time.
  // For responses, this field will be populated with the node configuration of
  // the first node pool. (For configuration of each node pool, see
  // `node_pool.config`)
  //
  // If unspecified, the defaults are used.
  // This field is deprecated, use node_pool.config instead.
  NodeConfig node_config = 4 [deprecated = true];

  // The authentication information for accessing the master endpoint.
  // If unspecified, the defaults are used:
  // For clusters before v1.12, if master_auth is unspecified, `username` will
  // be set to "admin", a random password will be generated, and a client
  // certificate will be issued.
  MasterAuth master_auth = 5;

  // The logging service the cluster should use to write logs.
  // Currently available options:
  //
  // * `logging.googleapis.com/kubernetes` - The Cloud Logging
  // service with a Kubernetes-native resource model
  // * `logging.googleapis.com` - The legacy Cloud Logging service (no longer
  //   available as of GKE 1.15).
  // * `none` - no logs will be exported from the cluster.
  //
  // If left as an empty string,`logging.googleapis.com/kubernetes` will be
  // used for GKE 1.14+ or `logging.googleapis.com` for earlier versions.
  string logging_service = 6;

  // The monitoring service the cluster should use to write metrics.
  // Currently available options:
  //
  // * "monitoring.googleapis.com/kubernetes" - The Cloud Monitoring
  // service with a Kubernetes-native resource model
  // * `monitoring.googleapis.com` - The legacy Cloud Monitoring service (no
  //   longer available as of GKE 1.15).
  // * `none` - No metrics will be exported from the cluster.
  //
  // If left as an empty string,`monitoring.googleapis.com/kubernetes` will be
  // used for GKE 1.14+ or `monitoring.googleapis.com` for earlier versions.
  string monitoring_service = 7;

  // The name of the Google Compute Engine
  // [network](https://cloud.google.com/compute/docs/networks-and-firewalls#networks)
  // to which the cluster is connected. If left unspecified, the `default`
  // network will be used. On output this shows the network ID instead of the
  // name.
  string network = 8;

  // The IP address range of the container pods in this cluster, in
  // [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
  // notation (e.g. `*********/14`). Leave blank to have
  // one automatically chosen or specify a `/14` block in `10.0.0.0/8`.
  string cluster_ipv4_cidr = 9;

  // Configurations for the various addons available to run in the cluster.
  AddonsConfig addons_config = 10;

  // The name of the Google Compute Engine
  // [subnetwork](https://cloud.google.com/compute/docs/subnetworks) to which
  // the cluster is connected. On output this shows the subnetwork ID instead of
  // the name.
  string subnetwork = 11;

  // The node pools associated with this cluster.
  // This field should not be set if "node_config" or "initial_node_count" are
  // specified.
  repeated NodePool node_pools = 12;

  // The list of Google Compute Engine
  // [zones](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster's nodes should be located.
  //
  // This field provides a default value if
  // [NodePool.Locations](https://cloud.google.com/kubernetes-engine/docs/reference/rest/v1/projects.locations.clusters.nodePools#NodePool.FIELDS.locations)
  // are not specified during node pool creation.
  //
  // Warning: changing cluster locations will update the
  // [NodePool.Locations](https://cloud.google.com/kubernetes-engine/docs/reference/rest/v1/projects.locations.clusters.nodePools#NodePool.FIELDS.locations)
  // of all node pools and will result in nodes being added and/or removed.
  repeated string locations = 13;

  // Kubernetes alpha features are enabled on this cluster. This includes alpha
  // API groups (e.g. v1beta1) and features that may not be production ready in
  // the kubernetes version of the master and nodes.
  // The cluster has no SLA for uptime and master/node upgrades are disabled.
  // Alpha enabled clusters are automatically deleted thirty days after
  // creation.
  bool enable_kubernetes_alpha = 14;

  // Kubernetes open source beta apis enabled on the cluster. Only beta apis.
  K8sBetaAPIConfig enable_k8s_beta_apis = 143;

  // The resource labels for the cluster to use to annotate any related
  // Google Compute Engine resources.
  map<string, string> resource_labels = 15;

  // The fingerprint of the set of labels for this cluster.
  string label_fingerprint = 16;

  // Configuration for the legacy ABAC authorization mode.
  LegacyAbac legacy_abac = 18;

  // Configuration options for the NetworkPolicy feature.
  NetworkPolicy network_policy = 19;

  // Configuration for cluster IP allocation.
  IPAllocationPolicy ip_allocation_policy = 20;

  // The configuration options for master authorized networks feature.
  MasterAuthorizedNetworksConfig master_authorized_networks_config = 22;

  // Configure the maintenance policy for this cluster.
  MaintenancePolicy maintenance_policy = 23;

  // Configuration for Binary Authorization.
  BinaryAuthorization binary_authorization = 24;

  // Configuration for the PodSecurityPolicy feature.
  PodSecurityPolicyConfig pod_security_policy_config = 25;

  // Cluster-level autoscaling configuration.
  ClusterAutoscaling autoscaling = 26;

  // Configuration for cluster networking.
  NetworkConfig network_config = 27;

  // If this is a private cluster setup. Private clusters are clusters that, by
  // default have no external IP addresses on the nodes and where nodes and the
  // master communicate over private IP addresses.
  // This field is deprecated, use private_cluster_config.enable_private_nodes
  // instead.
  bool private_cluster = 28 [deprecated = true];

  // The IP prefix in CIDR notation to use for the hosted master network.
  // This prefix will be used for assigning private IP addresses to the
  // master or set of masters, as well as the ILB VIP.
  // This field is deprecated, use
  // private_cluster_config.master_ipv4_cidr_block instead.
  string master_ipv4_cidr_block = 29 [deprecated = true];

  // The default constraint on the maximum number of pods that can be run
  // simultaneously on a node in the node pool of this cluster. Only honored
  // if cluster created with IP Alias support.
  MaxPodsConstraint default_max_pods_constraint = 30;

  // Configuration for exporting resource usages. Resource usage export is
  // disabled when this config unspecified.
  ResourceUsageExportConfig resource_usage_export_config = 33;

  // Configuration controlling RBAC group membership information.
  AuthenticatorGroupsConfig authenticator_groups_config = 34;

  // Configuration for private cluster.
  PrivateClusterConfig private_cluster_config = 37;

  // Cluster-level Vertical Pod Autoscaling configuration.
  VerticalPodAutoscaling vertical_pod_autoscaling = 39;

  // Shielded Nodes configuration.
  ShieldedNodes shielded_nodes = 40;

  // Release channel configuration. If left unspecified on cluster creation and
  // a version is specified, the cluster is enrolled in the most mature release
  // channel where the version is available (first checking STABLE, then
  // REGULAR, and finally RAPID). Otherwise, if no release channel
  // configuration and no version is specified, the cluster is enrolled in the
  // REGULAR channel with its default version.
  ReleaseChannel release_channel = 41;

  // Configuration for the use of Kubernetes Service Accounts in GCP IAM
  // policies.
  WorkloadIdentityConfig workload_identity_config = 43;

  // Configuration for issuance of mTLS keys and certificates to Kubernetes
  // pods.
  WorkloadCertificates workload_certificates = 52;

  // Configuration for issuance of mTLS keys and certificates to Kubernetes
  // pods.
  MeshCertificates mesh_certificates = 67;

  // Configuration for direct-path (via ALTS) with workload identity.
  WorkloadALTSConfig workload_alts_config = 53;

  // Configuration for the fine-grained cost management feature.
  CostManagementConfig cost_management_config = 45;

  // Telemetry integration for the cluster.
  ClusterTelemetry cluster_telemetry = 46;

  // Configuration for Cloud TPU support;
  TpuConfig tpu_config = 47;

  // Notification configuration of the cluster.
  NotificationConfig notification_config = 49;

  // Configuration of Confidential Nodes.
  // All the nodes in the cluster will be Confidential VM once enabled.
  ConfidentialNodes confidential_nodes = 50;

  // Configuration for Identity Service component.
  IdentityServiceConfig identity_service_config = 54;

  // Output only. Server-defined URL for the resource.
  string self_link = 100 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field is deprecated, use location instead.
  string zone = 101
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The IP address of this cluster's master endpoint.
  // The endpoint can be accessed from the internet at
  // `**********************************/`.
  //
  // See the `masterAuth` property of this resource for username and
  // password information.
  string endpoint = 102 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The initial Kubernetes version for this cluster.  Valid versions are those
  // found in validMasterVersions returned by getServerConfig.  The version can
  // be upgraded over time; such upgrades are reflected in
  // currentMasterVersion and currentNodeVersion.
  //
  // Users may specify either explicit versions offered by
  // Kubernetes Engine or version aliases, which have the following behavior:
  //
  // - "latest": picks the highest valid Kubernetes version
  // - "1.X": picks the highest valid patch+gke.N patch in the 1.X version
  // - "1.X.Y": picks the highest valid gke.N patch in the 1.X.Y version
  // - "1.X.Y-gke.N": picks an explicit Kubernetes version
  // - "","-": picks the default Kubernetes version
  string initial_cluster_version = 103;

  // Output only. The current software version of the master endpoint.
  string current_master_version = 104
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Deprecated, use
  // [NodePool.version](https://cloud.google.com/kubernetes-engine/docs/reference/rest/v1beta1/projects.locations.clusters.nodePools)
  // instead. The current version of the node software components.
  // If they are currently at multiple versions because they're in the process
  // of being upgraded, this reflects the minimum version of all nodes.
  string current_node_version = 105
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the cluster was created, in
  // [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
  string create_time = 106 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current status of this cluster.
  Status status = 107 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Deprecated. Use conditions instead.
  // Additional information about the current status of this
  // cluster, if available.
  string status_message = 108
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The size of the address space on each node for hosting
  // containers. This is provisioned from within the `container_ipv4_cidr`
  // range. This field will only be set when cluster is in route-based network
  // mode.
  int32 node_ipv4_cidr_size = 109 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The IP address range of the Kubernetes services in
  // this cluster, in
  // [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
  // notation (e.g. `*******/29`). Service addresses are
  // typically put in the last `/16` from the container CIDR.
  string services_ipv4_cidr = 110 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Deprecated. Use node_pools.instance_group_urls.
  repeated string instance_group_urls = 111
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of nodes currently in the cluster. Deprecated.
  // Call Kubernetes API directly to retrieve node information.
  int32 current_node_count = 112
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the cluster will be automatically
  // deleted in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
  string expire_time = 113 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/regions-zones/regions-zones#available)
  // or
  // [region](https://cloud.google.com/compute/docs/regions-zones/regions-zones#available)
  // in which the cluster resides.
  string location = 114 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Enable the ability to use Cloud TPUs in this cluster.
  // This field is deprecated, use tpu_config.enabled instead.
  bool enable_tpu = 115;

  // Output only. The IP address range of the Cloud TPUs in this cluster, in
  // [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
  // notation (e.g. `*******/29`).
  string tpu_ipv4_cidr_block = 116 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration of etcd encryption.
  DatabaseEncryption database_encryption = 38;

  // Which conditions caused the current cluster state.
  repeated StatusCondition conditions = 118;

  // Configuration for master components.
  Master master = 124;

  // Autopilot configuration for the cluster.
  Autopilot autopilot = 128;

  // Output only. Unique id for the cluster.
  string id = 129 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Default NodePool settings for the entire cluster. These settings are
  // overridden if specified on the specific NodePool object.
  optional NodePoolDefaults node_pool_defaults = 131;

  // Logging configuration for the cluster.
  LoggingConfig logging_config = 132;

  // Monitoring configuration for the cluster.
  MonitoringConfig monitoring_config = 133;

  // Node pool configs that apply to all auto-provisioned node pools
  // in autopilot clusters and node auto-provisioning enabled clusters.
  NodePoolAutoConfig node_pool_auto_config = 136;

  // Deprecated: Use SecurityPostureConfig instead.
  // Enable/Disable Protect API features for the cluster.
  optional ProtectConfig protect_config = 137 [deprecated = true];

  // This checksum is computed by the server based on the value of cluster
  // fields, and may be sent on update requests to ensure the client has an
  // up-to-date value before proceeding.
  string etag = 139;

  // Fleet information for the cluster.
  Fleet fleet = 140;

  // Enable/Disable Security Posture API features for the cluster.
  SecurityPostureConfig security_posture_config = 145;

  // GKE Enterprise Configuration.
  EnterpriseConfig enterprise_config = 149;

  // Secret CSI driver configuration.
  SecretManagerConfig secret_manager_config = 150;

  // Enable/Disable Compliance Posture features for the cluster.
  CompliancePostureConfig compliance_posture_config = 151;

  // Output only. Reserved for future use.
  optional bool satisfies_pzs = 152 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  optional bool satisfies_pzi = 153 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CompliancePostureConfig defines the settings needed to enable/disable
// features for the Compliance Posture.
message CompliancePostureConfig {
  // Mode defines enablement mode for Compliance Posture.
  enum Mode {
    // Default value not specified.
    MODE_UNSPECIFIED = 0;

    // Disables Compliance Posture features on the cluster.
    DISABLED = 1;

    // Enables Compliance Posture features on the cluster.
    ENABLED = 2;
  }

  // Defines the details of a compliance standard.
  message ComplianceStandard {
    // Name of the compliance standard.
    optional string standard = 1;
  }

  // Defines the enablement mode for Compliance Posture.
  optional Mode mode = 1;

  // List of enabled compliance standards.
  repeated ComplianceStandard compliance_standards = 2;
}

// Kubernetes open source beta apis enabled on the cluster.
message K8sBetaAPIConfig {
  // api name, e.g. storage.k8s.io/v1beta1/csistoragecapacities.
  repeated string enabled_apis = 1;
}

// WorkloadConfig defines the flags to enable or disable the
// workload configurations for the cluster.
message WorkloadConfig {
  // Mode defines how to audit the workload configs.
  enum Mode {
    // Default value meaning that no mode has been specified.
    MODE_UNSPECIFIED = 0;

    // This disables Workload Configuration auditing on the cluster,
    // meaning that nothing is surfaced.
    DISABLED = 1;

    // Applies the default set of policy auditing to a cluster's workloads.
    BASIC = 4;

    // Surfaces configurations that are not in line with the
    // Pod Security Standard Baseline policy.
    BASELINE = 2 [deprecated = true];

    // Surfaces configurations that are not in line with the
    // Pod Security Standard Restricted policy.
    RESTRICTED = 3 [deprecated = true];
  }

  // Sets which mode of auditing should be used for the cluster's workloads.
  optional Mode audit_mode = 1;
}

// ProtectConfig defines the flags needed to enable/disable features for the
// Protect API.
message ProtectConfig {
  // WorkloadVulnerabilityMode defines mode to perform vulnerability scanning.
  enum WorkloadVulnerabilityMode {
    // Default value not specified.
    WORKLOAD_VULNERABILITY_MODE_UNSPECIFIED = 0;

    // Disables Workload Vulnerability Scanning feature on the cluster.
    DISABLED = 1;

    // Applies basic vulnerability scanning settings for cluster workloads.
    BASIC = 2;
  }

  // WorkloadConfig defines which actions are enabled for a cluster's workload
  // configurations.
  optional WorkloadConfig workload_config = 1;

  // Sets which mode to use for Protect workload vulnerability scanning feature.
  optional WorkloadVulnerabilityMode workload_vulnerability_mode = 2;
}

// SecurityPostureConfig defines the flags needed to enable/disable features for
// the Security Posture API.
message SecurityPostureConfig {
  // Mode defines enablement mode for GKE Security posture features.
  enum Mode {
    // Default value not specified.
    MODE_UNSPECIFIED = 0;

    // Disables Security Posture features on the cluster.
    DISABLED = 1;

    // Applies Security Posture features on the cluster.
    BASIC = 2;

    // Applies the Security Posture off cluster Enterprise level features.
    ENTERPRISE = 3;
  }

  // VulnerabilityMode defines enablement mode for vulnerability scanning.
  enum VulnerabilityMode {
    // Default value not specified.
    VULNERABILITY_MODE_UNSPECIFIED = 0;

    // Disables vulnerability scanning on the cluster.
    VULNERABILITY_DISABLED = 1;

    // Applies basic vulnerability scanning on the cluster.
    VULNERABILITY_BASIC = 2;

    // Applies the Security Posture's vulnerability on cluster Enterprise level
    // features.
    VULNERABILITY_ENTERPRISE = 3;
  }

  // Sets which mode to use for Security Posture features.
  optional Mode mode = 1;

  // Sets which mode to use for vulnerability scanning.
  optional VulnerabilityMode vulnerability_mode = 2;
}

// Subset of Nodepool message that has defaults.
message NodePoolDefaults {
  // Subset of NodeConfig message that has defaults.
  NodeConfigDefaults node_config_defaults = 1;
}

// Subset of NodeConfig message that has defaults.
message NodeConfigDefaults {
  // GCFS (Google Container File System, also known as Riptide) options.
  GcfsConfig gcfs_config = 1;

  // Logging configuration for node pools.
  NodePoolLoggingConfig logging_config = 3;

  // Parameters for containerd customization.
  ContainerdConfig containerd_config = 4;

  // HostMaintenancePolicy contains the desired maintenance policy for the
  // Google Compute Engine hosts.
  HostMaintenancePolicy host_maintenance_policy = 5;

  // NodeKubeletConfig controls the defaults for new node-pools.
  //
  // Currently only `insecure_kubelet_readonly_port_enabled` can be set here.
  NodeKubeletConfig node_kubelet_config = 6;
}

// node pool configs that apply to all auto-provisioned node pools
// in autopilot clusters and node auto-provisioning enabled clusters
message NodePoolAutoConfig {
  // The list of instance tags applied to all nodes. Tags are used to identify
  // valid sources or targets for network firewalls and are specified by
  // the client during cluster creation. Each tag within the list
  // must comply with RFC1035.
  NetworkTags network_tags = 1;

  // Resource manager tag keys and values to be attached to the nodes
  // for managing Compute Engine firewalls using Network Firewall Policies.
  ResourceManagerTags resource_manager_tags = 2;

  // NodeKubeletConfig controls the defaults for autoprovisioned node-pools.
  //
  // Currently only `insecure_kubelet_readonly_port_enabled` can be set here.
  NodeKubeletConfig node_kubelet_config = 3;
}

// ClusterUpdate describes an update to the cluster. Exactly one update can
// be applied to a cluster with each request, so at most one field can be
// provided.
message ClusterUpdate {
  // The Kubernetes version to change the nodes to (typically an
  // upgrade).
  //
  // Users may specify either explicit versions offered by
  // Kubernetes Engine or version aliases, which have the following behavior:
  //
  // - "latest": picks the highest valid Kubernetes version
  // - "1.X": picks the highest valid patch+gke.N patch in the 1.X version
  // - "1.X.Y": picks the highest valid gke.N patch in the 1.X.Y version
  // - "1.X.Y-gke.N": picks an explicit Kubernetes version
  // - "-": picks the Kubernetes master version
  string desired_node_version = 4;

  // The monitoring service the cluster should use to write metrics.
  // Currently available options:
  //
  // * "monitoring.googleapis.com/kubernetes" - The Cloud Monitoring
  // service with a Kubernetes-native resource model
  // * `monitoring.googleapis.com` - The legacy Cloud Monitoring service (no
  //   longer available as of GKE 1.15).
  // * `none` - No metrics will be exported from the cluster.
  //
  // If left as an empty string,`monitoring.googleapis.com/kubernetes` will be
  // used for GKE 1.14+ or `monitoring.googleapis.com` for earlier versions.
  string desired_monitoring_service = 5;

  // Configurations for the various addons available to run in the cluster.
  AddonsConfig desired_addons_config = 6;

  // The node pool to be upgraded. This field is mandatory if
  // "desired_node_version", "desired_image_family",
  // "desired_node_pool_autoscaling", or "desired_workload_metadata_config"
  // is specified and there is more than one node pool on the cluster.
  string desired_node_pool_id = 7;

  // The desired image type for the node pool.
  // NOTE: Set the "desired_node_pool" field as well.
  string desired_image_type = 8;

  // Autoscaler configuration for the node pool specified in
  // desired_node_pool_id. If there is only one pool in the
  // cluster and desired_node_pool_id is not provided then
  // the change applies to that single node pool.
  NodePoolAutoscaling desired_node_pool_autoscaling = 9;

  // The desired list of Google Compute Engine
  // [zones](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster's nodes should be located.
  //
  // This list must always include the cluster's primary zone.
  //
  // Warning: changing cluster locations will update the locations of all node
  // pools and will result in nodes being added and/or removed.
  repeated string desired_locations = 10;

  // The desired configuration options for master authorized networks feature.
  MasterAuthorizedNetworksConfig desired_master_authorized_networks_config = 12;

  // The desired configuration options for the PodSecurityPolicy feature.
  PodSecurityPolicyConfig desired_pod_security_policy_config = 14;

  // Cluster-level autoscaling configuration.
  ClusterAutoscaling desired_cluster_autoscaling = 15;

  // The desired configuration options for the Binary Authorization feature.
  BinaryAuthorization desired_binary_authorization = 16;

  // The logging service the cluster should use to write logs.
  // Currently available options:
  //
  // * `logging.googleapis.com/kubernetes` - The Cloud Logging
  // service with a Kubernetes-native resource model
  // * `logging.googleapis.com` - The legacy Cloud Logging service (no longer
  //   available as of GKE 1.15).
  // * `none` - no logs will be exported from the cluster.
  //
  // If left as an empty string,`logging.googleapis.com/kubernetes` will be
  // used for GKE 1.14+ or `logging.googleapis.com` for earlier versions.
  string desired_logging_service = 19;

  // The desired configuration for exporting resource usage.
  ResourceUsageExportConfig desired_resource_usage_export_config = 21;

  // Cluster-level Vertical Pod Autoscaling configuration.
  VerticalPodAutoscaling desired_vertical_pod_autoscaling = 22;

  // The desired private cluster configuration. master_global_access_config is
  // the only field that can be changed via this field.
  // See also
  // [ClusterUpdate.desired_enable_private_endpoint][google.container.v1beta1.ClusterUpdate.desired_enable_private_endpoint]
  // for modifying other fields within
  // [PrivateClusterConfig][google.container.v1beta1.PrivateClusterConfig].
  PrivateClusterConfig desired_private_cluster_config = 25;

  // The desired config of Intra-node visibility.
  IntraNodeVisibilityConfig desired_intra_node_visibility_config = 26;

  // The desired status of whether to disable default sNAT for this cluster.
  DefaultSnatStatus desired_default_snat_status = 28;

  // The desired telemetry integration for the cluster.
  ClusterTelemetry desired_cluster_telemetry = 30;

  // The desired release channel configuration.
  ReleaseChannel desired_release_channel = 31;

  // The desired private cluster configuration. Has no effect. Use
  // [desired_private_cluster_config][google.container.v1beta1.ClusterUpdate.desired_private_cluster_config]
  // instead.
  PrivateClusterConfig private_cluster_config = 37 [deprecated = true];

  // The desired Cloud TPU configuration.
  TpuConfig desired_tpu_config = 38;

  // The desired L4 Internal Load Balancer Subsetting configuration.
  ILBSubsettingConfig desired_l4ilb_subsetting_config = 39;

  // The desired datapath provider for the cluster.
  DatapathProvider desired_datapath_provider = 50;

  // The desired state of IPv6 connectivity to Google Services.
  PrivateIPv6GoogleAccess desired_private_ipv6_google_access = 51;

  // The desired notification configuration.
  NotificationConfig desired_notification_config = 55;

  // The Kubernetes version to change the master to. The only valid value is the
  // latest supported version.
  //
  // Users may specify either explicit versions offered by
  // Kubernetes Engine or version aliases, which have the following behavior:
  //
  // - "latest": picks the highest valid Kubernetes version
  // - "1.X": picks the highest valid patch+gke.N patch in the 1.X version
  // - "1.X.Y": picks the highest valid gke.N patch in the 1.X.Y version
  // - "1.X.Y-gke.N": picks an explicit Kubernetes version
  // - "-": picks the default Kubernetes version
  string desired_master_version = 100;

  // The desired GCFS config for the cluster.
  GcfsConfig desired_gcfs_config = 109;

  // Configuration of etcd encryption.
  DatabaseEncryption desired_database_encryption = 46;

  // Configuration for Workload Identity.
  WorkloadIdentityConfig desired_workload_identity_config = 47;

  // Configuration for issuance of mTLS keys and certificates to Kubernetes
  // pods.
  WorkloadCertificates desired_workload_certificates = 61;

  // Configuration for issuance of mTLS keys and certificates to Kubernetes
  // pods.
  MeshCertificates desired_mesh_certificates = 67;

  // Configuration for direct-path (via ALTS) with workload identity.
  WorkloadALTSConfig desired_workload_alts_config = 62;

  // Configuration for Shielded Nodes.
  ShieldedNodes desired_shielded_nodes = 48;

  // The desired configuration for the fine-grained cost management feature.
  CostManagementConfig desired_cost_management_config = 49;

  // Configuration for master components.
  Master desired_master = 52;

  // DNSConfig contains clusterDNS config for this cluster.
  DNSConfig desired_dns_config = 53;

  // ServiceExternalIPsConfig specifies the config for the use of Services with
  // ExternalIPs field.
  ServiceExternalIPsConfig desired_service_external_ips_config = 60;

  // AuthenticatorGroupsConfig specifies the config for the cluster security
  // groups settings.
  AuthenticatorGroupsConfig desired_authenticator_groups_config = 63;

  // The desired logging configuration.
  LoggingConfig desired_logging_config = 64;

  // The desired monitoring configuration.
  MonitoringConfig desired_monitoring_config = 65;

  // The desired Identity Service component configuration.
  IdentityServiceConfig desired_identity_service_config = 66;

  // Enable/Disable private endpoint for the cluster's master.
  optional bool desired_enable_private_endpoint = 71;

  // The desired network tags that apply to all auto-provisioned node pools
  // in autopilot clusters and node auto-provisioning enabled clusters.
  NetworkTags desired_node_pool_auto_config_network_tags = 110;

  // Deprecated: Use DesiredSecurityPostureConfig instead.
  // Enable/Disable Protect API features for the cluster.
  optional ProtectConfig desired_protect_config = 112 [deprecated = true];

  // The desired config of Gateway API on this cluster.
  GatewayAPIConfig desired_gateway_api_config = 114;

  // The current etag of the cluster.
  // If an etag is provided and does not match the current etag of the cluster,
  // update will be blocked and an ABORTED error will be returned.
  string etag = 115;

  // The desired node pool logging configuration defaults for the cluster.
  NodePoolLoggingConfig desired_node_pool_logging_config = 116;

  // The desired fleet configuration for the cluster.
  Fleet desired_fleet = 117;

  // The desired stack type of the cluster.
  // If a stack type is provided and does not match the current stack type of
  // the cluster, update will attempt to change the stack type to the new type.
  StackType desired_stack_type = 119;

  // The additional pod ranges to be added to the cluster. These pod ranges
  // can be used by node pools to allocate pod IPs.
  AdditionalPodRangesConfig additional_pod_ranges_config = 120;

  // The additional pod ranges that are to be removed from the cluster.
  // The pod ranges specified here must have been specified earlier in the
  // 'additional_pod_ranges_config' argument.
  AdditionalPodRangesConfig removed_additional_pod_ranges_config = 121;

  // Kubernetes open source beta apis enabled on the cluster. Only beta apis
  K8sBetaAPIConfig enable_k8s_beta_apis = 122;

  // Enable/Disable Security Posture API features for the cluster.
  SecurityPostureConfig desired_security_posture_config = 124;

  // The desired network performance config.
  NetworkConfig.ClusterNetworkPerformanceConfig
      desired_network_performance_config = 125;

  // Enable/Disable FQDN Network Policy for the cluster.
  optional bool desired_enable_fqdn_network_policy = 126;

  // The desired workload policy configuration for the autopilot cluster.
  WorkloadPolicyConfig desired_autopilot_workload_policy_config = 128;

  // Beta APIs enabled for cluster.
  K8sBetaAPIConfig desired_k8s_beta_apis = 131;

  // HostMaintenancePolicy contains the desired maintenance policy for the
  // Google Compute Engine hosts.
  HostMaintenancePolicy desired_host_maintenance_policy = 132;

  // The desired containerd config for the cluster.
  ContainerdConfig desired_containerd_config = 134;

  // Enable/Disable Multi-Networking for the cluster
  optional bool desired_enable_multi_networking = 135;

  // The desired resource manager tags that apply to all auto-provisioned node
  // pools in autopilot clusters and node auto-provisioning enabled clusters.
  ResourceManagerTags desired_node_pool_auto_config_resource_manager_tags = 136;

  // Specify the details of in-transit encryption.
  // Now named inter-node transparent encryption.
  optional InTransitEncryptionConfig desired_in_transit_encryption_config = 137;

  // Enable/Disable Cilium Clusterwide Network Policy for the cluster.
  optional bool desired_enable_cilium_clusterwide_network_policy = 138;

  // Enable/Disable Secret Manager Config.
  optional SecretManagerConfig desired_secret_manager_config = 139;

  // The desired node kubelet config for the cluster.
  NodeKubeletConfig desired_node_kubelet_config = 141;

  // The desired node kubelet config for all auto-provisioned node pools
  // in autopilot clusters and node auto-provisioning enabled clusters.
  NodeKubeletConfig desired_node_pool_auto_config_kubelet_config = 142;
}

// AdditionalPodRangesConfig is the configuration for additional pod secondary
// ranges supporting the ClusterUpdate message.
message AdditionalPodRangesConfig {
  // Name for pod secondary ipv4 range which has the actual range defined ahead.
  repeated string pod_range_names = 1;

  // Output only. Information for additional pod range.
  repeated RangeInfo pod_range_info = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// RangeInfo contains the range name and the range utilization by this cluster.
message RangeInfo {
  // Output only. Name of a range.
  string range_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The utilization of the range.
  double utilization = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// This operation resource represents operations that may have happened or are
// happening on the cluster. All fields are output only.
message Operation {
  // Current status of the operation.
  enum Status {
    // Not set.
    STATUS_UNSPECIFIED = 0;

    // The operation has been created.
    PENDING = 1;

    // The operation is currently running.
    RUNNING = 2;

    // The operation is done, either cancelled or completed.
    DONE = 3;

    // The operation is aborting.
    ABORTING = 4;
  }

  // Operation type categorizes the operation.
  enum Type {
    // Not set.
    TYPE_UNSPECIFIED = 0;

    // The cluster is being created. The cluster should be assumed to be
    // unusable until the operation finishes.
    //
    // In the event of the operation failing, the cluster will enter the [ERROR
    // state][Cluster.Status.ERROR] and eventually be deleted.
    CREATE_CLUSTER = 1;

    // The cluster is being deleted. The cluster should be assumed to be
    // unusable as soon as this operation starts.
    //
    // In the event of the operation failing, the cluster will enter the [ERROR
    // state][Cluster.Status.ERROR] and the deletion will be automatically
    // retried until completed.
    DELETE_CLUSTER = 2;

    // The [cluster
    // version][google.container.v1beta1.ClusterUpdate.desired_master_version]
    // is being updated. Note that this includes "upgrades" to the same version,
    // which are simply a recreation. This also includes
    // [auto-upgrades](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-upgrades#upgrading_automatically).
    // For more details, see [documentation on cluster
    // upgrades](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-upgrades#cluster_upgrades).
    UPGRADE_MASTER = 3;

    // A node pool is being updated. Despite calling this an "upgrade", this
    // includes most forms of updates to node pools. This also includes
    // [auto-upgrades](https://cloud.google.com/kubernetes-engine/docs/how-to/node-auto-upgrades).
    //
    // This operation sets the
    // [progress][google.container.v1beta1.Operation.progress] field and may be
    // [canceled][google.container.v1beta1.ClusterManager.CancelOperation].
    //
    // The upgrade strategy depends on [node pool
    // configuration](https://cloud.google.com/kubernetes-engine/docs/concepts/node-pool-upgrade-strategies).
    // The nodes are generally still usable during this operation.
    UPGRADE_NODES = 4;

    // A problem has been detected with the control plane and is being repaired.
    // This operation type is initiated by GKE. For more details, see
    // [documentation on
    // repairs](https://cloud.google.com/kubernetes-engine/docs/concepts/maintenance-windows-and-exclusions#repairs).
    REPAIR_CLUSTER = 5;

    // The cluster is being updated. This is a broad category of operations and
    // includes operations that only change metadata as well as those that must
    // recreate the entire cluster. If the control plane must be recreated, this
    // will cause temporary downtime for zonal clusters.
    //
    // Some features require recreating the nodes as well. Those will be
    // recreated as separate operations and the update may not be completely
    // functional until the node pools recreations finish. Node recreations will
    // generally follow [maintenance
    // policies](https://cloud.google.com/kubernetes-engine/docs/concepts/maintenance-windows-and-exclusions).
    //
    // Some GKE-initiated operations use this type. This includes certain types
    // of auto-upgrades and incident mitigations.
    UPDATE_CLUSTER = 6;

    // A node pool is being created. The node pool should be assumed to be
    // unusable until this operation finishes. In the event of an error, the
    // node pool may be partially created.
    //
    // If enabled, [node
    // autoprovisioning](https://cloud.google.com/kubernetes-engine/docs/how-to/node-auto-provisioning)
    // may have automatically initiated such operations.
    CREATE_NODE_POOL = 7;

    // The node pool is being deleted. The node pool should be assumed to be
    // unusable as soon as this operation starts.
    DELETE_NODE_POOL = 8;

    // The node pool's
    // [manamagent][google.container.v1beta1.NodePool.management] field is being
    // updated. These operations only update metadata and may be concurrent with
    // most other operations.
    SET_NODE_POOL_MANAGEMENT = 9;

    // A problem has been detected with nodes and [they are being
    // repaired](https://cloud.google.com/kubernetes-engine/docs/how-to/node-auto-repair).
    // This operation type is initiated by GKE, typically automatically. This
    // operation may be concurrent with other operations and there may be
    // multiple repairs occurring on the same node pool.
    AUTO_REPAIR_NODES = 10;

    // Unused. Automatic node upgrade uses
    // [UPGRADE_NODES][google.container.v1beta1.Operation.Type.UPGRADE_NODES].
    AUTO_UPGRADE_NODES = 11 [deprecated = true];

    // Unused. Updating labels uses
    // [UPDATE_CLUSTER][google.container.v1beta1.Operation.Type.UPDATE_CLUSTER].
    SET_LABELS = 12 [deprecated = true];

    // Unused. Updating master auth uses
    // [UPDATE_CLUSTER][google.container.v1beta1.Operation.Type.UPDATE_CLUSTER].
    SET_MASTER_AUTH = 13 [deprecated = true];

    // The node pool is being resized. With the exception of resizing to or from
    // size zero, the node pool is generally usable during this operation.
    SET_NODE_POOL_SIZE = 14;

    // Unused. Updating network policy uses
    // [UPDATE_CLUSTER][google.container.v1beta1.Operation.Type.UPDATE_CLUSTER].
    SET_NETWORK_POLICY = 15 [deprecated = true];

    // Unused. Updating maintenance policy uses
    // [UPDATE_CLUSTER][google.container.v1beta1.Operation.Type.UPDATE_CLUSTER].
    SET_MAINTENANCE_POLICY = 16 [deprecated = true];

    // The control plane is being resized. This operation type is initiated by
    // GKE. These operations are often performed preemptively to ensure that the
    // control plane has sufficient resources and is not typically an indication
    // of issues. For more details, see
    // [documentation on
    // resizes](https://cloud.google.com/kubernetes-engine/docs/concepts/maintenance-windows-and-exclusions#repairs).
    RESIZE_CLUSTER = 18;

    // Fleet features of GKE Enterprise are being upgraded. The cluster should
    // be assumed to be blocked for other upgrades until the operation finishes.
    FLEET_FEATURE_UPGRADE = 19;
  }

  // Output only. The server-assigned ID for the operation.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // operation is taking place. This field is deprecated, use location instead.
  string zone = 2
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The operation type.
  Type operation_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current status of the operation.
  Status status = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Detailed operation progress, if available.
  string detail = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If an error has occurred, a textual description of the error.
  // Deprecated. Use field error instead.
  string status_message = 5
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined URI for the operation. Example:
  // `https://container.googleapis.com/v1alpha1/projects/123/locations/us-central1/operations/operation-123`.
  string self_link = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined URI for the target of the operation. The format
  // of this is a URI to the resource being modified (such as a cluster, node
  // pool, or node). For node pool repairs, there may be multiple nodes being
  // repaired, but only one will be the target.
  //
  // Examples:
  //
  // -
  // ##
  // `https://container.googleapis.com/v1/projects/123/locations/us-central1/clusters/my-cluster`
  //
  // ##
  // `https://container.googleapis.com/v1/projects/123/zones/us-central1-c/clusters/my-cluster/nodePools/my-np`
  //
  // `https://container.googleapis.com/v1/projects/123/zones/us-central1-c/clusters/my-cluster/nodePools/my-np/node/my-node`
  string target_link = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/regions-zones/regions-zones#available)
  // or
  // [region](https://cloud.google.com/compute/docs/regions-zones/regions-zones#available)
  // in which the cluster resides.
  string location = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation started, in
  // [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
  string start_time = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation completed, in
  // [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
  string end_time = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Progress information for an operation.
  OperationProgress progress = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Which conditions caused the current cluster state.
  // Deprecated. Use field error instead.
  repeated StatusCondition cluster_conditions = 13 [deprecated = true];

  // Which conditions caused the current node pool state.
  // Deprecated. Use field error instead.
  repeated StatusCondition nodepool_conditions = 14 [deprecated = true];

  // The error result of the operation in case of failure.
  google.rpc.Status error = 15;
}

// Information about operation (or operation stage) progress.
message OperationProgress {
  // Progress metric is (string, int|float|string) pair.
  message Metric {
    // Required. Metric name, e.g., "nodes total", "percent done".
    string name = 1 [(google.api.field_behavior) = REQUIRED];

    // Strictly one of the values is required.
    oneof value {
      // For metrics with integer value.
      int64 int_value = 2;

      // For metrics with floating point value.
      double double_value = 3;

      // For metrics with custom values (ratios, visual progress, etc.).
      string string_value = 4;
    }
  }

  // A non-parameterized string describing an operation stage.
  // Unset for single-stage operations.
  string name = 1;

  // Status of an operation stage.
  // Unset for single-stage operations.
  Operation.Status status = 2;

  // Progress metric bundle, for example:
  //   metrics: [{name: "nodes done",     int_value: 15},
  //             {name: "nodes total",    int_value: 32}]
  // or
  //   metrics: [{name: "progress",       double_value: 0.56},
  //             {name: "progress scale", double_value: 1.0}]
  repeated Metric metrics = 3;

  // Substages of an operation or a stage.
  repeated OperationProgress stages = 4;
}

// CreateClusterRequest creates a cluster.
message CreateClusterRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the parent field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the parent
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. A [cluster
  // resource](https://cloud.google.com/container-engine/reference/rest/v1beta1/projects.locations.clusters)
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // The parent (project and location) where the cluster will be created.
  // Specified in the format `projects/*/locations/*`.
  string parent = 5;
}

// GetClusterRequest gets the settings of a cluster.
message GetClusterRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to retrieve.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to retrieve.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 5;
}

// UpdateClusterRequest updates the settings of a cluster.
message UpdateClusterRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. A description of the update.
  ClusterUpdate update = 4 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to update.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 5;
}

// SetNodePoolVersionRequest updates the version of a node pool.
message UpdateNodePoolRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the node pool to upgrade.
  // This field has been deprecated and replaced by the name field.
  string node_pool_id = 4
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The Kubernetes version to change the nodes to (typically an
  // upgrade).
  //
  // Users may specify either explicit versions offered by Kubernetes Engine or
  // version aliases, which have the following behavior:
  //
  // - "latest": picks the highest valid Kubernetes version
  // - "1.X": picks the highest valid patch+gke.N patch in the 1.X version
  // - "1.X.Y": picks the highest valid gke.N patch in the 1.X.Y version
  // - "1.X.Y-gke.N": picks an explicit Kubernetes version
  // - "-": picks the Kubernetes master version
  string node_version = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The desired image type for the node pool. Please see
  // https://cloud.google.com/kubernetes-engine/docs/concepts/node-images for
  // available image types.
  string image_type = 6 [(google.api.field_behavior) = REQUIRED];

  // The desired list of Google Compute Engine
  // [zones](https://cloud.google.com/compute/docs/zones#available) in which the
  // node pool's nodes should be located. Changing the locations for a node pool
  // will result in nodes being either created or removed from the node pool,
  // depending on whether locations are being added or removed.
  repeated string locations = 13;

  // The desired workload metadata config for the node pool.
  WorkloadMetadataConfig workload_metadata_config = 14;

  // The name (project, location, cluster, node pool) of the node pool to
  // update. Specified in the format
  // `projects/*/locations/*/clusters/*/nodePools/*`.
  string name = 8;

  // Upgrade settings control disruption and speed of the upgrade.
  NodePool.UpgradeSettings upgrade_settings = 15;

  // The desired network tags to be applied to all nodes in the node pool.
  // If this field is not present, the tags will not be changed. Otherwise,
  // the existing network tags will be *replaced* with the provided tags.
  NetworkTags tags = 16;

  // The desired node taints to be applied to all nodes in the node pool.
  // If this field is not present, the taints will not be changed. Otherwise,
  // the existing node taints will be *replaced* with the provided taints.
  NodeTaints taints = 17;

  // The desired node labels to be applied to all nodes in the node pool.
  // If this field is not present, the labels will not be changed. Otherwise,
  // the existing node labels will be *replaced* with the provided labels.
  NodeLabels labels = 18;

  // Parameters that can be configured on Linux nodes.
  LinuxNodeConfig linux_node_config = 19;

  // Node kubelet configs.
  NodeKubeletConfig kubelet_config = 20;

  // Node network config.
  NodeNetworkConfig node_network_config = 21;

  // GCFS config.
  GcfsConfig gcfs_config = 22;

  // Confidential nodes config.
  // All the nodes in the node pool will be Confidential VM once enabled.
  ConfidentialNodes confidential_nodes = 23;

  // Enable or disable gvnic on the node pool.
  VirtualNIC gvnic = 29;

  // The current etag of the node pool.
  // If an etag is provided and does not match the current etag of the node
  // pool, update will be blocked and an ABORTED error will be returned.
  string etag = 30;

  // Enable or disable NCCL fast socket for the node pool.
  FastSocket fast_socket = 31;

  // Logging configuration.
  NodePoolLoggingConfig logging_config = 32;

  // The resource labels for the node pool to use to annotate any related
  // Google Compute Engine resources.
  ResourceLabels resource_labels = 33;

  // Parameters that can be configured on Windows nodes.
  WindowsNodeConfig windows_node_config = 34;

  // A list of hardware accelerators to be attached to each node.
  // See https://cloud.google.com/compute/docs/gpus for more information about
  // support for GPUs.
  repeated AcceleratorConfig accelerators = 35;

  // Optional. The desired machine type for nodes in the node pool.
  // Initiates an upgrade operation that migrates the nodes in the
  // node pool to the specified machine type.
  string machine_type = 36 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The desired disk type for nodes in the node pool.
  // Initiates an upgrade operation that migrates the nodes in the
  // node pool to the specified disk type.
  string disk_type = 37 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The desired disk size for nodes in the node pool.
  // Initiates an upgrade operation that migrates the nodes in the
  // node pool to the specified disk size.
  int64 disk_size_gb = 38 [(google.api.field_behavior) = OPTIONAL];

  // Desired resource manager tag keys and values to be attached to the nodes
  // for managing Compute Engine firewalls using Network Firewall Policies.
  // Existing tags will be replaced with new values.
  ResourceManagerTags resource_manager_tags = 39;

  // The desired containerd config for nodes in the node pool.
  // Initiates an upgrade operation that recreates the nodes with the new
  // config.
  ContainerdConfig containerd_config = 40;

  // Specifies the configuration of queued provisioning.
  NodePool.QueuedProvisioning queued_provisioning = 42;
}

// SetNodePoolAutoscalingRequest sets the autoscaler settings of a node pool.
message SetNodePoolAutoscalingRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the node pool to upgrade.
  // This field has been deprecated and replaced by the name field.
  string node_pool_id = 4
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Autoscaling configuration for the node pool.
  NodePoolAutoscaling autoscaling = 5 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster, node pool) of the node pool to set
  // autoscaler settings. Specified in the format
  // `projects/*/locations/*/clusters/*/nodePools/*`.
  string name = 6;
}

// SetLoggingServiceRequest sets the logging service of a cluster.
message SetLoggingServiceRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The logging service the cluster should use to write logs.
  // Currently available options:
  //
  // * `logging.googleapis.com/kubernetes` - The Cloud Logging
  // service with a Kubernetes-native resource model
  // * `logging.googleapis.com` - The legacy Cloud Logging service (no longer
  //   available as of GKE 1.15).
  // * `none` - no logs will be exported from the cluster.
  //
  // If left as an empty string,`logging.googleapis.com/kubernetes` will be
  // used for GKE 1.14+ or `logging.googleapis.com` for earlier versions.
  string logging_service = 4 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to set logging.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 5;
}

// SetMonitoringServiceRequest sets the monitoring service of a cluster.
message SetMonitoringServiceRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The monitoring service the cluster should use to write metrics.
  // Currently available options:
  //
  // * "monitoring.googleapis.com/kubernetes" - The Cloud Monitoring
  // service with a Kubernetes-native resource model
  // * `monitoring.googleapis.com` - The legacy Cloud Monitoring service (no
  //   longer available as of GKE 1.15).
  // * `none` - No metrics will be exported from the cluster.
  //
  // If left as an empty string,`monitoring.googleapis.com/kubernetes` will be
  // used for GKE 1.14+ or `monitoring.googleapis.com` for earlier versions.
  string monitoring_service = 4 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to set monitoring.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 6;
}

// SetAddonsRequest sets the addons associated with the cluster.
message SetAddonsConfigRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The desired configurations for the various addons available to
  // run in the cluster.
  AddonsConfig addons_config = 4 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to set addons.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 6;
}

// SetLocationsRequest sets the locations of the cluster.
message SetLocationsRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The desired list of Google Compute Engine
  // [zones](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster's nodes should be located. Changing the locations a cluster is in
  // will result in nodes being either created or removed from the cluster,
  // depending on whether locations are being added or removed.
  //
  // This list must always include the cluster's primary zone.
  repeated string locations = 4 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to set locations.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 6;
}

// UpdateMasterRequest updates the master of the cluster.
message UpdateMasterRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The Kubernetes version to change the master to.
  //
  // Users may specify either explicit versions offered by
  // Kubernetes Engine or version aliases, which have the following behavior:
  //
  // - "latest": picks the highest valid Kubernetes version
  // - "1.X": picks the highest valid patch+gke.N patch in the 1.X version
  // - "1.X.Y": picks the highest valid gke.N patch in the 1.X.Y version
  // - "1.X.Y-gke.N": picks an explicit Kubernetes version
  // - "-": picks the default Kubernetes version
  string master_version = 4 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to update.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 7;
}

// SetMasterAuthRequest updates the admin password of a cluster.
message SetMasterAuthRequest {
  // Operation type: what type update to perform.
  enum Action {
    // Operation is unknown and will error out.
    UNKNOWN = 0;

    // Set the password to a user generated value.
    SET_PASSWORD = 1;

    // Generate a new password and set it to that.
    GENERATE_PASSWORD = 2;

    // Set the username.  If an empty username is provided, basic authentication
    // is disabled for the cluster.  If a non-empty username is provided, basic
    // authentication is enabled, with either a provided password or a generated
    // one.
    SET_USERNAME = 3;
  }

  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to upgrade.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The exact form of action to be taken on the master auth.
  Action action = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. A description of the update.
  MasterAuth update = 5 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to set auth.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 7;
}

// DeleteClusterRequest deletes a cluster.
message DeleteClusterRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to delete.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster) of the cluster to delete.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 4;
}

// ListClustersRequest lists clusters.
message ListClustersRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the parent field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides, or "-" for all zones. This field has been deprecated and
  // replaced by the parent field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The parent (project and location) where the clusters will be listed.
  // Specified in the format `projects/*/locations/*`.
  // Location "-" matches all zones and all regions.
  string parent = 4;
}

// ListClustersResponse is the result of ListClustersRequest.
message ListClustersResponse {
  // A list of clusters in the project in the specified zone, or
  // across all ones.
  repeated Cluster clusters = 1;

  // If any zones are listed here, the list of clusters returned
  // may be missing those zones.
  repeated string missing_zones = 2;
}

// GetOperationRequest gets a single operation.
message GetOperationRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The server-assigned `name` of the operation.
  // This field has been deprecated and replaced by the name field.
  string operation_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, operation id) of the operation to get.
  // Specified in the format `projects/*/locations/*/operations/*`.
  string name = 5;
}

// ListOperationsRequest lists operations.
message ListOperationsRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the parent field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) to return
  // operations for, or `-` for all zones. This field has been deprecated and
  // replaced by the parent field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The parent (project and location) where the operations will be listed.
  // Specified in the format `projects/*/locations/*`.
  // Location "-" matches all zones and all regions.
  string parent = 4;
}

// CancelOperationRequest cancels a single operation.
message CancelOperationRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // operation resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The server-assigned `name` of the operation.
  // This field has been deprecated and replaced by the name field.
  string operation_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, operation id) of the operation to cancel.
  // Specified in the format `projects/*/locations/*/operations/*`.
  string name = 4;
}

// ListOperationsResponse is the result of ListOperationsRequest.
message ListOperationsResponse {
  // A list of operations in the project in the specified zone.
  repeated Operation operations = 1;

  // If any zones are listed here, the list of operations returned
  // may be missing the operations from those zones.
  repeated string missing_zones = 2;
}

// Gets the current Kubernetes Engine service configuration.
message GetServerConfigRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) to return
  // operations for. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project and location) of the server config to get,
  // specified in the format `projects/*/locations/*`.
  string name = 4;
}

// Kubernetes Engine service configuration.
message ServerConfig {
  // ReleaseChannelConfig exposes configuration for a release channel.
  message ReleaseChannelConfig {
    // Deprecated.
    message AvailableVersion {
      option deprecated = true;

      // Kubernetes version.
      string version = 1;

      // Reason for availability.
      string reason = 2;
    }

    // The release channel this configuration applies to.
    ReleaseChannel.Channel channel = 1;

    // The default version for newly created clusters on the channel.
    string default_version = 2;

    // Deprecated.
    // This field has been deprecated and replaced with the valid_versions
    // field.
    repeated AvailableVersion available_versions = 3 [deprecated = true];

    // List of valid versions for the channel.
    repeated string valid_versions = 4;
  }

  // Version of Kubernetes the service deploys by default.
  string default_cluster_version = 1;

  // List of valid node upgrade target versions, in descending order.
  repeated string valid_node_versions = 3;

  // Default image type.
  string default_image_type = 4;

  // List of valid image types.
  repeated string valid_image_types = 5;

  // List of valid master versions, in descending order.
  repeated string valid_master_versions = 6;

  // List of release channel configurations.
  repeated ReleaseChannelConfig channels = 9;

  // Maps of Kubernetes version and supported Windows server versions.
  map<string, WindowsVersions> windows_version_maps = 10;
}

// Best effort provisioning.
message BestEffortProvisioning {
  // When this is enabled, cluster/node pool creations will ignore non-fatal
  // errors like stockout to best provision as many nodes as possible right now
  // and eventually bring up all target number of nodes
  bool enabled = 1;

  // Minimum number of nodes to be provisioned to be considered as succeeded,
  // and the rest of nodes will be provisioned gradually and eventually when
  // stockout issue has been resolved.
  int32 min_provision_nodes = 2;
}

// Windows server versions.
message WindowsVersions {
  // Windows server version.
  message WindowsVersion {
    // Windows server image type
    string image_type = 1;

    // Windows server build number
    string os_version = 2;

    // Mainstream support end date
    google.type.Date support_end_date = 3;
  }

  // List of Windows server versions.
  repeated WindowsVersion windows_versions = 1;
}

// CreateNodePoolRequest creates a node pool for a cluster.
message CreateNodePoolRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the parent field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the parent
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster.
  // This field has been deprecated and replaced by the parent field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The node pool to create.
  NodePool node_pool = 4 [(google.api.field_behavior) = REQUIRED];

  // The parent (project, location, cluster name) where the node pool will be
  // created. Specified in the format
  // `projects/*/locations/*/clusters/*`.
  string parent = 6;
}

// DeleteNodePoolRequest deletes a node pool for a cluster.
message DeleteNodePoolRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the node pool to delete.
  // This field has been deprecated and replaced by the name field.
  string node_pool_id = 4
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster, node pool id) of the node pool to
  // delete. Specified in the format
  // `projects/*/locations/*/clusters/*/nodePools/*`.
  string name = 6;
}

// ListNodePoolsRequest lists the node pool(s) for a cluster.
message ListNodePoolsRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the parent field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the parent
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster.
  // This field has been deprecated and replaced by the parent field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The parent (project, location, cluster name) where the node pools will be
  // listed. Specified in the format `projects/*/locations/*/clusters/*`.
  string parent = 5;
}

// GetNodePoolRequest retrieves a node pool for a cluster.
message GetNodePoolRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the node pool.
  // This field has been deprecated and replaced by the name field.
  string node_pool_id = 4
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster, node pool id) of the node pool to
  // get. Specified in the format
  // `projects/*/locations/*/clusters/*/nodePools/*`.
  string name = 6;
}

// Settings for blue-green upgrade.
message BlueGreenSettings {
  // Standard rollout policy is the default policy for blue-green.
  message StandardRolloutPolicy {
    // Blue pool size to drain in a batch.
    oneof update_batch_size {
      // Percentage of the blue pool nodes to drain in a batch.
      // The range of this field should be (0.0, 1.0].
      float batch_percentage = 1;

      // Number of blue nodes to drain in a batch.
      int32 batch_node_count = 2;
    }

    // Soak time after each batch gets drained. Default to zero.
    optional google.protobuf.Duration batch_soak_duration = 3;
  }

  // Autoscaled rollout policy uses cluster autoscaler during
  // blue-green upgrades to scale both the green and blue pools.
  message AutoscaledRolloutPolicy {}

  // The rollout policy controls the general rollout progress of blue-green.
  oneof rollout_policy {
    // Standard policy for the blue-green upgrade.
    StandardRolloutPolicy standard_rollout_policy = 1;

    // Autoscaled policy for cluster autoscaler enabled blue-green upgrade.
    AutoscaledRolloutPolicy autoscaled_rollout_policy = 3;
  }

  // Time needed after draining entire blue pool. After this period, blue pool
  // will be cleaned up.
  optional google.protobuf.Duration node_pool_soak_duration = 2;
}

// NodePool contains the name and configuration for a cluster's node pool.
// Node pools are a set of nodes (i.e. VM's), with a common configuration and
// specification, under the control of the cluster master. They may have a set
// of Kubernetes labels applied to them, which may be used to reference them
// during pod scheduling. They may also be resized up or down, to accommodate
// the workload.
message NodePool {
  // These upgrade settings control the level of parallelism and the level of
  // disruption caused by an upgrade.
  //
  // maxUnavailable controls the number of nodes that can be simultaneously
  // unavailable.
  //
  // maxSurge controls the number of additional nodes that can be added to the
  // node pool temporarily for the time of the upgrade to increase the number of
  // available nodes.
  //
  // (maxUnavailable + maxSurge) determines the level of parallelism (how many
  // nodes are being upgraded at the same time).
  //
  // Note: upgrades inevitably introduce some disruption since workloads need to
  // be moved from old nodes to new, upgraded ones. Even if maxUnavailable=0,
  // this holds true. (Disruption stays within the limits of
  // PodDisruptionBudget, if it is configured.)
  //
  // Consider a hypothetical node pool with 5 nodes having maxSurge=2,
  // maxUnavailable=1. This means the upgrade process upgrades 3 nodes
  // simultaneously. It creates 2 additional (upgraded) nodes, then it brings
  // down 3 old (not yet upgraded) nodes at the same time. This ensures that
  // there are always at least 4 nodes available.
  //
  // These upgrade settings configure the upgrade strategy for the node pool.
  // Use strategy to switch between the strategies applied to the node pool.
  //
  // If the strategy is SURGE, use max_surge and max_unavailable to control
  // the level of parallelism and the level of disruption caused by upgrade.
  // 1. maxSurge controls the number of additional nodes that can be added to
  // the node pool temporarily for the time of the upgrade to increase the
  // number of available nodes.
  // 2. maxUnavailable controls the number of nodes that can be simultaneously
  // unavailable.
  // 3. (maxUnavailable + maxSurge) determines the level of parallelism (how
  // many nodes are being upgraded at the same time).
  //
  // If the strategy is BLUE_GREEN, use blue_green_settings to configure the
  // blue-green upgrade related settings.
  // 1. standard_rollout_policy is the default policy. The policy is used to
  // control the way blue pool gets drained. The draining is executed in the
  // batch mode. The batch size could be specified as either percentage of the
  // node pool size or the number of nodes. batch_soak_duration is the soak
  // time after each batch gets drained.
  // 2. node_pool_soak_duration is the soak time after all blue nodes are
  // drained. After this period, the blue pool nodes will be deleted.
  message UpgradeSettings {
    // The maximum number of nodes that can be created beyond the current size
    // of the node pool during the upgrade process.
    int32 max_surge = 1;

    // The maximum number of nodes that can be simultaneously unavailable during
    // the upgrade process. A node is considered available if its status is
    // Ready.
    int32 max_unavailable = 2;

    // Update strategy of the node pool.
    optional NodePoolUpdateStrategy strategy = 3;

    // Settings for blue-green upgrade strategy.
    optional BlueGreenSettings blue_green_settings = 4;
  }

  // UpdateInfo contains resource (instance groups, etc), status and other
  // intermediate information relevant to a node pool upgrade.
  message UpdateInfo {
    // Information relevant to blue-green upgrade.
    message BlueGreenInfo {
      // Phase represents the different stages blue-green upgrade is running in.
      enum Phase {
        // Unspecified phase.
        PHASE_UNSPECIFIED = 0;

        // blue-green upgrade has been initiated.
        UPDATE_STARTED = 1;

        // Start creating green pool nodes.
        CREATING_GREEN_POOL = 2;

        // Start cordoning blue pool nodes.
        CORDONING_BLUE_POOL = 3;

        // Start waiting after cordoning the blue pool and before draining it.
        WAITING_TO_DRAIN_BLUE_POOL = 8;

        // Start draining blue pool nodes.
        DRAINING_BLUE_POOL = 4;

        // Start soaking time after draining entire blue pool.
        NODE_POOL_SOAKING = 5;

        // Start deleting blue nodes.
        DELETING_BLUE_POOL = 6;

        // Rollback has been initiated.
        ROLLBACK_STARTED = 7;
      }

      // Current blue-green upgrade phase.
      Phase phase = 1;

      // The resource URLs of the [managed instance groups]
      // (/compute/docs/instance-groups/creating-groups-of-managed-instances)
      // associated with blue pool.
      repeated string blue_instance_group_urls = 2;

      // The resource URLs of the [managed instance groups]
      // (/compute/docs/instance-groups/creating-groups-of-managed-instances)
      // associated with green pool.
      repeated string green_instance_group_urls = 3;

      // Time to start deleting blue pool to complete blue-green upgrade,
      // in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
      string blue_pool_deletion_start_time = 4;

      // Version of green pool.
      string green_pool_version = 5;
    }

    // Information of a blue-green upgrade.
    BlueGreenInfo blue_green_info = 1;
  }

  // The current status of the node pool instance.
  enum Status {
    // Not set.
    STATUS_UNSPECIFIED = 0;

    // The PROVISIONING state indicates the node pool is being created.
    PROVISIONING = 1;

    // The RUNNING state indicates the node pool has been created
    // and is fully usable.
    RUNNING = 2;

    // The RUNNING_WITH_ERROR state indicates the node pool has been created
    // and is partially usable. Some error state has occurred and some
    // functionality may be impaired. Customer may need to reissue a request
    // or trigger a new update.
    RUNNING_WITH_ERROR = 3;

    // The RECONCILING state indicates that some work is actively being done on
    // the node pool, such as upgrading node software. Details can
    // be found in the `statusMessage` field.
    RECONCILING = 4;

    // The STOPPING state indicates the node pool is being deleted.
    STOPPING = 5;

    // The ERROR state indicates the node pool may be unusable. Details
    // can be found in the `statusMessage` field.
    ERROR = 6;
  }

  // PlacementPolicy defines the placement policy used by the node pool.
  message PlacementPolicy {
    // Type defines the type of placement policy.
    enum Type {
      // TYPE_UNSPECIFIED specifies no requirements on nodes
      // placement.
      TYPE_UNSPECIFIED = 0;

      // COMPACT specifies node placement in the same availability domain to
      // ensure low communication latency.
      COMPACT = 1;
    }

    // The type of placement.
    Type type = 1;

    // TPU placement topology for pod slice node pool.
    // https://cloud.google.com/tpu/docs/types-topologies#tpu_topologies
    string tpu_topology = 2;

    // If set, refers to the name of a custom resource policy supplied by the
    // user. The resource policy must be in the same project and region as the
    // node pool. If not found, InvalidArgument error is returned.
    string policy_name = 3;
  }

  // QueuedProvisioning defines the queued provisioning used by the node pool.
  message QueuedProvisioning {
    // Denotes that this nodepool is QRM specific, meaning nodes can be only
    // obtained through queuing via the Cluster Autoscaler ProvisioningRequest
    // API.
    bool enabled = 1;
  }

  // The name of the node pool.
  string name = 1;

  // The node configuration of the pool.
  NodeConfig config = 2;

  // The initial node count for the pool. You must ensure that your
  // Compute Engine [resource quota](https://cloud.google.com/compute/quotas)
  // is sufficient for this number of instances. You must also have available
  // firewall and routes quota.
  int32 initial_node_count = 3;

  // The list of Google Compute Engine
  // [zones](https://cloud.google.com/compute/docs/zones#available) in which the
  // NodePool's nodes should be located.
  //
  // If this value is unspecified during node pool creation, the
  // [Cluster.Locations](https://cloud.google.com/kubernetes-engine/docs/reference/rest/v1/projects.locations.clusters#Cluster.FIELDS.locations)
  // value will be used, instead.
  //
  // Warning: changing node pool locations will result in nodes being added
  // and/or removed.
  repeated string locations = 13;

  // Networking configuration for this NodePool. If specified, it overrides the
  // cluster-level defaults.
  NodeNetworkConfig network_config = 14;

  // Output only. Server-defined URL for the resource.
  string self_link = 100 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The version of Kubernetes running on this NodePool's nodes. If unspecified,
  // it defaults as described
  // [here](https://cloud.google.com/kubernetes-engine/versioning#specifying_node_version).
  string version = 101;

  // Output only. The resource URLs of the [managed instance
  // groups](https://cloud.google.com/compute/docs/instance-groups/creating-groups-of-managed-instances)
  // associated with this node pool.
  // During the node pool blue-green upgrade operation, the URLs contain both
  // blue and green resources.
  repeated string instance_group_urls = 102
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the nodes in this pool instance.
  Status status = 103 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Deprecated. Use conditions instead.
  // Additional information about the current status of this
  // node pool instance, if available.
  string status_message = 104
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Autoscaler configuration for this NodePool. Autoscaler is enabled
  // only if a valid configuration is present.
  NodePoolAutoscaling autoscaling = 4;

  // NodeManagement configuration for this NodePool.
  NodeManagement management = 5;

  // The constraint on the maximum number of pods that can be run
  // simultaneously on a node in the node pool.
  MaxPodsConstraint max_pods_constraint = 6;

  // Which conditions caused the current node pool state.
  repeated StatusCondition conditions = 105;

  // Output only. The pod CIDR block size per node in this node pool.
  int32 pod_ipv4_cidr_size = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Upgrade settings control disruption and speed of the upgrade.
  UpgradeSettings upgrade_settings = 107;

  // Specifies the node placement policy.
  PlacementPolicy placement_policy = 108;

  // Output only. Update info contains relevant information during a node
  // pool update.
  UpdateInfo update_info = 109 [(google.api.field_behavior) = OUTPUT_ONLY];

  // This checksum is computed by the server based on the value of node pool
  // fields, and may be sent on update requests to ensure the client has an
  // up-to-date value before proceeding.
  string etag = 110;

  // Specifies the configuration of queued provisioning.
  QueuedProvisioning queued_provisioning = 112;

  // Enable best effort provisioning for nodes
  BestEffortProvisioning best_effort_provisioning = 113;
}

// NodeManagement defines the set of node management services turned on for the
// node pool.
message NodeManagement {
  // Whether the nodes will be automatically upgraded.
  bool auto_upgrade = 1;

  // Whether the nodes will be automatically repaired.
  bool auto_repair = 2;

  // Specifies the Auto Upgrade knobs for the node pool.
  AutoUpgradeOptions upgrade_options = 10;
}

// AutoUpgradeOptions defines the set of options for the user to control how
// the Auto Upgrades will proceed.
message AutoUpgradeOptions {
  // Output only. This field is set when upgrades are about to commence
  // with the approximate start time for the upgrades, in
  // [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
  string auto_upgrade_start_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This field is set when upgrades are about to commence
  // with the description of the upgrade.
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// MaintenancePolicy defines the maintenance policy to be used for the cluster.
message MaintenancePolicy {
  // Specifies the maintenance window in which maintenance may be performed.
  MaintenanceWindow window = 1;

  // A hash identifying the version of this policy, so that updates to fields of
  // the policy won't accidentally undo intermediate changes (and so that users
  // of the API unaware of some fields won't accidentally remove other fields).
  // Make a `get()` request to the cluster to get the current
  // resource version and include it with requests to set the policy.
  string resource_version = 3;
}

// MaintenanceWindow defines the maintenance window to be used for the cluster.
message MaintenanceWindow {
  // Unimplemented, reserved for future use.
  // HourlyMaintenanceWindow hourly_maintenance_window = 1;
  oneof policy {
    // DailyMaintenanceWindow specifies a daily maintenance operation window.
    DailyMaintenanceWindow daily_maintenance_window = 2;

    // RecurringWindow specifies some number of recurring time periods for
    // maintenance to occur. The time windows may be overlapping. If no
    // maintenance windows are set, maintenance can occur at any time.
    RecurringTimeWindow recurring_window = 3;
  }

  // Exceptions to maintenance window. Non-emergency maintenance should not
  // occur in these windows.
  map<string, TimeWindow> maintenance_exclusions = 4;
}

// Represents an arbitrary window of time.
message TimeWindow {
  oneof options {
    // MaintenanceExclusionOptions provides maintenance exclusion related
    // options.
    MaintenanceExclusionOptions maintenance_exclusion_options = 3;
  }

  // The time that the window first starts.
  google.protobuf.Timestamp start_time = 1;

  // The time that the window ends. The end time should take place after the
  // start time.
  google.protobuf.Timestamp end_time = 2;
}

// Represents the Maintenance exclusion option.
message MaintenanceExclusionOptions {
  // Scope of exclusion.
  enum Scope {
    // NO_UPGRADES excludes all upgrades, including patch upgrades and minor
    // upgrades across control planes and nodes. This is the default exclusion
    // behavior.
    NO_UPGRADES = 0;

    // NO_MINOR_UPGRADES excludes all minor upgrades for the cluster, only
    // patches are allowed.
    NO_MINOR_UPGRADES = 1;

    // NO_MINOR_OR_NODE_UPGRADES excludes all minor upgrades for the cluster,
    // and also exclude all node pool upgrades. Only control
    // plane patches are allowed.
    NO_MINOR_OR_NODE_UPGRADES = 2;
  }

  // Scope specifies the upgrade scope which upgrades are blocked by the
  // exclusion.
  Scope scope = 1;
}

// Represents an arbitrary window of time that recurs.
message RecurringTimeWindow {
  // The window of the first recurrence.
  TimeWindow window = 1;

  // An RRULE (https://tools.ietf.org/html/rfc5545#section-3.8.5.3) for how
  // this window reccurs. They go on for the span of time between the start and
  // end time.
  //
  // For example, to have something repeat every weekday, you'd use:
  // `FREQ=WEEKLY;BYDAY=MO,TU,WE,TH,FR`
  //
  // To repeat some window daily (equivalent to the DailyMaintenanceWindow):
  // `FREQ=DAILY`
  //
  // For the first weekend of every month:
  // `FREQ=MONTHLY;BYSETPOS=1;BYDAY=SA,SU`
  //
  // This specifies how frequently the window starts. Eg, if you wanted to have
  // a 9-5 UTC-4 window every weekday, you'd use something like:
  // ```
  // start time = 2019-01-01T09:00:00-0400
  // end time = 2019-01-01T17:00:00-0400
  // recurrence = FREQ=WEEKLY;BYDAY=MO,TU,WE,TH,FR
  // ```
  //
  // Windows can span multiple days. Eg, to make the window encompass every
  // weekend from midnight Saturday till the last minute of Sunday UTC:
  // ```
  // start time = 2019-01-05T00:00:00Z
  // end time = 2019-01-07T23:59:00Z
  // recurrence = FREQ=WEEKLY;BYDAY=SA
  // ```
  //
  // Note the start and end time's specific dates are largely arbitrary except
  // to specify duration of the window and when it first starts.
  // The FREQ values of HOURLY, MINUTELY, and SECONDLY are not supported.
  string recurrence = 2;
}

// Time window specified for daily maintenance operations.
message DailyMaintenanceWindow {
  // Time within the maintenance window to start the maintenance operations.
  // It must be in format "HH:MM", where HH : [00-23] and MM : [00-59] GMT.
  string start_time = 2;

  // Output only. Duration of the time window, automatically chosen to be
  // smallest possible in the given scenario.
  string duration = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// SetNodePoolManagementRequest sets the node management properties of a node
// pool.
message SetNodePoolManagementRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to update.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the node pool to update.
  // This field has been deprecated and replaced by the name field.
  string node_pool_id = 4
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. NodeManagement configuration for the node pool.
  NodeManagement management = 5 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster, node pool id) of the node pool to set
  // management properties. Specified in the format
  // `projects/*/locations/*/clusters/*/nodePools/*`.
  string name = 7;
}

// SetNodePoolSizeRequest sets the size of a node pool.
message SetNodePoolSizeRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to update.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the node pool to update.
  // This field has been deprecated and replaced by the name field.
  string node_pool_id = 4
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The desired node count for the pool.
  int32 node_count = 5 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster, node pool id) of the node pool to set
  // size.
  // Specified in the format `projects/*/locations/*/clusters/*/nodePools/*`.
  string name = 7;
}

// CompleteNodePoolUpgradeRequest sets the name of target node pool to complete
// upgrade.
message CompleteNodePoolUpgradeRequest {
  // The name (project, location, cluster, node pool id) of the node pool to
  // complete upgrade.
  // Specified in the format `projects/*/locations/*/clusters/*/nodePools/*`.
  string name = 1;
}

// RollbackNodePoolUpgradeRequest rollbacks the previously Aborted or Failed
// NodePool upgrade. This will be an no-op if the last upgrade successfully
// completed.
message RollbackNodePoolUpgradeRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to rollback.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the node pool to rollback.
  // This field has been deprecated and replaced by the name field.
  string node_pool_id = 4
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster, node pool id) of the node poll to
  // rollback upgrade.
  // Specified in the format `projects/*/locations/*/clusters/*/nodePools/*`.
  string name = 6;

  // Option for rollback to ignore the PodDisruptionBudget.
  // Default value is false.
  bool respect_pdb = 7;
}

// ListNodePoolsResponse is the result of ListNodePoolsRequest.
message ListNodePoolsResponse {
  // A list of node pools for a cluster.
  repeated NodePool node_pools = 1;
}

// ClusterAutoscaling contains global, per-cluster information
// required by Cluster Autoscaler to automatically adjust
// the size of the cluster and create/delete
// node pools based on the current needs.
message ClusterAutoscaling {
  // Defines possible options for autoscaling_profile field.
  enum AutoscalingProfile {
    // No change to autoscaling configuration.
    PROFILE_UNSPECIFIED = 0;

    // Prioritize optimizing utilization of resources.
    OPTIMIZE_UTILIZATION = 1;

    // Use default (balanced) autoscaling configuration.
    BALANCED = 2;
  }

  // Enables automatic node pool creation and deletion.
  bool enable_node_autoprovisioning = 1;

  // Contains global constraints regarding minimum and maximum
  // amount of resources in the cluster.
  repeated ResourceLimit resource_limits = 2;

  // Defines autoscaling behaviour.
  AutoscalingProfile autoscaling_profile = 3;

  // AutoprovisioningNodePoolDefaults contains defaults for a node pool
  // created by NAP.
  AutoprovisioningNodePoolDefaults autoprovisioning_node_pool_defaults = 4;

  // The list of Google Compute Engine
  // [zones](https://cloud.google.com/compute/docs/zones#available) in which the
  // NodePool's nodes can be created by NAP.
  repeated string autoprovisioning_locations = 5;
}

// AutoprovisioningNodePoolDefaults contains defaults for a node pool created
// by NAP.
message AutoprovisioningNodePoolDefaults {
  // The set of Google API scopes to be made available on all of the
  // node VMs under the "default" service account.
  //
  // The following scopes are recommended, but not required, and by default are
  // not included:
  //
  // * `https://www.googleapis.com/auth/compute` is required for mounting
  // persistent storage on your nodes.
  // * `https://www.googleapis.com/auth/devstorage.read_only` is required for
  // communicating with **gcr.io**
  // (the [Google Container
  // Registry](https://cloud.google.com/container-registry/)).
  //
  // If unspecified, no scopes are added, unless Cloud Logging or Cloud
  // Monitoring are enabled, in which case their required scopes will be added.
  repeated string oauth_scopes = 1;

  // The Google Cloud Platform Service Account to be used by the node VMs.
  // Specify the email address of the Service Account; otherwise, if no Service
  // Account is specified, the "default" service account is used.
  string service_account = 2;

  // Upgrade settings control disruption and speed of the upgrade.
  NodePool.UpgradeSettings upgrade_settings = 3;

  // NodeManagement configuration for this NodePool.
  NodeManagement management = 4;

  // Deprecated. Minimum CPU platform to be used for NAP created node pools.
  // The instance may be scheduled on the specified or newer CPU platform.
  // Applicable values are the friendly names of CPU platforms, such as
  // minCpuPlatform: Intel Haswell or
  // minCpuPlatform: Intel Sandy Bridge. For more
  // information, read [how to specify min CPU
  // platform](https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform).
  // This field is deprecated, min_cpu_platform should be specified using
  // `cloud.google.com/requested-min-cpu-platform` label selector on the pod.
  // To unset the min cpu platform field pass "automatic"
  // as field value.
  string min_cpu_platform = 5 [deprecated = true];

  // Size of the disk attached to each node, specified in GB.
  // The smallest allowed disk size is 10GB.
  //
  // If unspecified, the default disk size is 100GB.
  int32 disk_size_gb = 6;

  // Type of the disk attached to each node (e.g. 'pd-standard', 'pd-ssd' or
  // 'pd-balanced')
  //
  // If unspecified, the default disk type is 'pd-standard'
  string disk_type = 7;

  // Shielded Instance options.
  ShieldedInstanceConfig shielded_instance_config = 8;

  //
  // The Customer Managed Encryption Key used to encrypt the boot disk attached
  // to each node in the node pool. This should be of the form
  // projects/[KEY_PROJECT_ID]/locations/[LOCATION]/keyRings/[RING_NAME]/cryptoKeys/[KEY_NAME].
  // For more information about protecting resources with Cloud KMS Keys please
  // see:
  // https://cloud.google.com/compute/docs/disks/customer-managed-encryption
  string boot_disk_kms_key = 9;

  // The image type to use for NAP created node. Please see
  // https://cloud.google.com/kubernetes-engine/docs/concepts/node-images for
  // available image types.
  string image_type = 10;

  // Enable or disable Kubelet read only port.
  optional bool insecure_kubelet_readonly_port_enabled = 13;
}

// Contains information about amount of some resource in the cluster.
// For memory, value should be in GB.
message ResourceLimit {
  // Resource name "cpu", "memory" or gpu-specific string.
  string resource_type = 1;

  // Minimum amount of the resource in the cluster.
  int64 minimum = 2;

  // Maximum amount of the resource in the cluster.
  int64 maximum = 3;
}

// NodePoolAutoscaling contains information required by cluster autoscaler to
// adjust the size of the node pool to the current cluster usage.
message NodePoolAutoscaling {
  // Location policy specifies how zones are picked when scaling up the
  // nodepool.
  enum LocationPolicy {
    // Not set.
    LOCATION_POLICY_UNSPECIFIED = 0;

    // BALANCED is a best effort policy that aims to balance the sizes of
    // different zones.
    BALANCED = 1;

    // ANY policy picks zones that have the highest capacity available.
    ANY = 2;
  }

  // Is autoscaling enabled for this node pool.
  bool enabled = 1;

  // Minimum number of nodes for one location in the NodePool. Must be >= 1 and
  // <= max_node_count.
  int32 min_node_count = 2;

  // Maximum number of nodes for one location in the NodePool. Must be >=
  // min_node_count. There has to be enough quota to scale up the cluster.
  int32 max_node_count = 3;

  // Can this node pool be deleted automatically.
  bool autoprovisioned = 4;

  // Location policy used when scaling up a nodepool.
  LocationPolicy location_policy = 5;

  // Minimum number of nodes in the node pool. Must be greater than 1 less than
  // total_max_node_count.
  // The total_*_node_count fields are mutually exclusive with the *_node_count
  // fields.
  int32 total_min_node_count = 6;

  // Maximum number of nodes in the node pool. Must be greater than
  // total_min_node_count. There has to be enough quota to scale up the cluster.
  // The total_*_node_count fields are mutually exclusive with the *_node_count
  // fields.
  int32 total_max_node_count = 7;
}

// SetLabelsRequest sets the Google Cloud Platform labels on a Google Container
// Engine cluster, which will in turn set them for Google Compute Engine
// resources used by that cluster
message SetLabelsRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. The labels to set for that cluster.
  map<string, string> resource_labels = 4
      [(google.api.field_behavior) = REQUIRED];

  // Required. The fingerprint of the previous set of labels for this resource,
  // used to detect conflicts. The fingerprint is initially generated by
  // Kubernetes Engine and changes after every request to modify or update
  // labels. You must always provide an up-to-date fingerprint hash when
  // updating or changing labels. Make a `get()` request to the
  // resource to get the latest fingerprint.
  string label_fingerprint = 5 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster name) of the cluster to set labels.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 7;
}

// SetLegacyAbacRequest enables or disables the ABAC authorization mechanism for
// a cluster.
message SetLegacyAbacRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster to update.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Whether ABAC authorization will be enabled in the cluster.
  bool enabled = 4 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster name) of the cluster to set legacy
  // abac. Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 6;
}

// StartIPRotationRequest creates a new IP for the cluster and then performs
// a node upgrade on each node pool to point to the new IP.
message StartIPRotationRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster name) of the cluster to start IP
  // rotation. Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 6;

  // Whether to rotate credentials during IP rotation.
  bool rotate_credentials = 7;
}

// CompleteIPRotationRequest moves the cluster master back into single-IP mode.
message CompleteIPRotationRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster name) of the cluster to complete IP
  // rotation. Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 7;
}

// AcceleratorConfig represents a Hardware Accelerator request.
message AcceleratorConfig {
  // The number of the accelerator cards exposed to an instance.
  int64 accelerator_count = 1;

  // The accelerator type resource name. List of supported accelerators
  // [here](https://cloud.google.com/compute/docs/gpus)
  string accelerator_type = 2;

  // Size of partitions to create on the GPU. Valid values are described in the
  // NVIDIA [mig user
  // guide](https://docs.nvidia.com/datacenter/tesla/mig-user-guide/#partitioning).
  string gpu_partition_size = 3;

  // The number of time-shared GPU resources to expose for each physical GPU.
  int64 max_time_shared_clients_per_gpu = 4 [deprecated = true];

  // The configuration for GPU sharing options.
  optional GPUSharingConfig gpu_sharing_config = 5;

  // The configuration for auto installation of GPU driver.
  optional GPUDriverInstallationConfig gpu_driver_installation_config = 6;
}

// GPUSharingConfig represents the GPU sharing configuration for Hardware
// Accelerators.
message GPUSharingConfig {
  // The type of GPU sharing strategy currently provided.
  enum GPUSharingStrategy {
    // Default value.
    GPU_SHARING_STRATEGY_UNSPECIFIED = 0;

    // GPUs are time-shared between containers.
    TIME_SHARING = 1;

    // GPUs are shared between containers with NVIDIA MPS.
    MPS = 2;
  }

  // The max number of containers that can share a physical GPU.
  int64 max_shared_clients_per_gpu = 1;

  // The type of GPU sharing strategy to enable on the GPU node.
  optional GPUSharingStrategy gpu_sharing_strategy = 2;
}

// GPUDriverInstallationConfig specifies the version of GPU driver to be auto
// installed.
message GPUDriverInstallationConfig {
  // The GPU driver version to install.
  enum GPUDriverVersion {
    // Default value is to not install any GPU driver.
    GPU_DRIVER_VERSION_UNSPECIFIED = 0;

    // Disable GPU driver auto installation and needs manual installation
    INSTALLATION_DISABLED = 1;

    // "Default" GPU driver in COS and Ubuntu.
    DEFAULT = 2;

    // "Latest" GPU driver in COS.
    LATEST = 3;
  }

  // Mode for how the GPU driver is installed.
  optional GPUDriverVersion gpu_driver_version = 1;
}

// ManagedPrometheusConfig defines the configuration for
// Google Cloud Managed Service for Prometheus.
message ManagedPrometheusConfig {
  // Enable Managed Collection.
  bool enabled = 1;
}

// WorkloadMetadataConfig defines the metadata configuration to expose to
// workloads on the node pool.
message WorkloadMetadataConfig {
  // NodeMetadata is the configuration for if and how to expose the node
  // metadata to the workload running on the node.
  enum NodeMetadata {
    // Not set.
    UNSPECIFIED = 0;

    // Prevent workloads not in hostNetwork from accessing certain VM metadata,
    // specifically kube-env, which contains Kubelet credentials, and the
    // instance identity token.
    //
    // Metadata concealment is a temporary security solution available while the
    // bootstrapping process for cluster nodes is being redesigned with
    // significant security improvements.  This feature is scheduled to be
    // deprecated in the future and later removed.
    SECURE = 1;

    // Expose all VM metadata to pods.
    EXPOSE = 2;

    // Run the GKE Metadata Server on this node. The GKE Metadata Server exposes
    // a metadata API to workloads that is compatible with the V1 Compute
    // Metadata APIs exposed by the Compute Engine and App Engine Metadata
    // Servers. This feature can only be enabled if Workload Identity is enabled
    // at the cluster level.
    GKE_METADATA_SERVER = 3;
  }

  // Mode is the configuration for how to expose metadata to workloads running
  // on the node.
  enum Mode {
    // Not set.
    MODE_UNSPECIFIED = 0;

    // Expose all Compute Engine metadata to pods.
    GCE_METADATA = 1;

    // Run the GKE Metadata Server on this node. The GKE Metadata Server exposes
    // a metadata API to workloads that is compatible with the V1 Compute
    // Metadata APIs exposed by the Compute Engine and App Engine Metadata
    // Servers. This feature can only be enabled if Workload Identity is enabled
    // at the cluster level.
    GKE_METADATA = 2;
  }

  // NodeMetadata is the configuration for how to expose metadata to the
  // workloads running on the node.
  NodeMetadata node_metadata = 1 [deprecated = true];

  // Mode is the configuration for how to expose metadata to workloads running
  // on the node pool.
  Mode mode = 2;
}

// SetNetworkPolicyRequest enables/disables network policy for a cluster.
message SetNetworkPolicyRequest {
  // Required. Deprecated. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  // This field has been deprecated and replaced by the name field.
  string project_id = 1
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides. This field has been deprecated and replaced by the name
  // field.
  string zone = 2 [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Deprecated. The name of the cluster.
  // This field has been deprecated and replaced by the name field.
  string cluster_id = 3
      [deprecated = true, (google.api.field_behavior) = REQUIRED];

  // Required. Configuration options for the NetworkPolicy feature.
  NetworkPolicy network_policy = 4 [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster name) of the cluster to set networking
  // policy. Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 6;
}

// SetMaintenancePolicyRequest sets the maintenance policy for a cluster.
message SetMaintenancePolicyRequest {
  // Required. The Google Developers Console [project ID or project
  // number](https://cloud.google.com/resource-manager/docs/creating-managing-projects).
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the Google Compute Engine
  // [zone](https://cloud.google.com/compute/docs/zones#available) in which the
  // cluster resides.
  string zone = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the cluster to update.
  string cluster_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The maintenance policy to be set for the cluster. An empty field
  // clears the existing maintenance policy.
  MaintenancePolicy maintenance_policy = 4
      [(google.api.field_behavior) = REQUIRED];

  // The name (project, location, cluster name) of the cluster to set
  // maintenance policy.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 5;
}

// ListLocationsRequest is used to request the locations that offer GKE.
message ListLocationsRequest {
  // Required. Contains the name of the resource requested.
  // Specified in the format `projects/*`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];
}

// ListLocationsResponse returns the list of all GKE locations and their
// recommendation state.
message ListLocationsResponse {
  // A full list of GKE locations.
  repeated Location locations = 1;

  // Only return ListLocationsResponse that occur after the page_token. This
  // value should be populated from the ListLocationsResponse.next_page_token if
  // that response token was set (which happens when listing more Locations than
  // fit in a single ListLocationsResponse).
  string next_page_token = 2;
}

// Location returns the location name, and if the location is recommended
// for GKE cluster scheduling.
message Location {
  // LocationType is the type of GKE location, regional or zonal.
  enum LocationType {
    // LOCATION_TYPE_UNSPECIFIED means the location type was not determined.
    LOCATION_TYPE_UNSPECIFIED = 0;

    // A GKE Location where Zonal clusters can be created.
    ZONE = 1;

    // A GKE Location where Regional clusters can be created.
    REGION = 2;
  }

  // Contains the type of location this Location is for.
  // Regional or Zonal.
  LocationType type = 1;

  // Contains the name of the resource requested.
  // Specified in the format `projects/*/locations/*`.
  string name = 2;

  // Whether the location is recommended for GKE cluster scheduling.
  bool recommended = 3;
}

// StatusCondition describes why a cluster or a node pool has a certain status
// (e.g., ERROR or DEGRADED).
message StatusCondition {
  // Code for each condition
  enum Code {
    option deprecated = true;

    // UNKNOWN indicates a generic condition.
    UNKNOWN = 0;

    // GCE_STOCKOUT indicates that Google Compute Engine resources are
    // temporarily unavailable.
    GCE_STOCKOUT = 1;

    // GKE_SERVICE_ACCOUNT_DELETED indicates that the user deleted their robot
    // service account.
    GKE_SERVICE_ACCOUNT_DELETED = 2;

    // Google Compute Engine quota was exceeded.
    GCE_QUOTA_EXCEEDED = 3;

    // Cluster state was manually changed by an SRE due to a system logic error.
    SET_BY_OPERATOR = 4;

    // Unable to perform an encrypt operation against the CloudKMS key used for
    // etcd level encryption.
    CLOUD_KMS_KEY_ERROR = 7;

    // Cluster CA is expiring soon.
    // More codes TBA
    CA_EXPIRING = 9;
  }

  // Machine-friendly representation of the condition
  // Deprecated. Use canonical_code instead.
  Code code = 1 [deprecated = true];

  // Human-friendly representation of the condition
  string message = 2;

  // Canonical code of the condition.
  google.rpc.Code canonical_code = 3;
}

// NetworkConfig reports the relative names of network & subnetwork.
message NetworkConfig {
  // Configuration of all network bandwidth tiers
  message ClusterNetworkPerformanceConfig {
    // Node network tier
    enum Tier {
      // Default value
      TIER_UNSPECIFIED = 0;

      // Higher bandwidth, actual values based on VM size.
      TIER_1 = 1;
    }

    // Specifies the total network bandwidth tier for the NodePool.
    optional Tier total_egress_bandwidth_tier = 1;
  }

  // Output only. The relative name of the Google Compute Engine
  // [network][google.container.v1beta1.NetworkConfig.network](https://cloud.google.com/compute/docs/networks-and-firewalls#networks)
  // to which the cluster is connected. Example:
  // projects/my-project/global/networks/my-network
  string network = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The relative name of the Google Compute Engine
  // [subnetwork](https://cloud.google.com/compute/docs/vpc) to which the
  // cluster is connected. Example:
  // projects/my-project/regions/us-central1/subnetworks/my-subnet
  string subnetwork = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether Intra-node visibility is enabled for this cluster.
  // This makes same node pod to pod traffic visible for VPC network.
  bool enable_intra_node_visibility = 5;

  // Whether the cluster disables default in-node sNAT rules. In-node sNAT rules
  // will be disabled when default_snat_status is disabled. When disabled is set
  // to false, default IP masquerade rules will be applied to the nodes to
  // prevent sNAT on cluster internal traffic.
  DefaultSnatStatus default_snat_status = 7;

  // Whether L4ILB Subsetting is enabled for this cluster.
  bool enable_l4ilb_subsetting = 10;

  // The desired datapath provider for this cluster. By default, uses the
  // IPTables-based kube-proxy implementation.
  DatapathProvider datapath_provider = 11;

  // The desired state of IPv6 connectivity to Google Services.
  // By default, no private IPv6 access to or from Google Services (all access
  // will be via IPv4)
  PrivateIPv6GoogleAccess private_ipv6_google_access = 12;

  // DNSConfig contains clusterDNS config for this cluster.
  DNSConfig dns_config = 13;

  // ServiceExternalIPsConfig specifies if services with externalIPs field are
  // blocked or not.
  ServiceExternalIPsConfig service_external_ips_config = 15;

  // GatewayAPIConfig contains the desired config of Gateway API on this
  // cluster.
  GatewayAPIConfig gateway_api_config = 16;

  // Whether multi-networking is enabled for this cluster.
  bool enable_multi_networking = 17;

  // Network bandwidth tier configuration.
  ClusterNetworkPerformanceConfig network_performance_config = 18;

  // Whether FQDN Network Policy is enabled on this cluster.
  optional bool enable_fqdn_network_policy = 19;

  // Specify the details of in-transit encryption.
  optional InTransitEncryptionConfig in_transit_encryption_config = 20;

  // Whether CiliumClusterWideNetworkPolicy is enabled on this cluster.
  optional bool enable_cilium_clusterwide_network_policy = 21;
}

// GatewayAPIConfig contains the desired config of Gateway API on this cluster.
message GatewayAPIConfig {
  // Channel describes if/how Gateway API should be installed and implemented in
  // a cluster.
  enum Channel {
    // Default value.
    CHANNEL_UNSPECIFIED = 0;

    // Gateway API support is disabled
    CHANNEL_DISABLED = 1;

    // Deprecated: use CHANNEL_STANDARD instead.
    // Gateway API support is enabled, experimental CRDs are installed
    CHANNEL_EXPERIMENTAL = 3 [deprecated = true];

    // Gateway API support is enabled, standard CRDs are installed
    CHANNEL_STANDARD = 4;
  }

  // The Gateway API release channel to use for Gateway API.
  Channel channel = 1;
}

// Config to block services with externalIPs field.
message ServiceExternalIPsConfig {
  // Whether Services with ExternalIPs field are allowed or not.
  bool enabled = 1;
}

// ListUsableSubnetworksRequest requests the list of usable subnetworks.
// available to a user for creating clusters.
message ListUsableSubnetworksRequest {
  // Required. The parent project where subnetworks are usable.
  // Specified in the format `projects/*`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Filtering currently only supports equality on the networkProjectId and must
  // be in the form: "networkProjectId=[PROJECTID]", where `networkProjectId`
  // is the project which owns the listed subnetworks. This defaults to the
  // parent project ID.
  string filter = 2;

  // The max number of results per page that should be returned. If the number
  // of available results is larger than `page_size`, a `next_page_token` is
  // returned which can be used to get the next page of results in subsequent
  // requests. Acceptable values are 0 to 500, inclusive. (Default: 500)
  int32 page_size = 3;

  // Specifies a page token to use. Set this to the nextPageToken returned by
  // previous list requests to get the next page of results.
  string page_token = 4;
}

// ListUsableSubnetworksResponse is the response of
// ListUsableSubnetworksRequest.
message ListUsableSubnetworksResponse {
  // A list of usable subnetworks in the specified network project.
  repeated UsableSubnetwork subnetworks = 1;

  // This token allows you to get the next page of results for list requests.
  // If the number of results is larger than `page_size`, use the
  // `next_page_token` as a value for the query parameter `page_token` in the
  // next request. The value will become empty when there are no more pages.
  string next_page_token = 2;
}

// Secondary IP range of a usable subnetwork.
message UsableSubnetworkSecondaryRange {
  // Status shows the current usage of a secondary IP range.
  enum Status {
    // UNKNOWN is the zero value of the Status enum. It's not a valid status.
    UNKNOWN = 0;

    // UNUSED denotes that this range is unclaimed by any cluster.
    UNUSED = 1;

    // IN_USE_SERVICE denotes that this range is claimed by a cluster for
    // services. It cannot be used for other clusters.
    IN_USE_SERVICE = 2;

    // IN_USE_SHAREABLE_POD denotes this range was created by the network admin
    // and is currently claimed by a cluster for pods. It can only be used by
    // other clusters as a pod range.
    IN_USE_SHAREABLE_POD = 3;

    // IN_USE_MANAGED_POD denotes this range was created by GKE and is claimed
    // for pods. It cannot be used for other clusters.
    IN_USE_MANAGED_POD = 4;
  }

  // The name associated with this subnetwork secondary range, used when adding
  // an alias IP range to a VM instance.
  string range_name = 1;

  // The range of IP addresses belonging to this subnetwork secondary range.
  string ip_cidr_range = 2;

  // This field is to determine the status of the secondary range programmably.
  Status status = 3;
}

// UsableSubnetwork resource returns the subnetwork name, its associated network
// and the primary CIDR range.
message UsableSubnetwork {
  // Subnetwork Name.
  // Example: projects/my-project/regions/us-central1/subnetworks/my-subnet
  string subnetwork = 1;

  // Network Name.
  // Example: projects/my-project/global/networks/my-network
  string network = 2;

  // The range of internal addresses that are owned by this subnetwork.
  string ip_cidr_range = 3;

  // Secondary IP ranges.
  repeated UsableSubnetworkSecondaryRange secondary_ip_ranges = 4;

  // A human readable status message representing the reasons for cases where
  // the caller cannot use the secondary ranges under the subnet. For example if
  // the secondary_ip_ranges is empty due to a permission issue, an insufficient
  // permission message will be given by status_message.
  string status_message = 5;
}

// VerticalPodAutoscaling contains global, per-cluster information
// required by Vertical Pod Autoscaler to automatically adjust
// the resources of pods controlled by it.
message VerticalPodAutoscaling {
  // Enables vertical pod autoscaling.
  bool enabled = 1;
}

// DefaultSnatStatus contains the desired state of whether default sNAT should
// be disabled on the cluster.
message DefaultSnatStatus {
  // Disables cluster default sNAT rules.
  bool disabled = 1;
}

// IntraNodeVisibilityConfig contains the desired config of the intra-node
// visibility on this cluster.
message IntraNodeVisibilityConfig {
  // Enables intra node visibility for this cluster.
  bool enabled = 1;
}

// ILBSubsettingConfig contains the desired config of L4 Internal LoadBalancer
// subsetting on this cluster.
message ILBSubsettingConfig {
  // Enables l4 ILB subsetting for this cluster
  bool enabled = 1;
}

// DNSConfig contains the desired set of options for configuring clusterDNS.
message DNSConfig {
  // Provider lists the various in-cluster DNS providers.
  enum Provider {
    // Default value
    PROVIDER_UNSPECIFIED = 0;

    // Use GKE default DNS provider(kube-dns) for DNS resolution.
    PLATFORM_DEFAULT = 1;

    // Use CloudDNS for DNS resolution.
    CLOUD_DNS = 2;

    // Use KubeDNS for DNS resolution.
    KUBE_DNS = 3;
  }

  // DNSScope lists the various scopes of access to cluster DNS records.
  enum DNSScope {
    // Default value, will be inferred as cluster scope.
    DNS_SCOPE_UNSPECIFIED = 0;

    // DNS records are accessible from within the cluster.
    CLUSTER_SCOPE = 1;

    // DNS records are accessible from within the VPC.
    VPC_SCOPE = 2;
  }

  // cluster_dns indicates which in-cluster DNS provider should be used.
  Provider cluster_dns = 1;

  // cluster_dns_scope indicates the scope of access to cluster DNS records.
  DNSScope cluster_dns_scope = 2;

  // cluster_dns_domain is the suffix used for all cluster service records.
  string cluster_dns_domain = 3;

  // Optional. The domain used in Additive VPC scope.
  string additive_vpc_scope_dns_domain = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Constraints applied to pods.
message MaxPodsConstraint {
  // Constraint enforced on the max num of pods per node.
  int64 max_pods_per_node = 1;
}

// Configuration for the use of Kubernetes Service Accounts in GCP IAM
// policies.
message WorkloadIdentityConfig {
  // IAM Identity Namespace to attach all Kubernetes Service Accounts to.
  string identity_namespace = 1 [deprecated = true];

  // The workload pool to attach all Kubernetes service accounts to.
  string workload_pool = 2;

  // identity provider is the third party identity provider.
  string identity_provider = 3;
}

// Configuration for direct-path (via ALTS) with workload identity.
message WorkloadALTSConfig {
  // enable_alts controls whether the alts handshaker should be enabled or not
  // for direct-path.
  //
  // Requires Workload Identity
  // ([workload_pool][google.container.v1beta1.WorkloadIdentityConfig.workload_pool]
  // must be non-empty).
  google.protobuf.BoolValue enable_alts = 1;
}

// Configuration for issuance of mTLS keys and certificates to Kubernetes pods.
message WorkloadCertificates {
  // enable_certificates controls issuance of workload mTLS certificates.
  //
  // If set, the GKE Workload Identity Certificates controller and node agent
  // will be deployed in the cluster, which can then be configured by creating a
  // WorkloadCertificateConfig Custom Resource.
  //
  // Requires Workload Identity
  // ([workload_pool][google.container.v1beta1.WorkloadIdentityConfig.workload_pool]
  // must be non-empty).
  google.protobuf.BoolValue enable_certificates = 1;
}

// Configuration for issuance of mTLS keys and certificates to Kubernetes pods.
message MeshCertificates {
  // enable_certificates controls issuance of workload mTLS certificates.
  //
  // If set, the GKE Workload Identity Certificates controller and node agent
  // will be deployed in the cluster, which can then be configured by creating a
  // WorkloadCertificateConfig Custom Resource.
  //
  // Requires Workload Identity
  // ([workload_pool][google.container.v1alpha1.WorkloadIdentityConfig.workload_pool]
  // must be non-empty).
  google.protobuf.BoolValue enable_certificates = 1;
}

// Configuration of etcd encryption.
message DatabaseEncryption {
  // State of etcd encryption.
  enum State {
    // Should never be set
    UNKNOWN = 0;

    // Secrets in etcd are encrypted.
    ENCRYPTED = 1;

    // Secrets in etcd are stored in plain text (at etcd level) - this is
    // unrelated to Compute Engine level full disk encryption.
    DECRYPTED = 2;
  }

  // Current State of etcd encryption.
  enum CurrentState {
    // Should never be set
    CURRENT_STATE_UNSPECIFIED = 0;

    // Secrets in etcd are encrypted.
    CURRENT_STATE_ENCRYPTED = 7;

    // Secrets in etcd are stored in plain text (at etcd level) - this is
    // unrelated to Compute Engine level full disk encryption.
    CURRENT_STATE_DECRYPTED = 2;

    // Encryption (or re-encryption with a different CloudKMS key)
    // of Secrets is in progress.
    CURRENT_STATE_ENCRYPTION_PENDING = 3;

    // Encryption (or re-encryption with a different CloudKMS key) of Secrets in
    // etcd encountered an error.
    CURRENT_STATE_ENCRYPTION_ERROR = 4;

    // De-crypting Secrets to plain text in etcd is in progress.
    CURRENT_STATE_DECRYPTION_PENDING = 5;

    // De-crypting Secrets to plain text in etcd encountered an error.
    CURRENT_STATE_DECRYPTION_ERROR = 6;
  }

  // OperationError records errors seen from CloudKMS keys
  // encountered during updates to DatabaseEncryption configuration.
  message OperationError {
    // CloudKMS key resource that had the error.
    string key_name = 1;

    // Description of the error seen during the operation.
    string error_message = 2;

    // Time when the CloudKMS error was seen.
    google.protobuf.Timestamp timestamp = 3;
  }

  // Name of CloudKMS key to use for the encryption of secrets in etcd.
  // Ex. projects/my-project/locations/global/keyRings/my-ring/cryptoKeys/my-key
  string key_name = 1;

  // The desired state of etcd encryption.
  State state = 2;

  // Output only. The current state of etcd encryption.
  optional CurrentState current_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Keys in use by the cluster for decrypting
  // existing objects, in addition to the key in `key_name`.
  //
  // Each item is a CloudKMS key resource.
  repeated string decryption_keys = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Records errors seen during DatabaseEncryption update
  // operations.
  repeated OperationError last_operation_errors = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for exporting cluster resource usages.
message ResourceUsageExportConfig {
  // Parameters for using BigQuery as the destination of resource usage export.
  message BigQueryDestination {
    // The ID of a BigQuery Dataset.
    string dataset_id = 1;
  }

  // Parameters for controlling consumption metering.
  message ConsumptionMeteringConfig {
    // Whether to enable consumption metering for this cluster. If enabled, a
    // second BigQuery table will be created to hold resource consumption
    // records.
    bool enabled = 1;
  }

  // Configuration to use BigQuery as usage export destination.
  BigQueryDestination bigquery_destination = 1;

  // Whether to enable network egress metering for this cluster. If enabled, a
  // daemonset will be created in the cluster to meter network egress traffic.
  bool enable_network_egress_metering = 2;

  // Configuration to enable resource consumption metering.
  ConsumptionMeteringConfig consumption_metering_config = 3;
}

// Configuration of Shielded Nodes feature.
message ShieldedNodes {
  // Whether Shielded Nodes features are enabled on all nodes in this cluster.
  bool enabled = 1;
}

// Configuration of gVNIC feature.
message VirtualNIC {
  // Whether gVNIC features are enabled in the node pool.
  bool enabled = 1;
}

// Configuration of Fast Socket feature.
message FastSocket {
  // Whether Fast Socket features are enabled in the node pool.
  bool enabled = 1;
}

// GetOpenIDConfigRequest gets the OIDC discovery document for the
// cluster. See the OpenID Connect Discovery 1.0 specification for details.
message GetOpenIDConfigRequest {
  // The cluster (project, location, cluster name) to get the discovery document
  // for. Specified in the format `projects/*/locations/*/clusters/*`.
  string parent = 1;
}

// GetOpenIDConfigResponse is an OIDC discovery document for the cluster.
// See the OpenID Connect Discovery 1.0 specification for details.
message GetOpenIDConfigResponse {
  // OIDC Issuer.
  string issuer = 1;

  // JSON Web Key uri.
  string jwks_uri = 2;

  // Supported response types.
  repeated string response_types_supported = 3;

  // Supported subject types.
  repeated string subject_types_supported = 4;

  // supported ID Token signing Algorithms.
  repeated string id_token_signing_alg_values_supported = 5;

  // Supported claims.
  repeated string claims_supported = 6;

  // Supported grant types.
  repeated string grant_types = 7;
}

// GetJSONWebKeysRequest gets the public component of the keys used by the
// cluster to sign token requests. This will be the jwks_uri for the discover
// document returned by getOpenIDConfig. See the OpenID Connect
// Discovery 1.0 specification for details.
message GetJSONWebKeysRequest {
  // The cluster (project, location, cluster name) to get keys for. Specified in
  // the format `projects/*/locations/*/clusters/*`.
  string parent = 1;
}

// Jwk is a JSON Web Key as specified in RFC 7517
message Jwk {
  // Key Type.
  string kty = 1;

  // Algorithm.
  string alg = 2;

  // Permitted uses for the public keys.
  string use = 3;

  // Key ID.
  string kid = 4;

  // Used for RSA keys.
  string n = 5;

  // Used for RSA keys.
  string e = 6;

  // Used for ECDSA keys.
  string x = 7;

  // Used for ECDSA keys.
  string y = 8;

  // Used for ECDSA keys.
  string crv = 9;
}

// GetJSONWebKeysResponse is a valid JSON Web Key Set as specififed in rfc 7517
message GetJSONWebKeysResponse {
  // The public component of the keys used by the cluster to sign token
  // requests.
  repeated Jwk keys = 1;
}

// CheckAutopilotCompatibilityRequest requests getting the blockers for the
// given operation in the cluster.
message CheckAutopilotCompatibilityRequest {
  // The name (project, location, cluster) of the cluster to retrieve.
  // Specified in the format `projects/*/locations/*/clusters/*`.
  string name = 1;
}

// AutopilotCompatibilityIssue contains information about a specific
// compatibility issue with Autopilot mode.
message AutopilotCompatibilityIssue {
  // The type of the reported issue.
  enum IssueType {
    // Default value, should not be used.
    UNSPECIFIED = 0;

    // Indicates that the issue is a known incompatibility between the
    // cluster and Autopilot mode.
    INCOMPATIBILITY = 1;

    // Indicates the issue is an incompatibility if customers take no further
    // action to resolve.
    ADDITIONAL_CONFIG_REQUIRED = 2;

    // Indicates the issue is not an incompatibility, but depending on the
    // workloads business logic, there is a potential that they won't work on
    // Autopilot.
    PASSED_WITH_OPTIONAL_CONFIG = 3;
  }

  // The last time when this issue was observed.
  google.protobuf.Timestamp last_observation = 1;

  // The constraint type of the issue.
  string constraint_type = 2;

  // The incompatibility type of this issue.
  IssueType incompatibility_type = 3;

  // The name of the resources which are subject to this issue.
  repeated string subjects = 4;

  // A URL to a public documnetation, which addresses resolving this issue.
  string documentation_url = 5;

  // The description of the issue.
  string description = 6;
}

// CheckAutopilotCompatibilityResponse has a list of compatibility issues.
message CheckAutopilotCompatibilityResponse {
  // The list of issues for the given operation.
  repeated AutopilotCompatibilityIssue issues = 1;

  // The summary of the autopilot compatibility response.
  string summary = 2;
}

// ReleaseChannel indicates which release channel a cluster is
// subscribed to. Release channels are arranged in order of risk.
//
// When a cluster is subscribed to a release channel, Google maintains
// both the master version and the node version. Node auto-upgrade
// defaults to true and cannot be disabled.
message ReleaseChannel {
  // Possible values for 'channel'.
  enum Channel {
    // No channel specified.
    UNSPECIFIED = 0;

    // RAPID channel is offered on an early access basis for customers who want
    // to test new releases.
    //
    // WARNING: Versions available in the RAPID Channel may be subject to
    // unresolved issues with no known workaround and are not subject to any
    // SLAs.
    RAPID = 1;

    // Clusters subscribed to REGULAR receive versions that are considered GA
    // quality. REGULAR is intended for production users who want to take
    // advantage of new features.
    REGULAR = 2;

    // Clusters subscribed to STABLE receive versions that are known to be
    // stable and reliable in production.
    STABLE = 3;

    // Clusters subscribed to EXTENDED receive extended support and availability
    // for versions which are known to be stable and reliable in production.
    EXTENDED = 4;
  }

  // channel specifies which release channel the cluster is subscribed to.
  Channel channel = 1;
}

// Configuration for fine-grained cost management feature.
message CostManagementConfig {
  // Whether the feature is enabled or not.
  bool enabled = 1;
}

// Configuration for Cloud TPU.
message TpuConfig {
  // Whether Cloud TPU integration is enabled or not.
  bool enabled = 1;

  // Whether to use service networking for Cloud TPU or not.
  bool use_service_networking = 2;

  // IPv4 CIDR block reserved for Cloud TPU in the VPC.
  string ipv4_cidr_block = 3;
}

// PrivateIPv6GoogleAccess controls whether and how the pods can communicate
// with Google Services through gRPC over IPv6.
enum PrivateIPv6GoogleAccess {
  // Default value. Same as DISABLED
  PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED = 0;

  // No private access to or from Google Services
  PRIVATE_IPV6_GOOGLE_ACCESS_DISABLED = 1;

  // Enables private IPv6 access to Google Services from GKE
  PRIVATE_IPV6_GOOGLE_ACCESS_TO_GOOGLE = 2;

  // Enables private IPv6 access to and from Google Services
  PRIVATE_IPV6_GOOGLE_ACCESS_BIDIRECTIONAL = 3;
}

// Master is the configuration for components on master.
message Master {}

// AutopilotConversionStatus represents conversion status.
message AutopilotConversionStatus {
  // The current state of the conversion.
  enum State {
    // STATE_UNSPECIFIED indicates the state is unspecified.
    STATE_UNSPECIFIED = 0;

    // DONE indicates the conversion has been completed. Old node pools will
    // continue being deleted in the background.
    DONE = 5;
  }

  // Output only. The current state of the conversion.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Autopilot is the configuration for Autopilot settings on the cluster.
message Autopilot {
  // Enable Autopilot
  bool enabled = 1;

  // Workload policy configuration for Autopilot.
  WorkloadPolicyConfig workload_policy_config = 2;

  // Output only. ConversionStatus shows conversion status.
  AutopilotConversionStatus conversion_status = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// WorkloadPolicyConfig is the configuration of workload policy for autopilot
// clusters.
message WorkloadPolicyConfig {
  // If true, workloads can use NET_ADMIN capability.
  optional bool allow_net_admin = 1;
}

// NotificationConfig is the configuration of notifications.
message NotificationConfig {
  // Types of notifications currently supported. Can be used to filter what
  // notifications are sent.
  enum EventType {
    // Not set, will be ignored.
    EVENT_TYPE_UNSPECIFIED = 0;

    // Corresponds with UpgradeAvailableEvent.
    UPGRADE_AVAILABLE_EVENT = 1;

    // Corresponds with UpgradeEvent.
    UPGRADE_EVENT = 2;

    // Corresponds with SecurityBulletinEvent.
    SECURITY_BULLETIN_EVENT = 3;
  }

  // Pub/Sub specific notification config.
  message PubSub {
    // Enable notifications for Pub/Sub.
    bool enabled = 1;

    // The desired Pub/Sub topic to which notifications will be
    // sent by GKE. Format is `projects/{project}/topics/{topic}`.
    string topic = 2 [
      (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
    ];

    // Allows filtering to one or more specific event types. If no filter is
    // specified, or if a filter is specified with no event types, all event
    // types will be sent
    Filter filter = 3;
  }

  // Allows filtering to one or more specific event types. If event types are
  // present, those and only those event types will be transmitted to the
  // cluster. Other types will be skipped. If no filter is specified, or no
  // event types are present, all event types will be sent
  message Filter {
    // Event types to allowlist.
    repeated EventType event_type = 1;
  }

  // Notification config for Pub/Sub.
  PubSub pubsub = 1;
}

// ConfidentialNodes is configuration for the confidential nodes feature, which
// makes nodes run on confidential VMs.
message ConfidentialNodes {
  // Whether Confidential Nodes feature is enabled.
  bool enabled = 1;
}

// UpgradeResourceType is the resource type that is upgrading. It is used
// in upgrade notifications.
enum UpgradeResourceType {
  // Default value. This shouldn't be used.
  UPGRADE_RESOURCE_TYPE_UNSPECIFIED = 0;

  // Master / control plane
  MASTER = 1;

  // Node pool
  NODE_POOL = 2;
}

// UpgradeEvent is a notification sent to customers by the cluster server when
// a resource is upgrading.
message UpgradeEvent {
  // The resource type that is upgrading.
  UpgradeResourceType resource_type = 1;

  // The operation associated with this upgrade.
  string operation = 2;

  // The time when the operation was started.
  google.protobuf.Timestamp operation_start_time = 3;

  // The current version before the upgrade.
  string current_version = 4;

  // The target version for the upgrade.
  string target_version = 5;

  // Optional relative path to the resource. For example in node pool upgrades,
  // the relative path of the node pool.
  string resource = 6;
}

// UpgradeAvailableEvent is a notification sent to customers when a new
// available version is released.
message UpgradeAvailableEvent {
  // The release version available for upgrade.
  string version = 1;

  // The resource type of the release version.
  UpgradeResourceType resource_type = 2;

  // The release channel of the version. If empty, it means a non-channel
  // release.
  ReleaseChannel release_channel = 3;

  // Optional relative path to the resource. For example, the relative path of
  // the node pool.
  string resource = 4;

  // Windows node versions info.
  WindowsVersions windows_versions = 5;
}

// SecurityBulletinEvent is a notification sent to customers when a security
// bulletin has been posted that they are vulnerable to.
message SecurityBulletinEvent {
  // The resource type (node/control plane) that has the vulnerability. Multiple
  // notifications (1 notification per resource type) will be sent for a
  // vulnerability that affects > 1 resource type.
  string resource_type_affected = 1;

  // The ID of the bulletin corresponding to the vulnerability.
  string bulletin_id = 2;

  // The CVEs associated with this bulletin.
  repeated string cve_ids = 3;

  // The severity of this bulletin as it relates to GKE.
  string severity = 4;

  // The URI link to the bulletin on the website for more information.
  string bulletin_uri = 5;

  // A brief description of the bulletin. See the bulletin pointed to by the
  // bulletin_uri field for an expanded description.
  string brief_description = 6;

  // The GKE minor versions affected by this vulnerability.
  repeated string affected_supported_minors = 7;

  // The GKE versions where this vulnerability is patched.
  repeated string patched_versions = 8;

  // This represents a version selected from the patched_versions field that
  // the cluster receiving this notification should most likely want to upgrade
  // to based on its current version. Note that if this notification is being
  // received by a given cluster, it means that this version is currently
  // available as an upgrade target in that cluster's location.
  string suggested_upgrade_target = 9;

  // If this field is specified, it means there are manual steps that the user
  // must take to make their clusters safe.
  bool manual_steps_required = 10;
}

// IdentityServiceConfig is configuration for Identity Service which allows
// customers to use external identity providers with the K8S API
message IdentityServiceConfig {
  // Whether to enable the Identity Service component
  bool enabled = 1;
}

// Strategy used for node pool update.
enum NodePoolUpdateStrategy {
  // Default value if unset. GKE internally defaults the update strategy to
  // SURGE for unspecified strategies.
  NODE_POOL_UPDATE_STRATEGY_UNSPECIFIED = 0;

  // blue-green upgrade.
  BLUE_GREEN = 2;

  // SURGE is the traditional way of upgrading a node pool.
  // max_surge and max_unavailable determines the level of upgrade parallelism.
  SURGE = 3;
}

// LoggingConfig is cluster logging configuration.
message LoggingConfig {
  // Logging components configuration
  LoggingComponentConfig component_config = 1;
}

// LoggingComponentConfig is cluster logging component configuration.
message LoggingComponentConfig {
  // GKE components exposing logs
  enum Component {
    // Default value. This shouldn't be used.
    COMPONENT_UNSPECIFIED = 0;

    // system components
    SYSTEM_COMPONENTS = 1;

    // workloads
    WORKLOADS = 2;

    // kube-apiserver
    APISERVER = 3;

    // kube-scheduler
    SCHEDULER = 4;

    // kube-controller-manager
    CONTROLLER_MANAGER = 5;
  }

  // Select components to collect logs. An empty set would disable all logging.
  repeated Component enable_components = 1;
}

// RayClusterLoggingConfig specifies logging configuration for Ray clusters.
message RayClusterLoggingConfig {
  // Enable log collection for Ray clusters.
  bool enabled = 1;
}

// MonitoringConfig is cluster monitoring configuration.
message MonitoringConfig {
  // Monitoring components configuration
  MonitoringComponentConfig component_config = 1;

  // Enable Google Cloud Managed Service for Prometheus
  // in the cluster.
  ManagedPrometheusConfig managed_prometheus_config = 2;

  // Configuration of Advanced Datapath Observability features.
  AdvancedDatapathObservabilityConfig advanced_datapath_observability_config =
      3;
}

// AdvancedDatapathObservabilityConfig specifies configuration of observability
// features of advanced datapath.
message AdvancedDatapathObservabilityConfig {
  // Supported Relay modes
  enum RelayMode {
    // Default value. This shouldn't be used.
    RELAY_MODE_UNSPECIFIED = 0;

    // disabled
    DISABLED = 1;

    // exposed via internal load balancer
    INTERNAL_VPC_LB = 3;

    // exposed via external load balancer
    EXTERNAL_LB = 4;
  }

  // Expose flow metrics on nodes
  bool enable_metrics = 1;

  // Method used to make Relay available
  RelayMode relay_mode = 2;

  // Enable Relay component
  optional bool enable_relay = 3;
}

// RayClusterMonitoringConfig specifies monitoring configuration for Ray
// clusters.
message RayClusterMonitoringConfig {
  // Enable metrics collection for Ray clusters.
  bool enabled = 1;
}

// NodePoolLoggingConfig specifies logging configuration for nodepools.
message NodePoolLoggingConfig {
  // Logging variant configuration.
  LoggingVariantConfig variant_config = 1;
}

// LoggingVariantConfig specifies the behaviour of the logging component.
message LoggingVariantConfig {
  // Logging component variants.
  enum Variant {
    // Default value. This shouldn't be used.
    VARIANT_UNSPECIFIED = 0;

    // default logging variant.
    DEFAULT = 1;

    // maximum logging throughput variant.
    MAX_THROUGHPUT = 2;
  }

  // Logging variant deployed on nodes.
  Variant variant = 1;
}

// MonitoringComponentConfig is cluster monitoring component configuration.
message MonitoringComponentConfig {
  // GKE components exposing metrics
  enum Component {
    // Default value. This shouldn't be used.
    COMPONENT_UNSPECIFIED = 0;

    // system components
    SYSTEM_COMPONENTS = 1;

    // Deprecated: Use Google Cloud Managed Service for Prometheus.
    WORKLOADS = 2 [deprecated = true];

    // kube-apiserver
    APISERVER = 3;

    // kube-scheduler
    SCHEDULER = 4;

    // kube-controller-manager
    CONTROLLER_MANAGER = 5;

    // Storage
    STORAGE = 7;

    // Horizontal Pod Autoscaling
    HPA = 8;

    // Pod
    POD = 9;

    // DaemonSet
    DAEMONSET = 10;

    // Deployment
    DEPLOYMENT = 11;

    // Statefulset
    STATEFULSET = 12;

    // CADVISOR
    CADVISOR = 13;

    // KUBELET
    KUBELET = 14;

    // NVIDIA Data Center GPU Manager (DCGM)
    DCGM = 15;
  }

  // Select components to collect metrics. An empty set would disable all
  // monitoring.
  repeated Component enable_components = 1;
}

// The datapath provider selects the implementation of the Kubernetes networking
// model for service resolution and network policy enforcement.
enum DatapathProvider {
  // Default value.
  DATAPATH_PROVIDER_UNSPECIFIED = 0;

  // Use the IPTables implementation based on kube-proxy.
  LEGACY_DATAPATH = 1;

  // Use the eBPF based GKE Dataplane V2 with additional features. See the [GKE
  // Dataplane V2
  // documentation](https://cloud.google.com/kubernetes-engine/docs/how-to/dataplane-v2)
  // for more.
  ADVANCED_DATAPATH = 2;
}

// Fleet is the fleet configuration for the cluster.
message Fleet {
  // The Fleet host project(project ID or project number) where this cluster
  // will be registered to. This field cannot be changed after the cluster has
  // been registered.
  string project = 1;

  // Output only. The full resource name of the registered fleet membership of
  // the cluster, in the format
  // `//gkehub.googleapis.com/projects/*/locations/*/memberships/*`.
  string membership = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether the cluster has been registered through the fleet
  // API.
  bool pre_registered = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Possible values for IP stack type
enum StackType {
  // By default, the clusters will be IPV4 only
  STACK_TYPE_UNSPECIFIED = 0;

  // The value used if the cluster is a IPV4 only
  IPV4 = 1;

  // The value used if the cluster is a dual stack cluster
  IPV4_IPV6 = 2;
}

// A map of resource manager tag keys and values to be attached to the nodes
// for managing Compute Engine firewalls using Network Firewall Policies.
// Tags must be according to specifications in
// https://cloud.google.com/vpc/docs/tags-firewalls-overview#specifications.
// A maximum of 5 tag key-value pairs can be specified.
// Existing tags will be replaced with new values.
message ResourceManagerTags {
  // Tags must be in one of the following formats ([KEY]=[VALUE])
  // 1. `tagKeys/{tag_key_id}=tagValues/{tag_value_id}`
  // 2. `{org_id}/{tag_key_name}={tag_value_name}`
  // 3. `{project_id}/{tag_key_name}={tag_value_name}`
  map<string, string> tags = 1;
}

// EnterpriseConfig is the cluster enterprise configuration.
message EnterpriseConfig {
  // Premium tiers for GKE Cluster.
  enum ClusterTier {
    // CLUSTER_TIER_UNSPECIFIED is when cluster_tier is not set.
    CLUSTER_TIER_UNSPECIFIED = 0;

    // STANDARD indicates a standard GKE cluster.
    STANDARD = 1;

    // ENTERPRISE indicates a GKE Enterprise cluster.
    ENTERPRISE = 2;
  }

  // Output only. cluster_tier specifies the premium tier of the cluster.
  ClusterTier cluster_tier = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// SecretManagerConfig is config for secret manager enablement.
message SecretManagerConfig {
  // Whether the cluster is configured to use secret manager CSI component.
  optional bool enabled = 1;
}

// SecondaryBootDisk represents a persistent disk attached to a node
// with special configurations based on its mode.
message SecondaryBootDisk {
  // Mode specifies how the secondary boot disk will be used.
  // This triggers mode-specified logic in the control plane.
  enum Mode {
    // MODE_UNSPECIFIED is when mode is not set.
    MODE_UNSPECIFIED = 0;

    // CONTAINER_IMAGE_CACHE is for using the secondary boot disk as
    // a container image cache.
    CONTAINER_IMAGE_CACHE = 1;
  }

  // Disk mode (container image cache, etc.)
  Mode mode = 1;

  // Fully-qualified resource ID for an existing disk image.
  string disk_image = 2;
}

// Options for in-transit encryption.
enum InTransitEncryptionConfig {
  // Unspecified, will be inferred as default -
  // IN_TRANSIT_ENCRYPTION_UNSPECIFIED.
  IN_TRANSIT_ENCRYPTION_CONFIG_UNSPECIFIED = 0;

  // In-transit encryption is disabled.
  IN_TRANSIT_ENCRYPTION_DISABLED = 1;

  // Data in-transit is encrypted using inter-node transparent encryption.
  IN_TRANSIT_ENCRYPTION_INTER_NODE_TRANSPARENT = 2;
}

// SecondaryBootDiskUpdateStrategy is a placeholder which will be extended
// in the future to define different options for updating secondary boot disks.
message SecondaryBootDiskUpdateStrategy {}
