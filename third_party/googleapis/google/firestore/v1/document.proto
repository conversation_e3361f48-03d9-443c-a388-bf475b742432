// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.firestore.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/type/latlng.proto";

option csharp_namespace = "Google.Cloud.Firestore.V1";
option go_package = "cloud.google.com/go/firestore/apiv1/firestorepb;firestorepb";
option java_multiple_files = true;
option java_outer_classname = "DocumentProto";
option java_package = "com.google.firestore.v1";
option objc_class_prefix = "GCFS";
option php_namespace = "Google\\Cloud\\Firestore\\V1";
option ruby_package = "Google::Cloud::Firestore::V1";

// A Firestore document.
//
// Must not exceed 1 MiB - 4 bytes.
message Document {
  // The resource name of the document, for example
  // `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
  string name = 1;

  // The document's fields.
  //
  // The map keys represent field names.
  //
  // Field names matching the regular expression `__.*__` are reserved. Reserved
  // field names are forbidden except in certain documented contexts. The field
  // names, represented as UTF-8, must not exceed 1,500 bytes and cannot be
  // empty.
  //
  // Field paths may be used in other contexts to refer to structured fields
  // defined here. For `map_value`, the field path is represented by a
  // dot-delimited (`.`) string of segments. Each segment is either a simple
  // field name (defined below) or a quoted field name. For example, the
  // structured field `"foo" : { map_value: { "x&y" : { string_value: "hello"
  // }}}` would be represented by the field path `` foo.`x&y` ``.
  //
  // A simple field name contains only characters `a` to `z`, `A` to `Z`,
  // `0` to `9`, or `_`, and must not start with `0` to `9`. For example,
  // `foo_bar_17`.
  //
  // A quoted field name starts and ends with `` ` `` and
  // may contain any character. Some characters, including `` ` ``, must be
  // escaped using a `\`. For example, `` `x&y` `` represents `x&y` and
  // `` `bak\`tik` `` represents `` bak`tik ``.
  map<string, Value> fields = 2;

  // Output only. The time at which the document was created.
  //
  // This value increases monotonically when a document is deleted then
  // recreated. It can also be compared to values from other documents and
  // the `read_time` of a query.
  google.protobuf.Timestamp create_time = 3;

  // Output only. The time at which the document was last changed.
  //
  // This value is initially set to the `create_time` then increases
  // monotonically with each change to the document. It can also be
  // compared to values from other documents and the `read_time` of a query.
  google.protobuf.Timestamp update_time = 4;
}

// A message that can hold any of the supported value types.
message Value {
  // Must have a value set.
  oneof value_type {
    // A null value.
    google.protobuf.NullValue null_value = 11;

    // A boolean value.
    bool boolean_value = 1;

    // An integer value.
    int64 integer_value = 2;

    // A double value.
    double double_value = 3;

    // A timestamp value.
    //
    // Precise only to microseconds. When stored, any additional precision is
    // rounded down.
    google.protobuf.Timestamp timestamp_value = 10;

    // A string value.
    //
    // The string, represented as UTF-8, must not exceed 1 MiB - 89 bytes.
    // Only the first 1,500 bytes of the UTF-8 representation are considered by
    // queries.
    string string_value = 17;

    // A bytes value.
    //
    // Must not exceed 1 MiB - 89 bytes.
    // Only the first 1,500 bytes are considered by queries.
    bytes bytes_value = 18;

    // A reference to a document. For example:
    // `projects/{project_id}/databases/{database_id}/documents/{document_path}`.
    string reference_value = 5;

    // A geo point value representing a point on the surface of Earth.
    google.type.LatLng geo_point_value = 8;

    // An array value.
    //
    // Cannot directly contain another array value, though can contain a
    // map which contains another array.
    ArrayValue array_value = 9;

    // A map value.
    MapValue map_value = 6;
  }
}

// An array value.
message ArrayValue {
  // Values in the array.
  repeated Value values = 1;
}

// A map value.
message MapValue {
  // The map's fields.
  //
  // The map keys represent field names. Field names matching the regular
  // expression `__.*__` are reserved. Reserved field names are forbidden except
  // in certain documented contexts. The map keys, represented as UTF-8, must
  // not exceed 1,500 bytes and cannot be empty.
  map<string, Value> fields = 1;
}
