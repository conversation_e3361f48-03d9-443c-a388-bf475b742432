# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "publish_proto",
    srcs = [
        "resources.proto",
        "rpcmessages.proto",
        "streetview_publish.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "publish_proto_with_info",
    deps = [
        ":publish_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "publish_java_proto",
    deps = [":publish_proto"],
)

java_grpc_library(
    name = "publish_java_grpc",
    srcs = [":publish_proto"],
    deps = [":publish_java_proto"],
)

java_gapic_library(
    name = "publish_java_gapic",
    srcs = [":publish_proto_with_info"],
    gapic_yaml = "streetview_publish_gapic.yaml",
    grpc_service_config = "streetview_publish_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "streetviewpublish.yaml",
    test_deps = [
        ":publish_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":publish_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "publish_java_gapic_test_suite",
    test_classes = [
        "com.google.streetview.publish.v1.StreetViewPublishServiceClientHttpJsonTest",
        "com.google.streetview.publish.v1.StreetViewPublishServiceClientTest",
    ],
    runtime_deps = [":publish_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-streetview-publish-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":publish_java_gapic",
        ":publish_java_grpc",
        ":publish_java_proto",
        ":publish_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "publish_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/streetview/publish/apiv1/publishpb",
    protos = [":publish_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "publish_go_gapic",
    srcs = [":publish_proto_with_info"],
    grpc_service_config = "streetview_publish_grpc_service_config.json",
    importpath = "cloud.google.com/go/streetview/publish/apiv1;publish",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "streetviewpublish.yaml",
    transport = "grpc+rest",
    deps = [
        ":publish_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-streetview-publish-v1-go",
    deps = [
        ":publish_go_gapic",
        ":publish_go_gapic_srcjar-metadata.srcjar",
        ":publish_go_gapic_srcjar-snippets.srcjar",
        ":publish_go_gapic_srcjar-test.srcjar",
        ":publish_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "publish_py_gapic",
    srcs = [":publish_proto"],
    grpc_service_config = "streetview_publish_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "streetviewpublish.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "publish_py_gapic_test",
    srcs = [
        "publish_py_gapic_pytest.py",
        "publish_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":publish_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "streetview-publish-v1-py",
    deps = [
        ":publish_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "publish_php_proto",
    deps = [":publish_proto"],
)

php_gapic_library(
    name = "publish_php_gapic",
    srcs = [":publish_proto_with_info"],
    grpc_service_config = "streetview_publish_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "streetviewpublish.yaml",
    transport = "grpc+rest",
    deps = [
        ":publish_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-streetview-publish-v1-php",
    deps = [
        ":publish_php_gapic",
        ":publish_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "publish_nodejs_gapic",
    package_name = "@google-cloud/publish",
    src = ":publish_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "streetview_publish_grpc_service_config.json",
    package = "google.streetview.publish.v1",
    rest_numeric_enums = True,
    service_yaml = "streetviewpublish.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "streetview-publish-v1-nodejs",
    deps = [
        ":publish_nodejs_gapic",
        ":publish_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "publish_ruby_proto",
    deps = [":publish_proto"],
)

ruby_grpc_library(
    name = "publish_ruby_grpc",
    srcs = [":publish_proto"],
    deps = [":publish_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "publish_ruby_gapic",
    srcs = [":publish_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-publish-v1"],
    grpc_service_config = "streetview_publish_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "streetviewpublish.yaml",
    transport = "grpc+rest",
    deps = [
        ":publish_ruby_grpc",
        ":publish_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-streetview-publish-v1-ruby",
    deps = [
        ":publish_ruby_gapic",
        ":publish_ruby_grpc",
        ":publish_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "publish_csharp_proto",
    extra_opts = [],
    deps = [":publish_proto"],
)

csharp_grpc_library(
    name = "publish_csharp_grpc",
    srcs = [":publish_proto"],
    deps = [":publish_csharp_proto"],
)

csharp_gapic_library(
    name = "publish_csharp_gapic",
    srcs = [":publish_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "streetview_publish_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "streetviewpublish.yaml",
    transport = "grpc+rest",
    deps = [
        ":publish_csharp_grpc",
        ":publish_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-streetview-publish-v1-csharp",
    deps = [
        ":publish_csharp_gapic",
        ":publish_csharp_grpc",
        ":publish_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "publish_cc_proto",
    deps = [":publish_proto"],
)

cc_grpc_library(
    name = "publish_cc_grpc",
    srcs = [":publish_proto"],
    grpc_only = True,
    deps = [":publish_cc_proto"],
)
