// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.cloud;

option csharp_namespace = "Google.Analytics.Cloud";
option go_package = "google.golang.org/genproto/googleapis/analytics/cloud;cloud";

// ExportStatusLog is used in a log entry to represent the status of a given
// BigQuery export job from Google Analytics. Typically, it is sent at the
// beginning of the day to indicate the completion of the previous day's export.
message ExportStatusLog {
  // The status of the export.
  enum Status {
    // The export status is unknown.
    UNKNOWN = 0;

    // The export completed and data is complete.
    COMPLETE = 1;

    // The export completed but data is incomplete.
    INCOMPLETE = 2;

    // The export failed.
    FAILED = 3;
  }

  // The date of the export. Usually set to the collection date (YYYYMMDD).
  string export_date = 1;

  // The status of the export.
  Status status = 2;

  // The number of events exported, may include events not eligible for SLA.
  int64 event_count = 3;

  // Additional information associated with the status.
  string message = 4;
}
