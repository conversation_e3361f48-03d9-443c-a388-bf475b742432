# This file was automatically generated by BuildFileGenerator

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "cloud_proto",
    srcs = [
        "bigquery_export_platform_log.proto",
    ],
    deps = [
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "cloud_java_proto",
    deps = [":cloud_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-analytics-cloud-java",
    deps = [
        ":cloud_java_proto",
        ":cloud_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_proto_library",
)

go_proto_library(
    name = "cloud_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/analytics/cloud",
    protos = [":cloud_proto"],
    deps = [
    ],
)

go_gapic_assembly_pkg(
    name = "google-analytics-cloud-go",
    deps = [
        ":cloud_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "cloud_moved_proto",
    srcs = [":cloud_proto"],
    deps = [
    ],
)

py_proto_library(
    name = "cloud_py_proto",
    deps = [":cloud_moved_proto"],
)

py_grpc_library(
    name = "cloud_py_grpc",
    srcs = [":cloud_moved_proto"],
    deps = [":cloud_py_proto"],
)

py_gapic_library(
    name = "cloud_py_gapic",
    srcs = [":cloud_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-analytics-cloud-py",
    deps = [
        ":cloud_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "cloud_php_proto",
    deps = [":cloud_proto"],
)

php_gapic_assembly_pkg(
    name = "google-analytics-cloud-php",
    deps = [
        ":cloud_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "cloud_ruby_proto",
    deps = [":cloud_proto"],
)

ruby_grpc_library(
    name = "cloud_ruby_grpc",
    srcs = [":cloud_proto"],
    deps = [":cloud_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "cloud_csharp_proto",
    deps = [":cloud_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-analytics-cloud-csharp",
    package_name = "Google.Analytics.Cloud",
    generate_nongapic_package = True,
    deps = [
        ":cloud_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "cloud_cc_proto",
    deps = [":cloud_proto"],
)

cc_grpc_library(
    name = "cloud_cc_grpc",
    srcs = [":cloud_proto"],
    grpc_only = True,
    deps = [":cloud_cc_proto"],
)
