// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.admin.v1beta;

import "google/analytics/admin/v1beta/access_report.proto";
import "google/analytics/admin/v1beta/resources.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/analytics/admin/apiv1beta/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "AnalyticsAdminProto";
option java_package = "com.google.analytics.admin.v1beta";

// Service Interface for the Analytics Admin API (GA4).
service AnalyticsAdminService {
  option (google.api.default_host) = "analyticsadmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/analytics.edit,"
      "https://www.googleapis.com/auth/analytics.readonly";

  // Lookup for a single Account.
  rpc GetAccount(GetAccountRequest) returns (Account) {
    option (google.api.http) = {
      get: "/v1beta/{name=accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns all accounts accessible by the caller.
  //
  // Note that these accounts might not currently have GA4 properties.
  // Soft-deleted (ie: "trashed") accounts are excluded by default.
  // Returns an empty list if no relevant accounts are found.
  rpc ListAccounts(ListAccountsRequest) returns (ListAccountsResponse) {
    option (google.api.http) = {
      get: "/v1beta/accounts"
    };
  }

  // Marks target Account as soft-deleted (ie: "trashed") and returns it.
  //
  // This API does not have a method to restore soft-deleted accounts.
  // However, they can be restored using the Trash Can UI.
  //
  // If the accounts are not restored before the expiration time, the account
  // and all child resources (eg: Properties, GoogleAdsLinks, Streams,
  // AccessBindings) will be permanently purged.
  // https://support.google.com/analytics/answer/6154772
  //
  // Returns an error if the target is not found.
  rpc DeleteAccount(DeleteAccountRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an account.
  rpc UpdateAccount(UpdateAccountRequest) returns (Account) {
    option (google.api.http) = {
      patch: "/v1beta/{account.name=accounts/*}"
      body: "account"
    };
    option (google.api.method_signature) = "account,update_mask";
  }

  // Requests a ticket for creating an account.
  rpc ProvisionAccountTicket(ProvisionAccountTicketRequest)
      returns (ProvisionAccountTicketResponse) {
    option (google.api.http) = {
      post: "/v1beta/accounts:provisionAccountTicket"
      body: "*"
    };
  }

  // Returns summaries of all accounts accessible by the caller.
  rpc ListAccountSummaries(ListAccountSummariesRequest)
      returns (ListAccountSummariesResponse) {
    option (google.api.http) = {
      get: "/v1beta/accountSummaries"
    };
  }

  // Lookup for a single "GA4" Property.
  rpc GetProperty(GetPropertyRequest) returns (Property) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns child Properties under the specified parent Account.
  //
  // Only "GA4" properties will be returned.
  // Properties will be excluded if the caller does not have access.
  // Soft-deleted (ie: "trashed") properties are excluded by default.
  // Returns an empty list if no relevant properties are found.
  rpc ListProperties(ListPropertiesRequest) returns (ListPropertiesResponse) {
    option (google.api.http) = {
      get: "/v1beta/properties"
    };
  }

  // Creates an "GA4" property with the specified location and attributes.
  rpc CreateProperty(CreatePropertyRequest) returns (Property) {
    option (google.api.http) = {
      post: "/v1beta/properties"
      body: "property"
    };
    option (google.api.method_signature) = "property";
  }

  // Marks target Property as soft-deleted (ie: "trashed") and returns it.
  //
  // This API does not have a method to restore soft-deleted properties.
  // However, they can be restored using the Trash Can UI.
  //
  // If the properties are not restored before the expiration time, the Property
  // and all child resources (eg: GoogleAdsLinks, Streams, AccessBindings)
  // will be permanently purged.
  // https://support.google.com/analytics/answer/6154772
  //
  // Returns an error if the target is not found, or is not a GA4 Property.
  rpc DeleteProperty(DeletePropertyRequest) returns (Property) {
    option (google.api.http) = {
      delete: "/v1beta/{name=properties/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a property.
  rpc UpdateProperty(UpdatePropertyRequest) returns (Property) {
    option (google.api.http) = {
      patch: "/v1beta/{property.name=properties/*}"
      body: "property"
    };
    option (google.api.method_signature) = "property,update_mask";
  }

  // Creates a FirebaseLink.
  //
  // Properties can have at most one FirebaseLink.
  rpc CreateFirebaseLink(CreateFirebaseLinkRequest) returns (FirebaseLink) {
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*}/firebaseLinks"
      body: "firebase_link"
    };
    option (google.api.method_signature) = "parent,firebase_link";
  }

  // Deletes a FirebaseLink on a property
  rpc DeleteFirebaseLink(DeleteFirebaseLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=properties/*/firebaseLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists FirebaseLinks on a property.
  // Properties can have at most one FirebaseLink.
  rpc ListFirebaseLinks(ListFirebaseLinksRequest)
      returns (ListFirebaseLinksResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*}/firebaseLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a GoogleAdsLink.
  rpc CreateGoogleAdsLink(CreateGoogleAdsLinkRequest) returns (GoogleAdsLink) {
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*}/googleAdsLinks"
      body: "google_ads_link"
    };
    option (google.api.method_signature) = "parent,google_ads_link";
  }

  // Updates a GoogleAdsLink on a property
  rpc UpdateGoogleAdsLink(UpdateGoogleAdsLinkRequest) returns (GoogleAdsLink) {
    option (google.api.http) = {
      patch: "/v1beta/{google_ads_link.name=properties/*/googleAdsLinks/*}"
      body: "google_ads_link"
    };
    option (google.api.method_signature) = "google_ads_link,update_mask";
  }

  // Deletes a GoogleAdsLink on a property
  rpc DeleteGoogleAdsLink(DeleteGoogleAdsLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=properties/*/googleAdsLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists GoogleAdsLinks on a property.
  rpc ListGoogleAdsLinks(ListGoogleAdsLinksRequest)
      returns (ListGoogleAdsLinksResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*}/googleAdsLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get data sharing settings on an account.
  // Data sharing settings are singletons.
  rpc GetDataSharingSettings(GetDataSharingSettingsRequest)
      returns (DataSharingSettings) {
    option (google.api.http) = {
      get: "/v1beta/{name=accounts/*/dataSharingSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single "GA4" MeasurementProtocolSecret.
  rpc GetMeasurementProtocolSecret(GetMeasurementProtocolSecretRequest)
      returns (MeasurementProtocolSecret) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/dataStreams/*/measurementProtocolSecrets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns child MeasurementProtocolSecrets under the specified parent
  // Property.
  rpc ListMeasurementProtocolSecrets(ListMeasurementProtocolSecretsRequest)
      returns (ListMeasurementProtocolSecretsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*/dataStreams/*}/measurementProtocolSecrets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a measurement protocol secret.
  rpc CreateMeasurementProtocolSecret(CreateMeasurementProtocolSecretRequest)
      returns (MeasurementProtocolSecret) {
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*/dataStreams/*}/measurementProtocolSecrets"
      body: "measurement_protocol_secret"
    };
    option (google.api.method_signature) = "parent,measurement_protocol_secret";
  }

  // Deletes target MeasurementProtocolSecret.
  rpc DeleteMeasurementProtocolSecret(DeleteMeasurementProtocolSecretRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=properties/*/dataStreams/*/measurementProtocolSecrets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a measurement protocol secret.
  rpc UpdateMeasurementProtocolSecret(UpdateMeasurementProtocolSecretRequest)
      returns (MeasurementProtocolSecret) {
    option (google.api.http) = {
      patch: "/v1beta/{measurement_protocol_secret.name=properties/*/dataStreams/*/measurementProtocolSecrets/*}"
      body: "measurement_protocol_secret"
    };
    option (google.api.method_signature) =
        "measurement_protocol_secret,update_mask";
  }

  // Acknowledges the terms of user data collection for the specified property.
  //
  // This acknowledgement must be completed (either in the Google Analytics UI
  // or through this API) before MeasurementProtocolSecret resources may be
  // created.
  rpc AcknowledgeUserDataCollection(AcknowledgeUserDataCollectionRequest)
      returns (AcknowledgeUserDataCollectionResponse) {
    option (google.api.http) = {
      post: "/v1beta/{property=properties/*}:acknowledgeUserDataCollection"
      body: "*"
    };
  }

  // Searches through all changes to an account or its children given the
  // specified set of filters.
  rpc SearchChangeHistoryEvents(SearchChangeHistoryEventsRequest)
      returns (SearchChangeHistoryEventsResponse) {
    option (google.api.http) = {
      post: "/v1beta/{account=accounts/*}:searchChangeHistoryEvents"
      body: "*"
    };
  }

  // Deprecated: Use `CreateKeyEvent` instead.
  // Creates a conversion event with the specified attributes.
  rpc CreateConversionEvent(CreateConversionEventRequest)
      returns (ConversionEvent) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*}/conversionEvents"
      body: "conversion_event"
    };
    option (google.api.method_signature) = "parent,conversion_event";
  }

  // Deprecated: Use `UpdateKeyEvent` instead.
  // Updates a conversion event with the specified attributes.
  rpc UpdateConversionEvent(UpdateConversionEventRequest)
      returns (ConversionEvent) {
    option deprecated = true;
    option (google.api.http) = {
      patch: "/v1beta/{conversion_event.name=properties/*/conversionEvents/*}"
      body: "conversion_event"
    };
    option (google.api.method_signature) = "conversion_event,update_mask";
  }

  // Deprecated: Use `GetKeyEvent` instead.
  // Retrieve a single conversion event.
  rpc GetConversionEvent(GetConversionEventRequest) returns (ConversionEvent) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/conversionEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deprecated: Use `DeleteKeyEvent` instead.
  // Deletes a conversion event in a property.
  rpc DeleteConversionEvent(DeleteConversionEventRequest)
      returns (google.protobuf.Empty) {
    option deprecated = true;
    option (google.api.http) = {
      delete: "/v1beta/{name=properties/*/conversionEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deprecated: Use `ListKeyEvents` instead.
  // Returns a list of conversion events in the specified parent property.
  //
  // Returns an empty list if no conversion events are found.
  rpc ListConversionEvents(ListConversionEventsRequest)
      returns (ListConversionEventsResponse) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*}/conversionEvents"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a Key Event.
  rpc CreateKeyEvent(CreateKeyEventRequest) returns (KeyEvent) {
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*}/keyEvents"
      body: "key_event"
    };
    option (google.api.method_signature) = "parent,key_event";
  }

  // Updates a Key Event.
  rpc UpdateKeyEvent(UpdateKeyEventRequest) returns (KeyEvent) {
    option (google.api.http) = {
      patch: "/v1beta/{key_event.name=properties/*/keyEvents/*}"
      body: "key_event"
    };
    option (google.api.method_signature) = "key_event,update_mask";
  }

  // Retrieve a single Key Event.
  rpc GetKeyEvent(GetKeyEventRequest) returns (KeyEvent) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/keyEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a Key Event.
  rpc DeleteKeyEvent(DeleteKeyEventRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=properties/*/keyEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of Key Events in the specified parent property.
  // Returns an empty list if no Key Events are found.
  rpc ListKeyEvents(ListKeyEventsRequest) returns (ListKeyEventsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*}/keyEvents"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a CustomDimension.
  rpc CreateCustomDimension(CreateCustomDimensionRequest)
      returns (CustomDimension) {
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*}/customDimensions"
      body: "custom_dimension"
    };
    option (google.api.method_signature) = "parent,custom_dimension";
  }

  // Updates a CustomDimension on a property.
  rpc UpdateCustomDimension(UpdateCustomDimensionRequest)
      returns (CustomDimension) {
    option (google.api.http) = {
      patch: "/v1beta/{custom_dimension.name=properties/*/customDimensions/*}"
      body: "custom_dimension"
    };
    option (google.api.method_signature) = "custom_dimension,update_mask";
  }

  // Lists CustomDimensions on a property.
  rpc ListCustomDimensions(ListCustomDimensionsRequest)
      returns (ListCustomDimensionsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*}/customDimensions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Archives a CustomDimension on a property.
  rpc ArchiveCustomDimension(ArchiveCustomDimensionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta/{name=properties/*/customDimensions/*}:archive"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single CustomDimension.
  rpc GetCustomDimension(GetCustomDimensionRequest) returns (CustomDimension) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/customDimensions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a CustomMetric.
  rpc CreateCustomMetric(CreateCustomMetricRequest) returns (CustomMetric) {
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*}/customMetrics"
      body: "custom_metric"
    };
    option (google.api.method_signature) = "parent,custom_metric";
  }

  // Updates a CustomMetric on a property.
  rpc UpdateCustomMetric(UpdateCustomMetricRequest) returns (CustomMetric) {
    option (google.api.http) = {
      patch: "/v1beta/{custom_metric.name=properties/*/customMetrics/*}"
      body: "custom_metric"
    };
    option (google.api.method_signature) = "custom_metric,update_mask";
  }

  // Lists CustomMetrics on a property.
  rpc ListCustomMetrics(ListCustomMetricsRequest)
      returns (ListCustomMetricsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*}/customMetrics"
    };
    option (google.api.method_signature) = "parent";
  }

  // Archives a CustomMetric on a property.
  rpc ArchiveCustomMetric(ArchiveCustomMetricRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta/{name=properties/*/customMetrics/*}:archive"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single CustomMetric.
  rpc GetCustomMetric(GetCustomMetricRequest) returns (CustomMetric) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/customMetrics/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the singleton data retention settings for this property.
  rpc GetDataRetentionSettings(GetDataRetentionSettingsRequest)
      returns (DataRetentionSettings) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/dataRetentionSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the singleton data retention settings for this property.
  rpc UpdateDataRetentionSettings(UpdateDataRetentionSettingsRequest)
      returns (DataRetentionSettings) {
    option (google.api.http) = {
      patch: "/v1beta/{data_retention_settings.name=properties/*/dataRetentionSettings}"
      body: "data_retention_settings"
    };
    option (google.api.method_signature) =
        "data_retention_settings,update_mask";
  }

  // Creates a DataStream.
  rpc CreateDataStream(CreateDataStreamRequest) returns (DataStream) {
    option (google.api.http) = {
      post: "/v1beta/{parent=properties/*}/dataStreams"
      body: "data_stream"
    };
    option (google.api.method_signature) = "parent,data_stream";
  }

  // Deletes a DataStream on a property.
  rpc DeleteDataStream(DeleteDataStreamRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=properties/*/dataStreams/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a DataStream on a property.
  rpc UpdateDataStream(UpdateDataStreamRequest) returns (DataStream) {
    option (google.api.http) = {
      patch: "/v1beta/{data_stream.name=properties/*/dataStreams/*}"
      body: "data_stream"
    };
    option (google.api.method_signature) = "data_stream,update_mask";
  }

  // Lists DataStreams on a property.
  rpc ListDataStreams(ListDataStreamsRequest)
      returns (ListDataStreamsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=properties/*}/dataStreams"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lookup for a single DataStream.
  rpc GetDataStream(GetDataStreamRequest) returns (DataStream) {
    option (google.api.http) = {
      get: "/v1beta/{name=properties/*/dataStreams/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a customized report of data access records. The report provides
  // records of each time a user reads Google Analytics reporting data. Access
  // records are retained for up to 2 years.
  //
  // Data Access Reports can be requested for a property. Reports may be
  // requested for any property, but dimensions that aren't related to quota can
  // only be requested on Google Analytics 360 properties. This method is only
  // available to Administrators.
  //
  // These data access records include GA4 UI Reporting, GA4 UI Explorations,
  // GA4 Data API, and other products like Firebase & Admob that can retrieve
  // data from Google Analytics through a linkage. These records don't include
  // property configuration changes like adding a stream or changing a
  // property's time zone. For configuration change history, see
  // [searchChangeHistoryEvents](https://developers.google.com/analytics/devguides/config/admin/v1/rest/v1alpha/accounts/searchChangeHistoryEvents).
  rpc RunAccessReport(RunAccessReportRequest)
      returns (RunAccessReportResponse) {
    option (google.api.http) = {
      post: "/v1beta/{entity=properties/*}:runAccessReport"
      body: "*"
      additional_bindings {
        post: "/v1beta/{entity=accounts/*}:runAccessReport"
        body: "*"
      }
    };
  }
}

// The request for a Data Access Record Report.
message RunAccessReportRequest {
  // The Data Access Report supports requesting at the property level or account
  // level. If requested at the account level, Data Access Reports include all
  // access for all properties under that account.
  //
  // To request at the property level, entity should be for example
  // 'properties/123' if "123" is your GA4 property ID. To request at the
  // account level, entity should be for example 'accounts/1234' if "1234" is
  // your GA4 Account ID.
  string entity = 1;

  // The dimensions requested and displayed in the response. Requests are
  // allowed up to 9 dimensions.
  repeated AccessDimension dimensions = 2;

  // The metrics requested and displayed in the response. Requests are allowed
  // up to 10 metrics.
  repeated AccessMetric metrics = 3;

  // Date ranges of access records to read. If multiple date ranges are
  // requested, each response row will contain a zero based date range index. If
  // two date ranges overlap, the access records for the overlapping days is
  // included in the response rows for both date ranges. Requests are allowed up
  // to 2 date ranges.
  repeated AccessDateRange date_ranges = 4;

  // Dimension filters let you restrict report response to specific
  // dimension values which match the filter. For example, filtering on access
  // records of a single user. To learn more, see [Fundamentals of Dimension
  // Filters](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#dimension_filters)
  // for examples. Metrics cannot be used in this filter.
  AccessFilterExpression dimension_filter = 5;

  // Metric filters allow you to restrict report response to specific metric
  // values which match the filter. Metric filters are applied after aggregating
  // the report's rows, similar to SQL having-clause. Dimensions cannot be used
  // in this filter.
  AccessFilterExpression metric_filter = 6;

  // The row count of the start row. The first row is counted as row 0. If
  // offset is unspecified, it is treated as 0. If offset is zero, then this
  // method will return the first page of results with `limit` entries.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 offset = 7;

  // The number of rows to return. If unspecified, 10,000 rows are returned. The
  // API returns a maximum of 100,000 rows per request, no matter how many you
  // ask for. `limit` must be positive.
  //
  // The API may return fewer rows than the requested `limit`, if there aren't
  // as many remaining rows as the `limit`. For instance, there are fewer than
  // 300 possible values for the dimension `country`, so when reporting on only
  // `country`, you can't get more than 300 rows, even if you set `limit` to a
  // higher value.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 limit = 8;

  // This request's time zone if specified. If unspecified, the property's time
  // zone is used. The request's time zone is used to interpret the start & end
  // dates of the report.
  //
  // Formatted as strings from the IANA Time Zone database
  // (https://www.iana.org/time-zones); for example "America/New_York" or
  // "Asia/Tokyo".
  string time_zone = 9;

  // Specifies how rows are ordered in the response.
  repeated AccessOrderBy order_bys = 10;

  // Toggles whether to return the current state of this Analytics Property's
  // quota. Quota is returned in [AccessQuota](#AccessQuota). For account-level
  // requests, this field must be false.
  bool return_entity_quota = 11;

  // Optional. Determines whether to include users who have never made an API
  // call in the response. If true, all users with access to the specified
  // property or account are included in the response, regardless of whether
  // they have made an API call or not. If false, only the users who have made
  // an API call will be included.
  bool include_all_users = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Decides whether to return the users within user groups. This
  // field works only when include_all_users is set to true. If true, it will
  // return all users with access to the specified property or account.
  // If false, only the users with direct access will be returned.
  bool expand_groups = 13 [(google.api.field_behavior) = OPTIONAL];
}

// The customized Data Access Record Report response.
message RunAccessReportResponse {
  // The header for a column in the report that corresponds to a specific
  // dimension. The number of DimensionHeaders and ordering of DimensionHeaders
  // matches the dimensions present in rows.
  repeated AccessDimensionHeader dimension_headers = 1;

  // The header for a column in the report that corresponds to a specific
  // metric. The number of MetricHeaders and ordering of MetricHeaders matches
  // the metrics present in rows.
  repeated AccessMetricHeader metric_headers = 2;

  // Rows of dimension value combinations and metric values in the report.
  repeated AccessRow rows = 3;

  // The total number of rows in the query result. `rowCount` is independent of
  // the number of rows returned in the response, the `limit` request
  // parameter, and the `offset` request parameter. For example if a query
  // returns 175 rows and includes `limit` of 50 in the API request, the
  // response will contain `rowCount` of 175 but only 50 rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int32 row_count = 4;

  // The quota state for this Analytics property including this request. This
  // field doesn't work with account-level requests.
  AccessQuota quota = 5;
}

// Request message for GetAccount RPC.
message GetAccountRequest {
  // Required. The name of the account to lookup.
  // Format: accounts/{account}
  // Example: "accounts/100"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];
}

// Request message for ListAccounts RPC.
message ListAccountsRequest {
  // The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 1;

  // A page token, received from a previous `ListAccounts` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListAccounts` must
  // match the call that provided the page token.
  string page_token = 2;

  // Whether to include soft-deleted (ie: "trashed") Accounts in the
  // results. Accounts can be inspected to determine whether they are deleted or
  // not.
  bool show_deleted = 3;
}

// Request message for ListAccounts RPC.
message ListAccountsResponse {
  // Results that were accessible to the caller.
  repeated Account accounts = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for DeleteAccount RPC.
message DeleteAccountRequest {
  // Required. The name of the Account to soft-delete.
  // Format: accounts/{account}
  // Example: "accounts/100"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];
}

// Request message for UpdateAccount RPC.
message UpdateAccountRequest {
  // Required. The account to update.
  // The account's `name` field is used to identify the account.
  Account account = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (for example, "field_to_update"). Omitted fields will not be updated.
  // To replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ProvisionAccountTicket RPC.
message ProvisionAccountTicketRequest {
  // The account to create.
  Account account = 1;

  // Redirect URI where the user will be sent after accepting Terms of Service.
  // Must be configured in Cloud Console as a Redirect URI.
  string redirect_uri = 2;
}

// Response message for ProvisionAccountTicket RPC.
message ProvisionAccountTicketResponse {
  // The param to be passed in the ToS link.
  string account_ticket_id = 1;
}

// Request message for GetProperty RPC.
message GetPropertyRequest {
  // Required. The name of the property to lookup.
  // Format: properties/{property_id}
  // Example: "properties/1000"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];
}

// Request message for ListProperties RPC.
message ListPropertiesRequest {
  // Required. An expression for filtering the results of the request.
  // Fields eligible for filtering are:
  // `parent:`(The resource name of the parent account/property) or
  // `ancestor:`(The resource name of the parent account) or
  // `firebase_project:`(The id or number of the linked firebase project).
  // Some examples of filters:
  //
  // ```
  // | Filter                      | Description                               |
  // |-----------------------------|-------------------------------------------|
  // | parent:accounts/123         | The account with account id: 123.       |
  // | parent:properties/123       | The property with property id: 123.       |
  // | ancestor:accounts/123       | The account with account id: 123.         |
  // | firebase_project:project-id | The firebase project with id: project-id. |
  // | firebase_project:123        | The firebase project with number: 123.    |
  // ```
  string filter = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListProperties` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListProperties` must
  // match the call that provided the page token.
  string page_token = 3;

  // Whether to include soft-deleted (ie: "trashed") Properties in the
  // results. Properties can be inspected to determine whether they are deleted
  // or not.
  bool show_deleted = 4;
}

// Response message for ListProperties RPC.
message ListPropertiesResponse {
  // Results that matched the filter criteria and were accessible to the caller.
  repeated Property properties = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for UpdateProperty RPC.
message UpdatePropertyRequest {
  // Required. The property to update.
  // The property's `name` field is used to identify the property to be
  // updated.
  Property property = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateProperty RPC.
message CreatePropertyRequest {
  // Required. The property to create.
  // Note: the supplied property must specify its parent.
  Property property = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteProperty RPC.
message DeletePropertyRequest {
  // Required. The name of the Property to soft-delete.
  // Format: properties/{property_id}
  // Example: "properties/1000"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];
}

// Request message for CreateFirebaseLink RPC
message CreateFirebaseLinkRequest {
  // Required. Format: properties/{property_id}
  //
  // Example: `properties/1234`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/FirebaseLink"
    }
  ];

  // Required. The Firebase link to create.
  FirebaseLink firebase_link = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteFirebaseLink RPC
message DeleteFirebaseLinkRequest {
  // Required. Format: properties/{property_id}/firebaseLinks/{firebase_link_id}
  //
  // Example: `properties/1234/firebaseLinks/5678`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/FirebaseLink"
    }
  ];
}

// Request message for ListFirebaseLinks RPC
message ListFirebaseLinksRequest {
  // Required. Format: properties/{property_id}
  //
  // Example: `properties/1234`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/FirebaseLink"
    }
  ];

  // The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListFirebaseLinks` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListFirebaseLinks` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListFirebaseLinks RPC
message ListFirebaseLinksResponse {
  // List of FirebaseLinks. This will have at most one value.
  repeated FirebaseLink firebase_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  // Currently, Google Analytics supports only one FirebaseLink per property,
  // so this will never be populated.
  string next_page_token = 2;
}

// Request message for CreateGoogleAdsLink RPC
message CreateGoogleAdsLinkRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/GoogleAdsLink"
    }
  ];

  // Required. The GoogleAdsLink to create.
  GoogleAdsLink google_ads_link = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateGoogleAdsLink RPC
message UpdateGoogleAdsLinkRequest {
  // The GoogleAdsLink to update
  GoogleAdsLink google_ads_link = 1;

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteGoogleAdsLink RPC.
message DeleteGoogleAdsLinkRequest {
  // Required. Example format: properties/1234/googleAdsLinks/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/GoogleAdsLink"
    }
  ];
}

// Request message for ListGoogleAdsLinks RPC.
message ListGoogleAdsLinksRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/GoogleAdsLink"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListGoogleAdsLinks` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListGoogleAdsLinks` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListGoogleAdsLinks RPC.
message ListGoogleAdsLinksResponse {
  // List of GoogleAdsLinks.
  repeated GoogleAdsLink google_ads_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetDataSharingSettings RPC.
message GetDataSharingSettingsRequest {
  // Required. The name of the settings to lookup.
  // Format: accounts/{account}/dataSharingSettings
  //
  // Example: `accounts/1000/dataSharingSettings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataSharingSettings"
    }
  ];
}

// Request message for ListAccountSummaries RPC.
message ListAccountSummariesRequest {
  // The maximum number of AccountSummary resources to return. The service may
  // return fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 1;

  // A page token, received from a previous `ListAccountSummaries` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListAccountSummaries`
  // must match the call that provided the page token.
  string page_token = 2;
}

// Response message for ListAccountSummaries RPC.
message ListAccountSummariesResponse {
  // Account summaries of all accounts the caller has access to.
  repeated AccountSummary account_summaries = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for AcknowledgeUserDataCollection RPC.
message AcknowledgeUserDataCollectionRequest {
  // Required. The property for which to acknowledge user data collection.
  string property = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];

  // Required. An acknowledgement that the caller of this method understands the
  // terms of user data collection.
  //
  // This field must contain the exact value:
  // "I acknowledge that I have the necessary privacy disclosures and rights
  // from my end users for the collection and processing of their data,
  // including the association of such data with the visitation information
  // Google Analytics collects from my site and/or app property."
  string acknowledgement = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for AcknowledgeUserDataCollection RPC.
message AcknowledgeUserDataCollectionResponse {}

// Request message for SearchChangeHistoryEvents RPC.
message SearchChangeHistoryEventsRequest {
  // Required. The account resource for which to return change history
  // resources. Format: accounts/{account}
  //
  // Example: `accounts/100`
  string account = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];

  // Optional. Resource name for a child property. If set, only return changes
  // made to this property or its child resources.
  // Format: properties/{propertyId}
  //
  // Example: `properties/100`
  string property = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];

  // Optional. If set, only return changes if they are for a resource that
  // matches at least one of these types.
  repeated ChangeHistoryResourceType resource_type = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, only return changes that match one or more of these types
  // of actions.
  repeated ActionType action = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, only return changes if they are made by a user in this
  // list.
  repeated string actor_email = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, only return changes made after this time (inclusive).
  google.protobuf.Timestamp earliest_change_time = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, only return changes made before this time (inclusive).
  google.protobuf.Timestamp latest_change_time = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of ChangeHistoryEvent items to return.
  // The service may return fewer than this value, even if there are additional
  // pages. If unspecified, at most 50 items will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `SearchChangeHistoryEvents` call. Provide this to retrieve the subsequent
  // page. When paginating, all other parameters provided to
  // `SearchChangeHistoryEvents` must match the call that provided the page
  // token.
  string page_token = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for SearchAccounts RPC.
message SearchChangeHistoryEventsResponse {
  // Results that were accessible to the caller.
  repeated ChangeHistoryEvent change_history_events = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetMeasurementProtocolSecret RPC.
message GetMeasurementProtocolSecretRequest {
  // Required. The name of the measurement protocol secret to lookup.
  // Format:
  // properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    }
  ];
}

// Request message for CreateMeasurementProtocolSecret RPC
message CreateMeasurementProtocolSecretRequest {
  // Required. The parent resource where this secret will be created.
  // Format: properties/{property}/dataStreams/{dataStream}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    }
  ];

  // Required. The measurement protocol secret to create.
  MeasurementProtocolSecret measurement_protocol_secret = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteMeasurementProtocolSecret RPC
message DeleteMeasurementProtocolSecretRequest {
  // Required. The name of the MeasurementProtocolSecret to delete.
  // Format:
  // properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    }
  ];
}

// Request message for UpdateMeasurementProtocolSecret RPC
message UpdateMeasurementProtocolSecretRequest {
  // Required. The measurement protocol secret to update.
  MeasurementProtocolSecret measurement_protocol_secret = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListMeasurementProtocolSecret RPC
message ListMeasurementProtocolSecretsRequest {
  // Required. The resource name of the parent stream.
  // Format:
  // properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 10 resources will be returned.
  // The maximum value is 10. Higher values will be coerced to the maximum.
  int32 page_size = 2;

  // A page token, received from a previous `ListMeasurementProtocolSecrets`
  // call. Provide this to retrieve the subsequent page. When paginating, all
  // other parameters provided to `ListMeasurementProtocolSecrets` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Response message for ListMeasurementProtocolSecret RPC
message ListMeasurementProtocolSecretsResponse {
  // A list of secrets for the parent stream specified in the request.
  repeated MeasurementProtocolSecret measurement_protocol_secrets = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateConversionEvent RPC
message CreateConversionEventRequest {
  // Required. The conversion event to create.
  ConversionEvent conversion_event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the parent property where this conversion
  // event will be created. Format: properties/123
  string parent = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/ConversionEvent"
    }
  ];
}

// Request message for UpdateConversionEvent RPC
message UpdateConversionEventRequest {
  // Required. The conversion event to update.
  // The `name` field is used to identify the settings to be updated.
  ConversionEvent conversion_event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetConversionEvent RPC
message GetConversionEventRequest {
  // Required. The resource name of the conversion event to retrieve.
  // Format: properties/{property}/conversionEvents/{conversion_event}
  // Example: "properties/123/conversionEvents/456"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/ConversionEvent"
    }
  ];
}

// Request message for DeleteConversionEvent RPC
message DeleteConversionEventRequest {
  // Required. The resource name of the conversion event to delete.
  // Format: properties/{property}/conversionEvents/{conversion_event}
  // Example: "properties/123/conversionEvents/456"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/ConversionEvent"
    }
  ];
}

// Request message for ListConversionEvents RPC
message ListConversionEventsRequest {
  // Required. The resource name of the parent property.
  // Example: 'properties/123'
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/ConversionEvent"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListConversionEvents` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListConversionEvents`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListConversionEvents RPC.
message ListConversionEventsResponse {
  // The requested conversion events
  repeated ConversionEvent conversion_events = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateKeyEvent RPC
message CreateKeyEventRequest {
  // Required. The Key Event to create.
  KeyEvent key_event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the parent property where this Key Event
  // will be created. Format: properties/123
  string parent = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/KeyEvent"
    }
  ];
}

// Request message for UpdateKeyEvent RPC
message UpdateKeyEventRequest {
  // Required. The Key Event to update.
  // The `name` field is used to identify the settings to be updated.
  KeyEvent key_event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetKeyEvent RPC
message GetKeyEventRequest {
  // Required. The resource name of the Key Event to retrieve.
  // Format: properties/{property}/keyEvents/{key_event}
  // Example: "properties/123/keyEvents/456"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/KeyEvent"
    }
  ];
}

// Request message for DeleteKeyEvent RPC
message DeleteKeyEventRequest {
  // Required. The resource name of the Key Event to delete.
  // Format: properties/{property}/keyEvents/{key_event}
  // Example: "properties/123/keyEvents/456"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/KeyEvent"
    }
  ];
}

// Request message for ListKeyEvents RPC
message ListKeyEventsRequest {
  // Required. The resource name of the parent property.
  // Example: 'properties/123'
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/KeyEvent"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListKeyEvents` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListKeyEvents`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListKeyEvents RPC.
message ListKeyEventsResponse {
  // The requested Key Events
  repeated KeyEvent key_events = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateCustomDimension RPC.
message CreateCustomDimensionRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CustomDimension"
    }
  ];

  // Required. The CustomDimension to create.
  CustomDimension custom_dimension = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateCustomDimension RPC.
message UpdateCustomDimensionRequest {
  // The CustomDimension to update
  CustomDimension custom_dimension = 1;

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListCustomDimensions RPC.
message ListCustomDimensionsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CustomDimension"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListCustomDimensions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCustomDimensions`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListCustomDimensions RPC.
message ListCustomDimensionsResponse {
  // List of CustomDimensions.
  repeated CustomDimension custom_dimensions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for ArchiveCustomDimension RPC.
message ArchiveCustomDimensionRequest {
  // Required. The name of the CustomDimension to archive.
  // Example format: properties/1234/customDimensions/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CustomDimension"
    }
  ];
}

// Request message for GetCustomDimension RPC.
message GetCustomDimensionRequest {
  // Required. The name of the CustomDimension to get.
  // Example format: properties/1234/customDimensions/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CustomDimension"
    }
  ];
}

// Request message for CreateCustomMetric RPC.
message CreateCustomMetricRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CustomMetric"
    }
  ];

  // Required. The CustomMetric to create.
  CustomMetric custom_metric = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateCustomMetric RPC.
message UpdateCustomMetricRequest {
  // The CustomMetric to update
  CustomMetric custom_metric = 1;

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListCustomMetrics RPC.
message ListCustomMetricsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CustomMetric"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListCustomMetrics` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCustomMetrics` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListCustomMetrics RPC.
message ListCustomMetricsResponse {
  // List of CustomMetrics.
  repeated CustomMetric custom_metrics = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for ArchiveCustomMetric RPC.
message ArchiveCustomMetricRequest {
  // Required. The name of the CustomMetric to archive.
  // Example format: properties/1234/customMetrics/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CustomMetric"
    }
  ];
}

// Request message for GetCustomMetric RPC.
message GetCustomMetricRequest {
  // Required. The name of the CustomMetric to get.
  // Example format: properties/1234/customMetrics/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CustomMetric"
    }
  ];
}

// Request message for GetDataRetentionSettings RPC.
message GetDataRetentionSettingsRequest {
  // Required. The name of the settings to lookup.
  // Format:
  // properties/{property}/dataRetentionSettings
  // Example: "properties/1000/dataRetentionSettings"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataRetentionSettings"
    }
  ];
}

// Request message for UpdateDataRetentionSettings RPC.
message UpdateDataRetentionSettingsRequest {
  // Required. The settings to update.
  // The `name` field is used to identify the settings to be updated.
  DataRetentionSettings data_retention_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateDataStream RPC.
message CreateDataStreamRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/DataStream"
    }
  ];

  // Required. The DataStream to create.
  DataStream data_stream = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteDataStream RPC.
message DeleteDataStreamRequest {
  // Required. The name of the DataStream to delete.
  // Example format: properties/1234/dataStreams/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataStream"
    }
  ];
}

// Request message for UpdateDataStream RPC.
message UpdateDataStreamRequest {
  // The DataStream to update
  DataStream data_stream = 1;

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListDataStreams RPC.
message ListDataStreamsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/DataStream"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListDataStreams` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDataStreams` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListDataStreams RPC.
message ListDataStreamsResponse {
  // List of DataStreams.
  repeated DataStream data_streams = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetDataStream RPC.
message GetDataStreamRequest {
  // Required. The name of the DataStream to get.
  // Example format: properties/1234/dataStreams/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataStream"
    }
  ];
}
