// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.admin.v1alpha;

import "google/analytics/admin/v1alpha/access_report.proto";
import "google/analytics/admin/v1alpha/audience.proto";
import "google/analytics/admin/v1alpha/channel_group.proto";
import "google/analytics/admin/v1alpha/event_create_and_edit.proto";
import "google/analytics/admin/v1alpha/expanded_data_set.proto";
import "google/analytics/admin/v1alpha/resources.proto";
import "google/analytics/admin/v1alpha/subproperty_event_filter.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/analytics/admin/apiv1alpha/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "AnalyticsAdminProto";
option java_package = "com.google.analytics.admin.v1alpha";

// Service Interface for the Analytics Admin API (GA4).
service AnalyticsAdminService {
  option (google.api.default_host) = "analyticsadmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/analytics.edit,"
      "https://www.googleapis.com/auth/analytics.manage.users,"
      "https://www.googleapis.com/auth/analytics.manage.users.readonly,"
      "https://www.googleapis.com/auth/analytics.readonly";

  // Lookup for a single Account.
  rpc GetAccount(GetAccountRequest) returns (Account) {
    option (google.api.http) = {
      get: "/v1alpha/{name=accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns all accounts accessible by the caller.
  //
  // Note that these accounts might not currently have GA4 properties.
  // Soft-deleted (ie: "trashed") accounts are excluded by default.
  // Returns an empty list if no relevant accounts are found.
  rpc ListAccounts(ListAccountsRequest) returns (ListAccountsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/accounts"
    };
  }

  // Marks target Account as soft-deleted (ie: "trashed") and returns it.
  //
  // This API does not have a method to restore soft-deleted accounts.
  // However, they can be restored using the Trash Can UI.
  //
  // If the accounts are not restored before the expiration time, the account
  // and all child resources (eg: Properties, GoogleAdsLinks, Streams,
  // AccessBindings) will be permanently purged.
  // https://support.google.com/analytics/answer/6154772
  //
  // Returns an error if the target is not found.
  rpc DeleteAccount(DeleteAccountRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an account.
  rpc UpdateAccount(UpdateAccountRequest) returns (Account) {
    option (google.api.http) = {
      patch: "/v1alpha/{account.name=accounts/*}"
      body: "account"
    };
    option (google.api.method_signature) = "account,update_mask";
  }

  // Requests a ticket for creating an account.
  rpc ProvisionAccountTicket(ProvisionAccountTicketRequest)
      returns (ProvisionAccountTicketResponse) {
    option (google.api.http) = {
      post: "/v1alpha/accounts:provisionAccountTicket"
      body: "*"
    };
  }

  // Returns summaries of all accounts accessible by the caller.
  rpc ListAccountSummaries(ListAccountSummariesRequest)
      returns (ListAccountSummariesResponse) {
    option (google.api.http) = {
      get: "/v1alpha/accountSummaries"
    };
  }

  // Lookup for a single "GA4" Property.
  rpc GetProperty(GetPropertyRequest) returns (Property) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns child Properties under the specified parent Account.
  //
  // Only "GA4" properties will be returned.
  // Properties will be excluded if the caller does not have access.
  // Soft-deleted (ie: "trashed") properties are excluded by default.
  // Returns an empty list if no relevant properties are found.
  rpc ListProperties(ListPropertiesRequest) returns (ListPropertiesResponse) {
    option (google.api.http) = {
      get: "/v1alpha/properties"
    };
  }

  // Creates an "GA4" property with the specified location and attributes.
  rpc CreateProperty(CreatePropertyRequest) returns (Property) {
    option (google.api.http) = {
      post: "/v1alpha/properties"
      body: "property"
    };
    option (google.api.method_signature) = "property";
  }

  // Marks target Property as soft-deleted (ie: "trashed") and returns it.
  //
  // This API does not have a method to restore soft-deleted properties.
  // However, they can be restored using the Trash Can UI.
  //
  // If the properties are not restored before the expiration time, the Property
  // and all child resources (eg: GoogleAdsLinks, Streams, AccessBindings)
  // will be permanently purged.
  // https://support.google.com/analytics/answer/6154772
  //
  // Returns an error if the target is not found, or is not a GA4 Property.
  rpc DeleteProperty(DeletePropertyRequest) returns (Property) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a property.
  rpc UpdateProperty(UpdatePropertyRequest) returns (Property) {
    option (google.api.http) = {
      patch: "/v1alpha/{property.name=properties/*}"
      body: "property"
    };
    option (google.api.method_signature) = "property,update_mask";
  }

  // Creates a FirebaseLink.
  //
  // Properties can have at most one FirebaseLink.
  rpc CreateFirebaseLink(CreateFirebaseLinkRequest) returns (FirebaseLink) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/firebaseLinks"
      body: "firebase_link"
    };
    option (google.api.method_signature) = "parent,firebase_link";
  }

  // Deletes a FirebaseLink on a property
  rpc DeleteFirebaseLink(DeleteFirebaseLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/firebaseLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists FirebaseLinks on a property.
  // Properties can have at most one FirebaseLink.
  rpc ListFirebaseLinks(ListFirebaseLinksRequest)
      returns (ListFirebaseLinksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/firebaseLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the Site Tag for the specified web stream.
  // Site Tags are immutable singletons.
  rpc GetGlobalSiteTag(GetGlobalSiteTagRequest) returns (GlobalSiteTag) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataStreams/*/globalSiteTag}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a GoogleAdsLink.
  rpc CreateGoogleAdsLink(CreateGoogleAdsLinkRequest) returns (GoogleAdsLink) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/googleAdsLinks"
      body: "google_ads_link"
    };
    option (google.api.method_signature) = "parent,google_ads_link";
  }

  // Updates a GoogleAdsLink on a property
  rpc UpdateGoogleAdsLink(UpdateGoogleAdsLinkRequest) returns (GoogleAdsLink) {
    option (google.api.http) = {
      patch: "/v1alpha/{google_ads_link.name=properties/*/googleAdsLinks/*}"
      body: "google_ads_link"
    };
    option (google.api.method_signature) = "google_ads_link,update_mask";
  }

  // Deletes a GoogleAdsLink on a property
  rpc DeleteGoogleAdsLink(DeleteGoogleAdsLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/googleAdsLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists GoogleAdsLinks on a property.
  rpc ListGoogleAdsLinks(ListGoogleAdsLinksRequest)
      returns (ListGoogleAdsLinksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/googleAdsLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get data sharing settings on an account.
  // Data sharing settings are singletons.
  rpc GetDataSharingSettings(GetDataSharingSettingsRequest)
      returns (DataSharingSettings) {
    option (google.api.http) = {
      get: "/v1alpha/{name=accounts/*/dataSharingSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single "GA4" MeasurementProtocolSecret.
  rpc GetMeasurementProtocolSecret(GetMeasurementProtocolSecretRequest)
      returns (MeasurementProtocolSecret) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataStreams/*/measurementProtocolSecrets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns child MeasurementProtocolSecrets under the specified parent
  // Property.
  rpc ListMeasurementProtocolSecrets(ListMeasurementProtocolSecretsRequest)
      returns (ListMeasurementProtocolSecretsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*/dataStreams/*}/measurementProtocolSecrets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a measurement protocol secret.
  rpc CreateMeasurementProtocolSecret(CreateMeasurementProtocolSecretRequest)
      returns (MeasurementProtocolSecret) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*/dataStreams/*}/measurementProtocolSecrets"
      body: "measurement_protocol_secret"
    };
    option (google.api.method_signature) = "parent,measurement_protocol_secret";
  }

  // Deletes target MeasurementProtocolSecret.
  rpc DeleteMeasurementProtocolSecret(DeleteMeasurementProtocolSecretRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/dataStreams/*/measurementProtocolSecrets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a measurement protocol secret.
  rpc UpdateMeasurementProtocolSecret(UpdateMeasurementProtocolSecretRequest)
      returns (MeasurementProtocolSecret) {
    option (google.api.http) = {
      patch: "/v1alpha/{measurement_protocol_secret.name=properties/*/dataStreams/*/measurementProtocolSecrets/*}"
      body: "measurement_protocol_secret"
    };
    option (google.api.method_signature) =
        "measurement_protocol_secret,update_mask";
  }

  // Acknowledges the terms of user data collection for the specified property.
  //
  // This acknowledgement must be completed (either in the Google Analytics UI
  // or through this API) before MeasurementProtocolSecret resources may be
  // created.
  rpc AcknowledgeUserDataCollection(AcknowledgeUserDataCollectionRequest)
      returns (AcknowledgeUserDataCollectionResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{property=properties/*}:acknowledgeUserDataCollection"
      body: "*"
    };
  }

  // Looks up a single SKAdNetworkConversionValueSchema.
  rpc GetSKAdNetworkConversionValueSchema(
      GetSKAdNetworkConversionValueSchemaRequest)
      returns (SKAdNetworkConversionValueSchema) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataStreams/*/sKAdNetworkConversionValueSchema/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a SKAdNetworkConversionValueSchema.
  rpc CreateSKAdNetworkConversionValueSchema(
      CreateSKAdNetworkConversionValueSchemaRequest)
      returns (SKAdNetworkConversionValueSchema) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*/dataStreams/*}/sKAdNetworkConversionValueSchema"
      body: "skadnetwork_conversion_value_schema"
    };
    option (google.api.method_signature) =
        "parent,skadnetwork_conversion_value_schema";
  }

  // Deletes target SKAdNetworkConversionValueSchema.
  rpc DeleteSKAdNetworkConversionValueSchema(
      DeleteSKAdNetworkConversionValueSchemaRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/dataStreams/*/sKAdNetworkConversionValueSchema/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a SKAdNetworkConversionValueSchema.
  rpc UpdateSKAdNetworkConversionValueSchema(
      UpdateSKAdNetworkConversionValueSchemaRequest)
      returns (SKAdNetworkConversionValueSchema) {
    option (google.api.http) = {
      patch: "/v1alpha/{skadnetwork_conversion_value_schema.name=properties/*/dataStreams/*/sKAdNetworkConversionValueSchema/*}"
      body: "skadnetwork_conversion_value_schema"
    };
    option (google.api.method_signature) =
        "skadnetwork_conversion_value_schema,update_mask";
  }

  // Lists SKAdNetworkConversionValueSchema on a stream.
  // Properties can have at most one SKAdNetworkConversionValueSchema.
  rpc ListSKAdNetworkConversionValueSchemas(
      ListSKAdNetworkConversionValueSchemasRequest)
      returns (ListSKAdNetworkConversionValueSchemasResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*/dataStreams/*}/sKAdNetworkConversionValueSchema"
    };
    option (google.api.method_signature) = "parent";
  }

  // Searches through all changes to an account or its children given the
  // specified set of filters.
  rpc SearchChangeHistoryEvents(SearchChangeHistoryEventsRequest)
      returns (SearchChangeHistoryEventsResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{account=accounts/*}:searchChangeHistoryEvents"
      body: "*"
    };
  }

  // Lookup for Google Signals settings for a property.
  rpc GetGoogleSignalsSettings(GetGoogleSignalsSettingsRequest)
      returns (GoogleSignalsSettings) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/googleSignalsSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates Google Signals settings for a property.
  rpc UpdateGoogleSignalsSettings(UpdateGoogleSignalsSettingsRequest)
      returns (GoogleSignalsSettings) {
    option (google.api.http) = {
      patch: "/v1alpha/{google_signals_settings.name=properties/*/googleSignalsSettings}"
      body: "google_signals_settings"
    };
    option (google.api.method_signature) =
        "google_signals_settings,update_mask";
  }

  // Deprecated: Use `CreateKeyEvent` instead.
  // Creates a conversion event with the specified attributes.
  rpc CreateConversionEvent(CreateConversionEventRequest)
      returns (ConversionEvent) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/conversionEvents"
      body: "conversion_event"
    };
    option (google.api.method_signature) = "parent,conversion_event";
  }

  // Deprecated: Use `UpdateKeyEvent` instead.
  // Updates a conversion event with the specified attributes.
  rpc UpdateConversionEvent(UpdateConversionEventRequest)
      returns (ConversionEvent) {
    option deprecated = true;
    option (google.api.http) = {
      patch: "/v1alpha/{conversion_event.name=properties/*/conversionEvents/*}"
      body: "conversion_event"
    };
    option (google.api.method_signature) = "conversion_event,update_mask";
  }

  // Deprecated: Use `GetKeyEvent` instead.
  // Retrieve a single conversion event.
  rpc GetConversionEvent(GetConversionEventRequest) returns (ConversionEvent) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/conversionEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deprecated: Use `DeleteKeyEvent` instead.
  // Deletes a conversion event in a property.
  rpc DeleteConversionEvent(DeleteConversionEventRequest)
      returns (google.protobuf.Empty) {
    option deprecated = true;
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/conversionEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deprecated: Use `ListKeyEvents` instead.
  // Returns a list of conversion events in the specified parent property.
  //
  // Returns an empty list if no conversion events are found.
  rpc ListConversionEvents(ListConversionEventsRequest)
      returns (ListConversionEventsResponse) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/conversionEvents"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a Key Event.
  rpc CreateKeyEvent(CreateKeyEventRequest) returns (KeyEvent) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/keyEvents"
      body: "key_event"
    };
    option (google.api.method_signature) = "parent,key_event";
  }

  // Updates a Key Event.
  rpc UpdateKeyEvent(UpdateKeyEventRequest) returns (KeyEvent) {
    option (google.api.http) = {
      patch: "/v1alpha/{key_event.name=properties/*/keyEvents/*}"
      body: "key_event"
    };
    option (google.api.method_signature) = "key_event,update_mask";
  }

  // Retrieve a single Key Event.
  rpc GetKeyEvent(GetKeyEventRequest) returns (KeyEvent) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/keyEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a Key Event.
  rpc DeleteKeyEvent(DeleteKeyEventRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/keyEvents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of Key Events in the specified parent property.
  // Returns an empty list if no Key Events are found.
  rpc ListKeyEvents(ListKeyEventsRequest) returns (ListKeyEventsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/keyEvents"
    };
    option (google.api.method_signature) = "parent";
  }

  // Look up a single DisplayVideo360AdvertiserLink
  rpc GetDisplayVideo360AdvertiserLink(GetDisplayVideo360AdvertiserLinkRequest)
      returns (DisplayVideo360AdvertiserLink) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/displayVideo360AdvertiserLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all DisplayVideo360AdvertiserLinks on a property.
  rpc ListDisplayVideo360AdvertiserLinks(
      ListDisplayVideo360AdvertiserLinksRequest)
      returns (ListDisplayVideo360AdvertiserLinksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/displayVideo360AdvertiserLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a DisplayVideo360AdvertiserLink.
  // This can only be utilized by users who have proper authorization both on
  // the Google Analytics property and on the Display & Video 360 advertiser.
  // Users who do not have access to the Display & Video 360 advertiser should
  // instead seek to create a DisplayVideo360LinkProposal.
  rpc CreateDisplayVideo360AdvertiserLink(
      CreateDisplayVideo360AdvertiserLinkRequest)
      returns (DisplayVideo360AdvertiserLink) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/displayVideo360AdvertiserLinks"
      body: "display_video_360_advertiser_link"
    };
    option (google.api.method_signature) =
        "parent,display_video_360_advertiser_link";
  }

  // Deletes a DisplayVideo360AdvertiserLink on a property.
  rpc DeleteDisplayVideo360AdvertiserLink(
      DeleteDisplayVideo360AdvertiserLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/displayVideo360AdvertiserLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a DisplayVideo360AdvertiserLink on a property.
  rpc UpdateDisplayVideo360AdvertiserLink(
      UpdateDisplayVideo360AdvertiserLinkRequest)
      returns (DisplayVideo360AdvertiserLink) {
    option (google.api.http) = {
      patch: "/v1alpha/{display_video_360_advertiser_link.name=properties/*/displayVideo360AdvertiserLinks/*}"
      body: "display_video_360_advertiser_link"
    };
    option (google.api.method_signature) =
        "display_video_360_advertiser_link,update_mask";
  }

  // Lookup for a single DisplayVideo360AdvertiserLinkProposal.
  rpc GetDisplayVideo360AdvertiserLinkProposal(
      GetDisplayVideo360AdvertiserLinkProposalRequest)
      returns (DisplayVideo360AdvertiserLinkProposal) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/displayVideo360AdvertiserLinkProposals/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists DisplayVideo360AdvertiserLinkProposals on a property.
  rpc ListDisplayVideo360AdvertiserLinkProposals(
      ListDisplayVideo360AdvertiserLinkProposalsRequest)
      returns (ListDisplayVideo360AdvertiserLinkProposalsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/displayVideo360AdvertiserLinkProposals"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a DisplayVideo360AdvertiserLinkProposal.
  rpc CreateDisplayVideo360AdvertiserLinkProposal(
      CreateDisplayVideo360AdvertiserLinkProposalRequest)
      returns (DisplayVideo360AdvertiserLinkProposal) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/displayVideo360AdvertiserLinkProposals"
      body: "display_video_360_advertiser_link_proposal"
    };
    option (google.api.method_signature) =
        "parent,display_video_360_advertiser_link_proposal";
  }

  // Deletes a DisplayVideo360AdvertiserLinkProposal on a property.
  // This can only be used on cancelled proposals.
  rpc DeleteDisplayVideo360AdvertiserLinkProposal(
      DeleteDisplayVideo360AdvertiserLinkProposalRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/displayVideo360AdvertiserLinkProposals/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Approves a DisplayVideo360AdvertiserLinkProposal.
  // The DisplayVideo360AdvertiserLinkProposal will be deleted and a new
  // DisplayVideo360AdvertiserLink will be created.
  rpc ApproveDisplayVideo360AdvertiserLinkProposal(
      ApproveDisplayVideo360AdvertiserLinkProposalRequest)
      returns (ApproveDisplayVideo360AdvertiserLinkProposalResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{name=properties/*/displayVideo360AdvertiserLinkProposals/*}:approve"
      body: "*"
    };
  }

  // Cancels a DisplayVideo360AdvertiserLinkProposal.
  // Cancelling can mean either:
  // - Declining a proposal initiated from Display & Video 360
  // - Withdrawing a proposal initiated from Google Analytics
  // After being cancelled, a proposal will eventually be deleted automatically.
  rpc CancelDisplayVideo360AdvertiserLinkProposal(
      CancelDisplayVideo360AdvertiserLinkProposalRequest)
      returns (DisplayVideo360AdvertiserLinkProposal) {
    option (google.api.http) = {
      post: "/v1alpha/{name=properties/*/displayVideo360AdvertiserLinkProposals/*}:cancel"
      body: "*"
    };
  }

  // Creates a CustomDimension.
  rpc CreateCustomDimension(CreateCustomDimensionRequest)
      returns (CustomDimension) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/customDimensions"
      body: "custom_dimension"
    };
    option (google.api.method_signature) = "parent,custom_dimension";
  }

  // Updates a CustomDimension on a property.
  rpc UpdateCustomDimension(UpdateCustomDimensionRequest)
      returns (CustomDimension) {
    option (google.api.http) = {
      patch: "/v1alpha/{custom_dimension.name=properties/*/customDimensions/*}"
      body: "custom_dimension"
    };
    option (google.api.method_signature) = "custom_dimension,update_mask";
  }

  // Lists CustomDimensions on a property.
  rpc ListCustomDimensions(ListCustomDimensionsRequest)
      returns (ListCustomDimensionsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/customDimensions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Archives a CustomDimension on a property.
  rpc ArchiveCustomDimension(ArchiveCustomDimensionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1alpha/{name=properties/*/customDimensions/*}:archive"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single CustomDimension.
  rpc GetCustomDimension(GetCustomDimensionRequest) returns (CustomDimension) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/customDimensions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a CustomMetric.
  rpc CreateCustomMetric(CreateCustomMetricRequest) returns (CustomMetric) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/customMetrics"
      body: "custom_metric"
    };
    option (google.api.method_signature) = "parent,custom_metric";
  }

  // Updates a CustomMetric on a property.
  rpc UpdateCustomMetric(UpdateCustomMetricRequest) returns (CustomMetric) {
    option (google.api.http) = {
      patch: "/v1alpha/{custom_metric.name=properties/*/customMetrics/*}"
      body: "custom_metric"
    };
    option (google.api.method_signature) = "custom_metric,update_mask";
  }

  // Lists CustomMetrics on a property.
  rpc ListCustomMetrics(ListCustomMetricsRequest)
      returns (ListCustomMetricsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/customMetrics"
    };
    option (google.api.method_signature) = "parent";
  }

  // Archives a CustomMetric on a property.
  rpc ArchiveCustomMetric(ArchiveCustomMetricRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1alpha/{name=properties/*/customMetrics/*}:archive"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single CustomMetric.
  rpc GetCustomMetric(GetCustomMetricRequest) returns (CustomMetric) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/customMetrics/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the singleton data retention settings for this property.
  rpc GetDataRetentionSettings(GetDataRetentionSettingsRequest)
      returns (DataRetentionSettings) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataRetentionSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the singleton data retention settings for this property.
  rpc UpdateDataRetentionSettings(UpdateDataRetentionSettingsRequest)
      returns (DataRetentionSettings) {
    option (google.api.http) = {
      patch: "/v1alpha/{data_retention_settings.name=properties/*/dataRetentionSettings}"
      body: "data_retention_settings"
    };
    option (google.api.method_signature) =
        "data_retention_settings,update_mask";
  }

  // Creates a DataStream.
  rpc CreateDataStream(CreateDataStreamRequest) returns (DataStream) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/dataStreams"
      body: "data_stream"
    };
    option (google.api.method_signature) = "parent,data_stream";
  }

  // Deletes a DataStream on a property.
  rpc DeleteDataStream(DeleteDataStreamRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/dataStreams/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a DataStream on a property.
  rpc UpdateDataStream(UpdateDataStreamRequest) returns (DataStream) {
    option (google.api.http) = {
      patch: "/v1alpha/{data_stream.name=properties/*/dataStreams/*}"
      body: "data_stream"
    };
    option (google.api.method_signature) = "data_stream,update_mask";
  }

  // Lists DataStreams on a property.
  rpc ListDataStreams(ListDataStreamsRequest)
      returns (ListDataStreamsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/dataStreams"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lookup for a single DataStream.
  rpc GetDataStream(GetDataStreamRequest) returns (DataStream) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataStreams/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single Audience.
  // Audiences created before 2020 may not be supported.
  // Default audiences will not show filter definitions.
  rpc GetAudience(GetAudienceRequest) returns (Audience) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/audiences/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Audiences on a property.
  // Audiences created before 2020 may not be supported.
  // Default audiences will not show filter definitions.
  rpc ListAudiences(ListAudiencesRequest) returns (ListAudiencesResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/audiences"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an Audience.
  rpc CreateAudience(CreateAudienceRequest) returns (Audience) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/audiences"
      body: "audience"
    };
    option (google.api.method_signature) = "parent,audience";
  }

  // Updates an Audience on a property.
  rpc UpdateAudience(UpdateAudienceRequest) returns (Audience) {
    option (google.api.http) = {
      patch: "/v1alpha/{audience.name=properties/*/audiences/*}"
      body: "audience"
    };
    option (google.api.method_signature) = "audience,update_mask";
  }

  // Archives an Audience on a property.
  rpc ArchiveAudience(ArchiveAudienceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1alpha/{name=properties/*/audiences/*}:archive"
      body: "*"
    };
  }

  // Look up a single SearchAds360Link
  rpc GetSearchAds360Link(GetSearchAds360LinkRequest)
      returns (SearchAds360Link) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/searchAds360Links/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all SearchAds360Links on a property.
  rpc ListSearchAds360Links(ListSearchAds360LinksRequest)
      returns (ListSearchAds360LinksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/searchAds360Links"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a SearchAds360Link.
  rpc CreateSearchAds360Link(CreateSearchAds360LinkRequest)
      returns (SearchAds360Link) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/searchAds360Links"
      body: "search_ads_360_link"
    };
    option (google.api.method_signature) = "parent,search_ads_360_link";
  }

  // Deletes a SearchAds360Link on a property.
  rpc DeleteSearchAds360Link(DeleteSearchAds360LinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/searchAds360Links/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a SearchAds360Link on a property.
  rpc UpdateSearchAds360Link(UpdateSearchAds360LinkRequest)
      returns (SearchAds360Link) {
    option (google.api.http) = {
      patch: "/v1alpha/{search_ads_360_link.name=properties/*/searchAds360Links/*}"
      body: "search_ads_360_link"
    };
    option (google.api.method_signature) = "search_ads_360_link,update_mask";
  }

  // Lookup for a AttributionSettings singleton.
  rpc GetAttributionSettings(GetAttributionSettingsRequest)
      returns (AttributionSettings) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/attributionSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates attribution settings on a property.
  rpc UpdateAttributionSettings(UpdateAttributionSettingsRequest)
      returns (AttributionSettings) {
    option (google.api.http) = {
      patch: "/v1alpha/{attribution_settings.name=properties/*/attributionSettings}"
      body: "attribution_settings"
    };
    option (google.api.method_signature) = "attribution_settings,update_mask";
  }

  // Returns a customized report of data access records. The report provides
  // records of each time a user reads Google Analytics reporting data. Access
  // records are retained for up to 2 years.
  //
  // Data Access Reports can be requested for a property. Reports may be
  // requested for any property, but dimensions that aren't related to quota can
  // only be requested on Google Analytics 360 properties. This method is only
  // available to Administrators.
  //
  // These data access records include GA4 UI Reporting, GA4 UI Explorations,
  // GA4 Data API, and other products like Firebase & Admob that can retrieve
  // data from Google Analytics through a linkage. These records don't include
  // property configuration changes like adding a stream or changing a
  // property's time zone. For configuration change history, see
  // [searchChangeHistoryEvents](https://developers.google.com/analytics/devguides/config/admin/v1/rest/v1alpha/accounts/searchChangeHistoryEvents).
  rpc RunAccessReport(RunAccessReportRequest)
      returns (RunAccessReportResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{entity=properties/*}:runAccessReport"
      body: "*"
      additional_bindings {
        post: "/v1alpha/{entity=accounts/*}:runAccessReport"
        body: "*"
      }
    };
  }

  // Creates an access binding on an account or property.
  rpc CreateAccessBinding(CreateAccessBindingRequest) returns (AccessBinding) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=accounts/*}/accessBindings"
      body: "access_binding"
      additional_bindings {
        post: "/v1alpha/{parent=properties/*}/accessBindings"
        body: "access_binding"
      }
    };
    option (google.api.method_signature) = "parent,access_binding";
  }

  // Gets information about an access binding.
  rpc GetAccessBinding(GetAccessBindingRequest) returns (AccessBinding) {
    option (google.api.http) = {
      get: "/v1alpha/{name=accounts/*/accessBindings/*}"
      additional_bindings {
        get: "/v1alpha/{name=properties/*/accessBindings/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an access binding on an account or property.
  rpc UpdateAccessBinding(UpdateAccessBindingRequest) returns (AccessBinding) {
    option (google.api.http) = {
      patch: "/v1alpha/{access_binding.name=accounts/*/accessBindings/*}"
      body: "access_binding"
      additional_bindings {
        patch: "/v1alpha/{access_binding.name=properties/*/accessBindings/*}"
        body: "access_binding"
      }
    };
    option (google.api.method_signature) = "access_binding";
  }

  // Deletes an access binding on an account or property.
  rpc DeleteAccessBinding(DeleteAccessBindingRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=accounts/*/accessBindings/*}"
      additional_bindings {
        delete: "/v1alpha/{name=properties/*/accessBindings/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all access bindings on an account or property.
  rpc ListAccessBindings(ListAccessBindingsRequest)
      returns (ListAccessBindingsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=accounts/*}/accessBindings"
      additional_bindings {
        get: "/v1alpha/{parent=properties/*}/accessBindings"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates information about multiple access bindings to an account or
  // property.
  //
  // This method is transactional. If any AccessBinding cannot be created, none
  // of the AccessBindings will be created.
  rpc BatchCreateAccessBindings(BatchCreateAccessBindingsRequest)
      returns (BatchCreateAccessBindingsResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=accounts/*}/accessBindings:batchCreate"
      body: "*"
      additional_bindings {
        post: "/v1alpha/{parent=properties/*}/accessBindings:batchCreate"
        body: "*"
      }
    };
  }

  // Gets information about multiple access bindings to an account or property.
  rpc BatchGetAccessBindings(BatchGetAccessBindingsRequest)
      returns (BatchGetAccessBindingsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=accounts/*}/accessBindings:batchGet"
      additional_bindings {
        get: "/v1alpha/{parent=properties/*}/accessBindings:batchGet"
      }
    };
  }

  // Updates information about multiple access bindings to an account or
  // property.
  rpc BatchUpdateAccessBindings(BatchUpdateAccessBindingsRequest)
      returns (BatchUpdateAccessBindingsResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=accounts/*}/accessBindings:batchUpdate"
      body: "*"
      additional_bindings {
        post: "/v1alpha/{parent=properties/*}/accessBindings:batchUpdate"
        body: "*"
      }
    };
  }

  // Deletes information about multiple users' links to an account or property.
  rpc BatchDeleteAccessBindings(BatchDeleteAccessBindingsRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=accounts/*}/accessBindings:batchDelete"
      body: "*"
      additional_bindings {
        post: "/v1alpha/{parent=properties/*}/accessBindings:batchDelete"
        body: "*"
      }
    };
  }

  // Lookup for a single ExpandedDataSet.
  rpc GetExpandedDataSet(GetExpandedDataSetRequest) returns (ExpandedDataSet) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/expandedDataSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ExpandedDataSets on a property.
  rpc ListExpandedDataSets(ListExpandedDataSetsRequest)
      returns (ListExpandedDataSetsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/expandedDataSets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a ExpandedDataSet.
  rpc CreateExpandedDataSet(CreateExpandedDataSetRequest)
      returns (ExpandedDataSet) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/expandedDataSets"
      body: "expanded_data_set"
    };
    option (google.api.method_signature) = "parent,expanded_data_set";
  }

  // Updates a ExpandedDataSet on a property.
  rpc UpdateExpandedDataSet(UpdateExpandedDataSetRequest)
      returns (ExpandedDataSet) {
    option (google.api.http) = {
      patch: "/v1alpha/{expanded_data_set.name=properties/*/expandedDataSets/*}"
      body: "expanded_data_set"
    };
    option (google.api.method_signature) = "expanded_data_set,update_mask";
  }

  // Deletes a ExpandedDataSet on a property.
  rpc DeleteExpandedDataSet(DeleteExpandedDataSetRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/expandedDataSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single ChannelGroup.
  rpc GetChannelGroup(GetChannelGroupRequest) returns (ChannelGroup) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/channelGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ChannelGroups on a property.
  rpc ListChannelGroups(ListChannelGroupsRequest)
      returns (ListChannelGroupsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/channelGroups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a ChannelGroup.
  rpc CreateChannelGroup(CreateChannelGroupRequest) returns (ChannelGroup) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/channelGroups"
      body: "channel_group"
    };
    option (google.api.method_signature) = "parent,channel_group";
  }

  // Updates a ChannelGroup.
  rpc UpdateChannelGroup(UpdateChannelGroupRequest) returns (ChannelGroup) {
    option (google.api.http) = {
      patch: "/v1alpha/{channel_group.name=properties/*/channelGroups/*}"
      body: "channel_group"
    };
    option (google.api.method_signature) = "channel_group,update_mask";
  }

  // Deletes a ChannelGroup on a property.
  rpc DeleteChannelGroup(DeleteChannelGroupRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/channelGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Sets the opt out status for the automated GA4 setup process for a UA
  // property.
  // Note: this has no effect on GA4 property.
  rpc SetAutomatedGa4ConfigurationOptOut(
      SetAutomatedGa4ConfigurationOptOutRequest)
      returns (SetAutomatedGa4ConfigurationOptOutResponse) {
    option (google.api.http) = {
      post: "/v1alpha/properties:setAutomatedGa4ConfigurationOptOut"
      body: "*"
    };
  }

  // Fetches the opt out status for the automated GA4 setup process for a UA
  // property.
  // Note: this has no effect on GA4 property.
  rpc FetchAutomatedGa4ConfigurationOptOut(
      FetchAutomatedGa4ConfigurationOptOutRequest)
      returns (FetchAutomatedGa4ConfigurationOptOutResponse) {
    option (google.api.http) = {
      post: "/v1alpha/properties:fetchAutomatedGa4ConfigurationOptOut"
      body: "*"
    };
  }

  // Creates a BigQueryLink.
  rpc CreateBigQueryLink(CreateBigQueryLinkRequest) returns (BigQueryLink) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/bigQueryLinks"
      body: "bigquery_link"
    };
    option (google.api.method_signature) = "parent,bigquery_link";
  }

  // Lookup for a single BigQuery Link.
  rpc GetBigQueryLink(GetBigQueryLinkRequest) returns (BigQueryLink) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/bigQueryLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists BigQuery Links on a property.
  rpc ListBigQueryLinks(ListBigQueryLinksRequest)
      returns (ListBigQueryLinksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/bigQueryLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a BigQueryLink on a property.
  rpc DeleteBigQueryLink(DeleteBigQueryLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/bigQueryLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a BigQueryLink.
  rpc UpdateBigQueryLink(UpdateBigQueryLinkRequest) returns (BigQueryLink) {
    option (google.api.http) = {
      patch: "/v1alpha/{bigquery_link.name=properties/*/bigQueryLinks/*}"
      body: "bigquery_link"
    };
    option (google.api.method_signature) = "bigquery_link,update_mask";
  }

  // Returns the enhanced measurement settings for this data stream.
  // Note that the stream must enable enhanced measurement for these settings to
  // take effect.
  rpc GetEnhancedMeasurementSettings(GetEnhancedMeasurementSettingsRequest)
      returns (EnhancedMeasurementSettings) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataStreams/*/enhancedMeasurementSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the enhanced measurement settings for this data stream.
  // Note that the stream must enable enhanced measurement for these settings to
  // take effect.
  rpc UpdateEnhancedMeasurementSettings(
      UpdateEnhancedMeasurementSettingsRequest)
      returns (EnhancedMeasurementSettings) {
    option (google.api.http) = {
      patch: "/v1alpha/{enhanced_measurement_settings.name=properties/*/dataStreams/*/enhancedMeasurementSettings}"
      body: "enhanced_measurement_settings"
    };
    option (google.api.method_signature) =
        "enhanced_measurement_settings,update_mask";
  }

  // Creates a connected site tag for a Universal Analytics property. You can
  // create a maximum of 20 connected site tags per property.
  // Note: This API cannot be used on GA4 properties.
  rpc CreateConnectedSiteTag(CreateConnectedSiteTagRequest)
      returns (CreateConnectedSiteTagResponse) {
    option (google.api.http) = {
      post: "/v1alpha/properties:createConnectedSiteTag"
      body: "*"
    };
  }

  // Deletes a connected site tag for a Universal Analytics property.
  // Note: this has no effect on GA4 properties.
  rpc DeleteConnectedSiteTag(DeleteConnectedSiteTagRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1alpha/properties:deleteConnectedSiteTag"
      body: "*"
    };
  }

  // Lists the connected site tags for a Universal Analytics property. A maximum
  // of 20 connected site tags will be returned. Note: this has no effect on GA4
  // property.
  rpc ListConnectedSiteTags(ListConnectedSiteTagsRequest)
      returns (ListConnectedSiteTagsResponse) {
    option (google.api.http) = {
      post: "/v1alpha/properties:listConnectedSiteTags"
      body: "*"
    };
  }

  // Given a specified UA property, looks up the GA4 property connected to it.
  // Note: this cannot be used with GA4 properties.
  rpc FetchConnectedGa4Property(FetchConnectedGa4PropertyRequest)
      returns (FetchConnectedGa4PropertyResponse) {
    option (google.api.http) = {
      get: "/v1alpha/properties:fetchConnectedGa4Property"
    };
  }

  // Looks up a single AdSenseLink.
  rpc GetAdSenseLink(GetAdSenseLinkRequest) returns (AdSenseLink) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/adSenseLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an AdSenseLink.
  rpc CreateAdSenseLink(CreateAdSenseLinkRequest) returns (AdSenseLink) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/adSenseLinks"
      body: "adsense_link"
    };
    option (google.api.method_signature) = "parent,adsense_link";
  }

  // Deletes an AdSenseLink.
  rpc DeleteAdSenseLink(DeleteAdSenseLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/adSenseLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists AdSenseLinks on a property.
  rpc ListAdSenseLinks(ListAdSenseLinksRequest)
      returns (ListAdSenseLinksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/adSenseLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lookup for a single EventCreateRule.
  rpc GetEventCreateRule(GetEventCreateRuleRequest) returns (EventCreateRule) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataStreams/*/eventCreateRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists EventCreateRules on a web data stream.
  rpc ListEventCreateRules(ListEventCreateRulesRequest)
      returns (ListEventCreateRulesResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*/dataStreams/*}/eventCreateRules"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an EventCreateRule.
  rpc CreateEventCreateRule(CreateEventCreateRuleRequest)
      returns (EventCreateRule) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*/dataStreams/*}/eventCreateRules"
      body: "event_create_rule"
    };
    option (google.api.method_signature) = "parent,event_create_rule";
  }

  // Updates an EventCreateRule.
  rpc UpdateEventCreateRule(UpdateEventCreateRuleRequest)
      returns (EventCreateRule) {
    option (google.api.http) = {
      patch: "/v1alpha/{event_create_rule.name=properties/*/dataStreams/*/eventCreateRules/*}"
      body: "event_create_rule"
    };
    option (google.api.method_signature) = "event_create_rule,update_mask";
  }

  // Deletes an EventCreateRule.
  rpc DeleteEventCreateRule(DeleteEventCreateRuleRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/dataStreams/*/eventCreateRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single EventEditRule.
  rpc GetEventEditRule(GetEventEditRuleRequest) returns (EventEditRule) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataStreams/*/eventEditRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists EventEditRules on a web data stream.
  rpc ListEventEditRules(ListEventEditRulesRequest)
      returns (ListEventEditRulesResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*/dataStreams/*}/eventEditRules"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an EventEditRule.
  rpc CreateEventEditRule(CreateEventEditRuleRequest) returns (EventEditRule) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*/dataStreams/*}/eventEditRules"
      body: "event_edit_rule"
    };
    option (google.api.method_signature) = "parent,event_edit_rule";
  }

  // Updates an EventEditRule.
  rpc UpdateEventEditRule(UpdateEventEditRuleRequest) returns (EventEditRule) {
    option (google.api.http) = {
      patch: "/v1alpha/{event_edit_rule.name=properties/*/dataStreams/*/eventEditRules/*}"
      body: "event_edit_rule"
    };
    option (google.api.method_signature) = "event_edit_rule,update_mask";
  }

  // Deletes an EventEditRule.
  rpc DeleteEventEditRule(DeleteEventEditRuleRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/dataStreams/*/eventEditRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Changes the processing order of event edit rules on the specified stream.
  rpc ReorderEventEditRules(ReorderEventEditRulesRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*/dataStreams/*}/eventEditRules:reorder"
      body: "*"
    };
  }

  // Updates a DataRedactionSettings on a property.
  rpc UpdateDataRedactionSettings(UpdateDataRedactionSettingsRequest)
      returns (DataRedactionSettings) {
    option (google.api.http) = {
      patch: "/v1alpha/{data_redaction_settings.name=properties/*/dataStreams/*/dataRedactionSettings}"
      body: "data_redaction_settings"
    };
    option (google.api.method_signature) =
        "data_redaction_settings,update_mask";
  }

  // Lookup for a single DataRedactionSettings.
  rpc GetDataRedactionSettings(GetDataRedactionSettingsRequest)
      returns (DataRedactionSettings) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/dataStreams/*/dataRedactionSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lookup for a single CalculatedMetric.
  rpc GetCalculatedMetric(GetCalculatedMetricRequest)
      returns (CalculatedMetric) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/calculatedMetrics/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a CalculatedMetric.
  rpc CreateCalculatedMetric(CreateCalculatedMetricRequest)
      returns (CalculatedMetric) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/calculatedMetrics"
      body: "calculated_metric"
    };
    option (google.api.method_signature) =
        "parent,calculated_metric,calculated_metric_id";
  }

  // Lists CalculatedMetrics on a property.
  rpc ListCalculatedMetrics(ListCalculatedMetricsRequest)
      returns (ListCalculatedMetricsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/calculatedMetrics"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a CalculatedMetric on a property.
  rpc UpdateCalculatedMetric(UpdateCalculatedMetricRequest)
      returns (CalculatedMetric) {
    option (google.api.http) = {
      patch: "/v1alpha/{calculated_metric.name=properties/*/calculatedMetrics/*}"
      body: "calculated_metric"
    };
    option (google.api.method_signature) = "calculated_metric,update_mask";
  }

  // Deletes a CalculatedMetric on a property.
  rpc DeleteCalculatedMetric(DeleteCalculatedMetricRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/calculatedMetrics/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a roll-up property and all roll-up property source links.
  rpc CreateRollupProperty(CreateRollupPropertyRequest)
      returns (CreateRollupPropertyResponse) {
    option (google.api.http) = {
      post: "/v1alpha/properties:createRollupProperty"
      body: "*"
    };
  }

  // Lookup for a single roll-up property source Link.
  // Only roll-up properties can have source links, so this method will throw an
  // error if used on other types of properties.
  rpc GetRollupPropertySourceLink(GetRollupPropertySourceLinkRequest)
      returns (RollupPropertySourceLink) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/rollupPropertySourceLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists roll-up property source Links on a property.
  // Only roll-up properties can have source links, so this method will throw an
  // error if used on other types of properties.
  rpc ListRollupPropertySourceLinks(ListRollupPropertySourceLinksRequest)
      returns (ListRollupPropertySourceLinksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/rollupPropertySourceLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a roll-up property source link.
  // Only roll-up properties can have source links, so this method will throw an
  // error if used on other types of properties.
  rpc CreateRollupPropertySourceLink(CreateRollupPropertySourceLinkRequest)
      returns (RollupPropertySourceLink) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/rollupPropertySourceLinks"
      body: "rollup_property_source_link"
    };
    option (google.api.method_signature) = "parent,rollup_property_source_link";
  }

  // Deletes a roll-up property source link.
  // Only roll-up properties can have source links, so this method will throw an
  // error if used on other types of properties.
  rpc DeleteRollupPropertySourceLink(DeleteRollupPropertySourceLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/rollupPropertySourceLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a subproperty and a subproperty event filter that applies to the
  // created subproperty.
  rpc ProvisionSubproperty(ProvisionSubpropertyRequest)
      returns (ProvisionSubpropertyResponse) {
    option (google.api.http) = {
      post: "/v1alpha/properties:provisionSubproperty"
      body: "*"
    };
  }

  // Creates a subproperty Event Filter.
  rpc CreateSubpropertyEventFilter(CreateSubpropertyEventFilterRequest)
      returns (SubpropertyEventFilter) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=properties/*}/subpropertyEventFilters"
      body: "subproperty_event_filter"
    };
    option (google.api.method_signature) = "parent,subproperty_event_filter";
  }

  // Lookup for a single subproperty Event Filter.
  rpc GetSubpropertyEventFilter(GetSubpropertyEventFilterRequest)
      returns (SubpropertyEventFilter) {
    option (google.api.http) = {
      get: "/v1alpha/{name=properties/*/subpropertyEventFilters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List all subproperty Event Filters on a property.
  rpc ListSubpropertyEventFilters(ListSubpropertyEventFiltersRequest)
      returns (ListSubpropertyEventFiltersResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=properties/*}/subpropertyEventFilters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a subproperty Event Filter.
  rpc UpdateSubpropertyEventFilter(UpdateSubpropertyEventFilterRequest)
      returns (SubpropertyEventFilter) {
    option (google.api.http) = {
      patch: "/v1alpha/{subproperty_event_filter.name=properties/*/subpropertyEventFilters/*}"
      body: "subproperty_event_filter"
    };
    option (google.api.method_signature) =
        "subproperty_event_filter,update_mask";
  }

  // Deletes a subproperty event filter.
  rpc DeleteSubpropertyEventFilter(DeleteSubpropertyEventFilterRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=properties/*/subpropertyEventFilters/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// The request for a Data Access Record Report.
message RunAccessReportRequest {
  // The Data Access Report supports requesting at the property level or account
  // level. If requested at the account level, Data Access Reports include all
  // access for all properties under that account.
  //
  // To request at the property level, entity should be for example
  // 'properties/123' if "123" is your GA4 property ID. To request at the
  // account level, entity should be for example 'accounts/1234' if "1234" is
  // your GA4 Account ID.
  string entity = 1;

  // The dimensions requested and displayed in the response. Requests are
  // allowed up to 9 dimensions.
  repeated AccessDimension dimensions = 2;

  // The metrics requested and displayed in the response. Requests are allowed
  // up to 10 metrics.
  repeated AccessMetric metrics = 3;

  // Date ranges of access records to read. If multiple date ranges are
  // requested, each response row will contain a zero based date range index. If
  // two date ranges overlap, the access records for the overlapping days is
  // included in the response rows for both date ranges. Requests are allowed up
  // to 2 date ranges.
  repeated AccessDateRange date_ranges = 4;

  // Dimension filters let you restrict report response to specific
  // dimension values which match the filter. For example, filtering on access
  // records of a single user. To learn more, see [Fundamentals of Dimension
  // Filters](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#dimension_filters)
  // for examples. Metrics cannot be used in this filter.
  AccessFilterExpression dimension_filter = 5;

  // Metric filters allow you to restrict report response to specific metric
  // values which match the filter. Metric filters are applied after aggregating
  // the report's rows, similar to SQL having-clause. Dimensions cannot be used
  // in this filter.
  AccessFilterExpression metric_filter = 6;

  // The row count of the start row. The first row is counted as row 0. If
  // offset is unspecified, it is treated as 0. If offset is zero, then this
  // method will return the first page of results with `limit` entries.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 offset = 7;

  // The number of rows to return. If unspecified, 10,000 rows are returned. The
  // API returns a maximum of 100,000 rows per request, no matter how many you
  // ask for. `limit` must be positive.
  //
  // The API may return fewer rows than the requested `limit`, if there aren't
  // as many remaining rows as the `limit`. For instance, there are fewer than
  // 300 possible values for the dimension `country`, so when reporting on only
  // `country`, you can't get more than 300 rows, even if you set `limit` to a
  // higher value.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int64 limit = 8;

  // This request's time zone if specified. If unspecified, the property's time
  // zone is used. The request's time zone is used to interpret the start & end
  // dates of the report.
  //
  // Formatted as strings from the IANA Time Zone database
  // (https://www.iana.org/time-zones); for example "America/New_York" or
  // "Asia/Tokyo".
  string time_zone = 9;

  // Specifies how rows are ordered in the response.
  repeated AccessOrderBy order_bys = 10;

  // Toggles whether to return the current state of this Analytics Property's
  // quota. Quota is returned in [AccessQuota](#AccessQuota). For account-level
  // requests, this field must be false.
  bool return_entity_quota = 11;

  // Optional. Determines whether to include users who have never made an API
  // call in the response. If true, all users with access to the specified
  // property or account are included in the response, regardless of whether
  // they have made an API call or not. If false, only the users who have made
  // an API call will be included.
  bool include_all_users = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Decides whether to return the users within user groups. This
  // field works only when include_all_users is set to true. If true, it will
  // return all users with access to the specified property or account.
  // If false, only the users with direct access will be returned.
  bool expand_groups = 13 [(google.api.field_behavior) = OPTIONAL];
}

// The customized Data Access Record Report response.
message RunAccessReportResponse {
  // The header for a column in the report that corresponds to a specific
  // dimension. The number of DimensionHeaders and ordering of DimensionHeaders
  // matches the dimensions present in rows.
  repeated AccessDimensionHeader dimension_headers = 1;

  // The header for a column in the report that corresponds to a specific
  // metric. The number of MetricHeaders and ordering of MetricHeaders matches
  // the metrics present in rows.
  repeated AccessMetricHeader metric_headers = 2;

  // Rows of dimension value combinations and metric values in the report.
  repeated AccessRow rows = 3;

  // The total number of rows in the query result. `rowCount` is independent of
  // the number of rows returned in the response, the `limit` request
  // parameter, and the `offset` request parameter. For example if a query
  // returns 175 rows and includes `limit` of 50 in the API request, the
  // response will contain `rowCount` of 175 but only 50 rows.
  //
  // To learn more about this pagination parameter, see
  // [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).
  int32 row_count = 4;

  // The quota state for this Analytics property including this request. This
  // field doesn't work with account-level requests.
  AccessQuota quota = 5;
}

// Request message for GetAccount RPC.
message GetAccountRequest {
  // Required. The name of the account to lookup.
  // Format: accounts/{account}
  // Example: "accounts/100"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];
}

// Request message for ListAccounts RPC.
message ListAccountsRequest {
  // The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 1;

  // A page token, received from a previous `ListAccounts` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListAccounts` must
  // match the call that provided the page token.
  string page_token = 2;

  // Whether to include soft-deleted (ie: "trashed") Accounts in the
  // results. Accounts can be inspected to determine whether they are deleted or
  // not.
  bool show_deleted = 3;
}

// Request message for ListAccounts RPC.
message ListAccountsResponse {
  // Results that were accessible to the caller.
  repeated Account accounts = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for DeleteAccount RPC.
message DeleteAccountRequest {
  // Required. The name of the Account to soft-delete.
  // Format: accounts/{account}
  // Example: "accounts/100"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];
}

// Request message for UpdateAccount RPC.
message UpdateAccountRequest {
  // Required. The account to update.
  // The account's `name` field is used to identify the account.
  Account account = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (for example, "field_to_update"). Omitted fields will not be updated.
  // To replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ProvisionAccountTicket RPC.
message ProvisionAccountTicketRequest {
  // The account to create.
  Account account = 1;

  // Redirect URI where the user will be sent after accepting Terms of Service.
  // Must be configured in Cloud Console as a Redirect URI.
  string redirect_uri = 2;
}

// Response message for ProvisionAccountTicket RPC.
message ProvisionAccountTicketResponse {
  // The param to be passed in the ToS link.
  string account_ticket_id = 1;
}

// Request message for GetProperty RPC.
message GetPropertyRequest {
  // Required. The name of the property to lookup.
  // Format: properties/{property_id}
  // Example: "properties/1000"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];
}

// Request message for ListProperties RPC.
message ListPropertiesRequest {
  // Required. An expression for filtering the results of the request.
  // Fields eligible for filtering are:
  // `parent:`(The resource name of the parent account/property) or
  // `ancestor:`(The resource name of the parent account) or
  // `firebase_project:`(The id or number of the linked firebase project).
  // Some examples of filters:
  //
  // ```
  // | Filter                      | Description                               |
  // |-----------------------------|-------------------------------------------|
  // | parent:accounts/123         | The account with account id: 123.       |
  // | parent:properties/123       | The property with property id: 123.       |
  // | ancestor:accounts/123       | The account with account id: 123.         |
  // | firebase_project:project-id | The firebase project with id: project-id. |
  // | firebase_project:123        | The firebase project with number: 123.    |
  // ```
  string filter = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListProperties` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListProperties` must
  // match the call that provided the page token.
  string page_token = 3;

  // Whether to include soft-deleted (ie: "trashed") Properties in the
  // results. Properties can be inspected to determine whether they are deleted
  // or not.
  bool show_deleted = 4;
}

// Response message for ListProperties RPC.
message ListPropertiesResponse {
  // Results that matched the filter criteria and were accessible to the caller.
  repeated Property properties = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for UpdateProperty RPC.
message UpdatePropertyRequest {
  // Required. The property to update.
  // The property's `name` field is used to identify the property to be
  // updated.
  Property property = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateProperty RPC.
message CreatePropertyRequest {
  // Required. The property to create.
  // Note: the supplied property must specify its parent.
  Property property = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteProperty RPC.
message DeletePropertyRequest {
  // Required. The name of the Property to soft-delete.
  // Format: properties/{property_id}
  // Example: "properties/1000"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];
}

// Request message for CreateFirebaseLink RPC
message CreateFirebaseLinkRequest {
  // Required. Format: properties/{property_id}
  //
  // Example: `properties/1234`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/FirebaseLink"
    }
  ];

  // Required. The Firebase link to create.
  FirebaseLink firebase_link = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteFirebaseLink RPC
message DeleteFirebaseLinkRequest {
  // Required. Format: properties/{property_id}/firebaseLinks/{firebase_link_id}
  //
  // Example: `properties/1234/firebaseLinks/5678`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/FirebaseLink"
    }
  ];
}

// Request message for ListFirebaseLinks RPC
message ListFirebaseLinksRequest {
  // Required. Format: properties/{property_id}
  //
  // Example: `properties/1234`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/FirebaseLink"
    }
  ];

  // The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListFirebaseLinks` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListFirebaseLinks` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListFirebaseLinks RPC
message ListFirebaseLinksResponse {
  // List of FirebaseLinks. This will have at most one value.
  repeated FirebaseLink firebase_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  // Currently, Google Analytics supports only one FirebaseLink per property,
  // so this will never be populated.
  string next_page_token = 2;
}

// Request message for GetGlobalSiteTag RPC.
message GetGlobalSiteTagRequest {
  // Required. The name of the site tag to lookup.
  // Note that site tags are singletons and do not have unique IDs.
  // Format: properties/{property_id}/dataStreams/{stream_id}/globalSiteTag
  //
  // Example: `properties/123/dataStreams/456/globalSiteTag`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/GlobalSiteTag"
    }
  ];
}

// Request message for CreateGoogleAdsLink RPC
message CreateGoogleAdsLinkRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/GoogleAdsLink"
    }
  ];

  // Required. The GoogleAdsLink to create.
  GoogleAdsLink google_ads_link = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateGoogleAdsLink RPC
message UpdateGoogleAdsLinkRequest {
  // The GoogleAdsLink to update
  GoogleAdsLink google_ads_link = 1;

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteGoogleAdsLink RPC.
message DeleteGoogleAdsLinkRequest {
  // Required. Example format: properties/1234/googleAdsLinks/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/GoogleAdsLink"
    }
  ];
}

// Request message for ListGoogleAdsLinks RPC.
message ListGoogleAdsLinksRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/GoogleAdsLink"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListGoogleAdsLinks` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListGoogleAdsLinks` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListGoogleAdsLinks RPC.
message ListGoogleAdsLinksResponse {
  // List of GoogleAdsLinks.
  repeated GoogleAdsLink google_ads_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetDataSharingSettings RPC.
message GetDataSharingSettingsRequest {
  // Required. The name of the settings to lookup.
  // Format: accounts/{account}/dataSharingSettings
  //
  // Example: `accounts/1000/dataSharingSettings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataSharingSettings"
    }
  ];
}

// Request message for ListAccountSummaries RPC.
message ListAccountSummariesRequest {
  // The maximum number of AccountSummary resources to return. The service may
  // return fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 1;

  // A page token, received from a previous `ListAccountSummaries` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListAccountSummaries`
  // must match the call that provided the page token.
  string page_token = 2;
}

// Response message for ListAccountSummaries RPC.
message ListAccountSummariesResponse {
  // Account summaries of all accounts the caller has access to.
  repeated AccountSummary account_summaries = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for AcknowledgeUserDataCollection RPC.
message AcknowledgeUserDataCollectionRequest {
  // Required. The property for which to acknowledge user data collection.
  string property = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];

  // Required. An acknowledgement that the caller of this method understands the
  // terms of user data collection.
  //
  // This field must contain the exact value:
  // "I acknowledge that I have the necessary privacy disclosures and rights
  // from my end users for the collection and processing of their data,
  // including the association of such data with the visitation information
  // Google Analytics collects from my site and/or app property."
  string acknowledgement = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for AcknowledgeUserDataCollection RPC.
message AcknowledgeUserDataCollectionResponse {}

// Request message for SearchChangeHistoryEvents RPC.
message SearchChangeHistoryEventsRequest {
  // Required. The account resource for which to return change history
  // resources. Format: accounts/{account}
  //
  // Example: `accounts/100`
  string account = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];

  // Optional. Resource name for a child property. If set, only return changes
  // made to this property or its child resources.
  // Format: properties/{propertyId}
  //
  // Example: `properties/100`
  string property = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];

  // Optional. If set, only return changes if they are for a resource that
  // matches at least one of these types.
  repeated ChangeHistoryResourceType resource_type = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, only return changes that match one or more of these types
  // of actions.
  repeated ActionType action = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, only return changes if they are made by a user in this
  // list.
  repeated string actor_email = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, only return changes made after this time (inclusive).
  google.protobuf.Timestamp earliest_change_time = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, only return changes made before this time (inclusive).
  google.protobuf.Timestamp latest_change_time = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of ChangeHistoryEvent items to return.
  // The service may return fewer than this value, even if there are additional
  // pages. If unspecified, at most 50 items will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `SearchChangeHistoryEvents` call. Provide this to retrieve the subsequent
  // page. When paginating, all other parameters provided to
  // `SearchChangeHistoryEvents` must match the call that provided the page
  // token.
  string page_token = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for SearchAccounts RPC.
message SearchChangeHistoryEventsResponse {
  // Results that were accessible to the caller.
  repeated ChangeHistoryEvent change_history_events = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetMeasurementProtocolSecret RPC.
message GetMeasurementProtocolSecretRequest {
  // Required. The name of the measurement protocol secret to lookup.
  // Format:
  // properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    }
  ];
}

// Request message for CreateMeasurementProtocolSecret RPC
message CreateMeasurementProtocolSecretRequest {
  // Required. The parent resource where this secret will be created.
  // Format: properties/{property}/dataStreams/{dataStream}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    }
  ];

  // Required. The measurement protocol secret to create.
  MeasurementProtocolSecret measurement_protocol_secret = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteMeasurementProtocolSecret RPC
message DeleteMeasurementProtocolSecretRequest {
  // Required. The name of the MeasurementProtocolSecret to delete.
  // Format:
  // properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    }
  ];
}

// Request message for UpdateMeasurementProtocolSecret RPC
message UpdateMeasurementProtocolSecretRequest {
  // Required. The measurement protocol secret to update.
  MeasurementProtocolSecret measurement_protocol_secret = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListMeasurementProtocolSecret RPC
message ListMeasurementProtocolSecretsRequest {
  // Required. The resource name of the parent stream.
  // Format:
  // properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 10 resources will be returned.
  // The maximum value is 10. Higher values will be coerced to the maximum.
  int32 page_size = 2;

  // A page token, received from a previous `ListMeasurementProtocolSecrets`
  // call. Provide this to retrieve the subsequent page. When paginating, all
  // other parameters provided to `ListMeasurementProtocolSecrets` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Response message for ListMeasurementProtocolSecret RPC
message ListMeasurementProtocolSecretsResponse {
  // A list of secrets for the parent stream specified in the request.
  repeated MeasurementProtocolSecret measurement_protocol_secrets = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetSKAdNetworkConversionValueSchema RPC.
message GetSKAdNetworkConversionValueSchemaRequest {
  // Required. The resource name of SKAdNetwork conversion value schema to look
  // up. Format:
  // properties/{property}/dataStreams/{dataStream}/sKAdNetworkConversionValueSchema/{skadnetwork_conversion_value_schema}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/SKAdNetworkConversionValueSchema"
    }
  ];
}

// Request message for CreateSKAdNetworkConversionValueSchema RPC.
message CreateSKAdNetworkConversionValueSchemaRequest {
  // Required. The parent resource where this schema will be created.
  // Format: properties/{property}/dataStreams/{dataStream}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/SKAdNetworkConversionValueSchema"
    }
  ];

  // Required. SKAdNetwork conversion value schema to create.
  SKAdNetworkConversionValueSchema skadnetwork_conversion_value_schema = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteSKAdNetworkConversionValueSchema RPC.
message DeleteSKAdNetworkConversionValueSchemaRequest {
  // Required. The name of the SKAdNetworkConversionValueSchema to delete.
  // Format:
  // properties/{property}/dataStreams/{dataStream}/sKAdNetworkConversionValueSchema/{skadnetwork_conversion_value_schema}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/SKAdNetworkConversionValueSchema"
    }
  ];
}

// Request message for UpdateSKAdNetworkConversionValueSchema RPC.
message UpdateSKAdNetworkConversionValueSchemaRequest {
  // Required. SKAdNetwork conversion value schema to update.
  SKAdNetworkConversionValueSchema skadnetwork_conversion_value_schema = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListSKAdNetworkConversionValueSchemas RPC
message ListSKAdNetworkConversionValueSchemasRequest {
  // Required. The DataStream resource to list schemas for.
  // Format:
  // properties/{property_id}/dataStreams/{dataStream}
  // Example: properties/1234/dataStreams/5678
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/SKAdNetworkConversionValueSchema"
    }
  ];

  // The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous
  // `ListSKAdNetworkConversionValueSchemas` call. Provide this to retrieve the
  // subsequent page. When paginating, all other parameters provided to
  // `ListSKAdNetworkConversionValueSchema` must match the call that provided
  // the page token.
  string page_token = 3;
}

// Response message for ListSKAdNetworkConversionValueSchemas RPC
message ListSKAdNetworkConversionValueSchemasResponse {
  // List of SKAdNetworkConversionValueSchemas. This will have at most one
  // value.
  repeated SKAdNetworkConversionValueSchema
      skadnetwork_conversion_value_schemas = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  // Currently, Google Analytics supports only one
  // SKAdNetworkConversionValueSchema per dataStream, so this will never be
  // populated.
  string next_page_token = 2;
}

// Request message for GetGoogleSignalsSettings RPC
message GetGoogleSignalsSettingsRequest {
  // Required. The name of the google signals settings to retrieve.
  // Format: properties/{property}/googleSignalsSettings
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/GoogleSignalsSettings"
    }
  ];
}

// Request message for UpdateGoogleSignalsSettings RPC
message UpdateGoogleSignalsSettingsRequest {
  // Required. The settings to update.
  // The `name` field is used to identify the settings to be updated.
  GoogleSignalsSettings google_signals_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateConversionEvent RPC
message CreateConversionEventRequest {
  // Required. The conversion event to create.
  ConversionEvent conversion_event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the parent property where this conversion
  // event will be created. Format: properties/123
  string parent = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/ConversionEvent"
    }
  ];
}

// Request message for UpdateConversionEvent RPC
message UpdateConversionEventRequest {
  // Required. The conversion event to update.
  // The `name` field is used to identify the settings to be updated.
  ConversionEvent conversion_event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetConversionEvent RPC
message GetConversionEventRequest {
  // Required. The resource name of the conversion event to retrieve.
  // Format: properties/{property}/conversionEvents/{conversion_event}
  // Example: "properties/123/conversionEvents/456"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/ConversionEvent"
    }
  ];
}

// Request message for DeleteConversionEvent RPC
message DeleteConversionEventRequest {
  // Required. The resource name of the conversion event to delete.
  // Format: properties/{property}/conversionEvents/{conversion_event}
  // Example: "properties/123/conversionEvents/456"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/ConversionEvent"
    }
  ];
}

// Request message for ListConversionEvents RPC
message ListConversionEventsRequest {
  // Required. The resource name of the parent property.
  // Example: 'properties/123'
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/ConversionEvent"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListConversionEvents` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListConversionEvents`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListConversionEvents RPC.
message ListConversionEventsResponse {
  // The requested conversion events
  repeated ConversionEvent conversion_events = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateKeyEvent RPC
message CreateKeyEventRequest {
  // Required. The Key Event to create.
  KeyEvent key_event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the parent property where this Key Event
  // will be created. Format: properties/123
  string parent = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/KeyEvent"
    }
  ];
}

// Request message for UpdateKeyEvent RPC
message UpdateKeyEventRequest {
  // Required. The Key Event to update.
  // The `name` field is used to identify the settings to be updated.
  KeyEvent key_event = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetKeyEvent RPC
message GetKeyEventRequest {
  // Required. The resource name of the Key Event to retrieve.
  // Format: properties/{property}/keyEvents/{key_event}
  // Example: "properties/123/keyEvents/456"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/KeyEvent"
    }
  ];
}

// Request message for DeleteKeyEvent RPC
message DeleteKeyEventRequest {
  // Required. The resource name of the Key Event to delete.
  // Format: properties/{property}/keyEvents/{key_event}
  // Example: "properties/123/keyEvents/456"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/KeyEvent"
    }
  ];
}

// Request message for ListKeyEvents RPC
message ListKeyEventsRequest {
  // Required. The resource name of the parent property.
  // Example: 'properties/123'
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/KeyEvent"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListKeyEvents` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListKeyEvents`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListKeyEvents RPC.
message ListKeyEventsResponse {
  // The requested Key Events
  repeated KeyEvent key_events = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetDisplayVideo360AdvertiserLink RPC.
message GetDisplayVideo360AdvertiserLinkRequest {
  // Required. The name of the DisplayVideo360AdvertiserLink to get.
  // Example format: properties/1234/displayVideo360AdvertiserLink/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLink"
    }
  ];
}

// Request message for ListDisplayVideo360AdvertiserLinks RPC.
message ListDisplayVideo360AdvertiserLinksRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLink"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListDisplayVideo360AdvertiserLinks`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListDisplayVideo360AdvertiserLinks` must match the call that provided the
  // page token.
  string page_token = 3;
}

// Response message for ListDisplayVideo360AdvertiserLinks RPC.
message ListDisplayVideo360AdvertiserLinksResponse {
  // List of DisplayVideo360AdvertiserLinks.
  repeated DisplayVideo360AdvertiserLink display_video_360_advertiser_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateDisplayVideo360AdvertiserLink RPC.
message CreateDisplayVideo360AdvertiserLinkRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLink"
    }
  ];

  // Required. The DisplayVideo360AdvertiserLink to create.
  DisplayVideo360AdvertiserLink display_video_360_advertiser_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteDisplayVideo360AdvertiserLink RPC.
message DeleteDisplayVideo360AdvertiserLinkRequest {
  // Required. The name of the DisplayVideo360AdvertiserLink to delete.
  // Example format: properties/1234/displayVideo360AdvertiserLinks/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLink"
    }
  ];
}

// Request message for UpdateDisplayVideo360AdvertiserLink RPC.
message UpdateDisplayVideo360AdvertiserLinkRequest {
  // The DisplayVideo360AdvertiserLink to update
  DisplayVideo360AdvertiserLink display_video_360_advertiser_link = 1;

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetDisplayVideo360AdvertiserLinkProposal RPC.
message GetDisplayVideo360AdvertiserLinkProposalRequest {
  // Required. The name of the DisplayVideo360AdvertiserLinkProposal to get.
  // Example format: properties/1234/displayVideo360AdvertiserLinkProposals/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLinkProposal"
    }
  ];
}

// Request message for ListDisplayVideo360AdvertiserLinkProposals RPC.
message ListDisplayVideo360AdvertiserLinkProposalsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLinkProposal"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous
  // `ListDisplayVideo360AdvertiserLinkProposals` call. Provide this to retrieve
  // the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListDisplayVideo360AdvertiserLinkProposals` must match the call that
  // provided the page token.
  string page_token = 3;
}

// Response message for ListDisplayVideo360AdvertiserLinkProposals RPC.
message ListDisplayVideo360AdvertiserLinkProposalsResponse {
  // List of DisplayVideo360AdvertiserLinkProposals.
  repeated DisplayVideo360AdvertiserLinkProposal
      display_video_360_advertiser_link_proposals = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateDisplayVideo360AdvertiserLinkProposal RPC.
message CreateDisplayVideo360AdvertiserLinkProposalRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLinkProposal"
    }
  ];

  // Required. The DisplayVideo360AdvertiserLinkProposal to create.
  DisplayVideo360AdvertiserLinkProposal
      display_video_360_advertiser_link_proposal = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteDisplayVideo360AdvertiserLinkProposal RPC.
message DeleteDisplayVideo360AdvertiserLinkProposalRequest {
  // Required. The name of the DisplayVideo360AdvertiserLinkProposal to delete.
  // Example format: properties/1234/displayVideo360AdvertiserLinkProposals/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLinkProposal"
    }
  ];
}

// Request message for ApproveDisplayVideo360AdvertiserLinkProposal RPC.
message ApproveDisplayVideo360AdvertiserLinkProposalRequest {
  // Required. The name of the DisplayVideo360AdvertiserLinkProposal to approve.
  // Example format: properties/1234/displayVideo360AdvertiserLinkProposals/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLinkProposal"
    }
  ];
}

// Response message for ApproveDisplayVideo360AdvertiserLinkProposal RPC.
message ApproveDisplayVideo360AdvertiserLinkProposalResponse {
  // The DisplayVideo360AdvertiserLink created as a result of approving the
  // proposal.
  DisplayVideo360AdvertiserLink display_video_360_advertiser_link = 1;
}

// Request message for CancelDisplayVideo360AdvertiserLinkProposal RPC.
message CancelDisplayVideo360AdvertiserLinkProposalRequest {
  // Required. The name of the DisplayVideo360AdvertiserLinkProposal to cancel.
  // Example format: properties/1234/displayVideo360AdvertiserLinkProposals/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLinkProposal"
    }
  ];
}

// Request message for GetSearchAds360Link RPC.
message GetSearchAds360LinkRequest {
  // Required. The name of the SearchAds360Link to get.
  // Example format: properties/1234/SearchAds360Link/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/SearchAds360Link"
    }
  ];
}

// Request message for ListSearchAds360Links RPC.
message ListSearchAds360LinksRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/SearchAds360Link"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListSearchAds360Links`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListSearchAds360Links` must match the call that provided the
  // page token.
  string page_token = 3;
}

// Response message for ListSearchAds360Links RPC.
message ListSearchAds360LinksResponse {
  // List of SearchAds360Links.
  repeated SearchAds360Link search_ads_360_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateSearchAds360Link RPC.
message CreateSearchAds360LinkRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/SearchAds360Link"
    }
  ];

  // Required. The SearchAds360Link to create.
  SearchAds360Link search_ads_360_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteSearchAds360Link RPC.
message DeleteSearchAds360LinkRequest {
  // Required. The name of the SearchAds360Link to delete.
  // Example format: properties/1234/SearchAds360Links/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/SearchAds360Link"
    }
  ];
}

// Request message for UpdateSearchAds360Link RPC.
message UpdateSearchAds360LinkRequest {
  // The SearchAds360Link to update
  SearchAds360Link search_ads_360_link = 1;

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateCustomDimension RPC.
message CreateCustomDimensionRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CustomDimension"
    }
  ];

  // Required. The CustomDimension to create.
  CustomDimension custom_dimension = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateCustomDimension RPC.
message UpdateCustomDimensionRequest {
  // The CustomDimension to update
  CustomDimension custom_dimension = 1;

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListCustomDimensions RPC.
message ListCustomDimensionsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CustomDimension"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListCustomDimensions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCustomDimensions`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListCustomDimensions RPC.
message ListCustomDimensionsResponse {
  // List of CustomDimensions.
  repeated CustomDimension custom_dimensions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for ArchiveCustomDimension RPC.
message ArchiveCustomDimensionRequest {
  // Required. The name of the CustomDimension to archive.
  // Example format: properties/1234/customDimensions/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CustomDimension"
    }
  ];
}

// Request message for GetCustomDimension RPC.
message GetCustomDimensionRequest {
  // Required. The name of the CustomDimension to get.
  // Example format: properties/1234/customDimensions/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CustomDimension"
    }
  ];
}

// Request message for CreateCustomMetric RPC.
message CreateCustomMetricRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CustomMetric"
    }
  ];

  // Required. The CustomMetric to create.
  CustomMetric custom_metric = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateCustomMetric RPC.
message UpdateCustomMetricRequest {
  // The CustomMetric to update
  CustomMetric custom_metric = 1;

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListCustomMetrics RPC.
message ListCustomMetricsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CustomMetric"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListCustomMetrics` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCustomMetrics` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListCustomMetrics RPC.
message ListCustomMetricsResponse {
  // List of CustomMetrics.
  repeated CustomMetric custom_metrics = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for ArchiveCustomMetric RPC.
message ArchiveCustomMetricRequest {
  // Required. The name of the CustomMetric to archive.
  // Example format: properties/1234/customMetrics/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CustomMetric"
    }
  ];
}

// Request message for GetCustomMetric RPC.
message GetCustomMetricRequest {
  // Required. The name of the CustomMetric to get.
  // Example format: properties/1234/customMetrics/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CustomMetric"
    }
  ];
}

// Request message for CreateCalculatedMetric RPC.
message CreateCalculatedMetricRequest {
  // Required. Format: properties/{property_id}
  // Example: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CalculatedMetric"
    }
  ];

  // Required. The ID to use for the calculated metric which will become the
  // final component of the calculated metric's resource name.
  //
  // This value should be 1-80 characters and valid characters are
  // /[a-zA-Z0-9_]/, no spaces allowed. calculated_metric_id must be unique
  // between all calculated metrics under a property. The calculated_metric_id
  // is used when referencing this calculated metric from external APIs, for
  // example, "calcMetric:{calculated_metric_id}".
  string calculated_metric_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The CalculatedMetric to create.
  CalculatedMetric calculated_metric = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateCalculatedMetric RPC.
message UpdateCalculatedMetricRequest {
  // Required. The CalculatedMetric to update
  CalculatedMetric calculated_metric = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteCalculatedMetric RPC.
message DeleteCalculatedMetricRequest {
  // Required. The name of the CalculatedMetric to delete.
  // Format: properties/{property_id}/calculatedMetrics/{calculated_metric_id}
  // Example: properties/1234/calculatedMetrics/Metric01
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CalculatedMetric"
    }
  ];
}

// Request message for ListCalculatedMetrics RPC.
message ListCalculatedMetricsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/CalculatedMetric"
    }
  ];

  // Optional. The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListCalculatedMetrics`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCalculatedMetrics`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListCalculatedMetrics RPC.
message ListCalculatedMetricsResponse {
  // List of CalculatedMetrics.
  repeated CalculatedMetric calculated_metrics = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetCalculatedMetric RPC.
message GetCalculatedMetricRequest {
  // Required. The name of the CalculatedMetric to get.
  // Format: properties/{property_id}/calculatedMetrics/{calculated_metric_id}
  // Example: properties/1234/calculatedMetrics/Metric01
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/CalculatedMetric"
    }
  ];
}

// Request message for GetDataRetentionSettings RPC.
message GetDataRetentionSettingsRequest {
  // Required. The name of the settings to lookup.
  // Format:
  // properties/{property}/dataRetentionSettings
  // Example: "properties/1000/dataRetentionSettings"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataRetentionSettings"
    }
  ];
}

// Request message for UpdateDataRetentionSettings RPC.
message UpdateDataRetentionSettingsRequest {
  // Required. The settings to update.
  // The `name` field is used to identify the settings to be updated.
  DataRetentionSettings data_retention_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateDataStream RPC.
message CreateDataStreamRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/DataStream"
    }
  ];

  // Required. The DataStream to create.
  DataStream data_stream = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteDataStream RPC.
message DeleteDataStreamRequest {
  // Required. The name of the DataStream to delete.
  // Example format: properties/1234/dataStreams/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataStream"
    }
  ];
}

// Request message for UpdateDataStream RPC.
message UpdateDataStreamRequest {
  // The DataStream to update
  DataStream data_stream = 1;

  // Required. The list of fields to be updated. Omitted fields will not be
  // updated. To replace the entire entity, use one path with the string "*" to
  // match all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListDataStreams RPC.
message ListDataStreamsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/DataStream"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListDataStreams` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDataStreams` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListDataStreams RPC.
message ListDataStreamsResponse {
  // List of DataStreams.
  repeated DataStream data_streams = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for GetDataStream RPC.
message GetDataStreamRequest {
  // Required. The name of the DataStream to get.
  // Example format: properties/1234/dataStreams/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataStream"
    }
  ];
}

// Request message for GetAudience RPC.
message GetAudienceRequest {
  // Required. The name of the Audience to get.
  // Example format: properties/1234/audiences/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Audience"
    }
  ];
}

// Request message for ListAudiences RPC.
message ListAudiencesRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/Audience"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListAudiences` call. Provide this
  // to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAudiences` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListAudiences RPC.
message ListAudiencesResponse {
  // List of Audiences.
  repeated Audience audiences = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateAudience RPC.
message CreateAudienceRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/Audience"
    }
  ];

  // Required. The audience to create.
  Audience audience = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateAudience RPC.
message UpdateAudienceRequest {
  // Required. The audience to update.
  // The audience's `name` field is used to identify the audience to be updated.
  Audience audience = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ArchiveAudience RPC.
message ArchiveAudienceRequest {
  // Required. Example format: properties/1234/audiences/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/Audience"
    }
  ];
}

// Request message for GetAttributionSettings RPC.
message GetAttributionSettingsRequest {
  // Required. The name of the attribution settings to retrieve.
  // Format: properties/{property}/attributionSettings
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/AttributionSettings"
    }
  ];
}

// Request message for UpdateAttributionSettings RPC
message UpdateAttributionSettingsRequest {
  // Required. The attribution settings to update.
  // The `name` field is used to identify the settings to be updated.
  AttributionSettings attribution_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetAccessBinding RPC.
message GetAccessBindingRequest {
  // Required. The name of the access binding to retrieve.
  // Formats:
  // - accounts/{account}/accessBindings/{accessBinding}
  // - properties/{property}/accessBindings/{accessBinding}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];
}

// Request message for BatchGetAccessBindings RPC.
message BatchGetAccessBindingsRequest {
  // Required. The account or property that owns the access bindings. The parent
  // of all provided values for the 'names' field must match this field.
  // Formats:
  // - accounts/{account}
  // - properties/{property}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];

  // Required. The names of the access bindings to retrieve.
  // A maximum of 1000 access bindings can be retrieved in a batch.
  // Formats:
  // - accounts/{account}/accessBindings/{accessBinding}
  // - properties/{property}/accessBindings/{accessBinding}
  repeated string names = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];
}

// Response message for BatchGetAccessBindings RPC.
message BatchGetAccessBindingsResponse {
  // The requested access bindings.
  repeated AccessBinding access_bindings = 1;
}

// Request message for ListAccessBindings RPC.
message ListAccessBindingsRequest {
  // Required. Formats:
  // - accounts/{account}
  // - properties/{property}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];

  // The maximum number of access bindings to return.
  // The service may return fewer than this value.
  // If unspecified, at most 200 access bindings will be returned.
  // The maximum value is 500; values above 500 will be coerced to 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListAccessBindings` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListAccessBindings` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListAccessBindings RPC.
message ListAccessBindingsResponse {
  // List of AccessBindings. These will be ordered stably, but in an arbitrary
  // order.
  repeated AccessBinding access_bindings = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateAccessBinding RPC.
message CreateAccessBindingRequest {
  // Required. Formats:
  // - accounts/{account}
  // - properties/{property}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];

  // Required. The access binding to create.
  AccessBinding access_binding = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for BatchCreateAccessBindings RPC.
message BatchCreateAccessBindingsRequest {
  // Required. The account or property that owns the access bindings. The parent
  // field in the CreateAccessBindingRequest messages must either be empty or
  // match this field. Formats:
  // - accounts/{account}
  // - properties/{property}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];

  // Required. The requests specifying the access bindings to create.
  // A maximum of 1000 access bindings can be created in a batch.
  repeated CreateAccessBindingRequest requests = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for BatchCreateAccessBindings RPC.
message BatchCreateAccessBindingsResponse {
  // The access bindings created.
  repeated AccessBinding access_bindings = 1;
}

// Request message for UpdateAccessBinding RPC.
message UpdateAccessBindingRequest {
  // Required. The access binding to update.
  AccessBinding access_binding = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for BatchUpdateAccessBindings RPC.
message BatchUpdateAccessBindingsRequest {
  // Required. The account or property that owns the access bindings. The parent
  // of all provided AccessBinding in UpdateAccessBindingRequest messages must
  // match this field.
  // Formats:
  // - accounts/{account}
  // - properties/{property}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];

  // Required. The requests specifying the access bindings to update.
  // A maximum of 1000 access bindings can be updated in a batch.
  repeated UpdateAccessBindingRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for BatchUpdateAccessBindings RPC.
message BatchUpdateAccessBindingsResponse {
  // The access bindings updated.
  repeated AccessBinding access_bindings = 1;
}

// Request message for DeleteAccessBinding RPC.
message DeleteAccessBindingRequest {
  // Required. Formats:
  // - accounts/{account}/accessBindings/{accessBinding}
  // - properties/{property}/accessBindings/{accessBinding}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];
}

// Request message for BatchDeleteAccessBindings RPC.
message BatchDeleteAccessBindingsRequest {
  // Required. The account or property that owns the access bindings. The parent
  // of all provided values for the 'names' field in DeleteAccessBindingRequest
  // messages must match this field. Formats:
  // - accounts/{account}
  // - properties/{property}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/AccessBinding"
    }
  ];

  // Required. The requests specifying the access bindings to delete.
  // A maximum of 1000 access bindings can be deleted in a batch.
  repeated DeleteAccessBindingRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateExpandedDataSet RPC.
message CreateExpandedDataSetRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/ExpandedDataSet"
    }
  ];

  // Required. The ExpandedDataSet to create.
  ExpandedDataSet expanded_data_set = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateExpandedDataSet RPC.
message UpdateExpandedDataSetRequest {
  // Required. The ExpandedDataSet to update.
  // The resource's `name` field is used to identify the ExpandedDataSet to be
  // updated.
  ExpandedDataSet expanded_data_set = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteExpandedDataSet RPC.
message DeleteExpandedDataSetRequest {
  // Required. Example format: properties/1234/expandedDataSets/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/ExpandedDataSet"
    }
  ];
}

// Request message for GetExpandedDataSet RPC.
message GetExpandedDataSetRequest {
  // Required. The name of the ExpandedDataSet to get.
  // Example format: properties/1234/expandedDataSets/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/ExpandedDataSet"
    }
  ];
}

// Request message for ListExpandedDataSets RPC.
message ListExpandedDataSetsRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/ExpandedDataSet"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListExpandedDataSets` call. Provide
  // this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListExpandedDataSet`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListExpandedDataSets RPC.
message ListExpandedDataSetsResponse {
  // List of ExpandedDataSet. These will be ordered stably, but in an arbitrary
  // order.
  repeated ExpandedDataSet expanded_data_sets = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateChannelGroup RPC.
message CreateChannelGroupRequest {
  // Required. The property for which to create a ChannelGroup.
  // Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/ChannelGroup"
    }
  ];

  // Required. The ChannelGroup to create.
  ChannelGroup channel_group = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateChannelGroup RPC.
message UpdateChannelGroupRequest {
  // Required. The ChannelGroup to update.
  // The resource's `name` field is used to identify the ChannelGroup to be
  // updated.
  ChannelGroup channel_group = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteChannelGroup RPC.
message DeleteChannelGroupRequest {
  // Required. The ChannelGroup to delete.
  // Example format: properties/1234/channelGroups/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/ChannelGroup"
    }
  ];
}

// Request message for GetChannelGroup RPC.
message GetChannelGroupRequest {
  // Required. The ChannelGroup to get.
  // Example format: properties/1234/channelGroups/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/ChannelGroup"
    }
  ];
}

// Request message for ListChannelGroups RPC.
message ListChannelGroupsRequest {
  // Required. The property for which to list ChannelGroups.
  // Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/ChannelGroup"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListChannelGroups` call. Provide
  // this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListChannelGroups`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListChannelGroups RPC.
message ListChannelGroupsResponse {
  // List of ChannelGroup. These will be ordered stably, but in an arbitrary
  // order.
  repeated ChannelGroup channel_groups = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request for setting the opt out status for the automated GA4 setup process.
message SetAutomatedGa4ConfigurationOptOutRequest {
  // Required. The UA property to set the opt out status. Note this request uses
  // the internal property ID, not the tracking ID of the form UA-XXXXXX-YY.
  // Format: properties/{internalWebPropertyId}
  // Example: properties/1234
  string property = 1 [(google.api.field_behavior) = REQUIRED];

  // The status to set.
  bool opt_out = 2;
}

// Response message for setting the opt out status for the automated GA4 setup
// process.
message SetAutomatedGa4ConfigurationOptOutResponse {}

// Request for fetching the opt out status for the automated GA4 setup process.
message FetchAutomatedGa4ConfigurationOptOutRequest {
  // Required. The UA property to get the opt out status. Note this request uses
  // the internal property ID, not the tracking ID of the form UA-XXXXXX-YY.
  // Format: properties/{internalWebPropertyId}
  // Example: properties/1234
  string property = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for fetching the opt out status for the automated GA4 setup
// process.
message FetchAutomatedGa4ConfigurationOptOutResponse {
  // The opt out status for the UA property.
  bool opt_out = 1;
}

// Request message for CreateBigQueryLink RPC.
message CreateBigQueryLinkRequest {
  // Required. Example format: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/BigQueryLink"
    }
  ];

  // Required. The BigQueryLink to create.
  BigQueryLink bigquery_link = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetBigQueryLink RPC.
message GetBigQueryLinkRequest {
  // Required. The name of the BigQuery link to lookup.
  // Format: properties/{property_id}/bigQueryLinks/{bigquery_link_id}
  // Example: properties/123/bigQueryLinks/456
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/BigQueryLink"
    }
  ];
}

// Request message for ListBigQueryLinks RPC.
message ListBigQueryLinksRequest {
  // Required. The name of the property to list BigQuery links under.
  // Format: properties/{property_id}
  // Example: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/BigQueryLink"
    }
  ];

  // The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2;

  // A page token, received from a previous `ListBigQueryLinks` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListBigQueryLinks` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListBigQueryLinks RPC
message ListBigQueryLinksResponse {
  // List of BigQueryLinks.
  repeated BigQueryLink bigquery_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for UpdateBigQueryLink RPC.
message UpdateBigQueryLinkRequest {
  // Required. The settings to update.
  // The `name` field is used to identify the settings to be updated.
  BigQueryLink bigquery_link = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteBigQueryLink RPC.
message DeleteBigQueryLinkRequest {
  // Required. The BigQueryLink to delete.
  // Example format: properties/1234/bigQueryLinks/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/BigQueryLink"
    }
  ];
}

// Request message for GetEnhancedMeasurementSettings RPC.
message GetEnhancedMeasurementSettingsRequest {
  // Required. The name of the settings to lookup.
  // Format:
  // properties/{property}/dataStreams/{data_stream}/enhancedMeasurementSettings
  // Example: "properties/1000/dataStreams/2000/enhancedMeasurementSettings"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/EnhancedMeasurementSettings"
    }
  ];
}

// Request message for UpdateEnhancedMeasurementSettings RPC.
message UpdateEnhancedMeasurementSettingsRequest {
  // Required. The settings to update.
  // The `name` field is used to identify the settings to be updated.
  EnhancedMeasurementSettings enhanced_measurement_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetDataRedactionSettings RPC.
message GetDataRedactionSettingsRequest {
  // Required. The name of the settings to lookup.
  // Format:
  // properties/{property}/dataStreams/{data_stream}/dataRedactionSettings
  // Example: "properties/1000/dataStreams/2000/dataRedactionSettings"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/DataRedactionSettings"
    }
  ];
}

// Request message for UpdateDataRedactionSettings RPC.
message UpdateDataRedactionSettingsRequest {
  // Required. The settings to update.
  // The `name` field is used to identify the settings to be updated.
  DataRedactionSettings data_redaction_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateConnectedSiteTag RPC.
message CreateConnectedSiteTagRequest {
  // The Universal Analytics property to create connected site tags for.
  // This API does not support GA4 properties.
  // Format: properties/{universalAnalyticsPropertyId}
  // Example: properties/1234
  string property = 1;

  // Required. The tag to add to the Universal Analytics property
  ConnectedSiteTag connected_site_tag = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for CreateConnectedSiteTag RPC.
message CreateConnectedSiteTagResponse {}

// Request message for DeleteConnectedSiteTag RPC.
message DeleteConnectedSiteTagRequest {
  // The Universal Analytics property to delete connected site tags for.
  // This API does not support GA4 properties.
  // Format: properties/{universalAnalyticsPropertyId}
  // Example: properties/1234
  string property = 1;

  // Tag ID to forward events to. Also known as the Measurement ID, or the
  // "G-ID"  (For example: G-12345).
  string tag_id = 2;
}

// Request message for ListConnectedSiteTags RPC.
message ListConnectedSiteTagsRequest {
  // The Universal Analytics property to fetch connected site tags for.
  // This does not work on GA4 properties. A maximum of 20 connected site tags
  // will be returned.
  // Example Format: `properties/1234`
  string property = 1;
}

// Response message for ListConnectedSiteTags RPC.
message ListConnectedSiteTagsResponse {
  // The site tags for the Universal Analytics property. A maximum of 20
  // connected site tags will be returned.
  repeated ConnectedSiteTag connected_site_tags = 1;
}

// Request message to be passed to CreateAdSenseLink method.
message CreateAdSenseLinkRequest {
  // Required. The property for which to create an AdSense Link.
  // Format: properties/{propertyId}
  // Example: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/AdSenseLink"
    }
  ];

  // Required. The AdSense Link to create
  AdSenseLink adsense_link = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message to be passed to GetAdSenseLink method.
message GetAdSenseLinkRequest {
  // Required. Unique identifier for the AdSense Link requested.
  // Format: properties/{propertyId}/adSenseLinks/{linkId}
  // Example: properties/1234/adSenseLinks/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/AdSenseLink"
    }
  ];
}

// Request message to be passed to DeleteAdSenseLink method.
message DeleteAdSenseLinkRequest {
  // Required. Unique identifier for the AdSense Link to be deleted.
  // Format: properties/{propertyId}/adSenseLinks/{linkId}
  // Example: properties/1234/adSenseLinks/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/AdSenseLink"
    }
  ];
}

// Request message to be passed to ListAdSenseLinks method.
message ListAdSenseLinksRequest {
  // Required. Resource name of the parent property.
  // Format: properties/{propertyId}
  // Example: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/AdSenseLink"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token received from a previous `ListAdSenseLinks` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAdSenseLinks` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListAdSenseLinks method.
message ListAdSenseLinksResponse {
  // List of AdSenseLinks.
  repeated AdSenseLink adsense_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request for looking up GA4 property connected to a UA property.
message FetchConnectedGa4PropertyRequest {
  // Required. The UA property for which to look up the connected GA4 property.
  // Note this request uses the
  // internal property ID, not the tracking ID of the form UA-XXXXXX-YY.
  // Format: properties/{internal_web_property_id}
  // Example: properties/1234
  string property = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];
}

// Response for looking up GA4 property connected to a UA property.
message FetchConnectedGa4PropertyResponse {
  // The GA4 property connected to the UA property. An empty string is returned
  // when there is no connected GA4 property.
  // Format: properties/{property_id}
  // Example: properties/1234
  string property = 1 [(google.api.resource_reference) = {
    type: "analyticsadmin.googleapis.com/Property"
  }];
}

// Request message for CreateEventCreateRule RPC.
message CreateEventCreateRuleRequest {
  // Required. Example format: properties/123/dataStreams/456
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/EventCreateRule"
    }
  ];

  // Required. The EventCreateRule to create.
  EventCreateRule event_create_rule = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateEventCreateRule RPC.
message UpdateEventCreateRuleRequest {
  // Required. The EventCreateRule to update.
  // The resource's `name` field is used to identify the EventCreateRule to be
  // updated.
  EventCreateRule event_create_rule = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteEventCreateRule RPC.
message DeleteEventCreateRuleRequest {
  // Required. Example format:
  // properties/123/dataStreams/456/eventCreateRules/789
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/EventCreateRule"
    }
  ];
}

// Request message for GetEventCreateRule RPC.
message GetEventCreateRuleRequest {
  // Required. The name of the EventCreateRule to get.
  // Example format: properties/123/dataStreams/456/eventCreateRules/789
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/EventCreateRule"
    }
  ];
}

// Request message for ListEventCreateRules RPC.
message ListEventCreateRulesRequest {
  // Required. Example format: properties/123/dataStreams/456
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/EventCreateRule"
    }
  ];

  // The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2;

  // A page token, received from a previous `ListEventCreateRules` call. Provide
  // this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListEventCreateRules`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListEventCreateRules RPC.
message ListEventCreateRulesResponse {
  // List of EventCreateRules. These will be ordered stably, but in an arbitrary
  // order.
  repeated EventCreateRule event_create_rules = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateEventEditRule RPC.
message CreateEventEditRuleRequest {
  // Required. Example format: properties/123/dataStreams/456
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/EventEditRule"
    }
  ];

  // Required. The EventEditRule to create.
  EventEditRule event_edit_rule = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateEventEditRule RPC.
message UpdateEventEditRuleRequest {
  // Required. The EventEditRule to update.
  // The resource's `name` field is used to identify the EventEditRule to be
  // updated.
  EventEditRule event_edit_rule = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. Field names must be in snake
  // case (e.g., "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteEventEditRule RPC.
message DeleteEventEditRuleRequest {
  // Required. Example format: properties/123/dataStreams/456/eventEditRules/789
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/EventEditRule"
    }
  ];
}

// Request message for GetEventEditRule RPC.
message GetEventEditRuleRequest {
  // Required. The name of the EventEditRule to get.
  // Example format: properties/123/dataStreams/456/eventEditRules/789
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/EventEditRule"
    }
  ];
}

// Request message for ListEventEditRules RPC.
message ListEventEditRulesRequest {
  // Required. Example format: properties/123/dataStreams/456
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/EventEditRule"
    }
  ];

  // Optional. The maximum number of resources to return.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200 (higher values will be coerced to the maximum).
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListEventEditRules` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListEventEditRules`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListEventEditRules RPC.
message ListEventEditRulesResponse {
  // List of EventEditRules. These will be ordered stably, but in an arbitrary
  // order.
  repeated EventEditRule event_edit_rules = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for ReorderEventEditRules RPC.
message ReorderEventEditRulesRequest {
  // Required. Example format: properties/123/dataStreams/456
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/EventEditRule"
    }
  ];

  // Required. EventEditRule resource names for the specified data stream, in
  // the needed processing order. All EventEditRules for the stream must be
  // present in the list.
  repeated string event_edit_rules = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for CreateRollupProperty RPC.
message CreateRollupPropertyRequest {
  // Required. The roll-up property to create.
  Property rollup_property = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The resource names of properties that will be sources to the
  // created roll-up property.
  repeated string source_properties = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Response message for CreateRollupProperty RPC.
message CreateRollupPropertyResponse {
  // The created roll-up property.
  Property rollup_property = 1;

  // The created roll-up property source links.
  repeated RollupPropertySourceLink rollup_property_source_links = 2;
}

// Request message for GetRollupPropertySourceLink RPC.
message GetRollupPropertySourceLinkRequest {
  // Required. The name of the roll-up property source link to lookup.
  // Format:
  // properties/{property_id}/rollupPropertySourceLinks/{rollup_property_source_link_id}
  // Example: properties/123/rollupPropertySourceLinks/456
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/RollupPropertySourceLink"
    }
  ];
}

// Request message for ListRollupPropertySourceLinks RPC.
message ListRollupPropertySourceLinksRequest {
  // Required. The name of the roll-up property to list roll-up property source
  // links under. Format: properties/{property_id} Example: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/RollupPropertySourceLink"
    }
  ];

  // Optional. The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages.
  // If unspecified, at most 50 resources will be returned.
  // The maximum value is 200; (higher values will be coerced to the maximum)
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListRollupPropertySourceLinks` call. Provide this to retrieve the
  // subsequent page. When paginating, all other parameters provided to
  // `ListRollupPropertySourceLinks` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListRollupPropertySourceLinks RPC.
message ListRollupPropertySourceLinksResponse {
  // List of RollupPropertySourceLinks.
  repeated RollupPropertySourceLink rollup_property_source_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateRollupPropertySourceLink RPC.
message CreateRollupPropertySourceLinkRequest {
  // Required. Format: properties/{property_id}
  // Example: properties/1234
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/RollupPropertySourceLink"
    }
  ];

  // Required. The roll-up property source link to create.
  RollupPropertySourceLink rollup_property_source_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteRollupPropertySourceLink RPC.
message DeleteRollupPropertySourceLinkRequest {
  // Required. Format:
  // properties/{property_id}/rollupPropertySourceLinks/{rollup_property_source_link_id}
  // Example: properties/1234/rollupPropertySourceLinks/5678
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/RollupPropertySourceLink"
    }
  ];
}

// Request message for CreateSubproperty RPC.
message ProvisionSubpropertyRequest {
  // Required. The subproperty to create.
  Property subproperty = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The subproperty event filter to create on an ordinary property.
  SubpropertyEventFilter subproperty_event_filter = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ProvisionSubproperty RPC.
message ProvisionSubpropertyResponse {
  // The created subproperty.
  Property subproperty = 1;

  // The created subproperty event filter.
  SubpropertyEventFilter subproperty_event_filter = 2;
}

// Request message for CreateSubpropertyEventFilter RPC.
message CreateSubpropertyEventFilterRequest {
  // Required. The ordinary property for which to create a subproperty event
  // filter. Format: properties/property_id Example: properties/123
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/SubpropertyEventFilter"
    }
  ];

  // Required. The subproperty event filter to create.
  SubpropertyEventFilter subproperty_event_filter = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetSubpropertyEventFilter RPC.
message GetSubpropertyEventFilterRequest {
  // Required. Resource name of the subproperty event filter to lookup.
  // Format:
  // properties/property_id/subpropertyEventFilters/subproperty_event_filter
  // Example: properties/123/subpropertyEventFilters/456
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/SubpropertyEventFilter"
    }
  ];
}

// Request message for ListSubpropertyEventFilters RPC.
message ListSubpropertyEventFiltersRequest {
  // Required. Resource name of the ordinary property.
  // Format: properties/property_id
  // Example: properties/123
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "analyticsadmin.googleapis.com/SubpropertyEventFilter"
    }
  ];

  // Optional. The maximum number of resources to return. The service may return
  // fewer than this value, even if there are additional pages. If unspecified,
  // at most 50 resources will be returned. The maximum value is 200; (higher
  // values will be coerced to the maximum)
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListSubpropertyEventFilters` call. Provide this to retrieve the subsequent
  // page. When paginating, all other parameters provided to
  // `ListSubpropertyEventFilters` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListSubpropertyEventFilter RPC.
message ListSubpropertyEventFiltersResponse {
  // List of subproperty event filters.
  repeated SubpropertyEventFilter subproperty_event_filters = 1;

  // A token, which can be sent as `page_token` to retrieve the next page. If
  // this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for UpdateSubpropertyEventFilter RPC.
message UpdateSubpropertyEventFilterRequest {
  // Required. The subproperty event filter to update.
  SubpropertyEventFilter subproperty_event_filter = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update. Field names must be in snake case
  // (for example, "field_to_update"). Omitted fields will not be updated. To
  // replace the entire entity, use one path with the string "*" to match all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteSubpropertyEventFilter RPC.
message DeleteSubpropertyEventFilterRequest {
  // Required. Resource name of the subproperty event filter to delete.
  // Format:
  // properties/property_id/subpropertyEventFilters/subproperty_event_filter
  // Example: properties/123/subpropertyEventFilters/456
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/SubpropertyEventFilter"
    }
  ];
}
