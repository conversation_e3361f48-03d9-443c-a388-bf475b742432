// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.admin.v1alpha;

import "google/analytics/admin/v1alpha/audience.proto";
import "google/analytics/admin/v1alpha/channel_group.proto";
import "google/analytics/admin/v1alpha/event_create_and_edit.proto";
import "google/analytics/admin/v1alpha/expanded_data_set.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/analytics/admin/apiv1alpha/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.analytics.admin.v1alpha";
option (google.api.resource_definition) = {
  type: "marketingplatformadmin.googleapis.com/Organization"
  pattern: "organizations/{organization}"
};

// The category selected for this property, used for industry benchmarking.
enum IndustryCategory {
  // Industry category unspecified
  INDUSTRY_CATEGORY_UNSPECIFIED = 0;

  // Automotive
  AUTOMOTIVE = 1;

  // Business and industrial markets
  BUSINESS_AND_INDUSTRIAL_MARKETS = 2;

  // Finance
  FINANCE = 3;

  // Healthcare
  HEALTHCARE = 4;

  // Technology
  TECHNOLOGY = 5;

  // Travel
  TRAVEL = 6;

  // Other
  OTHER = 7;

  // Arts and entertainment
  ARTS_AND_ENTERTAINMENT = 8;

  // Beauty and fitness
  BEAUTY_AND_FITNESS = 9;

  // Books and literature
  BOOKS_AND_LITERATURE = 10;

  // Food and drink
  FOOD_AND_DRINK = 11;

  // Games
  GAMES = 12;

  // Hobbies and leisure
  HOBBIES_AND_LEISURE = 13;

  // Home and garden
  HOME_AND_GARDEN = 14;

  // Internet and telecom
  INTERNET_AND_TELECOM = 15;

  // Law and government
  LAW_AND_GOVERNMENT = 16;

  // News
  NEWS = 17;

  // Online communities
  ONLINE_COMMUNITIES = 18;

  // People and society
  PEOPLE_AND_SOCIETY = 19;

  // Pets and animals
  PETS_AND_ANIMALS = 20;

  // Real estate
  REAL_ESTATE = 21;

  // Reference
  REFERENCE = 22;

  // Science
  SCIENCE = 23;

  // Sports
  SPORTS = 24;

  // Jobs and education
  JOBS_AND_EDUCATION = 25;

  // Shopping
  SHOPPING = 26;
}

// Various levels of service for Google Analytics.
enum ServiceLevel {
  // Service level not specified or invalid.
  SERVICE_LEVEL_UNSPECIFIED = 0;

  // The standard version of Google Analytics.
  GOOGLE_ANALYTICS_STANDARD = 1;

  // The paid, premium version of Google Analytics.
  GOOGLE_ANALYTICS_360 = 2;
}

// Different kinds of actors that can make changes to Google Analytics
// resources.
enum ActorType {
  // Unknown or unspecified actor type.
  ACTOR_TYPE_UNSPECIFIED = 0;

  // Changes made by the user specified in actor_email.
  USER = 1;

  // Changes made by the Google Analytics system.
  SYSTEM = 2;

  // Changes made by Google Analytics support team staff.
  SUPPORT = 3;
}

// Types of actions that may change a resource.
enum ActionType {
  // Action type unknown or not specified.
  ACTION_TYPE_UNSPECIFIED = 0;

  // Resource was created in this change.
  CREATED = 1;

  // Resource was updated in this change.
  UPDATED = 2;

  // Resource was deleted in this change.
  DELETED = 3;
}

// Types of resources whose changes may be returned from change history.
enum ChangeHistoryResourceType {
  // Resource type unknown or not specified.
  CHANGE_HISTORY_RESOURCE_TYPE_UNSPECIFIED = 0;

  // Account resource
  ACCOUNT = 1;

  // Property resource
  PROPERTY = 2;

  // FirebaseLink resource
  FIREBASE_LINK = 6;

  // GoogleAdsLink resource
  GOOGLE_ADS_LINK = 7;

  // GoogleSignalsSettings resource
  GOOGLE_SIGNALS_SETTINGS = 8;

  // ConversionEvent resource
  CONVERSION_EVENT = 9;

  // MeasurementProtocolSecret resource
  MEASUREMENT_PROTOCOL_SECRET = 10;

  // CustomDimension resource
  CUSTOM_DIMENSION = 11;

  // CustomMetric resource
  CUSTOM_METRIC = 12;

  // DataRetentionSettings resource
  DATA_RETENTION_SETTINGS = 13;

  // DisplayVideo360AdvertiserLink resource
  DISPLAY_VIDEO_360_ADVERTISER_LINK = 14;

  // DisplayVideo360AdvertiserLinkProposal resource
  DISPLAY_VIDEO_360_ADVERTISER_LINK_PROPOSAL = 15;

  // SearchAds360Link resource
  SEARCH_ADS_360_LINK = 16;

  // DataStream resource
  DATA_STREAM = 18;

  // AttributionSettings resource
  ATTRIBUTION_SETTINGS = 20;

  // ExpandedDataSet resource
  EXPANDED_DATA_SET = 21;

  // ChannelGroup resource
  CHANNEL_GROUP = 22;

  // BigQuery link resource
  BIGQUERY_LINK = 23;

  // EnhancedMeasurementSettings resource
  ENHANCED_MEASUREMENT_SETTINGS = 24;

  // DataRedactionSettings resource
  DATA_REDACTION_SETTINGS = 25;

  // SKAdNetworkConversionValueSchema resource
  SKADNETWORK_CONVERSION_VALUE_SCHEMA = 26;

  // AdSenseLink resource
  ADSENSE_LINK = 27;

  // Audience resource
  AUDIENCE = 28;

  // EventCreateRule resource
  EVENT_CREATE_RULE = 29;

  // CalculatedMetric resource
  CALCULATED_METRIC = 31;
}

// Status of the Google Signals settings.
enum GoogleSignalsState {
  // Google Signals status defaults to GOOGLE_SIGNALS_STATE_UNSPECIFIED to
  // represent that the user has not made an explicit choice.
  GOOGLE_SIGNALS_STATE_UNSPECIFIED = 0;

  // Google Signals is enabled.
  GOOGLE_SIGNALS_ENABLED = 1;

  // Google Signals is disabled.
  GOOGLE_SIGNALS_DISABLED = 2;
}

// Consent field of the Google Signals settings.
enum GoogleSignalsConsent {
  // Google Signals consent value defaults to
  // GOOGLE_SIGNALS_CONSENT_UNSPECIFIED.  This will be treated as
  // GOOGLE_SIGNALS_CONSENT_NOT_CONSENTED.
  GOOGLE_SIGNALS_CONSENT_UNSPECIFIED = 0;

  // Terms of service have been accepted
  GOOGLE_SIGNALS_CONSENT_CONSENTED = 2;

  // Terms of service have not been accepted
  GOOGLE_SIGNALS_CONSENT_NOT_CONSENTED = 1;
}

// An indication of which product the user initiated a link proposal from.
enum LinkProposalInitiatingProduct {
  // Unspecified product.
  LINK_PROPOSAL_INITIATING_PRODUCT_UNSPECIFIED = 0;

  // This proposal was created by a user from Google Analytics.
  GOOGLE_ANALYTICS = 1;

  // This proposal was created by a user from a linked product (not Google
  // Analytics).
  LINKED_PRODUCT = 2;
}

// The state of a link proposal resource.
enum LinkProposalState {
  // Unspecified state
  LINK_PROPOSAL_STATE_UNSPECIFIED = 0;

  // This proposal is awaiting review from a Google Analytics user. This
  // proposal will automatically expire after some time.
  AWAITING_REVIEW_FROM_GOOGLE_ANALYTICS = 1;

  // This proposal is awaiting review from a user of a linked product. This
  // proposal will automatically expire after some time.
  AWAITING_REVIEW_FROM_LINKED_PRODUCT = 2;

  // This proposal has been withdrawn by an admin on the initiating product.
  // This proposal will be automatically deleted after some time.
  WITHDRAWN = 3;

  // This proposal has been declined by an admin on the receiving product. This
  // proposal will be automatically deleted after some time.
  DECLINED = 4;

  // This proposal expired due to lack of response from an admin on the
  // receiving product. This proposal will be automatically deleted after some
  // time.
  EXPIRED = 5;

  // This proposal has become obsolete because a link was directly created to
  // the same external product resource that this proposal specifies. This
  // proposal will be automatically deleted after some time.
  OBSOLETE = 6;
}

// Types of Property resources.
enum PropertyType {
  // Unknown or unspecified property type
  PROPERTY_TYPE_UNSPECIFIED = 0;

  // Ordinary GA4 property
  PROPERTY_TYPE_ORDINARY = 1;

  // GA4 subproperty
  PROPERTY_TYPE_SUBPROPERTY = 2;

  // GA4 rollup property
  PROPERTY_TYPE_ROLLUP = 3;
}

// The coarse conversion value set on the updatePostbackConversionValue SDK call
// when a ConversionValues.event_mappings conditions are satisfied. For
// more information, see
// [SKAdNetwork.CoarseConversionValue](https://developer.apple.com/documentation/storekit/skadnetwork/coarseconversionvalue).
enum CoarseValue {
  // Coarse value not specified.
  COARSE_VALUE_UNSPECIFIED = 0;

  // Coarse value of low.
  COARSE_VALUE_LOW = 1;

  // Coarse value of medium.
  COARSE_VALUE_MEDIUM = 2;

  // Coarse value of high.
  COARSE_VALUE_HIGH = 3;
}

// A resource message representing a Google Analytics account.
message Account {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/Account"
    pattern: "accounts/{account}"
  };

  // Output only. Resource name of this account.
  // Format: accounts/{account}
  // Example: "accounts/100"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this account was originally created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when account payload fields were last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Human-readable display name for this account.
  string display_name = 4 [(google.api.field_behavior) = REQUIRED];

  // Country of business. Must be a Unicode CLDR region code.
  string region_code = 5;

  // Output only. Indicates whether this Account is soft-deleted or not. Deleted
  // accounts are excluded from List results unless specifically requested.
  bool deleted = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URI for a Google Marketing Platform organization resource.
  // Only set when this account is connected to a GMP organization.
  // Format: marketingplatformadmin.googleapis.com/organizations/{org_id}
  string gmp_organization = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "marketingplatformadmin.googleapis.com/Organization"
    }
  ];
}

// A resource message representing a Google Analytics GA4 property.
message Property {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/Property"
    pattern: "properties/{property}"
  };

  // Output only. Resource name of this property.
  // Format: properties/{property_id}
  // Example: "properties/1000"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The property type for this Property resource. When creating a
  // property, if the type is "PROPERTY_TYPE_UNSPECIFIED", then
  // "ORDINARY_PROPERTY" will be implied.
  PropertyType property_type = 14 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when the entity was originally created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when entity payload fields were last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Resource name of this property's logical parent.
  //
  // Note: The Property-Moving UI can be used to change the parent.
  // Format: accounts/{account}, properties/{property}
  // Example: "accounts/100", "properties/101"
  string parent = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Required. Human-readable display name for this property.
  //
  // The max allowed display name length is 100 UTF-16 code units.
  string display_name = 5 [(google.api.field_behavior) = REQUIRED];

  // Industry associated with this property
  // Example: AUTOMOTIVE, FOOD_AND_DRINK
  IndustryCategory industry_category = 6;

  // Required. Reporting Time Zone, used as the day boundary for reports,
  // regardless of where the data originates. If the time zone honors DST,
  // Analytics will automatically adjust for the changes.
  //
  // NOTE: Changing the time zone only affects data going forward, and is not
  // applied retroactively.
  //
  // Format: https://www.iana.org/time-zones
  // Example: "America/Los_Angeles"
  string time_zone = 7 [(google.api.field_behavior) = REQUIRED];

  // The currency type used in reports involving monetary values.
  //
  //
  // Format: https://en.wikipedia.org/wiki/ISO_4217
  // Examples: "USD", "EUR", "JPY"
  string currency_code = 8;

  // Output only. The Google Analytics service level that applies to this
  // property.
  ServiceLevel service_level = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set, the time at which this property was trashed. If not
  // set, then this property is not currently in the trash can.
  google.protobuf.Timestamp delete_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set, the time at which this trashed property will be
  // permanently deleted. If not set, then this property is not currently in the
  // trash can and is not slated to be deleted.
  google.protobuf.Timestamp expire_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The resource name of the parent account
  // Format: accounts/{account_id}
  // Example: "accounts/123"
  string account = 13 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];
}

// A resource message representing a data stream.
message DataStream {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DataStream"
    pattern: "properties/{property}/dataStreams/{data_stream}"
  };

  // Data specific to web streams.
  message WebStreamData {
    // Output only. Analytics Measurement ID.
    //
    // Example: "G-1A2BCD345E"
    string measurement_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. ID of the corresponding web app in Firebase, if any.
    // This ID can change if the web app is deleted and recreated.
    string firebase_app_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Domain name of the web app being measured, or empty.
    // Example: "http://www.google.com", "https://www.google.com"
    string default_uri = 3;
  }

  // Data specific to Android app streams.
  message AndroidAppStreamData {
    // Output only. ID of the corresponding Android app in Firebase, if any.
    // This ID can change if the Android app is deleted and recreated.
    string firebase_app_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Immutable. The package name for the app being measured.
    // Example: "com.example.myandroidapp"
    string package_name = 2 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Data specific to iOS app streams.
  message IosAppStreamData {
    // Output only. ID of the corresponding iOS app in Firebase, if any.
    // This ID can change if the iOS app is deleted and recreated.
    string firebase_app_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Required. Immutable. The Apple App Store Bundle ID for the app
    // Example: "com.example.myiosapp"
    string bundle_id = 2 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.field_behavior) = REQUIRED
    ];
  }

  // The type of the data stream.
  enum DataStreamType {
    // Type unknown or not specified.
    DATA_STREAM_TYPE_UNSPECIFIED = 0;

    // Web data stream.
    WEB_DATA_STREAM = 1;

    // Android app data stream.
    ANDROID_APP_DATA_STREAM = 2;

    // iOS app data stream.
    IOS_APP_DATA_STREAM = 3;
  }

  // Data for specific data stream types. The message that will be
  // set corresponds to the type of this stream.
  oneof stream_data {
    // Data specific to web streams. Must be populated if type is
    // WEB_DATA_STREAM.
    WebStreamData web_stream_data = 6;

    // Data specific to Android app streams. Must be populated if type is
    // ANDROID_APP_DATA_STREAM.
    AndroidAppStreamData android_app_stream_data = 7;

    // Data specific to iOS app streams. Must be populated if type is
    // IOS_APP_DATA_STREAM.
    IosAppStreamData ios_app_stream_data = 8;
  }

  // Output only. Resource name of this Data Stream.
  // Format: properties/{property_id}/dataStreams/{stream_id}
  // Example: "properties/1000/dataStreams/2000"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The type of this DataStream resource.
  DataStreamType type = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // Human-readable display name for the Data Stream.
  //
  // Required for web data streams.
  //
  // The max allowed display name length is 255 UTF-16 code units.
  string display_name = 3;

  // Output only. Time when this stream was originally created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when stream payload fields were last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A link between a GA4 property and a Firebase project.
message FirebaseLink {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/FirebaseLink"
    pattern: "properties/{property}/firebaseLinks/{firebase_link}"
  };

  // Output only. Example format: properties/1234/firebaseLinks/5678
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Firebase project resource name. When creating a FirebaseLink,
  // you may provide this resource name using either a project number or project
  // ID. Once this resource has been created, returned FirebaseLinks will always
  // have a project_name that contains a project number.
  //
  // Format: 'projects/{project number}'
  // Example: 'projects/1234'
  string project = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when this FirebaseLink was originally created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Read-only resource with the tag for sending data from a website to a
// DataStream. Only present for web DataStream resources.
message GlobalSiteTag {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/GlobalSiteTag"
    pattern: "properties/{property}/dataStreams/{data_stream}/globalSiteTag"
  };

  // Output only. Resource name for this GlobalSiteTag resource.
  // Format: properties/{property_id}/dataStreams/{stream_id}/globalSiteTag
  // Example: "properties/123/dataStreams/456/globalSiteTag"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. JavaScript code snippet to be pasted as the first item into the
  // head tag of every webpage to measure.
  string snippet = 2 [(google.api.field_behavior) = IMMUTABLE];
}

// A link between a GA4 property and a Google Ads account.
message GoogleAdsLink {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/GoogleAdsLink"
    pattern: "properties/{property}/googleAdsLinks/{google_ads_link}"
  };

  // Output only. Format:
  // properties/{propertyId}/googleAdsLinks/{googleAdsLinkId}
  //
  // Note: googleAdsLinkId is not the Google Ads customer ID.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Google Ads customer ID.
  string customer_id = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. If true, this link is for a Google Ads manager account.
  bool can_manage_clients = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Enable personalized advertising features with this integration.
  // Automatically publish my Google Analytics audience lists and Google
  // Analytics remarketing events/parameters to the linked Google Ads account.
  // If this field is not set on create/update, it will be defaulted to true.
  google.protobuf.BoolValue ads_personalization_enabled = 5;

  // Output only. Time when this link was originally created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this link was last updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the user that created the link.
  // An empty string will be returned if the email address can't be retrieved.
  string creator_email_address = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A resource message representing data sharing settings of a Google Analytics
// account.
message DataSharingSettings {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DataSharingSettings"
    pattern: "accounts/{account}/dataSharingSettings"
  };

  // Output only. Resource name.
  // Format: accounts/{account}/dataSharingSettings
  // Example: "accounts/1000/dataSharingSettings"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Allows Google support to access the data in order to help troubleshoot
  // issues.
  bool sharing_with_google_support_enabled = 2;

  // Allows Google sales teams that are assigned to the customer to access the
  // data in order to suggest configuration changes to improve results.
  // Sales team restrictions still apply when enabled.
  bool sharing_with_google_assigned_sales_enabled = 3;

  // Allows any of Google sales to access the data in order to suggest
  // configuration changes to improve results.
  bool sharing_with_google_any_sales_enabled = 4;

  // Allows Google to use the data to improve other Google products or services.
  bool sharing_with_google_products_enabled = 5;

  // Allows Google to share the data anonymously in aggregate form with others.
  bool sharing_with_others_enabled = 6;
}

// A virtual resource representing an overview of an account and
// all its child GA4 properties.
message AccountSummary {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/AccountSummary"
    pattern: "accountSummaries/{account_summary}"
  };

  // Resource name for this account summary.
  // Format: accountSummaries/{account_id}
  // Example: "accountSummaries/1000"
  string name = 1;

  // Resource name of account referred to by this account summary
  // Format: accounts/{account_id}
  // Example: "accounts/1000"
  string account = 2 [(google.api.resource_reference) = {
    type: "analyticsadmin.googleapis.com/Account"
  }];

  // Display name for the account referred to in this account summary.
  string display_name = 3;

  // List of summaries for child accounts of this account.
  repeated PropertySummary property_summaries = 4;
}

// A virtual resource representing metadata for a GA4 property.
message PropertySummary {
  // Resource name of property referred to by this property summary
  // Format: properties/{property_id}
  // Example: "properties/1000"
  string property = 1 [(google.api.resource_reference) = {
    type: "analyticsadmin.googleapis.com/Property"
  }];

  // Display name for the property referred to in this property summary.
  string display_name = 2;

  // The property's property type.
  PropertyType property_type = 3;

  // Resource name of this property's logical parent.
  //
  // Note: The Property-Moving UI can be used to change the parent.
  // Format: accounts/{account}, properties/{property}
  // Example: "accounts/100", "properties/200"
  string parent = 4;
}

// A secret value used for sending hits to Measurement Protocol.
message MeasurementProtocolSecret {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/MeasurementProtocolSecret"
    pattern: "properties/{property}/dataStreams/{data_stream}/measurementProtocolSecrets/{measurement_protocol_secret}"
  };

  // Output only. Resource name of this secret. This secret may be a child of
  // any type of stream. Format:
  // properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Human-readable display name for this secret.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The measurement protocol secret value. Pass this value to the
  // api_secret field of the Measurement Protocol API when sending hits to this
  // secret's parent property.
  string secret_value = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// SKAdNetwork conversion value schema of an iOS stream.
message SKAdNetworkConversionValueSchema {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/SKAdNetworkConversionValueSchema"
    pattern: "properties/{property}/dataStreams/{data_stream}/sKAdNetworkConversionValueSchema/{skadnetwork_conversion_value_schema}"
  };

  // Output only. Resource name of the schema. This will be child of ONLY an iOS
  // stream, and there can be at most one such child under an iOS stream.
  // Format:
  // properties/{property}/dataStreams/{dataStream}/sKAdNetworkConversionValueSchema
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The conversion value settings for the first postback window.
  // These differ from values for postback window two and three in that they
  // contain a "Fine" grained conversion value (a numeric value).
  //
  // Conversion values for this postback window must be set.  The other windows
  // are optional and may inherit this window's settings if unset or disabled.
  PostbackWindow postback_window_one = 2
      [(google.api.field_behavior) = REQUIRED];

  // The conversion value settings for the second postback window.
  //
  // This field should only be configured if there is a need to define different
  // conversion values for this postback window.
  //
  // If enable_postback_window_settings is set to false for this postback
  // window, the values from postback_window_one will be used.
  PostbackWindow postback_window_two = 3;

  // The conversion value settings for the third postback window.
  //
  // This field should only be set if the user chose to define different
  // conversion values for this postback window. It is allowed to configure
  // window 3 without setting window 2. In case window 1 & 2 settings are set
  // and enable_postback_window_settings for this postback window is set to
  // false, the schema will inherit settings from postback_window_two.
  PostbackWindow postback_window_three = 4;

  // If enabled, the GA SDK will set conversion values using this schema
  // definition, and schema will be exported to any Google Ads accounts linked
  // to this property. If disabled, the GA SDK will not automatically set
  // conversion values, and also the schema will not be exported to Ads.
  bool apply_conversion_values = 5;
}

// Settings for a SKAdNetwork conversion postback window.
message PostbackWindow {
  // Ordering of the repeated field will be used to prioritize the conversion
  // value settings. Lower indexed entries are prioritized higher. The first
  // conversion value setting that evaluates to true will be selected. It must
  // have at least one entry if enable_postback_window_settings is set to
  // true. It can have maximum of 128 entries.
  repeated ConversionValues conversion_values = 1;

  // If enable_postback_window_settings is true, conversion_values
  // must be populated and will be used for determining when and how to set the
  // Conversion Value on a client device and exporting schema to linked Ads
  // accounts. If false, the settings are not used, but are retained in case
  // they may be used in the future. This must always be true for
  // postback_window_one.
  bool postback_window_settings_enabled = 2;
}

// Conversion value settings for a postback window for SKAdNetwork conversion
// value schema.
message ConversionValues {
  // Display name of the SKAdNetwork conversion value.
  // The max allowed display name length is 50 UTF-16 code units.
  string display_name = 1;

  // The fine-grained conversion value.  This is applicable only to the first
  // postback window. Its valid values are [0,63], both inclusive. It must be
  // set for postback window 1, and must not be set for postback window 2 & 3.
  // This value is not guaranteed to be unique.
  //
  // If the configuration for the first postback window is re-used for second or
  // third postback windows this field has no effect.
  optional int32 fine_value = 2;

  // Required. A coarse grained conversion value.
  //
  // This value is not guaranteed to be unique.
  CoarseValue coarse_value = 3 [(google.api.field_behavior) = REQUIRED];

  // Event conditions that must be met for this Conversion Value to be achieved.
  // The conditions in this list are ANDed together. It must have minimum of 1
  // entry and maximum of 3 entries, if the postback window is enabled.
  repeated EventMapping event_mappings = 4;

  // If true, the SDK should lock to this conversion value for the current
  // postback window.
  bool lock_enabled = 5;
}

// Event setting conditions to match an event.
message EventMapping {
  // Required. Name of the GA4 event. It must always be set.
  // The max allowed display name length is 40 UTF-16 code units.
  string event_name = 1 [(google.api.field_behavior) = REQUIRED];

  // At least one of the following four min/max values must be set. The
  // values set will be ANDed together to qualify an event.
  // The minimum number of times the event occurred. If not set, minimum event
  // count won't be checked.
  optional int64 min_event_count = 2;

  // The maximum number of times the event occurred. If not set, maximum event
  // count won't be checked.
  optional int64 max_event_count = 3;

  // The minimum revenue generated due to the event. Revenue currency will be
  // defined at the property level. If not set, minimum event value won't be
  // checked.
  optional double min_event_value = 4;

  // The maximum revenue generated due to the event. Revenue currency will be
  // defined at the property level. If not set, maximum event value won't be
  // checked.
  optional double max_event_value = 5;
}

// A set of changes within a Google Analytics account or its child properties
// that resulted from the same cause. Common causes would be updates made in the
// Google Analytics UI, changes from customer support, or automatic Google
// Analytics system changes.
message ChangeHistoryEvent {
  // ID of this change history event. This ID is unique across Google Analytics.
  string id = 1;

  // Time when change was made.
  google.protobuf.Timestamp change_time = 2;

  // The type of actor that made this change.
  ActorType actor_type = 3;

  // Email address of the Google account that made the change. This will be a
  // valid email address if the actor field is set to USER, and empty otherwise.
  // Google accounts that have been deleted will cause an error.
  string user_actor_email = 4;

  // If true, then the list of changes returned was filtered, and does not
  // represent all changes that occurred in this event.
  bool changes_filtered = 5;

  // A list of changes made in this change history event that fit the filters
  // specified in SearchChangeHistoryEventsRequest.
  repeated ChangeHistoryChange changes = 6;
}

// A description of a change to a single Google Analytics resource.
message ChangeHistoryChange {
  // A snapshot of a resource as before or after the result of a change in
  // change history.
  message ChangeHistoryResource {
    oneof resource {
      // A snapshot of an Account resource in change history.
      Account account = 1;

      // A snapshot of a Property resource in change history.
      Property property = 2;

      // A snapshot of a FirebaseLink resource in change history.
      FirebaseLink firebase_link = 6;

      // A snapshot of a GoogleAdsLink resource in change history.
      GoogleAdsLink google_ads_link = 7;

      // A snapshot of a GoogleSignalsSettings resource in change history.
      GoogleSignalsSettings google_signals_settings = 8;

      // A snapshot of a DisplayVideo360AdvertiserLink resource in change
      // history.
      DisplayVideo360AdvertiserLink display_video_360_advertiser_link = 9;

      // A snapshot of a DisplayVideo360AdvertiserLinkProposal resource in
      // change history.
      DisplayVideo360AdvertiserLinkProposal
          display_video_360_advertiser_link_proposal = 10;

      // A snapshot of a ConversionEvent resource in change history.
      ConversionEvent conversion_event = 11;

      // A snapshot of a MeasurementProtocolSecret resource in change history.
      MeasurementProtocolSecret measurement_protocol_secret = 12;

      // A snapshot of a CustomDimension resource in change history.
      CustomDimension custom_dimension = 13;

      // A snapshot of a CustomMetric resource in change history.
      CustomMetric custom_metric = 14;

      // A snapshot of a data retention settings resource in change history.
      DataRetentionSettings data_retention_settings = 15;

      // A snapshot of a SearchAds360Link resource in change history.
      SearchAds360Link search_ads_360_link = 16;

      // A snapshot of a DataStream resource in change history.
      DataStream data_stream = 18;

      // A snapshot of AttributionSettings resource in change history.
      AttributionSettings attribution_settings = 20;

      // A snapshot of an ExpandedDataSet resource in change history.
      ExpandedDataSet expanded_data_set = 21;

      // A snapshot of a ChannelGroup resource in change history.
      ChannelGroup channel_group = 22;

      // A snapshot of a BigQuery link resource in change history.
      BigQueryLink bigquery_link = 23;

      // A snapshot of EnhancedMeasurementSettings resource in change history.
      EnhancedMeasurementSettings enhanced_measurement_settings = 24;

      // A snapshot of DataRedactionSettings resource in change history.
      DataRedactionSettings data_redaction_settings = 25;

      // A snapshot of SKAdNetworkConversionValueSchema resource in change
      // history.
      SKAdNetworkConversionValueSchema skadnetwork_conversion_value_schema = 26;

      // A snapshot of an AdSenseLink resource in change history.
      AdSenseLink adsense_link = 27;

      // A snapshot of an Audience resource in change history.
      Audience audience = 28;

      // A snapshot of an EventCreateRule resource in change history.
      EventCreateRule event_create_rule = 29;

      // A snapshot of a CalculatedMetric resource in change history.
      CalculatedMetric calculated_metric = 31;
    }
  }

  // Resource name of the resource whose changes are described by this entry.
  string resource = 1;

  // The type of action that changed this resource.
  ActionType action = 2;

  // Resource contents from before the change was made. If this resource was
  // created in this change, this field will be missing.
  ChangeHistoryResource resource_before_change = 3;

  // Resource contents from after the change was made. If this resource was
  // deleted in this change, this field will be missing.
  ChangeHistoryResource resource_after_change = 4;
}

// A link between a GA4 property and a Display & Video 360 advertiser.
message DisplayVideo360AdvertiserLink {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLink"
    pattern: "properties/{property}/displayVideo360AdvertiserLinks/{display_video_360_advertiser_link}"
  };

  // Output only. The resource name for this DisplayVideo360AdvertiserLink
  // resource. Format:
  // properties/{propertyId}/displayVideo360AdvertiserLinks/{linkId}
  //
  // Note: linkId is not the Display & Video 360 Advertiser ID
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The Display & Video 360 Advertiser's advertiser ID.
  string advertiser_id = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The display name of the Display & Video 360 Advertiser.
  string advertiser_display_name = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Enables personalized advertising features with this integration.
  // If this field is not set on create/update, it will be defaulted to true.
  google.protobuf.BoolValue ads_personalization_enabled = 4;

  // Immutable. Enables the import of campaign data from Display & Video 360
  // into the GA4 property. After link creation, this can only be updated from
  // the Display & Video 360 product. If this field is not set on create, it
  // will be defaulted to true.
  google.protobuf.BoolValue campaign_data_sharing_enabled = 5
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Enables the import of cost data from Display & Video 360 into
  // the GA4 property. This can only be enabled if campaign_data_sharing_enabled
  // is enabled. After link creation, this can only be updated from the Display
  // & Video 360 product. If this field is not set on create, it will be
  // defaulted to true.
  google.protobuf.BoolValue cost_data_sharing_enabled = 6
      [(google.api.field_behavior) = IMMUTABLE];
}

// A proposal for a link between a GA4 property and a Display & Video 360
// advertiser.
//
// A proposal is converted to a DisplayVideo360AdvertiserLink once approved.
// Google Analytics admins approve inbound proposals while Display & Video 360
// admins approve outbound proposals.
message DisplayVideo360AdvertiserLinkProposal {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DisplayVideo360AdvertiserLinkProposal"
    pattern: "properties/{property}/displayVideo360AdvertiserLinkProposals/{display_video_360_advertiser_link_proposal}"
  };

  // Output only. The resource name for this
  // DisplayVideo360AdvertiserLinkProposal resource. Format:
  // properties/{propertyId}/displayVideo360AdvertiserLinkProposals/{proposalId}
  //
  // Note: proposalId is not the Display & Video 360 Advertiser ID
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The Display & Video 360 Advertiser's advertiser ID.
  string advertiser_id = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The status information for this link proposal.
  LinkProposalStatusDetails link_proposal_status_details = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name of the Display & Video Advertiser.
  // Only populated for proposals that originated from Display & Video 360.
  string advertiser_display_name = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. On a proposal being sent to Display & Video 360, this field
  // must be set to the email address of an admin on the target advertiser. This
  // is used to verify that the Google Analytics admin is aware of at least one
  // admin on the Display & Video 360 Advertiser. This does not restrict
  // approval of the proposal to a single user. Any admin on the Display & Video
  // 360 Advertiser may approve the proposal.
  string validation_email = 5 [(google.api.field_behavior) = INPUT_ONLY];

  // Immutable. Enables personalized advertising features with this integration.
  // If this field is not set on create, it will be defaulted to true.
  google.protobuf.BoolValue ads_personalization_enabled = 6
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Enables the import of campaign data from Display & Video 360.
  // If this field is not set on create, it will be defaulted to true.
  google.protobuf.BoolValue campaign_data_sharing_enabled = 7
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Enables the import of cost data from Display & Video 360.
  // This can only be enabled if campaign_data_sharing_enabled is enabled.
  // If this field is not set on create, it will be defaulted to true.
  google.protobuf.BoolValue cost_data_sharing_enabled = 8
      [(google.api.field_behavior) = IMMUTABLE];
}

// A link between a GA4 property and a Search Ads 360 entity.
message SearchAds360Link {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/SearchAds360Link"
    pattern: "properties/{property}/searchAds360Links/{search_ads_360_link}"
  };

  // Output only. The resource name for this SearchAds360Link resource.
  // Format: properties/{propertyId}/searchAds360Links/{linkId}
  //
  // Note: linkId is not the Search Ads 360 advertiser ID
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. This field represents the Advertiser ID of the Search Ads 360
  // Advertiser. that has been linked.
  string advertiser_id = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Enables the import of campaign data from Search Ads 360 into the
  // GA4 property. After link creation, this can only be updated from the Search
  // Ads 360 product.
  // If this field is not set on create, it will be defaulted to true.
  google.protobuf.BoolValue campaign_data_sharing_enabled = 3
      [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Enables the import of cost data from Search Ads 360 to the GA4
  // property. This can only be enabled if campaign_data_sharing_enabled is
  // enabled. After link creation, this can only be updated from
  // the Search Ads 360 product.
  // If this field is not set on create, it will be defaulted to true.
  google.protobuf.BoolValue cost_data_sharing_enabled = 4
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The display name of the Search Ads 360 Advertiser.
  // Allows users to easily identify the linked resource.
  string advertiser_display_name = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Enables personalized advertising features with this integration.
  // If this field is not set on create, it will be defaulted to true.
  google.protobuf.BoolValue ads_personalization_enabled = 6;

  // Enables export of site stats with this integration.
  // If this field is not set on create, it will be defaulted to true.
  google.protobuf.BoolValue site_stats_sharing_enabled = 7;
}

// Status information for a link proposal.
message LinkProposalStatusDetails {
  // Output only. The source of this proposal.
  LinkProposalInitiatingProduct link_proposal_initiating_product = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The email address of the user that proposed this linkage.
  string requestor_email = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of this proposal.
  LinkProposalState link_proposal_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A conversion event in a Google Analytics property.
message ConversionEvent {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/ConversionEvent"
    pattern: "properties/{property}/conversionEvents/{conversion_event}"
  };

  // Defines a default value/currency for a conversion event. Both value and
  // currency must be provided.
  message DefaultConversionValue {
    // This value will be used to populate the value for all conversions
    // of the specified event_name where the event "value" parameter is unset.
    optional double value = 1;

    // When a conversion event for this event_name has no set currency,
    // this currency will be applied as the default. Must be in ISO 4217
    // currency code format. See https://en.wikipedia.org/wiki/ISO_4217 for
    // more information.
    optional string currency_code = 2;
  }

  // The method by which conversions will be counted across multiple events
  // within a session.
  enum ConversionCountingMethod {
    // Counting method not specified.
    CONVERSION_COUNTING_METHOD_UNSPECIFIED = 0;

    // Each Event instance is considered a Conversion.
    ONCE_PER_EVENT = 1;

    // An Event instance is considered a Conversion at most once per session per
    // user.
    ONCE_PER_SESSION = 2;
  }

  // Output only. Resource name of this conversion event.
  // Format: properties/{property}/conversionEvents/{conversion_event}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The event name for this conversion event.
  // Examples: 'click', 'purchase'
  string event_name = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when this conversion event was created in the property.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set, this event can currently be deleted with
  // DeleteConversionEvent.
  bool deletable = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set to true, this conversion event refers to a custom
  // event.  If set to false, this conversion event refers to a default event in
  // GA. Default events typically have special meaning in GA. Default events are
  // usually created for you by the GA system, but in some cases can be created
  // by property admins. Custom events count towards the maximum number of
  // custom conversion events that may be created per property.
  bool custom = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The method by which conversions will be counted across multiple
  // events within a session. If this value is not provided, it will be set to
  // `ONCE_PER_EVENT`.
  ConversionCountingMethod counting_method = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines a default value/currency for a conversion event.
  optional DefaultConversionValue default_conversion_value = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// A key event in a Google Analytics property.
message KeyEvent {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/KeyEvent"
    pattern: "properties/{property}/keyEvents/{key_event}"
    plural: "keyEvents"
    singular: "keyEvent"
  };

  // Defines a default value/currency for a key event.
  message DefaultValue {
    // Required. This will be used to populate the "value" parameter for all
    // occurrences of this Key Event (specified by event_name) where that
    // parameter is unset.
    double numeric_value = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. When an occurrence of this Key Event (specified by event_name)
    // has no set currency this currency will be applied as the default. Must be
    // in ISO 4217 currency code format.
    //
    // See https://en.wikipedia.org/wiki/ISO_4217 for more information.
    string currency_code = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // The method by which Key Events will be counted across multiple events
  // within a session.
  enum CountingMethod {
    // Counting method not specified.
    COUNTING_METHOD_UNSPECIFIED = 0;

    // Each Event instance is considered a Key Event.
    ONCE_PER_EVENT = 1;

    // An Event instance is considered a Key Event at most once per session per
    // user.
    ONCE_PER_SESSION = 2;
  }

  // Output only. Resource name of this key event.
  // Format: properties/{property}/keyEvents/{key_event}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The event name for this key event.
  // Examples: 'click', 'purchase'
  string event_name = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when this key event was created in the property.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set to true, this event can be deleted.
  bool deletable = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set to true, this key event refers to a custom event.  If
  // set to false, this key event refers to a default event in GA. Default
  // events typically have special meaning in GA. Default events are usually
  // created for you by the GA system, but in some cases can be created by
  // property admins. Custom events count towards the maximum number of
  // custom key events that may be created per property.
  bool custom = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The method by which Key Events will be counted across multiple
  // events within a session.
  CountingMethod counting_method = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Defines a default value/currency for a key event.
  DefaultValue default_value = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Settings values for Google Signals.  This is a singleton resource.
message GoogleSignalsSettings {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/GoogleSignalsSettings"
    pattern: "properties/{property}/googleSignalsSettings"
  };

  // Output only. Resource name of this setting.
  // Format: properties/{property_id}/googleSignalsSettings
  // Example: "properties/1000/googleSignalsSettings"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Status of this setting.
  GoogleSignalsState state = 3;

  // Output only. Terms of Service acceptance.
  GoogleSignalsConsent consent = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A definition for a CustomDimension.
message CustomDimension {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/CustomDimension"
    pattern: "properties/{property}/customDimensions/{custom_dimension}"
  };

  // Valid values for the scope of this dimension.
  enum DimensionScope {
    // Scope unknown or not specified.
    DIMENSION_SCOPE_UNSPECIFIED = 0;

    // Dimension scoped to an event.
    EVENT = 1;

    // Dimension scoped to a user.
    USER = 2;

    // Dimension scoped to eCommerce items
    ITEM = 3;
  }

  // Output only. Resource name for this CustomDimension resource.
  // Format: properties/{property}/customDimensions/{customDimension}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Tagging parameter name for this custom dimension.
  //
  // If this is a user-scoped dimension, then this is the user property name.
  // If this is an event-scoped dimension, then this is the event parameter
  // name.
  //
  // If this is an item-scoped dimension, then this is the parameter
  // name found in the eCommerce items array.
  //
  // May only contain alphanumeric and underscore characters, starting with a
  // letter. Max length of 24 characters for user-scoped dimensions, 40
  // characters for event-scoped dimensions.
  string parameter_name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Display name for this custom dimension as shown in the Analytics
  // UI. Max length of 82 characters, alphanumeric plus space and underscore
  // starting with a letter. Legacy system-generated display names may contain
  // square brackets, but updates to this field will never permit square
  // brackets.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description for this custom dimension. Max length of 150
  // characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. The scope of this dimension.
  DimensionScope scope = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. If set to true, sets this dimension as NPA and excludes it from
  // ads personalization.
  //
  // This is currently only supported by user-scoped custom dimensions.
  bool disallow_ads_personalization = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// A definition for a custom metric.
message CustomMetric {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/CustomMetric"
    pattern: "properties/{property}/customMetrics/{custom_metric}"
  };

  // Possible types of representing the custom metric's value.
  //
  // Currency representation may change in the future, requiring a breaking API
  // change.
  enum MeasurementUnit {
    // MeasurementUnit unspecified or missing.
    MEASUREMENT_UNIT_UNSPECIFIED = 0;

    // This metric uses default units.
    STANDARD = 1;

    // This metric measures a currency.
    CURRENCY = 2;

    // This metric measures feet.
    FEET = 3;

    // This metric measures meters.
    METERS = 4;

    // This metric measures kilometers.
    KILOMETERS = 5;

    // This metric measures miles.
    MILES = 6;

    // This metric measures milliseconds.
    MILLISECONDS = 7;

    // This metric measures seconds.
    SECONDS = 8;

    // This metric measures minutes.
    MINUTES = 9;

    // This metric measures hours.
    HOURS = 10;
  }

  // The scope of this metric.
  enum MetricScope {
    // Scope unknown or not specified.
    METRIC_SCOPE_UNSPECIFIED = 0;

    // Metric scoped to an event.
    EVENT = 1;
  }

  // Labels that mark the data in this custom metric as data that should be
  // restricted to specific users.
  enum RestrictedMetricType {
    // Type unknown or unspecified.
    RESTRICTED_METRIC_TYPE_UNSPECIFIED = 0;

    // Metric reports cost data.
    COST_DATA = 1;

    // Metric reports revenue data.
    REVENUE_DATA = 2;
  }

  // Output only. Resource name for this CustomMetric resource.
  // Format: properties/{property}/customMetrics/{customMetric}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Tagging name for this custom metric.
  //
  // If this is an event-scoped metric, then this is the event parameter
  // name.
  //
  // May only contain alphanumeric and underscore charactes, starting with a
  // letter. Max length of 40 characters for event-scoped metrics.
  string parameter_name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Display name for this custom metric as shown in the Analytics UI.
  // Max length of 82 characters, alphanumeric plus space and underscore
  // starting with a letter. Legacy system-generated display names may contain
  // square brackets, but updates to this field will never permit square
  // brackets.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description for this custom dimension.
  // Max length of 150 characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The type for the custom metric's value.
  MeasurementUnit measurement_unit = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. The scope of this custom metric.
  MetricScope scope = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Types of restricted data that this metric may contain. Required
  // for metrics with CURRENCY measurement unit. Must be empty for metrics with
  // a non-CURRENCY measurement unit.
  repeated RestrictedMetricType restricted_metric_type = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// A definition for a calculated metric.
message CalculatedMetric {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/CalculatedMetric"
    pattern: "properties/{property}/calculatedMetrics/{calculated_metric}"
    plural: "calculatedMetrics"
    singular: "calculatedMetric"
  };

  // Possible types of representing the calculated metric's value.
  enum MetricUnit {
    // MetricUnit unspecified or missing.
    METRIC_UNIT_UNSPECIFIED = 0;

    // This metric uses default units.
    STANDARD = 1;

    // This metric measures a currency.
    CURRENCY = 2;

    // This metric measures feet.
    FEET = 3;

    // This metric measures miles.
    MILES = 4;

    // This metric measures meters.
    METERS = 5;

    // This metric measures kilometers.
    KILOMETERS = 6;

    // This metric measures milliseconds.
    MILLISECONDS = 7;

    // This metric measures seconds.
    SECONDS = 8;

    // This metric measures minutes.
    MINUTES = 9;

    // This metric measures hours.
    HOURS = 10;
  }

  // Labels that mark the data in calculated metric used in conjunction with
  // user roles that restrict access to cost and/or revenue metrics.
  enum RestrictedMetricType {
    // Type unknown or unspecified.
    RESTRICTED_METRIC_TYPE_UNSPECIFIED = 0;

    // Metric reports cost data.
    COST_DATA = 1;

    // Metric reports revenue data.
    REVENUE_DATA = 2;
  }

  // Output only. Resource name for this CalculatedMetric.
  // Format: 'properties/{property_id}/calculatedMetrics/{calculated_metric_id}'
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description for this calculated metric.
  // Max length of 4096 characters.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Display name for this calculated metric as shown in the
  // Google Analytics UI. Max length 82 characters.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. The ID to use for the calculated metric. In the UI, this is
  // referred to as the "API name."
  //
  // The calculated_metric_id is used when referencing this calculated metric
  // from external APIs. For example, "calcMetric:{calculated_metric_id}".
  string calculated_metric_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type for the calculated metric's value.
  MetricUnit metric_unit = 5 [(google.api.field_behavior) = REQUIRED];

  // Output only. Types of restricted data that this metric contains.
  repeated RestrictedMetricType restricted_metric_type = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The calculated metric's definition. Maximum number of unique
  // referenced custom metrics is 5. Formulas supports the following operations:
  // + (addition),  - (subtraction), - (negative),  * (multiplication), /
  // (division), () (parenthesis). Any valid real numbers are acceptable that
  // fit in a Long (64bit integer) or a Double (64 bit floating point number).
  // Example formula:
  //   "( customEvent:parameter_name + cartPurchaseQuantity ) / 2.0"
  string formula = 7 [(google.api.field_behavior) = REQUIRED];

  // Output only. If true, this calculated metric has a invalid metric
  // reference. Anything using a calculated metric with invalid_metric_reference
  // set to true may fail, produce warnings, or produce unexpected results.
  bool invalid_metric_reference = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Settings values for data retention. This is a singleton resource.
message DataRetentionSettings {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DataRetentionSettings"
    pattern: "properties/{property}/dataRetentionSettings"
  };

  // Valid values for the data retention duration.
  enum RetentionDuration {
    // Data retention time duration is not specified.
    RETENTION_DURATION_UNSPECIFIED = 0;

    // The data retention time duration is 2 months.
    TWO_MONTHS = 1;

    // The data retention time duration is 14 months.
    FOURTEEN_MONTHS = 3;

    // The data retention time duration is 26 months.
    // Available to 360 properties only.
    TWENTY_SIX_MONTHS = 4;

    // The data retention time duration is 38 months.
    // Available to 360 properties only.
    THIRTY_EIGHT_MONTHS = 5;

    // The data retention time duration is 50 months.
    // Available to 360 properties only.
    FIFTY_MONTHS = 6;
  }

  // Output only. Resource name for this DataRetentionSetting resource.
  // Format: properties/{property}/dataRetentionSettings
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The length of time that event-level data is retained.
  RetentionDuration event_data_retention = 2;

  // If true, reset the retention period for the user identifier with every
  // event from that user.
  bool reset_user_data_on_new_activity = 3;
}

// The attribution settings used for a given property. This is a singleton
// resource.
message AttributionSettings {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/AttributionSettings"
    pattern: "properties/{property}/attributionSettings"
  };

  // How far back in time events should be considered for inclusion in a
  // converting path which leads to the first install of an app or the first
  // visit to a site.
  enum AcquisitionConversionEventLookbackWindow {
    // Lookback window size unspecified.
    ACQUISITION_CONVERSION_EVENT_LOOKBACK_WINDOW_UNSPECIFIED = 0;

    // 7-day lookback window.
    ACQUISITION_CONVERSION_EVENT_LOOKBACK_WINDOW_7_DAYS = 1;

    // 30-day lookback window.
    ACQUISITION_CONVERSION_EVENT_LOOKBACK_WINDOW_30_DAYS = 2;
  }

  // How far back in time events should be considered for inclusion in a
  // converting path for all conversions other than first app install/first site
  // visit.
  enum OtherConversionEventLookbackWindow {
    // Lookback window size unspecified.
    OTHER_CONVERSION_EVENT_LOOKBACK_WINDOW_UNSPECIFIED = 0;

    // 30-day lookback window.
    OTHER_CONVERSION_EVENT_LOOKBACK_WINDOW_30_DAYS = 1;

    // 60-day lookback window.
    OTHER_CONVERSION_EVENT_LOOKBACK_WINDOW_60_DAYS = 2;

    // 90-day lookback window.
    OTHER_CONVERSION_EVENT_LOOKBACK_WINDOW_90_DAYS = 3;
  }

  // The reporting attribution model used to calculate conversion credit in this
  // property's reports.
  enum ReportingAttributionModel {
    // Reporting attribution model unspecified.
    REPORTING_ATTRIBUTION_MODEL_UNSPECIFIED = 0;

    // Data-driven attribution distributes credit for the conversion based on
    // data for each conversion event. Each Data-driven model is specific to
    // each advertiser and each conversion event.
    // Previously CROSS_CHANNEL_DATA_DRIVEN
    PAID_AND_ORGANIC_CHANNELS_DATA_DRIVEN = 1;

    // Ignores direct traffic and attributes 100% of the conversion value to the
    // last channel that the customer clicked through (or engaged view through
    // for YouTube) before converting.
    // Previously CROSS_CHANNEL_LAST_CLICK
    PAID_AND_ORGANIC_CHANNELS_LAST_CLICK = 2;

    // Attributes 100% of the conversion value to the last Google Paid channel
    // that the customer clicked through before converting.
    // Previously ADS_PREFERRED_LAST_CLICK
    GOOGLE_PAID_CHANNELS_LAST_CLICK = 7;
  }

  // The Conversion Export Scope for data exported to linked Ads Accounts.
  enum AdsWebConversionDataExportScope {
    // Default value. This value is unused.
    ADS_WEB_CONVERSION_DATA_EXPORT_SCOPE_UNSPECIFIED = 0;

    // No data export scope selected yet.
    // Export scope can never be changed back to this value.
    NOT_SELECTED_YET = 1;

    // Paid and organic channels are eligible to receive conversion credit, but
    // only credit assigned to Google Ads channels will appear in your Ads
    // accounts. To learn more, see [Paid and Organic
    // channels](https://support.google.com/analytics/answer/********).
    PAID_AND_ORGANIC_CHANNELS = 2;

    // Only Google Ads paid channels are eligible to receive conversion credit.
    // To learn more, see [Google Paid
    // channels](https://support.google.com/analytics/answer/********).
    GOOGLE_PAID_CHANNELS = 3;
  }

  // Output only. Resource name of this attribution settings resource.
  // Format: properties/{property_id}/attributionSettings
  // Example: "properties/1000/attributionSettings"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The lookback window configuration for acquisition conversion
  // events. The default window size is 30 days.
  AcquisitionConversionEventLookbackWindow
      acquisition_conversion_event_lookback_window = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The lookback window for all other, non-acquisition conversion
  // events. The default window size is 90 days.
  OtherConversionEventLookbackWindow other_conversion_event_lookback_window = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. The reporting attribution model used to calculate conversion
  // credit in this property's reports.
  //
  // Changing the attribution model will apply to both historical and future
  // data. These changes will be reflected in reports with conversion and
  // revenue data. User and session data will be unaffected.
  ReportingAttributionModel reporting_attribution_model = 4
      [(google.api.field_behavior) = REQUIRED];

  // Required. The Conversion Export Scope for data exported to linked Ads
  // Accounts.
  AdsWebConversionDataExportScope ads_web_conversion_data_export_scope = 5
      [(google.api.field_behavior) = REQUIRED];
}

// A binding of a user to a set of roles.
message AccessBinding {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/AccessBinding"
    pattern: "accounts/{account}/accessBindings/{access_binding}"
    pattern: "properties/{property}/accessBindings/{access_binding}"
  };

  // The target for which to set roles for.
  oneof access_target {
    // If set, the email address of the user to set roles for.
    // Format: "<EMAIL>"
    string user = 2;
  }

  // Output only. Resource name of this binding.
  //
  // Format: accounts/{account}/accessBindings/{access_binding} or
  // properties/{property}/accessBindings/{access_binding}
  //
  // Example:
  // "accounts/100/accessBindings/200"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A list of roles for to grant to the parent resource.
  //
  // Valid values:
  // predefinedRoles/viewer
  // predefinedRoles/analyst
  // predefinedRoles/editor
  // predefinedRoles/admin
  // predefinedRoles/no-cost-data
  // predefinedRoles/no-revenue-data
  //
  // For users, if an empty list of roles is set, this AccessBinding will be
  // deleted.
  repeated string roles = 3;
}

// A link between a GA4 Property and BigQuery project.
message BigQueryLink {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/BigQueryLink"
    pattern: "properties/{property}/bigQueryLinks/{bigquery_link}"
  };

  // Output only. Resource name of this BigQuery link.
  // Format: 'properties/{property_id}/bigQueryLinks/{bigquery_link_id}'
  // Format: 'properties/1234/bigQueryLinks/abc567'
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The linked Google Cloud project. When creating a BigQueryLink,
  // you may provide this resource name using either a project number or project
  // ID. Once this resource has been created, the returned project will always
  // have a project that contains a project number.
  // Format: 'projects/{project number}'
  // Example: 'projects/1234'
  string project = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Time when the link was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // If set true, enables daily data export to the linked Google Cloud project.
  bool daily_export_enabled = 4;

  // If set true, enables streaming export to the linked Google Cloud project.
  bool streaming_export_enabled = 5;

  // If set true, enables fresh daily export to the linked Google Cloud project.
  bool fresh_daily_export_enabled = 9;

  // If set true, exported data will include advertising identifiers for mobile
  // app streams.
  bool include_advertising_id = 6;

  // The list of streams under the parent property for which data will be
  // exported.
  // Format: properties/{property_id}/dataStreams/{stream_id}
  // Example: ['properties/1000/dataStreams/2000']
  repeated string export_streams = 7;

  // The list of event names that will be excluded from exports.
  repeated string excluded_events = 8;

  // Required. Immutable. The geographic location where the created BigQuery
  // dataset should reside. See https://cloud.google.com/bigquery/docs/locations
  // for supported locations.
  string dataset_location = 10 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];
}

// Singleton resource under a web DataStream, configuring measurement of
// additional site interactions and content.
message EnhancedMeasurementSettings {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/EnhancedMeasurementSettings"
    pattern: "properties/{property}/dataStreams/{data_stream}/enhancedMeasurementSettings"
  };

  // Output only. Resource name of the Enhanced Measurement Settings.
  // Format:
  // properties/{property_id}/dataStreams/{data_stream}/enhancedMeasurementSettings
  // Example: "properties/1000/dataStreams/2000/enhancedMeasurementSettings"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Indicates whether Enhanced Measurement Settings will be used to
  // automatically measure interactions and content on this web stream.
  //
  // Changing this value does not affect the settings themselves, but determines
  // whether they are respected.
  bool stream_enabled = 2;

  // If enabled, capture scroll events each time a visitor gets to the bottom of
  // a page.
  bool scrolls_enabled = 3;

  // If enabled, capture an outbound click event each time a visitor clicks a
  // link that leads them away from your domain.
  bool outbound_clicks_enabled = 4;

  // If enabled, capture a view search results event each time a visitor
  // performs a search on your site (based on a query parameter).
  bool site_search_enabled = 5;

  // If enabled, capture video play, progress, and complete events as visitors
  // view embedded videos on your site.
  bool video_engagement_enabled = 6;

  // If enabled, capture a file download event each time a link is clicked with
  // a common document, compressed file, application, video, or audio extension.
  bool file_downloads_enabled = 7;

  // If enabled, capture a page view event each time the website changes the
  // browser history state.
  bool page_changes_enabled = 8;

  // If enabled, capture a form interaction event each time a visitor interacts
  // with a form on your website.
  // False by default.
  bool form_interactions_enabled = 9;

  // Required. URL query parameters to interpret as site search parameters.
  // Max length is 1024 characters. Must not be empty.
  string search_query_parameter = 10 [(google.api.field_behavior) = REQUIRED];

  // Additional URL query parameters.
  // Max length is 1024 characters.
  string uri_query_parameter = 11;
}

// Configuration for a specific Connected Site Tag.
message ConnectedSiteTag {
  // Required. User-provided display name for the connected site tag. Must be
  // less than 256 characters.
  string display_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. "Tag ID to forward events to. Also known as the Measurement ID,
  // or the "G-ID"  (For example: G-12345).
  string tag_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Settings for client-side data redaction. Singleton resource under a Web
// Stream.
message DataRedactionSettings {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/DataRedactionSettings"
    pattern: "properties/{property}/dataStreams/{data_stream}/dataRedactionSettings"
  };

  // Output only. Name of this Data Redaction Settings resource.
  // Format:
  // properties/{property_id}/dataStreams/{data_stream}/dataRedactionSettings
  // Example: "properties/1000/dataStreams/2000/dataRedactionSettings"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // If enabled, any event parameter or user property values that look like an
  // email will be redacted.
  bool email_redaction_enabled = 2;

  // Query Parameter redaction removes the key and value portions of a
  // query parameter if it is in the configured set of query parameters.
  //
  // If enabled, URL query replacement logic will be run for the Stream. Any
  // query parameters defined in query_parameter_keys will be redacted.
  bool query_parameter_redaction_enabled = 3;

  // The query parameter keys to apply redaction logic to if present in the URL.
  // Query parameter matching is case-insensitive.
  //
  // Must contain at least one element if query_parameter_replacement_enabled
  // is true. Keys cannot contain commas.
  repeated string query_parameter_keys = 4;
}

// A link between a GA4 Property and an AdSense for Content ad client.
message AdSenseLink {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/AdSenseLink"
    pattern: "properties/{property}/adSenseLinks/{adsense_link}"
  };

  // Output only. The resource name for this AdSense Link resource.
  // Format: properties/{propertyId}/adSenseLinks/{linkId}
  // Example: properties/1234/adSenseLinks/6789
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The AdSense ad client code that the GA4 property is linked to.
  // Example format: "ca-pub-1234567890"
  string ad_client_code = 2 [(google.api.field_behavior) = IMMUTABLE];
}

// A link that references a source property under the parent rollup property.
message RollupPropertySourceLink {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/RollupPropertySourceLink"
    pattern: "properties/{property}/rollupPropertySourceLinks/{rollup_property_source_link}"
    plural: "rollupPropertySourceLinks"
    singular: "rollupPropertySourceLink"
  };

  // Output only. Resource name of this RollupPropertySourceLink.
  // Format:
  // 'properties/{property_id}/rollupPropertySourceLinks/{rollup_property_source_link}'
  // Format: 'properties/123/rollupPropertySourceLinks/456'
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Resource name of the source property.
  // Format: properties/{property_id}
  // Example: "properties/789"
  string source_property = 2 [(google.api.field_behavior) = IMMUTABLE];
}
