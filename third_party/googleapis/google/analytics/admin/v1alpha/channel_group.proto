// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.analytics.admin.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/analytics/admin/apiv1alpha/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "ChannelGroupProto";
option java_package = "com.google.analytics.admin.v1alpha";

// A specific filter for a single dimension.
message ChannelGroupFilter {
  // Filter where the field value is a String. The match is case insensitive.
  message StringFilter {
    // How the filter will be used to determine a match.
    enum MatchType {
      // Default match type.
      MATCH_TYPE_UNSPECIFIED = 0;

      // Exact match of the string value.
      EXACT = 1;

      // Begins with the string value.
      BEGINS_WITH = 2;

      // Ends with the string value.
      ENDS_WITH = 3;

      // Contains the string value.
      CONTAINS = 4;

      // Full regular expression match with the string value.
      FULL_REGEXP = 5;

      // Partial regular expression match with the string value.
      PARTIAL_REGEXP = 6;
    }

    // Required. The match type for the string filter.
    MatchType match_type = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The string value to be matched against.
    string value = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // A filter for a string dimension that matches a particular list of options.
  // The match is case insensitive.
  message InListFilter {
    // Required. The list of possible string values to match against. Must be
    // non-empty.
    repeated string values = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // A StringFilter or InListFilter that defines this filters behavior.
  oneof value_filter {
    // A filter for a string-type dimension that matches a particular pattern.
    StringFilter string_filter = 2;

    // A filter for a string dimension that matches a particular list of
    // options.
    InListFilter in_list_filter = 3;
  }

  // Required. Immutable. The dimension name to filter.
  string field_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];
}

// A logical expression of Channel Group dimension filters.
message ChannelGroupFilterExpression {
  // The expression applied to a filter.
  oneof expr {
    // A list of expressions to be AND’ed together. It can only contain
    // ChannelGroupFilterExpressions with or_group. This must be set for the
    // top level ChannelGroupFilterExpression.
    ChannelGroupFilterExpressionList and_group = 1;

    // A list of expressions to OR’ed together. It cannot contain
    // ChannelGroupFilterExpressions with and_group or or_group.
    ChannelGroupFilterExpressionList or_group = 2;

    // A filter expression to be NOT'ed (that is inverted, complemented). It
    // can only include a dimension_or_metric_filter. This cannot be set on the
    // top level ChannelGroupFilterExpression.
    ChannelGroupFilterExpression not_expression = 3;

    // A filter on a single dimension. This cannot be set on the top
    // level ChannelGroupFilterExpression.
    ChannelGroupFilter filter = 4;
  }
}

// A list of Channel Group filter expressions.
message ChannelGroupFilterExpressionList {
  // A list of Channel Group filter expressions.
  repeated ChannelGroupFilterExpression filter_expressions = 1;
}

// The rules that govern how traffic is grouped into one channel.
message GroupingRule {
  // Required. Customer defined display name for the channel.
  string display_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The Filter Expression that defines the Grouping Rule.
  ChannelGroupFilterExpression expression = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A resource message representing a Channel Group.
message ChannelGroup {
  option (google.api.resource) = {
    type: "analyticsadmin.googleapis.com/ChannelGroup"
    pattern: "properties/{property}/channelGroups/{channel_group}"
  };

  // Output only. The resource name for this Channel Group resource.
  // Format: properties/{property}/channelGroups/{channel_group}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the Channel Group. Max length of 80
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // The description of the Channel Group. Max length of 256 characters.
  string description = 3;

  // Required. The grouping rules of channels. Maximum number of rules is 50.
  repeated GroupingRule grouping_rule = 4
      [(google.api.field_behavior) = REQUIRED];

  // Output only. If true, then this channel group is the Default Channel Group
  // predefined by Google Analytics. Display name and grouping rules cannot be
  // updated for this channel group.
  bool system_defined = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. If true, this channel group will be used as the default channel
  // group for reports. Only one channel group can be set as `primary` at any
  // time. If the `primary` field gets set on a channel group, it will get unset
  // on the previous primary channel group.
  //
  // The Google Analytics predefined channel group is the primary by default.
  bool primary = 6 [(google.api.field_behavior) = OPTIONAL];
}
