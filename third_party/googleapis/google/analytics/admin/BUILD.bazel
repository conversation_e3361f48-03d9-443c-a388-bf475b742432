# This build file includes a target for the Ruby wrapper library for
# google-analytics-admin.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for analyticsadmin.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1alpha in this case.
ruby_cloud_gapic_library(
    name = "analyticsadmin_ruby_wrapper",
    srcs = ["//google/analytics/admin/v1alpha:admin_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-analytics-admin",
        "ruby-cloud-env-prefix=ANALYTICS_ADMIN",
        "ruby-cloud-wrapper-of=v1alpha:0.27",
        "ruby-cloud-api-id=analyticsadmin.googleapis.com",
        "ruby-cloud-api-shortname=analyticsadmin",
    ],
    ruby_cloud_description = "The Analytics Admin API allows for programmatic access to the Google Analytics App+Web configuration data. You can use the Google Analytics Admin API to manage accounts and App+Web properties.",
    ruby_cloud_title = "Google Analytics Admin",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-analytics-admin-ruby",
    deps = [
        ":analyticsadmin_ruby_wrapper",
    ],
)
