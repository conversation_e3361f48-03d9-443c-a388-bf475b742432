# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "data_proto",
    srcs = [
        "analytics_data_api.proto",
        "data.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "data_proto_with_info",
    deps = [
        ":data_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "data_java_proto",
    deps = [":data_proto"],
)

java_grpc_library(
    name = "data_java_grpc",
    srcs = [":data_proto"],
    deps = [":data_java_proto"],
)

java_gapic_library(
    name = "data_java_gapic",
    srcs = [":data_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "analytics_data_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "analyticsdata_v1beta.yaml",
    test_deps = [
        ":data_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":data_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "data_java_gapic_test_suite",
    test_classes = [
        "com.google.analytics.data.v1beta.BetaAnalyticsDataClientHttpJsonTest",
        "com.google.analytics.data.v1beta.BetaAnalyticsDataClientTest",
    ],
    runtime_deps = [":data_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-analytics-data-v1beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":data_java_gapic",
        ":data_java_grpc",
        ":data_java_proto",
        ":data_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "data_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/analytics/data/v1beta",
    protos = [":data_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "data_go_gapic",
    srcs = [":data_proto_with_info"],
    grpc_service_config = "analytics_data_grpc_service_config.json",
    importpath = "google.golang.org/google/analytics/data/v1beta;data",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "analyticsdata_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":data_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-analytics-data-v1beta-go",
    deps = [
        ":data_go_gapic",
        ":data_go_gapic_srcjar-metadata.srcjar",
        ":data_go_gapic_srcjar-snippets.srcjar",
        ":data_go_gapic_srcjar-test.srcjar",
        ":data_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "data_py_gapic",
    srcs = [":data_proto"],
    grpc_service_config = "analytics_data_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "analyticsdata_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "data_py_gapic_test",
    srcs = [
        "data_py_gapic_pytest.py",
        "data_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":data_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-analytics-data-v1beta-py",
    deps = [
        ":data_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "data_php_proto",
    deps = [":data_proto"],
)

php_gapic_library(
    name = "data_php_gapic",
    srcs = [":data_proto_with_info"],
    grpc_service_config = "analytics_data_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "analyticsdata_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":data_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-analytics-data-v1beta-php",
    deps = [
        ":data_php_gapic",
        ":data_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "data_nodejs_gapic",
    package_name = "@google-analytics/data",
    src = ":data_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "analytics_data_grpc_service_config.json",
    package = "google.analytics.data.v1beta",
    rest_numeric_enums = True,
    service_yaml = "analyticsdata_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "google-analytics-data-v1beta-nodejs",
    deps = [
        ":data_nodejs_gapic",
        ":data_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "data_ruby_proto",
    deps = [":data_proto"],
)

ruby_grpc_library(
    name = "data_ruby_grpc",
    srcs = [":data_proto"],
    deps = [":data_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "data_ruby_gapic",
    srcs = [":data_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=analyticsdata.googleapis.com",
        "ruby-cloud-api-shortname=analyticsdata",
        "ruby-cloud-env-prefix=ANALYTICS_DATA",
        "ruby-cloud-gem-name=google-analytics-data-v1beta",
        "ruby-cloud-product-url=https://developers.google.com/analytics/devguides/reporting/data/v1",
        "ruby-cloud-service-override=BetaAnalyticsData=AnalyticsData",
    ],
    grpc_service_config = "analytics_data_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Google Analytics Data API provides programmatic methods to access report data in Google Analytics 4 (GA4) properties. Google Analytics 4 helps you understand how people use your web, iOS, or Android app.",
    ruby_cloud_title = "Google Analytics Data V1beta",
    service_yaml = "analyticsdata_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":data_ruby_grpc",
        ":data_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-analytics-data-v1beta-ruby",
    deps = [
        ":data_ruby_gapic",
        ":data_ruby_grpc",
        ":data_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "data_csharp_proto",
    deps = [":data_proto"],
)

csharp_grpc_library(
    name = "data_csharp_grpc",
    srcs = [":data_proto"],
    deps = [":data_csharp_proto"],
)

csharp_gapic_library(
    name = "data_csharp_gapic",
    srcs = [":data_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "analytics_data_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "analyticsdata_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":data_csharp_grpc",
        ":data_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-analytics-data-v1beta-csharp",
    deps = [
        ":data_csharp_gapic",
        ":data_csharp_grpc",
        ":data_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "data_cc_proto",
    deps = [":data_proto"],
)

cc_grpc_library(
    name = "data_cc_grpc",
    srcs = [":data_proto"],
    grpc_only = True,
    deps = [":data_cc_proto"],
)
