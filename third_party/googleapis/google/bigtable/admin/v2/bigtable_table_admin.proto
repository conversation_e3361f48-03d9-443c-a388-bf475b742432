// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.bigtable.admin.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/bigtable/admin/v2/common.proto";
import "google/bigtable/admin/v2/table.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Bigtable.Admin.V2";
option go_package = "cloud.google.com/go/bigtable/admin/apiv2/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "BigtableTableAdminProto";
option java_package = "com.google.bigtable.admin.v2";
option php_namespace = "Google\\Cloud\\Bigtable\\Admin\\V2";
option ruby_package = "Google::Cloud::Bigtable::Admin::V2";

// Service for creating, configuring, and deleting Cloud Bigtable tables.
//
//
// Provides access to the table schemas only, not the data stored within
// the tables.
service BigtableTableAdmin {
  option (google.api.default_host) = "bigtableadmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/bigtable.admin,"
      "https://www.googleapis.com/auth/bigtable.admin.table,"
      "https://www.googleapis.com/auth/cloud-bigtable.admin,"
      "https://www.googleapis.com/auth/cloud-bigtable.admin.table,"
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Creates a new table in the specified instance.
  // The table can be created with a full set of initial column families,
  // specified in the request.
  rpc CreateTable(CreateTableRequest) returns (Table) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/instances/*}/tables"
      body: "*"
    };
    option (google.api.method_signature) = "parent,table_id,table";
  }

  // Creates a new table from the specified snapshot. The target table must
  // not exist. The snapshot and the table must be in the same instance.
  //
  // Note: This is a private alpha release of Cloud Bigtable snapshots. This
  // feature is not currently available to most Cloud Bigtable customers. This
  // feature might be changed in backward-incompatible ways and is not
  // recommended for production use. It is not subject to any SLA or deprecation
  // policy.
  rpc CreateTableFromSnapshot(CreateTableFromSnapshotRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/instances/*}/tables:createFromSnapshot"
      body: "*"
    };
    option (google.api.method_signature) = "parent,table_id,source_snapshot";
    option (google.longrunning.operation_info) = {
      response_type: "Table"
      metadata_type: "CreateTableFromSnapshotMetadata"
    };
  }

  // Lists all tables served from a specified instance.
  rpc ListTables(ListTablesRequest) returns (ListTablesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/instances/*}/tables"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets metadata information about the specified table.
  rpc GetTable(GetTableRequest) returns (Table) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/instances/*/tables/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a specified table.
  rpc UpdateTable(UpdateTableRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{table.name=projects/*/instances/*/tables/*}"
      body: "table"
    };
    option (google.api.method_signature) = "table,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Table"
      metadata_type: "UpdateTableMetadata"
    };
  }

  // Permanently deletes a specified table and all of its data.
  rpc DeleteTable(DeleteTableRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/instances/*/tables/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Restores a specified table which was accidentally deleted.
  rpc UndeleteTable(UndeleteTableRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/instances/*/tables/*}:undelete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Table"
      metadata_type: "UndeleteTableMetadata"
    };
  }

  // Creates a new AuthorizedView in a table.
  rpc CreateAuthorizedView(CreateAuthorizedViewRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/instances/*/tables/*}/authorizedViews"
      body: "authorized_view"
    };
    option (google.api.method_signature) =
        "parent,authorized_view,authorized_view_id";
    option (google.longrunning.operation_info) = {
      response_type: "AuthorizedView"
      metadata_type: "CreateAuthorizedViewMetadata"
    };
  }

  // Lists all AuthorizedViews from a specific table.
  rpc ListAuthorizedViews(ListAuthorizedViewsRequest)
      returns (ListAuthorizedViewsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/instances/*/tables/*}/authorizedViews"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets information from a specified AuthorizedView.
  rpc GetAuthorizedView(GetAuthorizedViewRequest) returns (AuthorizedView) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/instances/*/tables/*/authorizedViews/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an AuthorizedView in a table.
  rpc UpdateAuthorizedView(UpdateAuthorizedViewRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{authorized_view.name=projects/*/instances/*/tables/*/authorizedViews/*}"
      body: "authorized_view"
    };
    option (google.api.method_signature) = "authorized_view,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AuthorizedView"
      metadata_type: "UpdateAuthorizedViewMetadata"
    };
  }

  // Permanently deletes a specified AuthorizedView.
  rpc DeleteAuthorizedView(DeleteAuthorizedViewRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/instances/*/tables/*/authorizedViews/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Performs a series of column family modifications on the specified table.
  // Either all or none of the modifications will occur before this method
  // returns, but data requests received prior to that point may see a table
  // where only some modifications have taken effect.
  rpc ModifyColumnFamilies(ModifyColumnFamiliesRequest) returns (Table) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/instances/*/tables/*}:modifyColumnFamilies"
      body: "*"
    };
    option (google.api.method_signature) = "name,modifications";
  }

  // Permanently drop/delete a row range from a specified table. The request can
  // specify whether to delete all rows in a table, or only those that match a
  // particular prefix.
  rpc DropRowRange(DropRowRangeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/instances/*/tables/*}:dropRowRange"
      body: "*"
    };
  }

  // Generates a consistency token for a Table, which can be used in
  // CheckConsistency to check whether mutations to the table that finished
  // before this call started have been replicated. The tokens will be available
  // for 90 days.
  rpc GenerateConsistencyToken(GenerateConsistencyTokenRequest)
      returns (GenerateConsistencyTokenResponse) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/instances/*/tables/*}:generateConsistencyToken"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Checks replication consistency based on a consistency token, that is, if
  // replication has caught up based on the conditions specified in the token
  // and the check request.
  rpc CheckConsistency(CheckConsistencyRequest)
      returns (CheckConsistencyResponse) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/instances/*/tables/*}:checkConsistency"
      body: "*"
    };
    option (google.api.method_signature) = "name,consistency_token";
  }

  // Creates a new snapshot in the specified cluster from the specified
  // source table. The cluster and the table must be in the same instance.
  //
  // Note: This is a private alpha release of Cloud Bigtable snapshots. This
  // feature is not currently available to most Cloud Bigtable customers. This
  // feature might be changed in backward-incompatible ways and is not
  // recommended for production use. It is not subject to any SLA or deprecation
  // policy.
  rpc SnapshotTable(SnapshotTableRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/instances/*/tables/*}:snapshot"
      body: "*"
    };
    option (google.api.method_signature) =
        "name,cluster,snapshot_id,description";
    option (google.longrunning.operation_info) = {
      response_type: "Snapshot"
      metadata_type: "SnapshotTableMetadata"
    };
  }

  // Gets metadata information about the specified snapshot.
  //
  // Note: This is a private alpha release of Cloud Bigtable snapshots. This
  // feature is not currently available to most Cloud Bigtable customers. This
  // feature might be changed in backward-incompatible ways and is not
  // recommended for production use. It is not subject to any SLA or deprecation
  // policy.
  rpc GetSnapshot(GetSnapshotRequest) returns (Snapshot) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/instances/*/clusters/*/snapshots/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all snapshots associated with the specified cluster.
  //
  // Note: This is a private alpha release of Cloud Bigtable snapshots. This
  // feature is not currently available to most Cloud Bigtable customers. This
  // feature might be changed in backward-incompatible ways and is not
  // recommended for production use. It is not subject to any SLA or deprecation
  // policy.
  rpc ListSnapshots(ListSnapshotsRequest) returns (ListSnapshotsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/instances/*/clusters/*}/snapshots"
    };
    option (google.api.method_signature) = "parent";
  }

  // Permanently deletes the specified snapshot.
  //
  // Note: This is a private alpha release of Cloud Bigtable snapshots. This
  // feature is not currently available to most Cloud Bigtable customers. This
  // feature might be changed in backward-incompatible ways and is not
  // recommended for production use. It is not subject to any SLA or deprecation
  // policy.
  rpc DeleteSnapshot(DeleteSnapshotRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/instances/*/clusters/*/snapshots/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Starts creating a new Cloud Bigtable Backup.  The returned backup
  // [long-running operation][google.longrunning.Operation] can be used to
  // track creation of the backup. The
  // [metadata][google.longrunning.Operation.metadata] field type is
  // [CreateBackupMetadata][google.bigtable.admin.v2.CreateBackupMetadata]. The
  // [response][google.longrunning.Operation.response] field type is
  // [Backup][google.bigtable.admin.v2.Backup], if successful. Cancelling the
  // returned operation will stop the creation and delete the backup.
  rpc CreateBackup(CreateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/instances/*/clusters/*}/backups"
      body: "backup"
    };
    option (google.api.method_signature) = "parent,backup_id,backup";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "CreateBackupMetadata"
    };
  }

  // Gets metadata on a pending or completed Cloud Bigtable Backup.
  rpc GetBackup(GetBackupRequest) returns (Backup) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/instances/*/clusters/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a pending or completed Cloud Bigtable Backup.
  rpc UpdateBackup(UpdateBackupRequest) returns (Backup) {
    option (google.api.http) = {
      patch: "/v2/{backup.name=projects/*/instances/*/clusters/*/backups/*}"
      body: "backup"
    };
    option (google.api.method_signature) = "backup,update_mask";
  }

  // Deletes a pending or completed Cloud Bigtable backup.
  rpc DeleteBackup(DeleteBackupRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/instances/*/clusters/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Cloud Bigtable backups. Returns both completed and pending
  // backups.
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/instances/*/clusters/*}/backups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a new table by restoring from a completed backup.  The
  // returned table [long-running operation][google.longrunning.Operation] can
  // be used to track the progress of the operation, and to cancel it.  The
  // [metadata][google.longrunning.Operation.metadata] field type is
  // [RestoreTableMetadata][google.bigtable.admin.v2.RestoreTableMetadata].  The
  // [response][google.longrunning.Operation.response] type is
  // [Table][google.bigtable.admin.v2.Table], if successful.
  rpc RestoreTable(RestoreTableRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/instances/*}/tables:restore"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Table"
      metadata_type: "RestoreTableMetadata"
    };
  }

  // Copy a Cloud Bigtable backup to a new backup in the destination cluster
  // located in the destination instance and project.
  rpc CopyBackup(CopyBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/instances/*/clusters/*}/backups:copy"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,backup_id,source_backup,expire_time";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "CopyBackupMetadata"
    };
  }

  // Gets the access control policy for a Table or Backup resource.
  // Returns an empty policy if the resource exists but does not have a policy
  // set.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/instances/*/tables/*}:getIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v2/{resource=projects/*/instances/*/clusters/*/backups/*}:getIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource";
  }

  // Sets the access control policy on a Table or Backup resource.
  // Replaces any existing policy.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/instances/*/tables/*}:setIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v2/{resource=projects/*/instances/*/clusters/*/backups/*}:setIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Returns permissions that the caller has on the specified Table or Backup
  // resource.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/instances/*/tables/*}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v2/{resource=projects/*/instances/*/clusters/*/backups/*}:testIamPermissions"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource,permissions";
  }
}

// The request for
// [RestoreTable][google.bigtable.admin.v2.BigtableTableAdmin.RestoreTable].
message RestoreTableRequest {
  // Required. The name of the instance in which to create the restored
  // table. Values are of the form `projects/<project>/instances/<instance>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];

  // Required. The id of the table to create and restore to. This
  // table must not already exist. The `table_id` appended to
  // `parent` forms the full table name of the form
  // `projects/<project>/instances/<instance>/tables/<table_id>`.
  string table_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The source from which to restore.
  oneof source {
    // Name of the backup from which to restore.  Values are of the form
    // `projects/<project>/instances/<instance>/clusters/<cluster>/backups/<backup>`.
    string backup = 3 [(google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Backup"
    }];
  }
}

// Metadata type for the long-running operation returned by
// [RestoreTable][google.bigtable.admin.v2.BigtableTableAdmin.RestoreTable].
message RestoreTableMetadata {
  // Name of the table being created and restored to.
  string name = 1;

  // The type of the restore source.
  RestoreSourceType source_type = 2;

  // Information about the source used to restore the table, as specified by
  // `source` in
  // [RestoreTableRequest][google.bigtable.admin.v2.RestoreTableRequest].
  oneof source_info {
    BackupInfo backup_info = 3;
  }

  // If exists, the name of the long-running operation that will be used to
  // track the post-restore optimization process to optimize the performance of
  // the restored table. The metadata type of the long-running operation is
  // [OptimizeRestoreTableMetadata][]. The response type is
  // [Empty][google.protobuf.Empty]. This long-running operation may be
  // automatically created by the system if applicable after the
  // RestoreTable long-running operation completes successfully. This operation
  // may not be created if the table is already optimized or the restore was
  // not successful.
  string optimize_table_operation_name = 4;

  // The progress of the
  // [RestoreTable][google.bigtable.admin.v2.BigtableTableAdmin.RestoreTable]
  // operation.
  OperationProgress progress = 5;
}

// Metadata type for the long-running operation used to track the progress
// of optimizations performed on a newly restored table. This long-running
// operation is automatically created by the system after the successful
// completion of a table restore, and cannot be cancelled.
message OptimizeRestoredTableMetadata {
  // Name of the restored table being optimized.
  string name = 1;

  // The progress of the post-restore optimizations.
  OperationProgress progress = 2;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.CreateTable][google.bigtable.admin.v2.BigtableTableAdmin.CreateTable]
message CreateTableRequest {
  // An initial split point for a newly created table.
  message Split {
    // Row key to use as an initial tablet boundary.
    bytes key = 1;
  }

  // Required. The unique name of the instance in which to create the table.
  // Values are of the form `projects/{project}/instances/{instance}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];

  // Required. The name by which the new table should be referred to within the
  // parent instance, e.g., `foobar` rather than `{parent}/tables/foobar`.
  // Maximum 50 characters.
  string table_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Table to create.
  Table table = 3 [(google.api.field_behavior) = REQUIRED];

  // The optional list of row keys that will be used to initially split the
  // table into several tablets (tablets are similar to HBase regions).
  // Given two split keys, `s1` and `s2`, three tablets will be created,
  // spanning the key ranges: `[, s1), [s1, s2), [s2, )`.
  //
  // Example:
  //
  // * Row keys := `["a", "apple", "custom", "customer_1", "customer_2",`
  //                `"other", "zz"]`
  // * initial_split_keys := `["apple", "customer_1", "customer_2", "other"]`
  // * Key assignment:
  //     - Tablet 1 `[, apple)                => {"a"}.`
  //     - Tablet 2 `[apple, customer_1)      => {"apple", "custom"}.`
  //     - Tablet 3 `[customer_1, customer_2) => {"customer_1"}.`
  //     - Tablet 4 `[customer_2, other)      => {"customer_2"}.`
  //     - Tablet 5 `[other, )                => {"other", "zz"}.`
  repeated Split initial_splits = 4;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.CreateTableFromSnapshot][google.bigtable.admin.v2.BigtableTableAdmin.CreateTableFromSnapshot]
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
message CreateTableFromSnapshotRequest {
  // Required. The unique name of the instance in which to create the table.
  // Values are of the form `projects/{project}/instances/{instance}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];

  // Required. The name by which the new table should be referred to within the
  // parent instance, e.g., `foobar` rather than `{parent}/tables/foobar`.
  string table_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The unique name of the snapshot from which to restore the table.
  // The snapshot and the table must be in the same instance. Values are of the
  // form
  // `projects/{project}/instances/{instance}/clusters/{cluster}/snapshots/{snapshot}`.
  string source_snapshot = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Snapshot"
    }
  ];
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.DropRowRange][google.bigtable.admin.v2.BigtableTableAdmin.DropRowRange]
message DropRowRangeRequest {
  // Required. The unique name of the table on which to drop a range of rows.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Table"
    }
  ];

  // Delete all rows or by prefix.
  oneof target {
    // Delete all rows that start with this row key prefix. Prefix cannot be
    // zero length.
    bytes row_key_prefix = 2;

    // Delete all rows in the table. Setting this to false is a no-op.
    bool delete_all_data_from_table = 3;
  }
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.ListTables][google.bigtable.admin.v2.BigtableTableAdmin.ListTables]
message ListTablesRequest {
  // Required. The unique name of the instance for which tables should be
  // listed. Values are of the form `projects/{project}/instances/{instance}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Instance"
    }
  ];

  // The view to be applied to the returned tables' fields.
  // NAME_ONLY view (default) and REPLICATION_VIEW are supported.
  Table.View view = 2;

  // Maximum number of results per page.
  //
  // A page_size of zero lets the server choose the number of items to return.
  // A page_size which is strictly positive will return at most that many items.
  // A negative page_size will cause an error.
  //
  // Following the first request, subsequent paginated calls are not required
  // to pass a page_size. If a page_size is set in subsequent calls, it must
  // match the page_size given in the first request.
  int32 page_size = 4;

  // The value of `next_page_token` returned by a previous call.
  string page_token = 3;
}

// Response message for
// [google.bigtable.admin.v2.BigtableTableAdmin.ListTables][google.bigtable.admin.v2.BigtableTableAdmin.ListTables]
message ListTablesResponse {
  // The tables present in the requested instance.
  repeated Table tables = 1;

  // Set if not all tables could be returned in a single response.
  // Pass this value to `page_token` in another request to get the next
  // page of results.
  string next_page_token = 2;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.GetTable][google.bigtable.admin.v2.BigtableTableAdmin.GetTable]
message GetTableRequest {
  // Required. The unique name of the requested table.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Table"
    }
  ];

  // The view to be applied to the returned table's fields.
  // Defaults to `SCHEMA_VIEW` if unspecified.
  Table.View view = 2;
}

// The request for
// [UpdateTable][google.bigtable.admin.v2.BigtableTableAdmin.UpdateTable].
message UpdateTableRequest {
  // Required. The table to update.
  // The table's `name` field is used to identify the table to update.
  Table table = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  // A mask specifying which fields (e.g. `change_stream_config`) in the `table`
  // field should be updated. This mask is relative to the `table` field, not to
  // the request message. The wildcard (*) path is currently not supported.
  // Currently UpdateTable is only supported for the following fields:
  //
  // * `change_stream_config`
  // * `change_stream_config.retention_period`
  // * `deletion_protection`
  //
  // If `column_families` is set in `update_mask`, it will return an
  // UNIMPLEMENTED error.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Metadata type for the operation returned by
// [UpdateTable][google.bigtable.admin.v2.BigtableTableAdmin.UpdateTable].
message UpdateTableMetadata {
  // The name of the table being updated.
  string name = 1;

  // The time at which this operation started.
  google.protobuf.Timestamp start_time = 2;

  // If set, the time at which this operation finished or was canceled.
  google.protobuf.Timestamp end_time = 3;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.DeleteTable][google.bigtable.admin.v2.BigtableTableAdmin.DeleteTable]
message DeleteTableRequest {
  // Required. The unique name of the table to be deleted.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Table"
    }
  ];
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.UndeleteTable][google.bigtable.admin.v2.BigtableTableAdmin.UndeleteTable]
message UndeleteTableRequest {
  // Required. The unique name of the table to be restored.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Table"
    }
  ];
}

// Metadata type for the operation returned by
// [google.bigtable.admin.v2.BigtableTableAdmin.UndeleteTable][google.bigtable.admin.v2.BigtableTableAdmin.UndeleteTable].
message UndeleteTableMetadata {
  // The name of the table being restored.
  string name = 1;

  // The time at which this operation started.
  google.protobuf.Timestamp start_time = 2;

  // If set, the time at which this operation finished or was cancelled.
  google.protobuf.Timestamp end_time = 3;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.ModifyColumnFamilies][google.bigtable.admin.v2.BigtableTableAdmin.ModifyColumnFamilies]
message ModifyColumnFamiliesRequest {
  // A create, update, or delete of a particular column family.
  message Modification {
    // The ID of the column family to be modified.
    string id = 1;

    // Column family modifications.
    oneof mod {
      // Create a new column family with the specified schema, or fail if
      // one already exists with the given ID.
      ColumnFamily create = 2;

      // Update an existing column family to the specified schema, or fail
      // if no column family exists with the given ID.
      ColumnFamily update = 3;

      // Drop (delete) the column family with the given ID, or fail if no such
      // family exists.
      bool drop = 4;
    }

    // Optional. A mask specifying which fields (e.g. `gc_rule`) in the `update`
    // mod should be updated, ignored for other modification types. If unset or
    // empty, we treat it as updating `gc_rule` to be backward compatible.
    google.protobuf.FieldMask update_mask = 6
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The unique name of the table whose families should be modified.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Table"
    }
  ];

  // Required. Modifications to be atomically applied to the specified table's
  // families. Entries are applied in order, meaning that earlier modifications
  // can be masked by later ones (in the case of repeated updates to the same
  // family, for example).
  repeated Modification modifications = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If true, ignore safety checks when modifying the column families.
  bool ignore_warnings = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.GenerateConsistencyToken][google.bigtable.admin.v2.BigtableTableAdmin.GenerateConsistencyToken]
message GenerateConsistencyTokenRequest {
  // Required. The unique name of the Table for which to create a consistency
  // token. Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Table"
    }
  ];
}

// Response message for
// [google.bigtable.admin.v2.BigtableTableAdmin.GenerateConsistencyToken][google.bigtable.admin.v2.BigtableTableAdmin.GenerateConsistencyToken]
message GenerateConsistencyTokenResponse {
  // The generated consistency token.
  string consistency_token = 1;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.CheckConsistency][google.bigtable.admin.v2.BigtableTableAdmin.CheckConsistency]
message CheckConsistencyRequest {
  // Required. The unique name of the Table for which to check replication
  // consistency. Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Table"
    }
  ];

  // Required. The token created using GenerateConsistencyToken for the Table.
  string consistency_token = 2 [(google.api.field_behavior) = REQUIRED];

  // Which type of read needs to consistently observe which type of write?
  // Default: `standard_read_remote_writes`
  oneof mode {
    // Checks that reads using an app profile with `StandardIsolation` can
    // see all writes committed before the token was created, even if the
    // read and write target different clusters.
    StandardReadRemoteWrites standard_read_remote_writes = 3;

    // Checks that reads using an app profile with `DataBoostIsolationReadOnly`
    // can see all writes committed before the token was created, but only if
    // the read and write target the same cluster.
    DataBoostReadLocalWrites data_boost_read_local_writes = 4;
  }
}

// Checks that all writes before the consistency token was generated are
// replicated in every cluster and readable.
message StandardReadRemoteWrites {}

// Checks that all writes before the consistency token was generated in the same
// cluster are readable by Databoost.
message DataBoostReadLocalWrites {}

// Response message for
// [google.bigtable.admin.v2.BigtableTableAdmin.CheckConsistency][google.bigtable.admin.v2.BigtableTableAdmin.CheckConsistency]
message CheckConsistencyResponse {
  // True only if the token is consistent. A token is consistent if replication
  // has caught up with the restrictions specified in the request.
  bool consistent = 1;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.SnapshotTable][google.bigtable.admin.v2.BigtableTableAdmin.SnapshotTable]
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
message SnapshotTableRequest {
  // Required. The unique name of the table to have the snapshot taken.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Table"
    }
  ];

  // Required. The name of the cluster where the snapshot will be created in.
  // Values are of the form
  // `projects/{project}/instances/{instance}/clusters/{cluster}`.
  string cluster = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Cluster"
    }
  ];

  // Required. The ID by which the new snapshot should be referred to within the
  // parent cluster, e.g., `mysnapshot` of the form:
  // `[_a-zA-Z0-9][-_.a-zA-Z0-9]*` rather than
  // `projects/{project}/instances/{instance}/clusters/{cluster}/snapshots/mysnapshot`.
  string snapshot_id = 3 [(google.api.field_behavior) = REQUIRED];

  // The amount of time that the new snapshot can stay active after it is
  // created. Once 'ttl' expires, the snapshot will get deleted. The maximum
  // amount of time a snapshot can stay active is 7 days. If 'ttl' is not
  // specified, the default value of 24 hours will be used.
  google.protobuf.Duration ttl = 4;

  // Description of the snapshot.
  string description = 5;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.GetSnapshot][google.bigtable.admin.v2.BigtableTableAdmin.GetSnapshot]
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
message GetSnapshotRequest {
  // Required. The unique name of the requested snapshot.
  // Values are of the form
  // `projects/{project}/instances/{instance}/clusters/{cluster}/snapshots/{snapshot}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Snapshot"
    }
  ];
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.ListSnapshots][google.bigtable.admin.v2.BigtableTableAdmin.ListSnapshots]
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
message ListSnapshotsRequest {
  // Required. The unique name of the cluster for which snapshots should be
  // listed. Values are of the form
  // `projects/{project}/instances/{instance}/clusters/{cluster}`.
  // Use `{cluster} = '-'` to list snapshots for all clusters in an instance,
  // e.g., `projects/{project}/instances/{instance}/clusters/-`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Cluster"
    }
  ];

  // The maximum number of snapshots to return per page.
  // CURRENTLY UNIMPLEMENTED AND IGNORED.
  int32 page_size = 2;

  // The value of `next_page_token` returned by a previous call.
  string page_token = 3;
}

// Response message for
// [google.bigtable.admin.v2.BigtableTableAdmin.ListSnapshots][google.bigtable.admin.v2.BigtableTableAdmin.ListSnapshots]
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
message ListSnapshotsResponse {
  // The snapshots present in the requested cluster.
  repeated Snapshot snapshots = 1;

  // Set if not all snapshots could be returned in a single response.
  // Pass this value to `page_token` in another request to get the next
  // page of results.
  string next_page_token = 2;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.DeleteSnapshot][google.bigtable.admin.v2.BigtableTableAdmin.DeleteSnapshot]
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
message DeleteSnapshotRequest {
  // Required. The unique name of the snapshot to be deleted.
  // Values are of the form
  // `projects/{project}/instances/{instance}/clusters/{cluster}/snapshots/{snapshot}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Snapshot"
    }
  ];
}

// The metadata for the Operation returned by SnapshotTable.
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
message SnapshotTableMetadata {
  // The request that prompted the initiation of this SnapshotTable operation.
  SnapshotTableRequest original_request = 1;

  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 2;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 3;
}

// The metadata for the Operation returned by CreateTableFromSnapshot.
//
// Note: This is a private alpha release of Cloud Bigtable snapshots. This
// feature is not currently available to most Cloud Bigtable customers. This
// feature might be changed in backward-incompatible ways and is not recommended
// for production use. It is not subject to any SLA or deprecation policy.
message CreateTableFromSnapshotMetadata {
  // The request that prompted the initiation of this CreateTableFromSnapshot
  // operation.
  CreateTableFromSnapshotRequest original_request = 1;

  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 2;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 3;
}

// The request for
// [CreateBackup][google.bigtable.admin.v2.BigtableTableAdmin.CreateBackup].
message CreateBackupRequest {
  // Required. This must be one of the clusters in the instance in which this
  // table is located. The backup will be stored in this cluster. Values are
  // of the form `projects/{project}/instances/{instance}/clusters/{cluster}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Cluster"
    }
  ];

  // Required. The id of the backup to be created. The `backup_id` along with
  // the parent `parent` are combined as {parent}/backups/{backup_id} to create
  // the full backup name, of the form:
  // `projects/{project}/instances/{instance}/clusters/{cluster}/backups/{backup_id}`.
  // This string must be between 1 and 50 characters in length and match the
  // regex [_a-zA-Z0-9][-_.a-zA-Z0-9]*.
  string backup_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The backup to create.
  Backup backup = 3 [(google.api.field_behavior) = REQUIRED];
}

// Metadata type for the operation returned by
// [CreateBackup][google.bigtable.admin.v2.BigtableTableAdmin.CreateBackup].
message CreateBackupMetadata {
  // The name of the backup being created.
  string name = 1;

  // The name of the table the backup is created from.
  string source_table = 2;

  // The time at which this operation started.
  google.protobuf.Timestamp start_time = 3;

  // If set, the time at which this operation finished or was cancelled.
  google.protobuf.Timestamp end_time = 4;
}

// The request for
// [UpdateBackup][google.bigtable.admin.v2.BigtableTableAdmin.UpdateBackup].
message UpdateBackupRequest {
  // Required. The backup to update. `backup.name`, and the fields to be updated
  // as specified by `update_mask` are required. Other fields are ignored.
  // Update is only supported for the following fields:
  //
  //  * `backup.expire_time`.
  Backup backup = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A mask specifying which fields (e.g. `expire_time`) in the
  // Backup resource should be updated. This mask is relative to the Backup
  // resource, not to the request message. The field mask must always be
  // specified; this prevents any future fields from being erased accidentally
  // by clients that do not know about them.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request for
// [GetBackup][google.bigtable.admin.v2.BigtableTableAdmin.GetBackup].
message GetBackupRequest {
  // Required. Name of the backup.
  // Values are of the form
  // `projects/{project}/instances/{instance}/clusters/{cluster}/backups/{backup}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Backup"
    }
  ];
}

// The request for
// [DeleteBackup][google.bigtable.admin.v2.BigtableTableAdmin.DeleteBackup].
message DeleteBackupRequest {
  // Required. Name of the backup to delete.
  // Values are of the form
  // `projects/{project}/instances/{instance}/clusters/{cluster}/backups/{backup}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Backup"
    }
  ];
}

// The request for
// [ListBackups][google.bigtable.admin.v2.BigtableTableAdmin.ListBackups].
message ListBackupsRequest {
  // Required. The cluster to list backups from.  Values are of the
  // form `projects/{project}/instances/{instance}/clusters/{cluster}`.
  // Use `{cluster} = '-'` to list backups for all clusters in an instance,
  // e.g., `projects/{project}/instances/{instance}/clusters/-`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Cluster"
    }
  ];

  // A filter expression that filters backups listed in the response.
  // The expression must specify the field name, a comparison operator,
  // and the value that you want to use for filtering. The value must be a
  // string, a number, or a boolean. The comparison operator must be
  // <, >, <=, >=, !=, =, or :. Colon ':' represents a HAS operator which is
  // roughly synonymous with equality. Filter rules are case insensitive.
  //
  // The fields eligible for filtering are:
  //
  // * `name`
  // * `source_table`
  // * `state`
  // * `start_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ)
  // * `end_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ)
  // * `expire_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ)
  // * `size_bytes`
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. By default, each expression is an AND expression. However,
  // you can include AND, OR, and NOT expressions explicitly.
  //
  // Some examples of using filters are:
  //
  // * `name:"exact"` --> The backup's name is the string "exact".
  // * `name:howl` --> The backup's name contains the string "howl".
  // * `source_table:prod`
  //        --> The source_table's name contains the string "prod".
  // * `state:CREATING` --> The backup is pending creation.
  // * `state:READY` --> The backup is fully created and ready for use.
  // * `(name:howl) AND (start_time < \"2018-03-28T14:50:00Z\")`
  //        --> The backup name contains the string "howl" and start_time
  //            of the backup is before 2018-03-28T14:50:00Z.
  // * `size_bytes > 10000000000` --> The backup's size is greater than 10GB
  string filter = 2;

  // An expression for specifying the sort order of the results of the request.
  // The string value should specify one or more fields in
  // [Backup][google.bigtable.admin.v2.Backup]. The full syntax is described at
  // https://aip.dev/132#ordering.
  //
  // Fields supported are:
  //
  // * name
  // * source_table
  // * expire_time
  // * start_time
  // * end_time
  // * size_bytes
  // * state
  //
  // For example, "start_time". The default sorting order is ascending.
  // To specify descending order for the field, a suffix " desc" should
  // be appended to the field name. For example, "start_time desc".
  // Redundant space characters in the syntax are insigificant.
  //
  // If order_by is empty, results will be sorted by `start_time` in descending
  // order starting from the most recently created backup.
  string order_by = 3;

  // Number of backups to be returned in the response. If 0 or
  // less, defaults to the server's maximum allowed page size.
  int32 page_size = 4;

  // If non-empty, `page_token` should contain a
  // [next_page_token][google.bigtable.admin.v2.ListBackupsResponse.next_page_token]
  // from a previous
  // [ListBackupsResponse][google.bigtable.admin.v2.ListBackupsResponse] to the
  // same `parent` and with the same `filter`.
  string page_token = 5;
}

// The response for
// [ListBackups][google.bigtable.admin.v2.BigtableTableAdmin.ListBackups].
message ListBackupsResponse {
  // The list of matching backups.
  repeated Backup backups = 1;

  // `next_page_token` can be sent in a subsequent
  // [ListBackups][google.bigtable.admin.v2.BigtableTableAdmin.ListBackups] call
  // to fetch more of the matching backups.
  string next_page_token = 2;
}

// The request for
// [CopyBackup][google.bigtable.admin.v2.BigtableTableAdmin.CopyBackup].
message CopyBackupRequest {
  // Required. The name of the destination cluster that will contain the backup
  // copy. The cluster must already exist. Values are of the form:
  // `projects/{project}/instances/{instance}/clusters/{cluster}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Cluster"
    }
  ];

  // Required. The id of the new backup. The `backup_id` along with `parent`
  // are combined as {parent}/backups/{backup_id} to create the full backup
  // name, of the form:
  // `projects/{project}/instances/{instance}/clusters/{cluster}/backups/{backup_id}`.
  // This string must be between 1 and 50 characters in length and match the
  // regex [_a-zA-Z0-9][-_.a-zA-Z0-9]*.
  string backup_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The source backup to be copied from.
  // The source backup needs to be in READY state for it to be copied.
  // Copying a copied backup is not allowed.
  // Once CopyBackup is in progress, the source backup cannot be deleted or
  // cleaned up on expiration until CopyBackup is finished.
  // Values are of the form:
  // `projects/<project>/instances/<instance>/clusters/<cluster>/backups/<backup>`.
  string source_backup = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/Backup"
    }
  ];

  // Required. Required. The expiration time of the copied backup with
  // microsecond granularity that must be at least 6 hours and at most 30 days
  // from the time the request is received. Once the `expire_time` has
  // passed, Cloud Bigtable will delete the backup and free the resources used
  // by the backup.
  google.protobuf.Timestamp expire_time = 4
      [(google.api.field_behavior) = REQUIRED];
}

// Metadata type for the google.longrunning.Operation returned by
// [CopyBackup][google.bigtable.admin.v2.BigtableTableAdmin.CopyBackup].
message CopyBackupMetadata {
  // The name of the backup being created through the copy operation.
  // Values are of the form
  // `projects/<project>/instances/<instance>/clusters/<cluster>/backups/<backup>`.
  string name = 1 [(google.api.resource_reference) = {
    type: "bigtableadmin.googleapis.com/Backup"
  }];

  // Information about the source backup that is being copied from.
  BackupInfo source_backup_info = 2;

  // The progress of the
  // [CopyBackup][google.bigtable.admin.v2.BigtableTableAdmin.CopyBackup]
  // operation.
  OperationProgress progress = 3;
}

// The request for
// [CreateAuthorizedView][google.bigtable.admin.v2.BigtableTableAdmin.CreateAuthorizedView]
message CreateAuthorizedViewRequest {
  // Required. This is the name of the table the AuthorizedView belongs to.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "bigtableadmin.googleapis.com/AuthorizedView"
    }
  ];

  // Required. The id of the AuthorizedView to create. This AuthorizedView must
  // not already exist. The `authorized_view_id` appended to `parent` forms the
  // full AuthorizedView name of the form
  // `projects/{project}/instances/{instance}/tables/{table}/authorizedView/{authorized_view}`.
  string authorized_view_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The AuthorizedView to create.
  AuthorizedView authorized_view = 3 [(google.api.field_behavior) = REQUIRED];
}

// The metadata for the Operation returned by CreateAuthorizedView.
message CreateAuthorizedViewMetadata {
  // The request that prompted the initiation of this CreateInstance operation.
  CreateAuthorizedViewRequest original_request = 1;

  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 2;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 3;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.ListAuthorizedViews][google.bigtable.admin.v2.BigtableTableAdmin.ListAuthorizedViews]
message ListAuthorizedViewsRequest {
  // Required. The unique name of the table for which AuthorizedViews should be
  // listed. Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "bigtableadmin.googleapis.com/AuthorizedView"
    }
  ];

  // Optional. Maximum number of results per page.
  //
  // A page_size of zero lets the server choose the number of items to return.
  // A page_size which is strictly positive will return at most that many items.
  // A negative page_size will cause an error.
  //
  // Following the first request, subsequent paginated calls are not required
  // to pass a page_size. If a page_size is set in subsequent calls, it must
  // match the page_size given in the first request.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of `next_page_token` returned by a previous call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The resource_view to be applied to the returned views' fields.
  // Default to NAME_ONLY.
  AuthorizedView.ResponseView view = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [google.bigtable.admin.v2.BigtableTableAdmin.ListAuthorizedViews][google.bigtable.admin.v2.BigtableTableAdmin.ListAuthorizedViews]
message ListAuthorizedViewsResponse {
  // The AuthorizedViews present in the requested table.
  repeated AuthorizedView authorized_views = 1;

  // Set if not all tables could be returned in a single response.
  // Pass this value to `page_token` in another request to get the next
  // page of results.
  string next_page_token = 2;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.GetAuthorizedView][google.bigtable.admin.v2.BigtableTableAdmin.GetAuthorizedView]
message GetAuthorizedViewRequest {
  // Required. The unique name of the requested AuthorizedView.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}/authorizedViews/{authorized_view}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/AuthorizedView"
    }
  ];

  // Optional. The resource_view to be applied to the returned AuthorizedView's
  // fields. Default to BASIC.
  AuthorizedView.ResponseView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The request for
// [UpdateAuthorizedView][google.bigtable.admin.v2.BigtableTableAdmin.UpdateAuthorizedView].
message UpdateAuthorizedViewRequest {
  // Required. The AuthorizedView to update. The `name` in `authorized_view` is
  // used to identify the AuthorizedView. AuthorizedView name must in this
  // format
  // projects/<project>/instances/<instance>/tables/<table>/authorizedViews/<authorized_view>
  AuthorizedView authorized_view = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The list of fields to update.
  // A mask specifying which fields in the AuthorizedView resource should be
  // updated. This mask is relative to the AuthorizedView resource, not to the
  // request message. A field will be overwritten if it is in the mask. If
  // empty, all fields set in the request will be overwritten. A special value
  // `*` means to overwrite all fields (including fields not set in the
  // request).
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, ignore the safety checks when updating the
  // AuthorizedView.
  bool ignore_warnings = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Metadata for the google.longrunning.Operation returned by
// [UpdateAuthorizedView][google.bigtable.admin.v2.BigtableTableAdmin.UpdateAuthorizedView].
message UpdateAuthorizedViewMetadata {
  // The request that prompted the initiation of this UpdateAuthorizedView
  // operation.
  UpdateAuthorizedViewRequest original_request = 1;

  // The time at which the original request was received.
  google.protobuf.Timestamp request_time = 2;

  // The time at which the operation failed or was completed successfully.
  google.protobuf.Timestamp finish_time = 3;
}

// Request message for
// [google.bigtable.admin.v2.BigtableTableAdmin.DeleteAuthorizedView][google.bigtable.admin.v2.BigtableTableAdmin.DeleteAuthorizedView]
message DeleteAuthorizedViewRequest {
  // Required. The unique name of the AuthorizedView to be deleted.
  // Values are of the form
  // `projects/{project}/instances/{instance}/tables/{table}/authorizedViews/{authorized_view}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "bigtableadmin.googleapis.com/AuthorizedView"
    }
  ];

  // Optional. The current etag of the AuthorizedView.
  // If an etag is provided and does not match the current etag of the
  // AuthorizedView, deletion will be blocked and an ABORTED error will be
  // returned.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}
