# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "bytestream_proto",
    srcs = [
        "bytestream.proto",
    ],
)

proto_library_with_info(
    name = "bytestream_proto_with_info",
    deps = [
        ":bytestream_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "bytestream_java_proto",
    deps = [":bytestream_proto"],
)

java_grpc_library(
    name = "bytestream_java_grpc",
    srcs = [":bytestream_proto"],
    deps = [":bytestream_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "bytestream_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/bytestream",
    protos = [":bytestream_proto"],
)

##############################################################################
# Python
##############################################################################

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "bytestream_php_proto",
    deps = [":bytestream_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "bytestream_ruby_proto",
    deps = [":bytestream_proto"],
)

ruby_grpc_library(
    name = "bytestream_ruby_grpc",
    srcs = [":bytestream_proto"],
    deps = [":bytestream_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "bytestream_csharp_proto",
    extra_opts = [],
    deps = [":bytestream_proto"],
)

csharp_grpc_library(
    name = "bytestream_csharp_grpc",
    srcs = [":bytestream_proto"],
    deps = [":bytestream_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "bytestream_cc_proto",
    deps = [":bytestream_proto"],
)

cc_grpc_library(
    name = "bytestream_cc_grpc",
    srcs = [":bytestream_proto"],
    grpc_only = True,
    deps = [":bytestream_cc_proto"],
)
