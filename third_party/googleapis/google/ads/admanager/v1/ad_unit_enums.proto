// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "AdUnitEnumsProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// Wrapper message for
// [AdUnitStatus][google.ads.admanager.v1.AdUnitStatusEnum.AdUnitStatus]
message AdUnitStatusEnum {
  // The status of an AdUnit.
  enum AdUnitStatus {
    // Default value. This value is unused.
    AD_UNIT_STATUS_UNSPECIFIED = 0;

    // The ad unit is active, available for targeting, and serving.
    ACTIVE = 1;

    // The ad unit will be visible in the UI, but ignored by serving.
    INACTIVE = 2;

    // The ad unit will be hidden in the UI and ignored by serving.
    ARCHIVED = 3;
  }
}

// Wrapper message for
// [SmartSizeMode][google.ads.admanager.v1.SmartSizeModeEnum.SmartSizeMode].
message SmartSizeModeEnum {
  // The smart size mode for this ad unit. This attribute is optional and
  // defaults to SmartSizeMode.NONE for fixed sizes.
  enum SmartSizeMode {
    // Default value. This value is unused.
    SMART_SIZE_MODE_UNSPECIFIED = 0;

    // Fixed size mode (default).
    NONE = 1;

    // The height is fixed for the request, the width is a range.
    SMART_BANNER = 2;

    // Height and width are ranges.
    DYNAMIC_SIZE = 3;
  }
}

// Wrapper message for
// [TargetWindow][google.ads.admanager.v1.TargetWindowEnum.TargetWindow].
message TargetWindowEnum {
  // Corresponds to an HTML link's target attribute.
  // See http://www.w3.org/TR/html401/present/frames.html#adef-target
  enum TargetWindow {
    // Default value. This value is unused.
    TARGET_WINDOW_UNSPECIFIED = 0;

    // Specifies that the link should open in the full body of the page.
    TOP = 1;

    // Specifies that the link should open in a new window.
    BLANK = 2;
  }
}
