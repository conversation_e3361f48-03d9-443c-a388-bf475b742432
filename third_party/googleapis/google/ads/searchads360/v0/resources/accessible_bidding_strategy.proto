// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.resources;

import "google/ads/searchads360/v0/enums/bidding_strategy_type.proto";
import "google/ads/searchads360/v0/enums/target_impression_share_location.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AccessibleBiddingStrategyProto";
option java_package = "com.google.ads.searchads360.v0.resources";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Resources";
option ruby_package = "Google::Ads::SearchAds360::V0::Resources";

// Represents a view of BiddingStrategies owned by and shared with the customer.
//
// In contrast to BiddingStrategy, this resource includes strategies owned by
// managers of the customer and shared with this customer - in addition to
// strategies owned by this customer. This resource does not provide metrics and
// only exposes a limited subset of the BiddingStrategy attributes.
message AccessibleBiddingStrategy {
  option (google.api.resource) = {
    type: "searchads360.googleapis.com/AccessibleBiddingStrategy"
    pattern: "customers/{customer_id}/accessibleBiddingStrategies/{bidding_strategy_id}"
  };

  // An automated bidding strategy to help get the most conversion value for
  // your campaigns while spending your budget.
  message MaximizeConversionValue {
    // Output only. The target return on ad spend (ROAS) option. If set, the bid
    // strategy will maximize revenue while averaging the target return on ad
    // spend. If the target ROAS is high, the bid strategy may not be able to
    // spend the full budget. If the target ROAS is not set, the bid strategy
    // will aim to achieve the highest possible ROAS for the budget.
    double target_roas = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // An automated bidding strategy to help get the most conversions for your
  // campaigns while spending your budget.
  message MaximizeConversions {
    // Output only. The target cost per acquisition (CPA) option. This is the
    // average amount that you would like to spend per acquisition.
    int64 target_cpa = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The target cost per acquisition (CPA) option. This is the
    // average amount that you would like to spend per acquisition.
    int64 target_cpa_micros = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // An automated bid strategy that sets bids to help get as many conversions as
  // possible at the target cost-per-acquisition (CPA) you set.
  message TargetCpa {
    // Output only. Average CPA target.
    // This target should be greater than or equal to minimum billable unit
    // based on the currency for the account.
    optional int64 target_cpa_micros = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // An automated bidding strategy that sets bids so that a certain percentage
  // of search ads are shown at the top of the first page (or other targeted
  // location).
  message TargetImpressionShare {
    // Output only. The targeted location on the search results page.
    google.ads.searchads360.v0.enums.TargetImpressionShareLocationEnum
        .TargetImpressionShareLocation location = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // The chosen fraction of ads to be shown in the targeted location in
    // micros. For example, 1% equals 10,000.
    optional int64 location_fraction_micros = 2;

    // Output only. The highest CPC bid the automated bidding system is
    // permitted to specify. This is a required field entered by the advertiser
    // that sets the ceiling and specified in local micros.
    optional int64 cpc_bid_ceiling_micros = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // An automated bidding strategy that helps you maximize revenue while
  // averaging a specific target return on ad spend (ROAS).
  message TargetRoas {
    // Output only. The chosen revenue (based on conversion data) per unit of
    // spend.
    optional double target_roas = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // An automated bid strategy that sets your bids to help get as many clicks
  // as possible within your budget.
  message TargetSpend {
    // Output only. The spend target under which to maximize clicks.
    // A TargetSpend bidder will attempt to spend the smaller of this value
    // or the natural throttling spend amount.
    // If not specified, the budget is used as the spend target.
    // This field is deprecated and should no longer be used. See
    // https://ads-developers.googleblog.com/2020/05/reminder-about-sunset-creation-of.html
    // for details.
    optional int64 target_spend_micros = 1
        [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Maximum bid limit that can be set by the bid strategy.
    // The limit applies to all keywords managed by the strategy.
    optional int64 cpc_bid_ceiling_micros = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The resource name of the accessible bidding strategy.
  // AccessibleBiddingStrategy resource names have the form:
  //
  // `customers/{customer_id}/accessibleBiddingStrategies/{bidding_strategy_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "searchads360.googleapis.com/AccessibleBiddingStrategy"
    }
  ];

  // Output only. The ID of the bidding strategy.
  int64 id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the bidding strategy.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the bidding strategy.
  google.ads.searchads360.v0.enums.BiddingStrategyTypeEnum.BiddingStrategyType
      type = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the Customer which owns the bidding strategy.
  int64 owner_customer_id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. descriptive_name of the Customer which owns the bidding
  // strategy.
  string owner_descriptive_name = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The bidding scheme.
  //
  // Only one can be set.
  oneof scheme {
    // Output only. An automated bidding strategy to help get the most
    // conversion value for your campaigns while spending your budget.
    MaximizeConversionValue maximize_conversion_value = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. An automated bidding strategy to help get the most
    // conversions for your campaigns while spending your budget.
    MaximizeConversions maximize_conversions = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A bidding strategy that sets bids to help get as many
    // conversions as possible at the target cost-per-acquisition (CPA) you set.
    TargetCpa target_cpa = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A bidding strategy that automatically optimizes towards a
    // chosen percentage of impressions.
    TargetImpressionShare target_impression_share = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A bidding strategy that helps you maximize revenue while
    // averaging a specific target Return On Ad Spend (ROAS).
    TargetRoas target_roas = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A bid strategy that sets your bids to help get as many
    // clicks as possible within your budget.
    TargetSpend target_spend = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}
