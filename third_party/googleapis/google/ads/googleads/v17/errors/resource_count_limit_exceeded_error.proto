// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "ResourceCountLimitExceededErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing resource count limit exceeded errors.

// Container for enum describing possible resource count limit exceeded errors.
message ResourceCountLimitExceededErrorEnum {
  // Enum describing possible resource count limit exceeded errors.
  enum ResourceCountLimitExceededError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Indicates that this request would exceed the number of allowed resources
    // for the Google Ads account. The exact resource type and limit being
    // checked can be inferred from accountLimitType.
    ACCOUNT_LIMIT = 2;

    // Indicates that this request would exceed the number of allowed resources
    // in a Campaign. The exact resource type and limit being checked can be
    // inferred from accountLimitType, and the numeric id of the
    // Campaign involved is given by enclosingId.
    CAMPAIGN_LIMIT = 3;

    // Indicates that this request would exceed the number of allowed resources
    // in an ad group. The exact resource type and limit being checked can be
    // inferred from accountLimitType, and the numeric id of the
    // ad group involved is given by enclosingId.
    ADGROUP_LIMIT = 4;

    // Indicates that this request would exceed the number of allowed resources
    // in an ad group ad. The exact resource type and limit being checked can
    // be inferred from accountLimitType, and the enclosingId
    // contains the ad group id followed by the ad id, separated by a single
    // comma (,).
    AD_GROUP_AD_LIMIT = 5;

    // Indicates that this request would exceed the number of allowed resources
    // in an ad group criterion. The exact resource type and limit being checked
    // can be inferred from accountLimitType, and the
    // enclosingId contains the ad group id followed by the
    // criterion id, separated by a single comma (,).
    AD_GROUP_CRITERION_LIMIT = 6;

    // Indicates that this request would exceed the number of allowed resources
    // in this shared set. The exact resource type and limit being checked can
    // be inferred from accountLimitType, and the numeric id of the
    // shared set involved is given by enclosingId.
    SHARED_SET_LIMIT = 7;

    // Exceeds a limit related to a matching function.
    MATCHING_FUNCTION_LIMIT = 8;

    // The response for this request would exceed the maximum number of rows
    // that can be returned.
    RESPONSE_ROW_LIMIT_EXCEEDED = 9;

    // This request would exceed a limit on the number of allowed resources.
    // The details of which type of limit was exceeded will eventually be
    // returned in ErrorDetails.
    RESOURCE_LIMIT = 10;
  }
}
