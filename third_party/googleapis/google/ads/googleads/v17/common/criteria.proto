// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.common;

import "google/ads/googleads/v17/enums/age_range_type.proto";
import "google/ads/googleads/v17/enums/app_payment_model_type.proto";
import "google/ads/googleads/v17/enums/brand_request_rejection_reason.proto";
import "google/ads/googleads/v17/enums/brand_state.proto";
import "google/ads/googleads/v17/enums/content_label_type.proto";
import "google/ads/googleads/v17/enums/day_of_week.proto";
import "google/ads/googleads/v17/enums/device.proto";
import "google/ads/googleads/v17/enums/gender_type.proto";
import "google/ads/googleads/v17/enums/hotel_date_selection_type.proto";
import "google/ads/googleads/v17/enums/income_range_type.proto";
import "google/ads/googleads/v17/enums/interaction_type.proto";
import "google/ads/googleads/v17/enums/keyword_match_type.proto";
import "google/ads/googleads/v17/enums/listing_group_type.proto";
import "google/ads/googleads/v17/enums/location_group_radius_units.proto";
import "google/ads/googleads/v17/enums/minute_of_hour.proto";
import "google/ads/googleads/v17/enums/parental_status_type.proto";
import "google/ads/googleads/v17/enums/product_category_level.proto";
import "google/ads/googleads/v17/enums/product_channel.proto";
import "google/ads/googleads/v17/enums/product_channel_exclusivity.proto";
import "google/ads/googleads/v17/enums/product_condition.proto";
import "google/ads/googleads/v17/enums/product_custom_attribute_index.proto";
import "google/ads/googleads/v17/enums/product_type_level.proto";
import "google/ads/googleads/v17/enums/proximity_radius_units.proto";
import "google/ads/googleads/v17/enums/webpage_condition_operand.proto";
import "google/ads/googleads/v17/enums/webpage_condition_operator.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/common;common";
option java_multiple_files = true;
option java_outer_classname = "CriteriaProto";
option java_package = "com.google.ads.googleads.v17.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Common";
option ruby_package = "Google::Ads::GoogleAds::V17::Common";

// Proto file describing criteria types.

// A keyword criterion.
message KeywordInfo {
  // The text of the keyword (at most 80 characters and 10 words).
  optional string text = 3;

  // The match type of the keyword.
  google.ads.googleads.v17.enums.KeywordMatchTypeEnum.KeywordMatchType
      match_type = 2;
}

// A placement criterion. This can be used to modify bids for sites when
// targeting the content network.
message PlacementInfo {
  // URL of the placement.
  //
  // For example, "http://www.domain.com".
  optional string url = 2;
}

// A Negative Keyword List criterion. Represents a shared set
// of negative keywords that can be excluded at the account-level.
// Only one negative keyword list criterion can be attached per account.
message NegativeKeywordListInfo {
  // The NegativeKeywordListInfo shared set resource name.
  optional string shared_set = 1;
}

// A mobile app category criterion.
message MobileAppCategoryInfo {
  // The mobile app category constant resource name.
  optional string mobile_app_category_constant = 2
      [(google.api.resource_reference) = {
        type: "googleads.googleapis.com/MobileAppCategoryConstant"
      }];
}

// A mobile application criterion.
message MobileApplicationInfo {
  // A string that uniquely identifies a mobile application to Google Ads API.
  // The format of this string is "{platform}-{platform_native_id}", where
  // platform is "1" for iOS apps and "2" for Android apps, and where
  // platform_native_id is the mobile application identifier native to the
  // corresponding platform.
  // For iOS, this native identifier is the 9 digit string that appears at the
  // end of an App Store URL (for example, "*********" for "Flood-It! 2" whose
  // App Store link is
  // "http://itunes.apple.com/us/app/flood-it!-2/id*********"). For Android,
  // this native identifier is the application's package name (for example,
  // "com.labpixies.colordrips" for "Color Drips" given Google Play link
  // "https://play.google.com/store/apps/details?id=com.labpixies.colordrips").
  // A well formed app id for Google Ads API would thus be "1-*********" for iOS
  // and "2-com.labpixies.colordrips" for Android.
  // This field is required and must be set in CREATE operations.
  optional string app_id = 4;

  // Name of this mobile application.
  optional string name = 5;
}

// A location criterion.
message LocationInfo {
  // The geo target constant resource name.
  optional string geo_target_constant = 2;
}

// A device criterion.
message DeviceInfo {
  // Type of the device.
  google.ads.googleads.v17.enums.DeviceEnum.Device type = 1;
}

// A listing group criterion.
message ListingGroupInfo {
  // Type of the listing group.
  google.ads.googleads.v17.enums.ListingGroupTypeEnum.ListingGroupType type = 1;

  // Dimension value with which this listing group is refining its parent.
  // Undefined for the root group.
  ListingDimensionInfo case_value = 2;

  // Resource name of ad group criterion which is the parent listing group
  // subdivision. Null for the root group.
  optional string parent_ad_group_criterion = 4;

  // The path of dimensions defining this listing group.
  optional ListingDimensionPath path = 5;
}

// The path of dimensions defining a listing group.
message ListingDimensionPath {
  // The complete path of dimensions through the listing group hierarchy, from
  // the root (excluding the root itself) to this listing group.
  repeated ListingDimensionInfo dimensions = 1;
}

// A listing scope criterion.
message ListingScopeInfo {
  // Scope of the campaign criterion.
  repeated ListingDimensionInfo dimensions = 2;
}

// Listing dimensions for listing group criterion.
message ListingDimensionInfo {
  // Dimension of one of the types below is always present.
  oneof dimension {
    // Advertiser-specific hotel ID.
    HotelIdInfo hotel_id = 2;

    // Class of the hotel as a number of stars 1 to 5.
    HotelClassInfo hotel_class = 3;

    // Country or Region the hotel is located in.
    HotelCountryRegionInfo hotel_country_region = 4;

    // State the hotel is located in.
    HotelStateInfo hotel_state = 5;

    // City the hotel is located in.
    HotelCityInfo hotel_city = 6;

    // Category of a product offer.
    ProductCategoryInfo product_category = 24;

    // Brand of a product offer.
    ProductBrandInfo product_brand = 15;

    // Locality of a product offer.
    ProductChannelInfo product_channel = 8;

    // Availability of a product offer.
    ProductChannelExclusivityInfo product_channel_exclusivity = 9;

    // Condition of a product offer.
    ProductConditionInfo product_condition = 10;

    // Custom attribute of a product offer.
    ProductCustomAttributeInfo product_custom_attribute = 16;

    // Item id of a product offer.
    ProductItemIdInfo product_item_id = 11;

    // Type of a product offer.
    ProductTypeInfo product_type = 12;

    // Grouping of a product offer. This listing dimension is deprecated and it
    // is supported only in Display campaigns.
    ProductGroupingInfo product_grouping = 17;

    // Labels of a product offer. This listing dimension is deprecated and it is
    // supported only in Display campaigns.
    ProductLabelsInfo product_labels = 18;

    // Legacy condition of a product offer. This listing dimension is deprecated
    // and it is supported only in Display campaigns.
    ProductLegacyConditionInfo product_legacy_condition = 19;

    // Full type of a product offer. This listing dimension is deprecated and it
    // is supported only in Display campaigns.
    ProductTypeFullInfo product_type_full = 20;

    // Advertiser-specific activity ID.
    ActivityIdInfo activity_id = 21;

    // Rating of the activity as a number 1 to 5, where 5 is the best.
    ActivityRatingInfo activity_rating = 22;

    // The country where the travel activity is available.
    ActivityCountryInfo activity_country = 23;

    // The state where the travel activity is available.
    ActivityStateInfo activity_state = 25;

    // The city where the travel activity is available.
    ActivityCityInfo activity_city = 26;

    // Unknown dimension. Set when no other listing dimension is set.
    UnknownListingDimensionInfo unknown_listing_dimension = 14;
  }
}

// Advertiser-specific hotel ID.
message HotelIdInfo {
  // String value of the hotel ID.
  optional string value = 2;
}

// Class of the hotel as a number of stars 1 to 5.
message HotelClassInfo {
  // Long value of the hotel class.
  optional int64 value = 2;
}

// Country or Region the hotel is located in.
message HotelCountryRegionInfo {
  // The Geo Target Constant resource name.
  optional string country_region_criterion = 2;
}

// State the hotel is located in.
message HotelStateInfo {
  // The Geo Target Constant resource name.
  optional string state_criterion = 2;
}

// City the hotel is located in.
message HotelCityInfo {
  // The Geo Target Constant resource name.
  optional string city_criterion = 2;
}

// Category of a product offer.
message ProductCategoryInfo {
  // ID of the product category.
  //
  // This ID is equivalent to the google_product_category ID as described in
  // this article: https://support.google.com/merchants/answer/6324436
  optional int64 category_id = 1;

  // Level of the product category.
  google.ads.googleads.v17.enums.ProductCategoryLevelEnum.ProductCategoryLevel
      level = 2;
}

// Brand of the product.
message ProductBrandInfo {
  // String value of the product brand.
  optional string value = 2;
}

// Locality of a product offer.
message ProductChannelInfo {
  // Value of the locality.
  google.ads.googleads.v17.enums.ProductChannelEnum.ProductChannel channel = 1;
}

// Availability of a product offer.
message ProductChannelExclusivityInfo {
  // Value of the availability.
  google.ads.googleads.v17.enums.ProductChannelExclusivityEnum
      .ProductChannelExclusivity channel_exclusivity = 1;
}

// Condition of a product offer.
message ProductConditionInfo {
  // Value of the condition.
  google.ads.googleads.v17.enums.ProductConditionEnum.ProductCondition
      condition = 1;
}

// Custom attribute of a product offer.
message ProductCustomAttributeInfo {
  // String value of the product custom attribute.
  optional string value = 3;

  // Indicates the index of the custom attribute.
  google.ads.googleads.v17.enums.ProductCustomAttributeIndexEnum
      .ProductCustomAttributeIndex index = 2;
}

// Item id of a product offer.
message ProductItemIdInfo {
  // Value of the id.
  optional string value = 2;
}

// Type of a product offer.
message ProductTypeInfo {
  // Value of the type.
  optional string value = 3;

  // Level of the type.
  google.ads.googleads.v17.enums.ProductTypeLevelEnum.ProductTypeLevel level =
      2;
}

// Grouping of a product offer. This listing dimension is deprecated and it is
// supported only in Display campaigns.
message ProductGroupingInfo {
  // String value of the product grouping.
  optional string value = 1;
}

// Labels of a product offer. This listing dimension is deprecated and it is
// supported only in Display campaigns.
message ProductLabelsInfo {
  // String value of the product labels.
  optional string value = 1;
}

// Legacy condition of a product offer. This listing dimension is deprecated and
// it is supported only in Display campaigns.
message ProductLegacyConditionInfo {
  // String value of the product legacy condition.
  optional string value = 1;
}

// Full type of a product offer. This listing dimension is deprecated and it is
// supported only in Display campaigns.
message ProductTypeFullInfo {
  // String value of the product full type.
  optional string value = 1;
}

// Unknown listing dimension.
message UnknownListingDimensionInfo {}

// Criterion for hotel date selection (default dates versus user selected).
message HotelDateSelectionTypeInfo {
  // Type of the hotel date selection
  google.ads.googleads.v17.enums.HotelDateSelectionTypeEnum
      .HotelDateSelectionType type = 1;
}

// Criterion for number of days prior to the stay the booking is being made.
message HotelAdvanceBookingWindowInfo {
  // Low end of the number of days prior to the stay.
  optional int64 min_days = 3;

  // High end of the number of days prior to the stay.
  optional int64 max_days = 4;
}

// Criterion for length of hotel stay in nights.
message HotelLengthOfStayInfo {
  // Low end of the number of nights in the stay.
  optional int64 min_nights = 3;

  // High end of the number of nights in the stay.
  optional int64 max_nights = 4;
}

// Criterion for a check-in date range.
message HotelCheckInDateRangeInfo {
  // Start date in the YYYY-MM-DD format.
  string start_date = 1;

  // End date in the YYYY-MM-DD format.
  string end_date = 2;
}

// Criterion for day of the week the booking is for.
message HotelCheckInDayInfo {
  // The day of the week.
  google.ads.googleads.v17.enums.DayOfWeekEnum.DayOfWeek day_of_week = 1;
}

// Advertiser-specific activity ID.
message ActivityIdInfo {
  // String value of the activity ID.
  optional string value = 1;
}

// Rating of the activity as a number 1 to 5, where 5 is the best.
message ActivityRatingInfo {
  // Long value of the activity rating.
  optional int64 value = 1;
}

// The country where the travel activity is available.
message ActivityCountryInfo {
  // String value of the activity country. The Geo Target Constant resource
  // name.
  optional string value = 1;
}

// The state where the travel activity is available.
message ActivityStateInfo {
  // String value of the activity state. The Geo Target Constant resource
  // name.
  optional string value = 1;
}

// The city where the travel activity is available.
message ActivityCityInfo {
  // String value of the activity city. The Geo Target Constant resource
  // name.
  optional string value = 1;
}

// Criterion for Interaction Type.
message InteractionTypeInfo {
  // The interaction type.
  google.ads.googleads.v17.enums.InteractionTypeEnum.InteractionType type = 1;
}

// Represents an AdSchedule criterion.
//
// AdSchedule is specified as the day of the week and a time interval
// within which ads will be shown.
//
// No more than six AdSchedules can be added for the same day.
message AdScheduleInfo {
  // Minutes after the start hour at which this schedule starts.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  google.ads.googleads.v17.enums.MinuteOfHourEnum.MinuteOfHour start_minute = 1;

  // Minutes after the end hour at which this schedule ends. The schedule is
  // exclusive of the end minute.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  google.ads.googleads.v17.enums.MinuteOfHourEnum.MinuteOfHour end_minute = 2;

  // Starting hour in 24 hour time.
  // This field must be between 0 and 23, inclusive.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  optional int32 start_hour = 6;

  // Ending hour in 24 hour time; 24 signifies end of the day.
  // This field must be between 0 and 24, inclusive.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  optional int32 end_hour = 7;

  // Day of the week the schedule applies to.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  google.ads.googleads.v17.enums.DayOfWeekEnum.DayOfWeek day_of_week = 5;
}

// An age range criterion.
message AgeRangeInfo {
  // Type of the age range.
  google.ads.googleads.v17.enums.AgeRangeTypeEnum.AgeRangeType type = 1;
}

// A gender criterion.
message GenderInfo {
  // Type of the gender.
  google.ads.googleads.v17.enums.GenderTypeEnum.GenderType type = 1;
}

// An income range criterion.
message IncomeRangeInfo {
  // Type of the income range.
  google.ads.googleads.v17.enums.IncomeRangeTypeEnum.IncomeRangeType type = 1;
}

// A parental status criterion.
message ParentalStatusInfo {
  // Type of the parental status.
  google.ads.googleads.v17.enums.ParentalStatusTypeEnum.ParentalStatusType
      type = 1;
}

// A YouTube Video criterion.
message YouTubeVideoInfo {
  // YouTube video id as it appears on the YouTube watch page.
  optional string video_id = 2;
}

// A YouTube Channel criterion.
message YouTubeChannelInfo {
  // The YouTube uploader channel id or the channel code of a YouTube channel.
  optional string channel_id = 2;
}

// A User List criterion. Represents a user list that is defined by the
// advertiser to be targeted.
message UserListInfo {
  // The User List resource name.
  optional string user_list = 2;
}

// A Proximity criterion. The geo point and radius determine what geographical
// area is included. The address is a description of the geo point that does
// not affect ad serving.
//
// There are two ways to create a proximity. First, by setting an address
// and radius. The geo point will be automatically computed. Second, by
// setting a geo point and radius. The address is an optional label that won't
// be validated.
message ProximityInfo {
  // Latitude and longitude.
  GeoPointInfo geo_point = 1;

  // The radius of the proximity.
  optional double radius = 5;

  // The unit of measurement of the radius. Default is KILOMETERS.
  google.ads.googleads.v17.enums.ProximityRadiusUnitsEnum.ProximityRadiusUnits
      radius_units = 3;

  // Full address.
  AddressInfo address = 4;
}

// Geo point for proximity criterion.
message GeoPointInfo {
  // Micro degrees for the longitude.
  optional int32 longitude_in_micro_degrees = 3;

  // Micro degrees for the latitude.
  optional int32 latitude_in_micro_degrees = 4;
}

// Address for proximity criterion.
message AddressInfo {
  // Postal code.
  optional string postal_code = 8;

  // Province or state code.
  optional string province_code = 9;

  // Country code.
  optional string country_code = 10;

  // Province or state name.
  optional string province_name = 11;

  // Street address line 1.
  optional string street_address = 12;

  // Street address line 2. This field is write-only. It is only used for
  // calculating the longitude and latitude of an address when geo_point is
  // empty.
  optional string street_address2 = 13;

  // Name of the city.
  optional string city_name = 14;
}

// A topic criterion. Use topics to target or exclude placements in the
// Google Display Network based on the category into which the placement falls
// (for example, "Pets & Animals/Pets/Dogs").
message TopicInfo {
  // The Topic Constant resource name.
  optional string topic_constant = 3 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/TopicConstant"
  }];

  // The category to target or exclude. Each subsequent element in the array
  // describes a more specific sub-category. For example,
  // "Pets & Animals", "Pets", "Dogs" represents the "Pets & Animals/Pets/Dogs"
  // category.
  repeated string path = 4;
}

// A language criterion.
message LanguageInfo {
  // The language constant resource name.
  optional string language_constant = 2;
}

// An IpBlock criterion used for IP exclusions. We allow:
//  - IPv4 and IPv6 addresses
//  - individual addresses (***********)
//  - masks for individual addresses (***********/32)
//  - masks for Class C networks (***********/24)
message IpBlockInfo {
  // The IP address of this IP block.
  optional string ip_address = 2;
}

// Content Label for category exclusion.
message ContentLabelInfo {
  // Content label type, required for CREATE operations.
  google.ads.googleads.v17.enums.ContentLabelTypeEnum.ContentLabelType type = 1;
}

// Represents a Carrier Criterion.
message CarrierInfo {
  // The Carrier constant resource name.
  optional string carrier_constant = 2 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CarrierConstant"
  }];
}

// Represents a particular interest-based topic to be targeted.
message UserInterestInfo {
  // The UserInterest resource name.
  optional string user_interest_category = 2;
}

// Represents a criterion for targeting webpages of an advertiser's website.
message WebpageInfo {
  // The name of the criterion that is defined by this parameter. The name value
  // will be used for identifying, sorting and filtering criteria with this type
  // of parameters.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  optional string criterion_name = 3;

  // Conditions, or logical expressions, for webpage targeting. The list of
  // webpage targeting conditions are and-ed together when evaluated
  // for targeting. An empty list of conditions indicates all pages of the
  // campaign's website are targeted.
  //
  // This field is required for CREATE operations and is prohibited on UPDATE
  // operations.
  repeated WebpageConditionInfo conditions = 2;

  // Website criteria coverage percentage. This is the computed percentage
  // of website coverage based on the website target, negative website target
  // and negative keywords in the ad group and campaign. For instance, when
  // coverage returns as 1, it indicates it has 100% coverage. This field is
  // read-only.
  double coverage_percentage = 4;

  // List of sample urls that match the website target. This field is read-only.
  WebpageSampleInfo sample = 5;
}

// Logical expression for targeting webpages of an advertiser's website.
message WebpageConditionInfo {
  // Operand of webpage targeting condition.
  google.ads.googleads.v17.enums.WebpageConditionOperandEnum
      .WebpageConditionOperand operand = 1;

  // Operator of webpage targeting condition.
  google.ads.googleads.v17.enums.WebpageConditionOperatorEnum
      .WebpageConditionOperator
      operator = 2;

  // Argument of webpage targeting condition.
  optional string argument = 4;
}

// List of sample urls that match the website target
message WebpageSampleInfo {
  // Webpage sample urls
  repeated string sample_urls = 1;
}

// Represents an operating system version to be targeted.
message OperatingSystemVersionInfo {
  // The operating system version constant resource name.
  optional string operating_system_version_constant = 2
      [(google.api.resource_reference) = {
        type: "googleads.googleapis.com/OperatingSystemVersionConstant"
      }];
}

// An app payment model criterion.
message AppPaymentModelInfo {
  // Type of the app payment model.
  google.ads.googleads.v17.enums.AppPaymentModelTypeEnum.AppPaymentModelType
      type = 1;
}

// A mobile device criterion.
message MobileDeviceInfo {
  // The mobile device constant resource name.
  optional string mobile_device_constant = 2
      [(google.api.resource_reference) = {
        type: "googleads.googleapis.com/MobileDeviceConstant"
      }];
}

// A custom affinity criterion.
// A criterion of this type is only targetable.
message CustomAffinityInfo {
  // The CustomInterest resource name.
  optional string custom_affinity = 2;
}

// A custom intent criterion.
// A criterion of this type is only targetable.
message CustomIntentInfo {
  // The CustomInterest resource name.
  optional string custom_intent = 2;
}

// A radius around a list of locations specified through a feed or assetSet.
message LocationGroupInfo {
  // Feed specifying locations for targeting. Cannot be set with AssetSet
  // fields. This is required and must be set in CREATE operations.
  optional string feed = 5;

  // Geo target constant(s) restricting the scope of the geographic area within
  // the feed. Currently only one geo target constant is allowed. Cannot be set
  // with AssetSet fields.
  repeated string geo_target_constants = 6;

  // Distance in units specifying the radius around targeted locations.
  // This is required and must be set in CREATE operations.
  optional int64 radius = 7;

  // Unit of the radius. Miles and meters are supported for geo target
  // constants. Milli miles and meters are supported for feed item sets and
  // asset sets. This is required and must be set in CREATE operations.
  google.ads.googleads.v17.enums.LocationGroupRadiusUnitsEnum
      .LocationGroupRadiusUnits radius_units = 4;

  // FeedItemSets whose FeedItems are targeted. If multiple IDs are specified,
  // then all items that appear in at least one set are targeted. This field
  // cannot be used with geo_target_constants. This is optional and can only be
  // set in CREATE operations. Cannot be set with AssetSet fields.
  repeated string feed_item_sets = 8;

  // Denotes that the latest customer level asset set is used for targeting.
  // Used with radius and radius_units. Cannot be used with
  // feed, geo target constants or feed item sets. When using asset sets, either
  // this field or location_group_asset_sets should be specified. Both cannot be
  // used at the same time. This can only be set in CREATE operations.
  optional bool enable_customer_level_location_asset_set = 9;

  // AssetSets whose Assets are targeted. If multiple IDs are specified, then
  // all items that appear in at least one set are targeted. This field cannot
  // be used with feed, geo target constants or feed item sets. When using asset
  // sets, either this field or enable_customer_level_location_asset_set should
  // be specified. Both cannot be used at the same time. This can only be set
  // in CREATE operations.
  repeated string location_group_asset_sets = 10;
}

// A custom audience criterion.
message CustomAudienceInfo {
  // The CustomAudience resource name.
  string custom_audience = 1;
}

// A combined audience criterion.
message CombinedAudienceInfo {
  // The CombinedAudience resource name.
  string combined_audience = 1 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/CombinedAudience"
  }];
}

// An audience criterion.
message AudienceInfo {
  // The Audience resource name.
  string audience = 1;
}

// A Smart Campaign keyword theme.
message KeywordThemeInfo {
  // Either a predefined keyword theme constant or free-form text may be
  // specified.
  oneof keyword_theme {
    // The resource name of a Smart Campaign keyword theme constant.
    // `keywordThemeConstants/{keyword_theme_id}~{sub_keyword_theme_id}`
    string keyword_theme_constant = 1 [(google.api.resource_reference) = {
      type: "googleads.googleapis.com/KeywordThemeConstant"
    }];

    // Free-form text to be matched to a Smart Campaign keyword theme constant
    // on a best-effort basis.
    string free_form_keyword_theme = 2;
  }
}

// A Local Services Ads service ID. Represents a service type
// (such as install_faucet) that a Local Services Campaign can target.
message LocalServiceIdInfo {
  // The criterion resource name.
  string service_id = 1;
}

// A Search Theme criterion only on Performance Max campaign. Represents a
// keyword-like advertiser input.
message SearchThemeInfo {
  // Each Search Theme has a value of a simple string, like keywords.
  // There are limits on overall length, allowed characters, and number
  // of words.
  string text = 1;
}

// Represents a Brand Criterion used for targeting based on commercial knowledge
// graph.
message BrandInfo {
  // Output only. A text representation of a brand.
  optional string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The Commercial KG MID for the brand.
  optional string entity_id = 1;

  // Output only. The primary url of a brand.
  optional string primary_url = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The rejection reason when a brand status is REJECTED.
  optional google.ads.googleads.v17.enums.BrandRequestRejectionReasonEnum
      .BrandRequestRejectionReason rejection_reason = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of a brand.
  optional google.ads.googleads.v17.enums.BrandStateEnum.BrandState status = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A Brand List Criterion is used to specify a list of brands.  The list is
// represented as a SharedSet id type BRAND_HINT. A criterion of this type can
// be either targeted or excluded.
message BrandListInfo {
  // Shared set resource name of the brand list.
  optional string shared_set = 1;
}
