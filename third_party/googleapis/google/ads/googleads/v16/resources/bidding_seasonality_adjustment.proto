// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/advertising_channel_type.proto";
import "google/ads/googleads/v16/enums/device.proto";
import "google/ads/googleads/v16/enums/seasonality_event_scope.proto";
import "google/ads/googleads/v16/enums/seasonality_event_status.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "BiddingSeasonalityAdjustmentProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Represents a bidding seasonality adjustment.
//
// See "About seasonality adjustments" at
// https://support.google.com/google-ads/answer/10369906.
message BiddingSeasonalityAdjustment {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/BiddingSeasonalityAdjustment"
    pattern: "customers/{customer_id}/biddingSeasonalityAdjustments/{seasonality_event_id}"
  };

  // Immutable. The resource name of the seasonality adjustment.
  // Seasonality adjustment resource names have the form:
  //
  // `customers/{customer_id}/biddingSeasonalityAdjustments/{seasonality_adjustment_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/BiddingSeasonalityAdjustment"
    }
  ];

  // Output only. The ID of the seasonality adjustment.
  int64 seasonality_adjustment_id = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The scope of the seasonality adjustment.
  google.ads.googleads.v16.enums.SeasonalityEventScopeEnum.SeasonalityEventScope
      scope = 3;

  // Output only. The status of the seasonality adjustment.
  google.ads.googleads.v16.enums.SeasonalityEventStatusEnum
      .SeasonalityEventStatus status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The inclusive start time of the seasonality adjustment in
  // yyyy-MM-dd HH:mm:ss format.
  //
  // A seasonality adjustment is forward looking and should be used for events
  // that start and end in the future.
  string start_date_time = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The exclusive end time of the seasonality adjustment in
  // yyyy-MM-dd HH:mm:ss format.
  //
  // The length of [start_date_time, end_date_time) interval must be
  // within (0, 14 days].
  string end_date_time = 6 [(google.api.field_behavior) = REQUIRED];

  // The name of the seasonality adjustment. The name can be at most 255
  // characters.
  string name = 7;

  // The description of the seasonality adjustment. The description can be at
  // most 2048 characters.
  string description = 8;

  // If not specified, all devices will be included in this adjustment.
  // Otherwise, only the specified targeted devices will be included in this
  // adjustment.
  repeated google.ads.googleads.v16.enums.DeviceEnum.Device devices = 9;

  // Conversion rate modifier estimated based on expected conversion rate
  // changes. When this field is unset or set to 1.0 no adjustment will be
  // applied to traffic. The allowed range is 0.1 to 10.0.
  double conversion_rate_modifier = 10;

  // The seasonality adjustment will apply to the campaigns listed when the
  // scope of this adjustment is CAMPAIGN. The maximum number of campaigns per
  // event is 2000.
  // Note: a seasonality adjustment with both advertising_channel_types and
  // campaign_ids is not supported.
  repeated string campaigns = 11 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/Campaign"
  }];

  // The seasonality adjustment will apply to all the campaigns under the listed
  // channels retroactively as well as going forward when the scope of this
  // adjustment is CHANNEL.
  // The supported advertising channel types are DISPLAY, SEARCH and SHOPPING.
  // Note: a seasonality adjustment with both advertising_channel_types and
  // campaign_ids is not supported.
  repeated google.ads.googleads.v16.enums.AdvertisingChannelTypeEnum
      .AdvertisingChannelType advertising_channel_types = 12;
}
