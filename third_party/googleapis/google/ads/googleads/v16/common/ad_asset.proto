// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/common/asset_policy.proto";
import "google/ads/googleads/v16/enums/asset_performance_label.proto";
import "google/ads/googleads/v16/enums/served_asset_field_type.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "AdAssetProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file describing assets used inside an ad.

// A text asset used inside an ad.
message AdTextAsset {
  // Asset text.
  optional string text = 4;

  // The pinned field of the asset. This restricts the asset to only serve
  // within this field. Multiple assets can be pinned to the same field. An
  // asset that is unpinned or pinned to a different field will not serve in a
  // field where some other asset has been pinned.
  google.ads.googleads.v16.enums.ServedAssetFieldTypeEnum.ServedAssetFieldType
      pinned_field = 2;

  // The performance label of this text asset.
  google.ads.googleads.v16.enums.AssetPerformanceLabelEnum.AssetPerformanceLabel
      asset_performance_label = 5;

  // The policy summary of this text asset.
  AdAssetPolicySummary policy_summary_info = 6;
}

// An image asset used inside an ad.
message AdImageAsset {
  // The Asset resource name of this image.
  optional string asset = 2;
}

// A video asset used inside an ad.
message AdVideoAsset {
  // The Asset resource name of this video.
  optional string asset = 2;
}

// A media bundle asset used inside an ad.
message AdMediaBundleAsset {
  // The Asset resource name of this media bundle.
  optional string asset = 2;
}

// A discovery carousel card asset used inside an ad.
message AdDiscoveryCarouselCardAsset {
  // The Asset resource name of this discovery carousel card.
  optional string asset = 1;
}

// A call to action asset used inside an ad.
message AdCallToActionAsset {
  // The Asset resource name of this call to action asset.
  optional string asset = 1;
}
