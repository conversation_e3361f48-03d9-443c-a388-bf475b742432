// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/contentwarehouse/v1/rule_engine.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "RuleSetServiceRequestProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// Request message for RuleSetService.CreateRuleSet.
message CreateRuleSetRequest {
  // Required. The parent name.
  // Format: projects/{project_number}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // Required. The rule set to create.
  RuleSet rule_set = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for RuleSetService.GetRuleSet.
message GetRuleSetRequest {
  // Required. The name of the rule set to retrieve.
  // Format:
  // projects/{project_number}/locations/{location}/ruleSets/{rule_set_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/RuleSet"
    }
  ];
}

// Request message for RuleSetService.UpdateRuleSet.
message UpdateRuleSetRequest {
  // Required. The name of the rule set to update.
  // Format:
  // projects/{project_number}/locations/{location}/ruleSets/{rule_set_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/RuleSet"
    }
  ];

  // Required. The rule set to update.
  RuleSet rule_set = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for RuleSetService.DeleteRuleSet.
message DeleteRuleSetRequest {
  // Required. The name of the rule set to delete.
  // Format:
  // projects/{project_number}/locations/{location}/ruleSets/{rule_set_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/RuleSet"
    }
  ];
}

// Request message for RuleSetService.ListRuleSets.
message ListRuleSetsRequest {
  // Required. The parent, which owns this collection of document.
  // Format: projects/{project_number}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // The maximum number of rule sets to return. The service may return
  // fewer than this value.
  // If unspecified, at most 50 rule sets will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListRuleSets` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListRuleSets`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for RuleSetService.ListRuleSets.
message ListRuleSetsResponse {
  // The rule sets from the specified parent.
  repeated RuleSet rule_sets = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
