{"methodConfig": [{"name": [{"service": "google.cloud.apigeeregistry.v1.Registry", "method": "ListApis"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "GetApi"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "CreateApi"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "UpdateA<PERSON>"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "DeleteApi"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "ListApiVersions"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "GetApiVersion"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "CreateApiVersion"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "UpdateApiVersion"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "DeleteApiVersion"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "ListApiSpecs"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "GetApiSpec"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "GetApiSpecContents"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "CreateApiSpec"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "UpdateApiSpec"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "DeleteApiSpec"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "TagApiSpecRevision"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "ListApiSpecRevisions"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "DeleteApiSpecRevision"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "ListApiDeployments"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "GetApiDeployment"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "CreateApiDeployment"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "UpdateApiDeployment"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "DeleteApiDeployment"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "TagApiDeploymentRevision"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "ListApiDeploymentRevisions"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "DeleteApiDeploymentRevision"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "ListArtifacts"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "GetArtifact"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "GetArtifactContents"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "CreateArtifact"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "ReplaceArtifact"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "DeleteArtifact"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.200s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["ABORTED", "CANCELLED", "DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.apigeeregistry.v1.Registry", "method": "RollbackApiSpec"}, {"service": "google.cloud.apigeeregistry.v1.Registry", "method": "RollbackApiDeployment"}], "timeout": "60s"}]}