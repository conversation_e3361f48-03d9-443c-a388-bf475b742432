// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.resourcemanager.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ResourceManager.V3";
option go_package = "cloud.google.com/go/resourcemanager/apiv3/resourcemanagerpb;resourcemanagerpb";
option java_multiple_files = true;
option java_outer_classname = "ProjectsProto";
option java_package = "com.google.cloud.resourcemanager.v3";
option php_namespace = "Google\\Cloud\\ResourceManager\\V3";
option ruby_package = "Google::Cloud::ResourceManager::V3";

// Manages Google Cloud Projects.
service Projects {
  option (google.api.default_host) = "cloudresourcemanager.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Retrieves the project identified by the specified `name` (for example,
  // `projects/415104041262`).
  //
  // The caller must have `resourcemanager.projects.get` permission
  // for this project.
  rpc GetProject(GetProjectRequest) returns (Project) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists projects that are direct children of the specified folder or
  // organization resource. `list()` provides a strongly consistent view of the
  // projects underneath the specified parent resource. `list()` returns
  // projects sorted based upon the (ascending) lexical ordering of their
  // `display_name`. The caller must have `resourcemanager.projects.list`
  // permission on the identified parent.
  rpc ListProjects(ListProjectsRequest) returns (ListProjectsResponse) {
    option (google.api.http) = {
      get: "/v3/projects"
    };
    option (google.api.method_signature) = "parent";
  }

  // Search for projects that the caller has both `resourcemanager.projects.get`
  // permission on, and also satisfy the specified query.
  //
  // This method returns projects in an unspecified order.
  //
  // This method is eventually consistent with project mutations; this means
  // that a newly created project may not appear in the results or recent
  // updates to an existing project may not be reflected in the results. To
  // retrieve the latest state of a project, use the
  // [GetProject][google.cloud.resourcemanager.v3.Projects.GetProject] method.
  rpc SearchProjects(SearchProjectsRequest) returns (SearchProjectsResponse) {
    option (google.api.http) = {
      get: "/v3/projects:search"
    };
    option (google.api.method_signature) = "query";
  }

  // Request that a new project be created. The result is an `Operation` which
  // can be used to track the creation process. This process usually takes a few
  // seconds, but can sometimes take much longer. The tracking `Operation` is
  // automatically deleted after a few hours, so there is no need to call
  // `DeleteOperation`.
  rpc CreateProject(CreateProjectRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/projects"
      body: "project"
    };
    option (google.api.method_signature) = "project";
    option (google.longrunning.operation_info) = {
      response_type: "Project"
      metadata_type: "CreateProjectMetadata"
    };
  }

  // Updates the `display_name` and labels of the project identified by the
  // specified `name` (for example, `projects/415104041262`). Deleting all
  // labels requires an update mask for labels field.
  //
  // The caller must have `resourcemanager.projects.update` permission for this
  // project.
  rpc UpdateProject(UpdateProjectRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v3/{project.name=projects/*}"
      body: "project"
    };
    option (google.api.method_signature) = "project,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Project"
      metadata_type: "UpdateProjectMetadata"
    };
  }

  // Move a project to another place in your resource hierarchy, under a new
  // resource parent.
  //
  // Returns an operation which can be used to track the process of the project
  // move workflow.
  // Upon success, the `Operation.response` field will be populated with the
  // moved project.
  //
  // The caller must have `resourcemanager.projects.move` permission on the
  // project, on the project's current and proposed new parent.
  //
  // If project has no current parent, or it currently does not have an
  // associated organization resource, you will also need the
  // `resourcemanager.projects.setIamPolicy` permission in the project.
  //
  //
  rpc MoveProject(MoveProjectRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*}:move"
      body: "*"
    };
    option (google.api.method_signature) = "name, destination_parent";
    option (google.longrunning.operation_info) = {
      response_type: "Project"
      metadata_type: "MoveProjectMetadata"
    };
  }

  // Marks the project identified by the specified
  // `name` (for example, `projects/415104041262`) for deletion.
  //
  // This method will only affect the project if it has a lifecycle state of
  // [ACTIVE][google.cloud.resourcemanager.v3.Project.State.ACTIVE].
  //
  // This method changes the Project's lifecycle state from
  // [ACTIVE][google.cloud.resourcemanager.v3.Project.State.ACTIVE]
  // to
  // [DELETE_REQUESTED][google.cloud.resourcemanager.v3.Project.State.DELETE_REQUESTED].
  // The deletion starts at an unspecified time,
  // at which point the Project is no longer accessible.
  //
  // Until the deletion completes, you can check the lifecycle state
  // checked by retrieving the project with [GetProject]
  // [google.cloud.resourcemanager.v3.Projects.GetProject],
  // and the project remains visible to [ListProjects]
  // [google.cloud.resourcemanager.v3.Projects.ListProjects].
  // However, you cannot update the project.
  //
  // After the deletion completes, the project is not retrievable by
  // the  [GetProject]
  // [google.cloud.resourcemanager.v3.Projects.GetProject],
  // [ListProjects]
  // [google.cloud.resourcemanager.v3.Projects.ListProjects], and
  // [SearchProjects][google.cloud.resourcemanager.v3.Projects.SearchProjects]
  // methods.
  //
  // This method behaves idempotently, such that deleting a `DELETE_REQUESTED`
  // project will not cause an error, but also won't do anything.
  //
  // The caller must have `resourcemanager.projects.delete` permissions for this
  // project.
  rpc DeleteProject(DeleteProjectRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v3/{name=projects/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Project"
      metadata_type: "DeleteProjectMetadata"
    };
  }

  // Restores the project identified by the specified
  // `name` (for example, `projects/415104041262`).
  // You can only use this method for a project that has a lifecycle state of
  // [DELETE_REQUESTED]
  // [Projects.State.DELETE_REQUESTED].
  // After deletion starts, the project cannot be restored.
  //
  // The caller must have `resourcemanager.projects.undelete` permission for
  // this project.
  rpc UndeleteProject(UndeleteProjectRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*}:undelete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Project"
      metadata_type: "UndeleteProjectMetadata"
    };
  }

  // Returns the IAM access control policy for the specified project, in the
  // format `projects/{ProjectIdOrNumber}` e.g. projects/123.
  // Permission is denied if the policy or the resource do not exist.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v3/{resource=projects/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Sets the IAM access control policy for the specified project, in the
  // format `projects/{ProjectIdOrNumber}` e.g. projects/123.
  //
  // CAUTION: This method will replace the existing policy, and cannot be used
  // to append additional IAM settings.
  //
  // Note: Removing service accounts from policies or changing their roles can
  // render services completely inoperable. It is important to understand how
  // the service account is being used before removing or updating its roles.
  //
  // The following constraints apply when using `setIamPolicy()`:
  //
  // + Project does not support `allUsers` and `allAuthenticatedUsers` as
  // `members` in a `Binding` of a `Policy`.
  //
  // + The owner role can be granted to a `user`, `serviceAccount`, or a group
  // that is part of an organization. For example,
  // <EMAIL> could be added as an owner to a project in
  // the myownpersonaldomain.com organization, but not the examplepetstore.com
  // organization.
  //
  // + Service accounts can be made owners of a project directly
  // without any restrictions. However, to be added as an owner, a user must be
  // invited using the Cloud Platform console and must accept the invitation.
  //
  // + A user cannot be granted the owner role using `setIamPolicy()`. The user
  // must be granted the owner role using the Cloud Platform Console and must
  // explicitly accept the invitation.
  //
  // + Invitations to grant the owner role cannot be sent using
  // `setIamPolicy()`;
  // they must be sent only using the Cloud Platform Console.
  //
  // + If the project is not part of an organization, there must be at least
  // one owner who has accepted the Terms of Service (ToS) agreement in the
  // policy. Calling `setIamPolicy()` to remove the last ToS-accepted owner
  // from the policy will fail. This restriction also applies to legacy
  // projects that no longer have owners who have accepted the ToS. Edits to
  // IAM policies will be rejected until the lack of a ToS-accepting owner is
  // rectified. If the project is part of an organization, you can remove all
  // owners, potentially making the organization inaccessible.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v3/{resource=projects/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource, policy";
  }

  // Returns permissions that a caller has on the specified project, in the
  // format `projects/{ProjectIdOrNumber}` e.g. projects/123..
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v3/{resource=projects/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource, permissions";
  }
}

// A project is a high-level Google Cloud entity. It is a
// container for ACLs, APIs, App Engine Apps, VMs, and other
// Google Cloud Platform resources.
message Project {
  option (google.api.resource) = {
    type: "cloudresourcemanager.googleapis.com/Project"
    pattern: "projects/{project}"
    style: DECLARATIVE_FRIENDLY
  };

  // Project lifecycle states.
  enum State {
    // Unspecified state.  This is only used/useful for distinguishing
    // unset values.
    STATE_UNSPECIFIED = 0;

    // The normal and active state.
    ACTIVE = 1;

    // The project has been marked for deletion by the user
    // (by invoking
    // [DeleteProject][google.cloud.resourcemanager.v3.Projects.DeleteProject])
    // or by the system (Google Cloud Platform).
    // This can generally be reversed by invoking [UndeleteProject]
    // [google.cloud.resourcemanager.v3.Projects.UndeleteProject].
    DELETE_REQUESTED = 2;
  }

  // Output only. The unique resource name of the project. It is an int64
  // generated number prefixed by "projects/".
  //
  // Example: `projects/415104041262`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A reference to a parent Resource. eg., `organizations/123` or
  // `folders/876`.
  string parent = 2 [(google.api.field_behavior) = OPTIONAL];

  // Immutable. The unique, user-assigned id of the project.
  // It must be 6 to 30 lowercase ASCII letters, digits, or hyphens.
  // It must start with a letter.
  // Trailing hyphens are prohibited.
  //
  // Example: `tokyo-rain-123`
  string project_id = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The project lifecycle state.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A user-assigned display name of the project.
  // When present it must be between 4 to 30 characters.
  // Allowed characters are: lowercase and uppercase letters, numbers,
  // hyphen, single-quote, double-quote, space, and exclamation point.
  //
  // Example: `My Project`
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Creation time.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The most recent time this resource was modified.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this resource was requested for deletion.
  google.protobuf.Timestamp delete_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A checksum computed by the server based on the current value
  // of the Project resource. This may be sent on update and delete requests to
  // ensure the client has an up-to-date value before proceeding.
  string etag = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The labels associated with this project.
  //
  // Label keys must be between 1 and 63 characters long and must conform
  // to the following regular expression: \[a-z\](\[-a-z0-9\]*\[a-z0-9\])?.
  //
  // Label values must be between 0 and 63 characters long and must conform
  // to the regular expression (\[a-z\](\[-a-z0-9\]*\[a-z0-9\])?)?.
  //
  // No more than 64 labels can be associated with a given resource.
  //
  // Clients should store labels in a representation such as JSON that does not
  // depend on specific characters being disallowed.
  //
  // Example: `"myBusinessDimension" : "businessValue"`
  map<string, string> labels = 10 [(google.api.field_behavior) = OPTIONAL];
}

// The request sent to the
// [GetProject][google.cloud.resourcemanager.v3.Projects.GetProject]
// method.
message GetProjectRequest {
  // Required. The name of the project (for example, `projects/415104041262`).
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];
}

// The request sent to the
// [ListProjects][google.cloud.resourcemanager.v3.Projects.ListProjects]
// method.
message ListProjectsRequest {
  // Required. The name of the parent resource whose projects are being listed.
  // Only children of this parent resource are listed; descendants are not
  // listed.
  //
  // If the parent is a folder, use the value `folders/{folder_id}`. If the
  // parent is an organization, use the value `organizations/{org_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "*" }
  ];

  // Optional. A pagination token returned from a previous call to
  // [ListProjects] [google.cloud.resourcemanager.v3.Projects.ListProjects] that
  // indicates from where listing should continue.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of projects to return in the response.
  // The server can return fewer projects than requested.
  // If unspecified, server picks an appropriate default.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicate that projects in the `DELETE_REQUESTED` state should
  // also be returned. Normally only `ACTIVE` projects are returned.
  bool show_deleted = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A page of the response received from the
// [ListProjects][google.cloud.resourcemanager.v3.Projects.ListProjects]
// method.
//
// A paginated response where more pages are available has
// `next_page_token` set. This token can be used in a subsequent request to
// retrieve the next request page.
//
// NOTE: A response may contain fewer elements than the request `page_size` and
// still have a `next_page_token`.
message ListProjectsResponse {
  // The list of Projects under the parent. This list can be paginated.
  repeated Project projects = 1;

  // Pagination token.
  //
  // If the result set is too large to fit in a single response, this token
  // is returned. It encodes the position of the current result cursor.
  // Feeding this value into a new list request with the `page_token` parameter
  // gives the next page of the results.
  //
  // When `next_page_token` is not filled in, there is no next page and
  // the list returned is the last page in the result set.
  //
  // Pagination tokens have a limited lifetime.
  string next_page_token = 2;
}

// The request sent to the
// [SearchProjects][google.cloud.resourcemanager.v3.Projects.SearchProjects]
// method.
message SearchProjectsRequest {
  // Optional. A query string for searching for projects that the caller has
  // `resourcemanager.projects.get` permission to. If multiple fields are
  // included in the query, then it will return results that match any of the
  // fields. Some eligible fields are:
  //
  // - **`displayName`, `name`**: Filters by displayName.
  // - **`parent`**: Project's parent (for example: `folders/123`,
  // `organizations/*`). Prefer `parent` field over `parent.type` and
  // `parent.id`.
  // - **`parent.type`**: Parent's type: `folder` or `organization`.
  // - **`parent.id`**: Parent's id number (for example: `123`).
  // - **`id`, `projectId`**: Filters by projectId.
  // - **`state`, `lifecycleState`**: Filters by state.
  // - **`labels`**: Filters by label name or value.
  // - **`labels.<key>` (where `<key>` is the name of a label)**: Filters by label
  // name.
  //
  // Search expressions are case insensitive.
  //
  // Some examples queries:
  //
  //
  // - **`name:how*`**: The project's name starts with "how".
  // - **`name:Howl`**: The project's name is `Howl` or `howl`.
  // - **`name:HOWL`**: Equivalent to above.
  // - **`NAME:howl`**: Equivalent to above.
  // - **`labels.color:*`**: The project has the label `color`.
  // - **`labels.color:red`**:  The project's label `color` has the value `red`.
  // - **`labels.color:red labels.size:big`**: The project's label `color` has
  // the value `red` or its label `size` has the value `big`.
  //
  // If no query is specified, the call will return projects for which the user
  // has the `resourcemanager.projects.get` permission.
  string query = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous call to
  // [ListProjects] [google.cloud.resourcemanager.v3.Projects.ListProjects] that
  // indicates from where listing should continue.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of projects to return in the response.
  // The server can return fewer projects than requested.
  // If unspecified, server picks an appropriate default.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A page of the response received from the
// [SearchProjects][google.cloud.resourcemanager.v3.Projects.SearchProjects]
// method.
//
// A paginated response where more pages are available has
// `next_page_token` set. This token can be used in a subsequent request to
// retrieve the next request page.
message SearchProjectsResponse {
  // The list of Projects that matched the list filter query. This list can
  // be paginated.
  repeated Project projects = 1;

  // Pagination token.
  //
  // If the result set is too large to fit in a single response, this token
  // is returned. It encodes the position of the current result cursor.
  // Feeding this value into a new list request with the `page_token` parameter
  // gives the next page of the results.
  //
  // When `next_page_token` is not filled in, there is no next page and
  // the list returned is the last page in the result set.
  //
  // Pagination tokens have a limited lifetime.
  string next_page_token = 2;
}

// The request sent to the
// [CreateProject][google.cloud.resourcemanager.v3.Projects.CreateProject]
// method.
message CreateProjectRequest {
  // Required. The Project to create.
  //
  // Project ID is required. If the requested ID is unavailable, the request
  // fails.
  //
  // If the `parent` field is set, the `resourcemanager.projects.create`
  // permission is checked on the parent resource. If no parent is set and
  // the authorization credentials belong to an Organization, the parent
  // will be set to that Organization.
  Project project = 1 [(google.api.field_behavior) = REQUIRED];
}

// A status object which is used as the `metadata` field for the Operation
// returned by CreateProject. It provides insight for when significant phases of
// Project creation have completed.
message CreateProjectMetadata {
  // Creation time of the project creation workflow.
  google.protobuf.Timestamp create_time = 1;

  // True if the project can be retrieved using `GetProject`. No other
  // operations on the project are guaranteed to work until the project creation
  // is complete.
  bool gettable = 2;

  // True if the project creation process is complete.
  bool ready = 3;
}

// The request sent to the
// [UpdateProject][google.cloud.resourcemanager.v3.Projects.UpdateProject]
// method.
//
// Only the `display_name` and `labels` fields can be change. Use the
// [MoveProject][google.cloud.resourcemanager.v3.Projects.MoveProject] method to
// change the `parent` field.
message UpdateProjectRequest {
  // Required. The new definition of the project.
  Project project = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. An update mask to selectively update fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// A status object which is used as the `metadata` field for the Operation
// returned by UpdateProject.
message UpdateProjectMetadata {}

// The request sent to
// [MoveProject][google.cloud.resourcemanager.v3.Projects.MoveProject]
// method.
message MoveProjectRequest {
  // Required. The name of the project to move.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The new parent to move the Project under.
  string destination_parent = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "*" }
  ];
}

// A status object which is used as the `metadata` field for the Operation
// returned by MoveProject.
message MoveProjectMetadata {}

// [DeleteProject][google.cloud.resourcemanager.v3.Projects.DeleteProject]
// method.
message DeleteProjectRequest {
  // Required. The name of the Project (for example, `projects/415104041262`).
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];
}

// A status object which is used as the `metadata` field for the Operation
// returned by `DeleteProject`.
message DeleteProjectMetadata {}

// The request sent to the [UndeleteProject]
// [google.cloud.resourcemanager.v3.Projects.UndeleteProject]
// method.
message UndeleteProjectRequest {
  // Required. The name of the project (for example, `projects/415104041262`).
  //
  // Required.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];
}

// A status object which is used as the `metadata` field for the Operation
// returned by `UndeleteProject`.
message UndeleteProjectMetadata {}
