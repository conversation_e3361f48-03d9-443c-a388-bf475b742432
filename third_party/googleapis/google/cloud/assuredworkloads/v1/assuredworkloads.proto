// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.assuredworkloads.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AssuredWorkloads.V1";
option go_package = "cloud.google.com/go/assuredworkloads/apiv1/assuredworkloadspb;assuredworkloadspb";
option java_multiple_files = true;
option java_outer_classname = "AssuredworkloadsProto";
option java_package = "com.google.cloud.assuredworkloads.v1";
option php_namespace = "Google\\Cloud\\AssuredWorkloads\\V1";
option ruby_package = "Google::Cloud::AssuredWorkloads::V1";
option (google.api.resource_definition) = {
  type: "assuredworkloads.googleapis.com/Location"
  pattern: "organizations/{organization}/locations/{location}"
};

// Service to manage AssuredWorkloads.
service AssuredWorkloadsService {
  option (google.api.default_host) = "assuredworkloads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Creates Assured Workload.
  rpc CreateWorkload(CreateWorkloadRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/locations/*}/workloads"
      body: "workload"
    };
    option (google.api.method_signature) = "parent,workload";
    option (google.longrunning.operation_info) = {
      response_type: "Workload"
      metadata_type: "CreateWorkloadOperationMetadata"
    };
  }

  // Updates an existing workload.
  // Currently allows updating of workload display_name and labels.
  // For force updates don't set etag field in the Workload.
  // Only one update operation per workload can be in progress.
  rpc UpdateWorkload(UpdateWorkloadRequest) returns (Workload) {
    option (google.api.http) = {
      patch: "/v1/{workload.name=organizations/*/locations/*/workloads/*}"
      body: "workload"
    };
    option (google.api.method_signature) = "workload,update_mask";
  }

  // Restrict the list of resources allowed in the Workload environment.
  // The current list of allowed products can be found at
  // https://cloud.google.com/assured-workloads/docs/supported-products
  // In addition to assuredworkloads.workload.update permission, the user should
  // also have orgpolicy.policy.set permission on the folder resource
  // to use this functionality.
  rpc RestrictAllowedResources(RestrictAllowedResourcesRequest) returns (RestrictAllowedResourcesResponse) {
    option (google.api.http) = {
      post: "/v1/{name=organizations/*/locations/*/workloads/*}:restrictAllowedResources"
      body: "*"
    };
  }

  // Deletes the workload. Make sure that workload's direct children are already
  // in a deleted state, otherwise the request will fail with a
  // FAILED_PRECONDITION error.
  rpc DeleteWorkload(DeleteWorkloadRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/locations/*/workloads/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets Assured Workload associated with a CRM Node
  rpc GetWorkload(GetWorkloadRequest) returns (Workload) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/workloads/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Assured Workloads under a CRM Node.
  rpc ListWorkloads(ListWorkloadsRequest) returns (ListWorkloadsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*}/workloads"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the Violations in the AssuredWorkload Environment.
  // Callers may also choose to read across multiple Workloads as per
  // [AIP-159](https://google.aip.dev/159) by using '-' (the hyphen or dash
  // character) as a wildcard character instead of workload-id in the parent.
  // Format `organizations/{org_id}/locations/{location}/workloads/-`
  rpc ListViolations(ListViolationsRequest) returns (ListViolationsResponse) {
    option (google.api.method_signature) = "parent";
  }

  // Retrieves Assured Workload Violation based on ID.
  rpc GetViolation(GetViolationRequest) returns (Violation) {
    option (google.api.method_signature) = "name";
  }

  // Acknowledges an existing violation. By acknowledging a violation, users
  // acknowledge the existence of a compliance violation in their workload and
  // decide to ignore it due to a valid business justification. Acknowledgement
  // is a permanent operation and it cannot be reverted.
  rpc AcknowledgeViolation(AcknowledgeViolationRequest) returns (AcknowledgeViolationResponse) {
  }
}

// Request for creating a workload.
message CreateWorkloadRequest {
  // Required. The resource name of the new Workload's parent.
  // Must be of the form `organizations/{org_id}/locations/{location_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "assuredworkloads.googleapis.com/Workload"
    }
  ];

  // Required. Assured Workload to create
  Workload workload = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A identifier associated with the workload and underlying projects which
  // allows for the break down of billing costs for a workload. The value
  // provided for the identifier will add a label to the workload and contained
  // projects with the identifier as the value.
  string external_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for Updating a workload.
message UpdateWorkloadRequest {
  // Required. The workload to update.
  // The workload's `name` field is used to identify the workload to be updated.
  // Format:
  // organizations/{org_id}/locations/{location_id}/workloads/{workload_id}
  Workload workload = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for deleting a Workload.
message DeleteWorkloadRequest {
  // Required. The `name` field is used to identify the workload.
  // Format:
  // organizations/{org_id}/locations/{location_id}/workloads/{workload_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "assuredworkloads.googleapis.com/Workload"
    }
  ];

  // Optional. The etag of the workload.
  // If this is provided, it must match the server's etag.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for fetching a workload.
message GetWorkloadRequest {
  // Required. The resource name of the Workload to fetch. This is the workload's
  // relative path in the API, formatted as
  // "organizations/{organization_id}/locations/{location_id}/workloads/{workload_id}".
  // For example,
  // "organizations/123/locations/us-east1/workloads/assured-workload-1".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "assuredworkloads.googleapis.com/Workload"
    }
  ];
}

// Request for fetching workloads in an organization.
message ListWorkloadsRequest {
  // Required. Parent Resource to list workloads from.
  // Must be of the form `organizations/{org_id}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "assuredworkloads.googleapis.com/Workload"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token returned from previous request. Page token contains context from
  // previous request. Page token needs to be passed in the second and following
  // requests.
  string page_token = 3;

  // A custom filter for filtering by properties of a workload. At this time,
  // only filtering by labels is supported.
  string filter = 4;
}

// Response of ListWorkloads endpoint.
message ListWorkloadsResponse {
  // List of Workloads under a given parent.
  repeated Workload workloads = 1;

  // The next page token. Return empty if reached the last page.
  string next_page_token = 2;
}

// A Workload object for managing highly regulated workloads of cloud
// customers.
message Workload {
  option (google.api.resource) = {
    type: "assuredworkloads.googleapis.com/Workload"
    pattern: "organizations/{organization}/locations/{location}/workloads/{workload}"
  };

  // Represent the resources that are children of this Workload.
  message ResourceInfo {
    // The type of resource.
    enum ResourceType {
      // Unknown resource type.
      RESOURCE_TYPE_UNSPECIFIED = 0;

      // Consumer project.
      // AssuredWorkloads Projects are no longer supported. This field will be
      // ignored only in CreateWorkload requests. ListWorkloads and GetWorkload
      // will continue to provide projects information.
      // Use CONSUMER_FOLDER instead.
      CONSUMER_PROJECT = 1 [deprecated = true];

      // Consumer Folder.
      CONSUMER_FOLDER = 4;

      // Consumer project containing encryption keys.
      ENCRYPTION_KEYS_PROJECT = 2;

      // Keyring resource that hosts encryption keys.
      KEYRING = 3;
    }

    // Resource identifier.
    // For a project this represents project_number.
    int64 resource_id = 1;

    // Indicates the type of resource.
    ResourceType resource_type = 2;
  }

  // Supported Compliance Regimes.
  enum ComplianceRegime {
    // Unknown compliance regime.
    COMPLIANCE_REGIME_UNSPECIFIED = 0;

    // Information protection as per DoD IL4 requirements.
    IL4 = 1;

    // Criminal Justice Information Services (CJIS) Security policies.
    CJIS = 2;

    // FedRAMP High data protection controls
    FEDRAMP_HIGH = 3;

    // FedRAMP Moderate data protection controls
    FEDRAMP_MODERATE = 4;

    // Assured Workloads For US Regions data protection controls
    US_REGIONAL_ACCESS = 5;

    // Health Insurance Portability and Accountability Act controls
    HIPAA = 6;

    // Health Information Trust Alliance controls
    HITRUST = 7;

    // Assured Workloads For EU Regions and Support controls
    EU_REGIONS_AND_SUPPORT = 8;

    // Assured Workloads For Canada Regions and Support controls
    CA_REGIONS_AND_SUPPORT = 9;

    // International Traffic in Arms Regulations
    ITAR = 10;

    // Assured Workloads for Australia Regions and Support controls
    // Available for public preview consumption.
    // Don't create production workloads.
    AU_REGIONS_AND_US_SUPPORT = 11;

    // Assured Workloads for Partners
    ASSURED_WORKLOADS_FOR_PARTNERS = 12;
  }

  // Settings specific to the Key Management Service.
  // This message is deprecated.
  // In order to create a Keyring, callers should specify,
  // ENCRYPTION_KEYS_PROJECT or KEYRING in ResourceSettings.resource_type field.
  message KMSSettings {
    option deprecated = true;

    // Required. Input only. Immutable. The time at which the Key Management Service will automatically create a
    // new version of the crypto key and mark it as the primary.
    google.protobuf.Timestamp next_rotation_time = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];

    // Required. Input only. Immutable. [next_rotation_time] will be advanced by this period when the Key
    // Management Service automatically rotates a key. Must be at least 24 hours
    // and at most 876,000 hours.
    google.protobuf.Duration rotation_period = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Represent the custom settings for the resources to be created.
  message ResourceSettings {
    // Resource identifier.
    // For a project this represents project_id. If the project is already
    // taken, the workload creation will fail.
    // For KeyRing, this represents the keyring_id.
    // For a folder, don't set this value as folder_id is assigned by Google.
    string resource_id = 1;

    // Indicates the type of resource. This field should be specified to
    // correspond the id to the right resource type (CONSUMER_FOLDER or
    // ENCRYPTION_KEYS_PROJECT)
    ResourceInfo.ResourceType resource_type = 2;

    // User-assigned resource display name.
    // If not empty it will be used to create a resource with the specified
    // name.
    string display_name = 3;
  }

  // Key Access Justifications(KAJ) Enrollment State.
  enum KajEnrollmentState {
    // Default State for KAJ Enrollment.
    KAJ_ENROLLMENT_STATE_UNSPECIFIED = 0;

    // Pending State for KAJ Enrollment.
    KAJ_ENROLLMENT_STATE_PENDING = 1;

    // Complete State for KAJ Enrollment.
    KAJ_ENROLLMENT_STATE_COMPLETE = 2;
  }

  // Signed Access Approvals (SAA) enrollment response.
  message SaaEnrollmentResponse {
    // Setup state of SAA enrollment.
    enum SetupState {
      // Unspecified.
      SETUP_STATE_UNSPECIFIED = 0;

      // SAA enrollment pending.
      STATUS_PENDING = 1;

      // SAA enrollment comopleted.
      STATUS_COMPLETE = 2;
    }

    // Setup error of SAA enrollment.
    enum SetupError {
      // Unspecified.
      SETUP_ERROR_UNSPECIFIED = 0;

      // Invalid states for all customers, to be redirected to AA UI for
      // additional details.
      ERROR_INVALID_BASE_SETUP = 1;

      // Returned when there is not an EKM key configured.
      ERROR_MISSING_EXTERNAL_SIGNING_KEY = 2;

      // Returned when there are no enrolled services or the customer is
      // enrolled in CAA only for a subset of services.
      ERROR_NOT_ALL_SERVICES_ENROLLED = 3;

      // Returned when exception was encountered during evaluation of other
      // criteria.
      ERROR_SETUP_CHECK_FAILED = 4;
    }

    // Indicates SAA enrollment status of a given workload.
    optional SetupState setup_status = 1;

    // Indicates SAA enrollment setup error if any.
    repeated SetupError setup_errors = 2;
  }

  // Supported Assured Workloads Partners.
  enum Partner {
    // Unknown partner regime/controls.
    PARTNER_UNSPECIFIED = 0;

    // S3NS regime/controls.
    LOCAL_CONTROLS_BY_S3NS = 1;
  }

  // Optional. The resource name of the workload.
  // Format:
  // organizations/{organization}/locations/{location}/workloads/{workload}
  //
  // Read-only.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The user-assigned display name of the Workload.
  // When present it must be between 4 to 30 characters.
  // Allowed characters are: lowercase and uppercase letters, numbers,
  // hyphen, and spaces.
  //
  // Example: My Workload
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The resources associated with this workload.
  // These resources will be created when creating the workload.
  // If any of the projects already exist, the workload creation will fail.
  // Always read only.
  repeated ResourceInfo resources = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Compliance Regime associated with this workload.
  ComplianceRegime compliance_regime = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Immutable. The Workload creation timestamp.
  google.protobuf.Timestamp create_time = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. The billing account used for the resources which are
  // direct children of workload. This billing account is initially associated
  // with the resources created as part of Workload creation.
  // After the initial creation of these resources, the customer can change
  // the assigned billing account.
  // The resource name has the form
  // `billingAccounts/{billing_account_id}`. For example,
  // `billingAccounts/012345-567890-ABCDEF`.
  string billing_account = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. ETag of the workload, it is calculated on the basis
  // of the Workload contents. It will be used in Update & Delete operations.
  string etag = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels applied to the workload.
  map<string, string> labels = 10 [(google.api.field_behavior) = OPTIONAL];

  // Input only. The parent resource for the resources managed by this Assured Workload. May
  // be either empty or a folder resource which is a child of the
  // Workload parent. If not specified all resources are created under the
  // parent organization.
  // Format:
  // folders/{folder_id}
  string provisioned_resources_parent = 13 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. Settings used to create a CMEK crypto key. When set, a project with a KMS
  // CMEK key is provisioned.
  // This field is deprecated as of Feb 28, 2022.
  // In order to create a Keyring, callers should specify,
  // ENCRYPTION_KEYS_PROJECT or KEYRING in ResourceSettings.resource_type field.
  KMSSettings kms_settings = 14 [
    deprecated = true,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Input only. Resource properties that are used to customize workload resources.
  // These properties (such as custom project id) will be used to create
  // workload resources if possible. This field is optional.
  repeated ResourceSettings resource_settings = 15 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Represents the KAJ enrollment state of the given workload.
  KajEnrollmentState kaj_enrollment_state = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Indicates the sovereignty status of the given workload.
  // Currently meant to be used by Europe/Canada customers.
  bool enable_sovereign_controls = 18 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Represents the SAA enrollment response of the given workload.
  // SAA enrollment response is queried during GetWorkload call.
  // In failure cases, user friendly error message is shown in SAA details page.
  SaaEnrollmentResponse saa_enrollment_response = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Urls for services which are compliant for this Assured Workload, but which
  // are currently disallowed by the ResourceUsageRestriction org policy.
  // Invoke RestrictAllowedResources endpoint to allow your project developers
  // to use these services in their environment."
  repeated string compliant_but_disallowed_services = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Compliance Regime associated with this workload.
  Partner partner = 25 [(google.api.field_behavior) = OPTIONAL];
}

// Operation metadata to give request details of CreateWorkload.
message CreateWorkloadOperationMetadata {
  // Optional. Time when the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The display name of the workload.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The parent of the workload.
  string parent = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Compliance controls that should be applied to the resources managed by
  // the workload.
  Workload.ComplianceRegime compliance_regime = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for restricting list of available resources in Workload environment.
message RestrictAllowedResourcesRequest {
  // The type of restriction.
  enum RestrictionType {
    // Unknown restriction type.
    RESTRICTION_TYPE_UNSPECIFIED = 0;

    // Allow the use all of all gcp products, irrespective of the compliance
    // posture. This effectively removes gcp.restrictServiceUsage OrgPolicy
    // on the AssuredWorkloads Folder.
    ALLOW_ALL_GCP_RESOURCES = 1;

    // Based on Workload's compliance regime, allowed list changes.
    // See - https://cloud.google.com/assured-workloads/docs/supported-products
    // for the list of supported resources.
    ALLOW_COMPLIANT_RESOURCES = 2;
  }

  // Required. The resource name of the Workload. This is the workloads's
  // relative path in the API, formatted as
  // "organizations/{organization_id}/locations/{location_id}/workloads/{workload_id}".
  // For example,
  // "organizations/123/locations/us-east1/workloads/assured-workload-1".
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of restriction for using gcp products in the Workload environment.
  RestrictionType restriction_type = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response for restricting the list of allowed resources.
message RestrictAllowedResourcesResponse {

}

// Request for acknowledging the violation
// Next Id: 4
message AcknowledgeViolationRequest {
  // Required. The resource name of the Violation to acknowledge.
  // Format:
  // organizations/{organization}/locations/{location}/workloads/{workload}/violations/{violation}
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Business justification explaining the need for violation acknowledgement
  string comment = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. This field is deprecated and will be removed in future version of the API.
  // Name of the OrgPolicy which was modified with non-compliant change and
  // resulted in this violation.
  // Format:
  // projects/{project_number}/policies/{constraint_name}
  // folders/{folder_id}/policies/{constraint_name}
  // organizations/{organization_id}/policies/{constraint_name}
  string non_compliant_org_policy = 3 [
    deprecated = true,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Response for violation acknowledgement
message AcknowledgeViolationResponse {

}

// Interval defining a time window.
message TimeWindow {
  // The start of the time window.
  google.protobuf.Timestamp start_time = 1;

  // The end of the time window.
  google.protobuf.Timestamp end_time = 2;
}

// Request for fetching violations in an organization.
message ListViolationsRequest {
  // Required. The Workload name.
  // Format `organizations/{org_id}/locations/{location}/workloads/{workload}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "assuredworkloads.googleapis.com/Violation"
    }
  ];

  // Optional. Specifies the time window for retrieving active Violations.
  // When specified, retrieves Violations that were active between start_time
  // and end_time.
  TimeWindow interval = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token returned from previous request.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A custom filter for filtering by the Violations properties.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response of ListViolations endpoint.
message ListViolationsResponse {
  // List of Violations under a Workload.
  repeated Violation violations = 1;

  // The next page token. Returns empty if reached the last page.
  string next_page_token = 2;
}

// Request for fetching a Workload Violation.
message GetViolationRequest {
  // Required. The resource name of the Violation to fetch (ie. Violation.name).
  // Format:
  // organizations/{organization}/locations/{location}/workloads/{workload}/violations/{violation}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "assuredworkloads.googleapis.com/Violation"
    }
  ];
}

// Workload monitoring Violation.
message Violation {
  option (google.api.resource) = {
    type: "assuredworkloads.googleapis.com/Violation"
    pattern: "organizations/{organization}/locations/{location}/workloads/{workload}/violations/{violation}"
  };

  // Violation State Values
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // Violation is resolved.
    RESOLVED = 2;

    // Violation is Unresolved
    UNRESOLVED = 3;

    // Violation is Exception
    EXCEPTION = 4;
  }

  // Represents remediation guidance to resolve compliance violation for
  // AssuredWorkload
  message Remediation {
    // Classifying remediation into various types based on the kind of
    // violation. For example, violations caused due to changes in boolean org
    // policy requires different remediation instructions compared to violation
    // caused due to changes in allowed values of list org policy.
    enum RemediationType {
      // Unspecified remediation type
      REMEDIATION_TYPE_UNSPECIFIED = 0;

      // Remediation type for boolean org policy
      REMEDIATION_BOOLEAN_ORG_POLICY_VIOLATION = 1;

      // Remediation type for list org policy which have allowed values in the
      // monitoring rule
      REMEDIATION_LIST_ALLOWED_VALUES_ORG_POLICY_VIOLATION = 2;

      // Remediation type for list org policy which have denied values in the
      // monitoring rule
      REMEDIATION_LIST_DENIED_VALUES_ORG_POLICY_VIOLATION = 3;

      // Remediation type for gcp.restrictCmekCryptoKeyProjects
      REMEDIATION_RESTRICT_CMEK_CRYPTO_KEY_PROJECTS_ORG_POLICY_VIOLATION = 4;
    }

    // Instructions to remediate violation
    message Instructions {
      // Remediation instructions to resolve violation via gcloud cli
      message Gcloud {
        // Gcloud command to resolve violation
        repeated string gcloud_commands = 1;

        // Steps to resolve violation via gcloud cli
        repeated string steps = 2;

        // Additional urls for more information about steps
        repeated string additional_links = 3;
      }

      // Remediation instructions to resolve violation via cloud console
      message Console {
        // Link to console page where violations can be resolved
        repeated string console_uris = 1;

        // Steps to resolve violation via cloud console
        repeated string steps = 2;

        // Additional urls for more information about steps
        repeated string additional_links = 3;
      }

      // Remediation instructions to resolve violation via gcloud cli
      Gcloud gcloud_instructions = 1;

      // Remediation instructions to resolve violation via cloud console
      Console console_instructions = 2;
    }

    // Required. Remediation instructions to resolve violations
    Instructions instructions = 1 [(google.api.field_behavior) = REQUIRED];

    // Values that can resolve the violation
    // For example: for list org policy violations, this will either be the list
    // of allowed or denied values
    repeated string compliant_values = 2;

    // Output only. Reemediation type based on the type of org policy values violated
    RemediationType remediation_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Immutable. Name of the Violation.
  // Format:
  // organizations/{organization}/locations/{location}/workloads/{workload_id}/violations/{violations_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Description for the Violation.
  // e.g. OrgPolicy gcp.resourceLocations has non compliant value.
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time of the event which triggered the Violation.
  google.protobuf.Timestamp begin_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last time when the Violation record was updated.
  google.protobuf.Timestamp update_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time of the event which fixed the Violation.
  // If the violation is ACTIVE this will be empty.
  google.protobuf.Timestamp resolve_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Category under which this violation is mapped.
  // e.g. Location, Service Usage, Access, Encryption, etc.
  string category = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the violation
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Immutable. The org-policy-constraint that was incorrectly changed, which resulted in
  // this violation.
  string org_policy_constraint = 8 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Immutable. Audit Log Link for violated resource
  // Format:
  // https://console.cloud.google.com/logs/query;query={logName}{protoPayload.resourceName}{timeRange}{folder}
  string audit_log_link = 11 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Immutable. Name of the OrgPolicy which was modified with non-compliant change and
  // resulted this violation.
  //  Format:
  //  projects/{project_number}/policies/{constraint_name}
  //  folders/{folder_id}/policies/{constraint_name}
  //  organizations/{organization_id}/policies/{constraint_name}
  string non_compliant_org_policy = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Compliance violation remediation
  Remediation remediation = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A boolean that indicates if the violation is acknowledged
  bool acknowledged = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Timestamp when this violation was acknowledged last.
  // This will be absent when acknowledged field is marked as false.
  optional google.protobuf.Timestamp acknowledgement_time = 15 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Immutable. Audit Log link to find business justification provided for violation
  // exception. Format:
  // https://console.cloud.google.com/logs/query;query={logName}{protoPayload.resourceName}{protoPayload.methodName}{timeRange}{organization}
  string exception_audit_log_link = 16 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];
}
