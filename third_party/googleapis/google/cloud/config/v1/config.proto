// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.config.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Config.V1";
option go_package = "cloud.google.com/go/config/apiv1/configpb;configpb";
option java_multiple_files = true;
option java_outer_classname = "ConfigProto";
option java_package = "com.google.cloud.config.v1";
option php_namespace = "Google\\Cloud\\Config\\V1";
option ruby_package = "Google::Cloud::ConfigService::V1";
option (google.api.resource_definition) = {
  type: "iam.googleapis.com/ServiceAccount"
  pattern: "projects/{project}/serviceAccounts/{service_account}"
};
option (google.api.resource_definition) = {
  type: "cloudbuild.googleapis.com/WorkerPool"
  pattern: "projects/{project}/locations/{location}/workerPools/{worker_pool}"
};

// Infrastructure Manager is a managed service that automates the deployment and
// management of Google Cloud infrastructure resources.
service Config {
  option (google.api.default_host) = "config.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists [Deployment][google.cloud.config.v1.Deployment]s in a given project
  // and location.
  rpc ListDeployments(ListDeploymentsRequest)
      returns (ListDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/deployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details about a [Deployment][google.cloud.config.v1.Deployment].
  rpc GetDeployment(GetDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a [Deployment][google.cloud.config.v1.Deployment].
  rpc CreateDeployment(CreateDeploymentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/deployments"
      body: "deployment"
    };
    option (google.api.method_signature) = "parent,deployment,deployment_id";
    option (google.longrunning.operation_info) = {
      response_type: "Deployment"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a [Deployment][google.cloud.config.v1.Deployment].
  rpc UpdateDeployment(UpdateDeploymentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{deployment.name=projects/*/locations/*/deployments/*}"
      body: "deployment"
    };
    option (google.api.method_signature) = "deployment,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Deployment"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a [Deployment][google.cloud.config.v1.Deployment].
  rpc DeleteDeployment(DeleteDeploymentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Deployment"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists [Revision][google.cloud.config.v1.Revision]s of a deployment.
  rpc ListRevisions(ListRevisionsRequest) returns (ListRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/deployments/*}/revisions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details about a [Revision][google.cloud.config.v1.Revision].
  rpc GetRevision(GetRevisionRequest) returns (Revision) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deployments/*/revisions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets details about a [Resource][google.cloud.config.v1.Resource] deployed
  // by Infra Manager.
  rpc GetResource(GetResourceRequest) returns (Resource) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deployments/*/revisions/*/resources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [Resources][google.cloud.config.v1.Resource] in a given revision.
  rpc ListResources(ListResourcesRequest) returns (ListResourcesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/deployments/*/revisions/*}/resources"
    };
    option (google.api.method_signature) = "parent";
  }

  // Exports Terraform state file from a given deployment.
  rpc ExportDeploymentStatefile(ExportDeploymentStatefileRequest)
      returns (Statefile) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/deployments/*}:exportState"
      body: "*"
    };
  }

  // Exports Terraform state file from a given revision.
  rpc ExportRevisionStatefile(ExportRevisionStatefileRequest)
      returns (Statefile) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/deployments/*/revisions/*}:exportState"
      body: "*"
    };
  }

  // Imports Terraform state file in a given deployment. The state file does not
  // take effect until the Deployment has been unlocked.
  rpc ImportStatefile(ImportStatefileRequest) returns (Statefile) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/deployments/*}:importState"
      body: "*"
    };
    option (google.api.method_signature) = "parent,lock_id";
  }

  // Deletes Terraform state file in a given deployment.
  rpc DeleteStatefile(DeleteStatefileRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deployments/*}:deleteState"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Locks a deployment.
  rpc LockDeployment(LockDeploymentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deployments/*}:lock"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Deployment"
      metadata_type: "OperationMetadata"
    };
  }

  // Unlocks a locked deployment.
  rpc UnlockDeployment(UnlockDeploymentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deployments/*}:unlock"
      body: "*"
    };
    option (google.api.method_signature) = "name,lock_id";
    option (google.longrunning.operation_info) = {
      response_type: "Deployment"
      metadata_type: "OperationMetadata"
    };
  }

  // Exports the lock info on a locked deployment.
  rpc ExportLockInfo(ExportLockInfoRequest) returns (LockInfo) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deployments/*}:exportLock"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a [Preview][google.cloud.config.v1.Preview].
  rpc CreatePreview(CreatePreviewRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/previews"
      body: "preview"
    };
    option (google.api.method_signature) = "parent,preview";
    option (google.longrunning.operation_info) = {
      response_type: "Preview"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details about a [Preview][google.cloud.config.v1.Preview].
  rpc GetPreview(GetPreviewRequest) returns (Preview) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/previews/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [Preview][google.cloud.config.v1.Preview]s in a given project and
  // location.
  rpc ListPreviews(ListPreviewsRequest) returns (ListPreviewsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/previews"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a [Preview][google.cloud.config.v1.Preview].
  rpc DeletePreview(DeletePreviewRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/previews/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Preview"
      metadata_type: "OperationMetadata"
    };
  }

  // Export [Preview][google.cloud.config.v1.Preview] results.
  rpc ExportPreviewResult(ExportPreviewResultRequest)
      returns (ExportPreviewResultResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/previews/*}:export"
      body: "*"
    };
  }

  // Lists [TerraformVersion][google.cloud.config.v1.TerraformVersion]s in a
  // given project and location.
  rpc ListTerraformVersions(ListTerraformVersionsRequest)
      returns (ListTerraformVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/terraformVersions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details about a
  // [TerraformVersion][google.cloud.config.v1.TerraformVersion].
  rpc GetTerraformVersion(GetTerraformVersionRequest)
      returns (TerraformVersion) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/terraformVersions/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Enum values to control quota checks for resources in terraform
// configuration files.
enum QuotaValidation {
  // The default value.
  // QuotaValidation on terraform configuration files will be disabled in
  // this case.
  QUOTA_VALIDATION_UNSPECIFIED = 0;

  // Enable computing quotas for resources in terraform configuration files to
  // get visibility on resources with insufficient quotas.
  ENABLED = 1;

  // Enforce quota checks so deployment fails if there isn't sufficient quotas
  // available to deploy resources in terraform configuration files.
  ENFORCED = 2;
}

// A Deployment is a group of resources and configs managed and provisioned by
// Infra Manager.
message Deployment {
  option (google.api.resource) = {
    type: "config.googleapis.com/Deployment"
    pattern: "projects/{project}/locations/{location}/deployments/{deployment}"
  };

  // Possible states of a deployment.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The deployment is being created.
    CREATING = 1;

    // The deployment is healthy.
    ACTIVE = 2;

    // The deployment is being updated.
    UPDATING = 3;

    // The deployment is being deleted.
    DELETING = 4;

    // The deployment has encountered an unexpected error.
    FAILED = 5;

    // The deployment is no longer being actively reconciled.
    // This may be the result of recovering the project after deletion.
    SUSPENDED = 6;

    // The deployment has been deleted.
    DELETED = 7;
  }

  // Possible errors that can occur with deployments.
  enum ErrorCode {
    // No error code was specified.
    ERROR_CODE_UNSPECIFIED = 0;

    // The revision failed. See Revision for more details.
    REVISION_FAILED = 1;

    // Cloud Build failed due to a permission issue.
    CLOUD_BUILD_PERMISSION_DENIED = 3;

    // Cloud Build job associated with a deployment deletion could not be
    // started.
    DELETE_BUILD_API_FAILED = 5;

    // Cloud Build job associated with a deployment deletion was started but
    // failed.
    DELETE_BUILD_RUN_FAILED = 6;

    // Cloud Storage bucket creation failed due to a permission issue.
    BUCKET_CREATION_PERMISSION_DENIED = 7;

    // Cloud Storage bucket creation failed due to an issue unrelated to
    // permissions.
    BUCKET_CREATION_FAILED = 8;
  }

  // Possible lock states of a deployment.
  enum LockState {
    // The default value. This value is used if the lock state is omitted.
    LOCK_STATE_UNSPECIFIED = 0;

    // The deployment is locked.
    LOCKED = 1;

    // The deployment is unlocked.
    UNLOCKED = 2;

    // The deployment is being locked.
    LOCKING = 3;

    // The deployment is being unlocked.
    UNLOCKING = 4;

    // The deployment has failed to lock.
    LOCK_FAILED = 5;

    // The deployment has failed to unlock.
    UNLOCK_FAILED = 6;
  }

  // Blueprint to deploy.
  oneof blueprint {
    // A blueprint described using Terraform's HashiCorp Configuration Language
    // as a root module.
    TerraformBlueprint terraform_blueprint = 6;
  }

  // Resource name of the deployment.
  // Format: `projects/{project}/locations/{location}/deployments/{deployment}`
  string name = 1;

  // Output only. Time when the deployment was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the deployment was last modified.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-defined metadata for the deployment.
  map<string, string> labels = 4;

  // Output only. Current state of the deployment.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Revision name that was most recently applied.
  // Format: `projects/{project}/locations/{location}/deployments/{deployment}/
  // revisions/{revision}`
  string latest_revision = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information regarding the current state.
  string state_detail = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Error code describing errors that may have occurred.
  ErrorCode error_code = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of artifacts from a DeleteDeployment operation.
  ApplyResults delete_results = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud Build instance UUID associated with deleting this
  // deployment.
  string delete_build = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of Cloud Build logs in Google Cloud Storage,
  // populated when deleting this deployment. Format: `gs://{bucket}/{object}`.
  string delete_logs = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Errors encountered when deleting this deployment.
  // Errors are truncated to 10 entries, see `delete_results` and `error_logs`
  // for full details.
  repeated TerraformError tf_errors = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of Terraform error logs in Google Cloud Storage.
  // Format: `gs://{bucket}/{object}`.
  string error_logs = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User-defined location of Cloud Build logs and artifacts in Google
  // Cloud Storage. Format: `gs://{bucket}/{folder}`
  //
  // A default bucket will be bootstrapped if the field is not set or empty.
  // Default bucket format: `gs://<project number>-<region>-blueprint-config`
  // Constraints:
  // - The bucket needs to be in the same project as the deployment
  // - The path cannot be within the path of `gcs_source`
  // - The field cannot be updated, including changing its presence
  optional string artifacts_gcs_bucket = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Required. User-specified Service Account (SA) credentials to be used when
  // actuating resources.
  // Format: `projects/{projectID}/serviceAccounts/{serviceAccount}`
  optional string service_account = 16 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // By default, Infra Manager will return a failure when
  // Terraform encounters a 409 code (resource conflict error) during actuation.
  // If this flag is set to true, Infra Manager will instead
  // attempt to automatically import the resource into the Terraform state (for
  // supported resource types) and continue actuation.
  //
  // Not all resource types are supported, refer to documentation.
  optional bool import_existing_resources = 17;

  // Optional. The user-specified Cloud Build worker pool resource in which the
  // Cloud Build job will execute. Format:
  // `projects/{project}/locations/{location}/workerPools/{workerPoolId}`.
  // If this field is unspecified, the default Cloud Build worker pool will be
  // used.
  optional string worker_pool = 19 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }
  ];

  // Output only. Current lock state of the deployment.
  LockState lock_state = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The user-specified Terraform version constraint.
  // Example: "=1.3.10".
  optional string tf_version_constraint = 21
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current Terraform version set on the deployment.
  // It is in the format of "Major.Minor.Patch", for example, "1.3.10".
  string tf_version = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Input to control quota checks for resources in terraform
  // configuration files. There are limited resources on which quota validation
  // applies.
  QuotaValidation quota_validation = 23
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Arbitrary key-value metadata storage e.g. to help client tools
  // identify deployments during automation. See
  // https://google.aip.dev/148#annotations for details on format and size
  // limitations.
  map<string, string> annotations = 24 [(google.api.field_behavior) = OPTIONAL];
}

// TerraformBlueprint describes the source of a Terraform root module which
// describes the resources and configs to be deployed.
message TerraformBlueprint {
  // Location of the source configs.
  // Required.
  oneof source {
    // URI of an object in Google Cloud Storage.
    // Format: `gs://{bucket}/{object}`
    //
    // URI may also specify an object version for zipped objects.
    // Format: `gs://{bucket}/{object}#{version}`
    string gcs_source = 1;

    // URI of a public Git repo.
    GitSource git_source = 2;
  }

  // Input variable values for the Terraform blueprint.
  map<string, TerraformVariable> input_values = 4;
}

// A Terraform input variable.
message TerraformVariable {
  // Input variable value.
  google.protobuf.Value input_value = 5;
}

// Outputs and artifacts from applying a deployment.
message ApplyResults {
  // Location of a blueprint copy and other manifests in Google Cloud Storage.
  // Format: `gs://{bucket}/{object}`
  string content = 1;

  // Location of artifacts (e.g. logs) in Google Cloud Storage.
  // Format: `gs://{bucket}/{object}`
  string artifacts = 2;

  // Map of output name to output info.
  map<string, TerraformOutput> outputs = 3;
}

// Describes a Terraform output.
message TerraformOutput {
  // Identifies whether Terraform has set this output as a potential
  // sensitive value.
  bool sensitive = 1;

  // Value of output.
  google.protobuf.Value value = 2;
}

message ListDeploymentsRequest {
  // Required. The parent in whose context the Deployments are listed. The
  // parent value is in the format:
  // 'projects/{project_id}/locations/{location}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // When requesting a page of resources, 'page_size' specifies number of
  // resources to return. If unspecified, at most 500 will be returned. The
  // maximum value is 1000.
  int32 page_size = 2;

  // Token returned by previous call to 'ListDeployments' which specifies the
  // position in the list from where to continue listing the resources.
  string page_token = 3;

  // Lists the Deployments that match the filter expression. A filter
  // expression filters the resources listed in the response. The expression
  // must be of the form '{field} {operator} {value}' where operators: '<', '>',
  // '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS
  // operator which is roughly synonymous with equality). {field} can refer to a
  // proto or JSON field, or a synthetic field. Field names can be camelCase or
  // snake_case.
  //
  // Examples:
  // - Filter by name:
  //   name = "projects/foo/locations/us-central1/deployments/bar
  //
  // - Filter by labels:
  //   - Resources that have a key called 'foo'
  //     labels.foo:*
  //   - Resources that have a key called 'foo' whose value is 'bar'
  //     labels.foo = bar
  //
  // - Filter by state:
  //   - Deployments in CREATING state.
  //     state=CREATING
  string filter = 4;

  // Field to use to sort the list.
  string order_by = 5;
}

message ListDeploymentsResponse {
  // List of [Deployment][google.cloud.config.v1.Deployment]s.
  repeated Deployment deployments = 1;

  // Token to be supplied to the next ListDeployments request via `page_token`
  // to obtain the next set of results.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

message GetDeploymentRequest {
  // Required. The name of the deployment. Format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];
}

// A request to list Revisions passed to a 'ListRevisions' call.
message ListRevisionsRequest {
  // Required. The parent in whose context the Revisions are listed. The parent
  // value is in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];

  // When requesting a page of resources, `page_size` specifies number of
  // resources to return. If unspecified, at most 500 will be returned. The
  // maximum value is 1000.
  int32 page_size = 2;

  // Token returned by previous call to 'ListRevisions' which specifies the
  // position in the list from where to continue listing the resources.
  string page_token = 3;

  // Lists the Revisions that match the filter expression. A filter
  // expression filters the resources listed in the response. The expression
  // must be of the form '{field} {operator} {value}' where operators: '<', '>',
  // '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS
  // operator which is roughly synonymous with equality). {field} can refer to a
  // proto or JSON field, or a synthetic field. Field names can be camelCase or
  // snake_case.
  //
  // Examples:
  // - Filter by name:
  //   name = "projects/foo/locations/us-central1/deployments/dep/revisions/bar
  //
  // - Filter by labels:
  //   - Resources that have a key called 'foo'
  //     labels.foo:*
  //   - Resources that have a key called 'foo' whose value is 'bar'
  //     labels.foo = bar
  //
  // - Filter by state:
  //   - Revisions in CREATING state.
  //     state=CREATING
  string filter = 4;

  // Field to use to sort the list.
  string order_by = 5;
}

// A response to a 'ListRevisions' call. Contains a list of Revisions.
message ListRevisionsResponse {
  // List of [Revision][google.cloud.config.v1.Revision]s.
  repeated Revision revisions = 1;

  // A token to request the next page of resources from the 'ListRevisions'
  // method. The value of an empty string means that there are no more resources
  // to return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to get a Revision from a 'GetRevision' call.
message GetRevisionRequest {
  // Required. The name of the Revision in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}/revisions/{revision}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "config.googleapis.com/Revision" }
  ];
}

message CreateDeploymentRequest {
  // Required. The parent in whose context the Deployment is created. The parent
  // value is in the format: 'projects/{project_id}/locations/{location}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The Deployment ID.
  string deployment_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. [Deployment][google.cloud.config.v1.Deployment] resource to be
  // created.
  Deployment deployment = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

message UpdateDeploymentRequest {
  // Optional. Field mask used to specify the fields to be overwritten in the
  // Deployment resource by the update.
  //
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. [Deployment][google.cloud.config.v1.Deployment] to update.
  //
  // The deployment's `name` field is used to identify the resource to be
  // updated. Format:
  // `projects/{project}/locations/{location}/deployments/{deployment}`
  Deployment deployment = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

message DeleteDeploymentRequest {
  // Policy on how resources actuated by the deployment should be deleted.
  enum DeletePolicy {
    // Unspecified policy, resources will be deleted.
    DELETE_POLICY_UNSPECIFIED = 0;

    // Deletes resources actuated by the deployment.
    DELETE = 1;

    // Abandons resources and only deletes the deployment and its metadata.
    ABANDON = 2;
  }

  // Required. The name of the Deployment in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, any revisions for this deployment will also be
  // deleted. (Otherwise, the request will only work if the deployment has no
  // revisions.)
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Policy on how resources actuated by the deployment should be
  // deleted. If unspecified, the default behavior is to delete the underlying
  // resources.
  DeletePolicy delete_policy = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Ephemeral metadata about the state of an operation for a particular
  // resource.
  oneof resource_metadata {
    // Output only. Metadata about the deployment operation state.
    DeploymentOperationMetadata deployment_metadata = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Metadata about the preview operation state.
    PreviewOperationMetadata preview_metadata = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Time when the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation of the
  // operation. Operations that have successfully been cancelled have
  // [google.longrunning.Operation.error][google.longrunning.Operation.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of `1`,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A child resource of a Deployment generated by a 'CreateDeployment' or
// 'UpdateDeployment' call. Each Revision contains metadata pertaining to a
// snapshot of a particular Deployment.
message Revision {
  option (google.api.resource) = {
    type: "config.googleapis.com/Revision"
    pattern: "projects/{project}/locations/{location}/deployments/{deployment}/revisions/{revision}"
  };

  // Actions that generate a revision.
  enum Action {
    // The default value. This value is used if the action is omitted.
    ACTION_UNSPECIFIED = 0;

    // The revision was generated by creating a deployment.
    CREATE = 1;

    // The revision was generated by updating a deployment.
    UPDATE = 2;

    // The revision was deleted.
    DELETE = 3;
  }

  // Possible states of a revision.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The revision is being applied.
    APPLYING = 1;

    // The revision was applied successfully.
    APPLIED = 2;

    // The revision could not be applied successfully.
    FAILED = 3;
  }

  // Possible errors if Revision could not be created or updated successfully.
  enum ErrorCode {
    // No error code was specified.
    ERROR_CODE_UNSPECIFIED = 0;

    // Cloud Build failed due to a permission issue.
    CLOUD_BUILD_PERMISSION_DENIED = 1;

    // Cloud Build job associated with creating or updating a deployment could
    // not be started.
    APPLY_BUILD_API_FAILED = 4;

    // Cloud Build job associated with creating or updating a deployment was
    // started but failed.
    APPLY_BUILD_RUN_FAILED = 5;

    // quota validation failed for one or more resources in terraform
    // configuration files.
    QUOTA_VALIDATION_FAILED = 7;
  }

  // Blueprint that was deployed.
  oneof blueprint {
    // Output only. A blueprint described using Terraform's HashiCorp
    // Configuration Language as a root module.
    TerraformBlueprint terraform_blueprint = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Revision name. Format:
  // `projects/{project}/locations/{location}/deployments/{deployment}/
  // revisions/{revision}`
  string name = 1;

  // Output only. Time when the revision was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the revision was last modified.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The action which created this revision
  Action action = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state of the revision.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Outputs and artifacts from applying a deployment.
  ApplyResults apply_results = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional info regarding the current state.
  string state_detail = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Code describing any errors that may have occurred.
  ErrorCode error_code = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud Build instance UUID associated with this revision.
  string build = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of Revision operation logs in
  // `gs://{bucket}/{object}` format.
  string logs = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Errors encountered when creating or updating this deployment.
  // Errors are truncated to 10 entries, see `delete_results` and `error_logs`
  // for full details.
  repeated TerraformError tf_errors = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of Terraform error logs in Google Cloud Storage.
  // Format: `gs://{bucket}/{object}`.
  string error_logs = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. User-specified Service Account (SA) to be used as credential
  // to manage resources. Format:
  // `projects/{projectID}/serviceAccounts/{serviceAccount}`
  string service_account = 14 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // Output only. By default, Infra Manager will return a failure when
  // Terraform encounters a 409 code (resource conflict error) during actuation.
  // If this flag is set to true, Infra Manager will instead
  // attempt to automatically import the resource into the Terraform state (for
  // supported resource types) and continue actuation.
  //
  // Not all resource types are supported, refer to documentation.
  bool import_existing_resources = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user-specified Cloud Build worker pool resource in which
  // the Cloud Build job will execute. Format:
  // `projects/{project}/locations/{location}/workerPools/{workerPoolId}`.
  // If this field is unspecified, the default Cloud Build worker pool will be
  // used.
  string worker_pool = 17 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }
  ];

  // Output only. The user-specified Terraform version constraint.
  // Example: "=1.3.10".
  string tf_version_constraint = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The version of Terraform used to create the Revision.
  // It is in the format of "Major.Minor.Patch", for example, "1.3.10".
  string tf_version = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud Storage path containing quota validation results. This
  // field is set when a user sets Deployment.quota_validation field to ENABLED
  // or ENFORCED. Format: `gs://{bucket}/{object}`.
  string quota_validation_results = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Input to control quota checks for resources in terraform
  // configuration files. There are limited resources on which quota validation
  // applies.
  QuotaValidation quota_validation = 20
      [(google.api.field_behavior) = OPTIONAL];
}

// Errors encountered during actuation using Terraform
message TerraformError {
  // Address of the resource associated with the error,
  // e.g. `google_compute_network.vpc_network`.
  string resource_address = 1;

  // HTTP response code returned from Google Cloud Platform APIs when Terraform
  // fails to provision the resource. If unset or 0, no HTTP response code was
  // returned by Terraform.
  int32 http_response_code = 2;

  // A human-readable error description.
  string error_description = 3;

  // Original error response from underlying Google API, if available.
  google.rpc.Status error = 4;
}

// A set of files in a Git repository.
message GitSource {
  // Optional. Repository URL.
  // Example: 'https://github.com/kubernetes/examples.git'
  optional string repo = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Subdirectory inside the repository.
  // Example: 'staging/my-package'
  optional string directory = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Git reference (e.g. branch or tag).
  optional string ref = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Ephemeral metadata content describing the state of a deployment operation.
message DeploymentOperationMetadata {
  // The possible steps a deployment may be running.
  enum DeploymentStep {
    // Unspecified deployment step
    DEPLOYMENT_STEP_UNSPECIFIED = 0;

    // Infra Manager is creating a Google Cloud Storage bucket to store
    // artifacts and metadata about the deployment and revision
    PREPARING_STORAGE_BUCKET = 1;

    // Downloading the blueprint onto the Google Cloud Storage bucket
    DOWNLOADING_BLUEPRINT = 2;

    // Initializing Terraform using `terraform init`
    RUNNING_TF_INIT = 3;

    // Running `terraform plan`
    RUNNING_TF_PLAN = 4;

    // Actuating resources using Terraform using `terraform apply`
    RUNNING_TF_APPLY = 5;

    // Destroying resources using Terraform using `terraform destroy`
    RUNNING_TF_DESTROY = 6;

    // Validating the uploaded TF state file when unlocking a deployment
    RUNNING_TF_VALIDATE = 7;

    // Unlocking a deployment
    UNLOCKING_DEPLOYMENT = 8;

    // Operation was successful
    SUCCEEDED = 9;

    // Operation failed
    FAILED = 10;

    // Validating the provided repository.
    VALIDATING_REPOSITORY = 11;

    // Running quota validation
    RUNNING_QUOTA_VALIDATION = 12;
  }

  // The current step the deployment operation is running.
  DeploymentStep step = 1;

  // Outputs and artifacts from applying a deployment.
  ApplyResults apply_results = 2;

  // Output only. Cloud Build instance UUID associated with this operation.
  string build = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of Deployment operations logs in
  // `gs://{bucket}/{object}` format.
  string logs = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Resource represents a Google Cloud Platform resource actuated by IM.
// Resources are child resources of Revisions.
message Resource {
  option (google.api.resource) = {
    type: "config.googleapis.com/Resource"
    pattern: "projects/{project}/locations/{location}/deployments/{deployment}/revisions/{revision}/resources/{resource}"
  };

  // Possible intent of the resource.
  enum Intent {
    // The default value. This value is used if the intent is omitted.
    INTENT_UNSPECIFIED = 0;

    // Infra Manager will create this Resource.
    CREATE = 1;

    // Infra Manager will update this Resource.
    UPDATE = 2;

    // Infra Manager will delete this Resource.
    DELETE = 3;

    // Infra Manager will destroy and recreate this Resource.
    RECREATE = 4;

    // Infra Manager will leave this Resource untouched.
    UNCHANGED = 5;
  }

  // Possible states of a resource.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // Resource has been planned for reconcile.
    PLANNED = 1;

    // Resource is actively reconciling into the intended state.
    IN_PROGRESS = 2;

    // Resource has reconciled to intended state.
    RECONCILED = 3;

    // Resource failed to reconcile.
    FAILED = 4;
  }

  // Output only. Resource name.
  // Format:
  // `projects/{project}/locations/{location}/deployments/{deployment}/revisions/{revision}/resources/{resource}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Terraform-specific info if this resource was created using
  // Terraform.
  ResourceTerraformInfo terraform_info = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Map of Cloud Asset Inventory (CAI) type to CAI info (e.g. CAI
  // ID). CAI type format follows
  // https://cloud.google.com/asset-inventory/docs/supported-asset-types
  map<string, ResourceCAIInfo> cai_assets = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Intent of the resource.
  Intent intent = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state of the resource.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Terraform info of a Resource.
message ResourceTerraformInfo {
  // TF resource address that uniquely identifies this resource within this
  // deployment.
  string address = 1;

  // TF resource type
  string type = 2;

  // ID attribute of the TF resource
  string id = 3;
}

// CAI info of a Resource.
message ResourceCAIInfo {
  // CAI resource name in the format following
  // https://cloud.google.com/apis/design/resource_names#full_resource_name
  string full_resource_name = 1;
}

// A request to get a Resource from a 'GetResource' call.
message GetResourceRequest {
  // Required. The name of the Resource in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}/revisions/{revision}/resource/{resource}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "config.googleapis.com/Resource" }
  ];
}

// A request to list Resources passed to a 'ListResources' call.
message ListResourcesRequest {
  // Required. The parent in whose context the Resources are listed. The parent
  // value is in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}/revisions/{revision}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "config.googleapis.com/Revision" }
  ];

  // When requesting a page of resources, 'page_size' specifies number of
  // resources to return. If unspecified, at most 500 will be returned. The
  // maximum value is 1000.
  int32 page_size = 2;

  // Token returned by previous call to 'ListResources' which specifies the
  // position in the list from where to continue listing the resources.
  string page_token = 3;

  // Lists the Resources that match the filter expression. A filter
  // expression filters the resources listed in the response. The expression
  // must be of the form '{field} {operator} {value}' where operators: '<', '>',
  // '<=',
  // '>=',
  // '!=', '=', ':' are supported (colon ':' represents a HAS operator which is
  // roughly synonymous with equality). {field} can refer to a proto or JSON
  // field, or a synthetic field. Field names can be camelCase or snake_case.
  //
  // Examples:
  // - Filter by name:
  //   name =
  //   "projects/foo/locations/us-central1/deployments/dep/revisions/bar/resources/baz
  string filter = 4;

  // Field to use to sort the list.
  string order_by = 5;
}

// A response to a 'ListResources' call. Contains a list of Resources.
message ListResourcesResponse {
  // List of [Resources][google.cloud.config.v1.Resource].
  repeated Resource resources = 1;

  // A token to request the next page of resources from the 'ListResources'
  // method. The value of an empty string means that there are no more resources
  // to return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Contains info about a Terraform state file
message Statefile {
  // Output only. Cloud Storage signed URI used for downloading or uploading the
  // state file.
  string signed_uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A request to export a state file passed to a 'ExportDeploymentStatefile'
// call.
message ExportDeploymentStatefileRequest {
  // Required. The parent in whose context the statefile is listed. The parent
  // value is in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];

  // Optional. If this flag is set to true, the exported deployment state file
  // will be the draft state. This will enable the draft file to be validated
  // before copying it over to the working state on unlock.
  bool draft = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to export a state file passed to a 'ExportRevisionStatefile'
// call.
message ExportRevisionStatefileRequest {
  // Required. The parent in whose context the statefile is listed. The parent
  // value is in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}/revisions/{revision}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "config.googleapis.com/Revision" }
  ];
}

// A request to import a state file passed to a 'ImportStatefile' call.
message ImportStatefileRequest {
  // Required. The parent in whose context the statefile is listed. The parent
  // value is in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];

  // Required. Lock ID of the lock file to verify that the user who is importing
  // the state file previously locked the Deployment.
  int64 lock_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional.
  bool skip_draft = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a state file passed to a 'DeleteStatefile' call.
message DeleteStatefileRequest {
  // Required. The name of the deployment in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];

  // Required. Lock ID of the lock file to verify that the user who is deleting
  // the state file previously locked the Deployment.
  int64 lock_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// A request to lock a deployment passed to a 'LockDeployment' call.
message LockDeploymentRequest {
  // Required. The name of the deployment in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];
}

// A request to unlock a state file passed to a 'UnlockDeployment' call.
message UnlockDeploymentRequest {
  // Required. The name of the deployment in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];

  // Required. Lock ID of the lock file to be unlocked.
  int64 lock_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// A request to get a state file lock info passed to a 'ExportLockInfo' call.
message ExportLockInfoRequest {
  // Required. The name of the deployment in the format:
  // 'projects/{project_id}/locations/{location}/deployments/{deployment}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];
}

// Details about the lock which locked the deployment.
message LockInfo {
  // Unique ID for the lock to be overridden with generation ID in the backend.
  int64 lock_id = 1;

  // Terraform operation, provided by the caller.
  string operation = 2;

  // Extra information to store with the lock, provided by the caller.
  string info = 3;

  // user@hostname when available
  string who = 4;

  // Terraform version
  string version = 5;

  // Time that the lock was taken.
  google.protobuf.Timestamp create_time = 6;
}

// A preview represents a set of actions Infra Manager would perform
// to move the resources towards the desired state as specified in the
// configuration.
message Preview {
  option (google.api.resource) = {
    type: "config.googleapis.com/Preview"
    pattern: "projects/{project}/locations/{location}/previews/{preview}"
    plural: "previews"
    singular: "preview"
  };

  // Possible states of a preview.
  enum State {
    // The default value. This value is used if the state is unknown.
    STATE_UNSPECIFIED = 0;

    // The preview is being created.
    CREATING = 1;

    // The preview has succeeded.
    SUCCEEDED = 2;

    // The preview is being applied.
    APPLYING = 3;

    // The preview is stale. A preview can become stale if a revision has been
    // applied after this preview was created.
    STALE = 4;

    // The preview is being deleted.
    DELETING = 5;

    // The preview has encountered an unexpected error.
    FAILED = 6;

    // The preview has been deleted.
    DELETED = 7;
  }

  // Preview mode provides options for customizing preview operations.
  enum PreviewMode {
    // Unspecified policy, default mode will be used.
    PREVIEW_MODE_UNSPECIFIED = 0;

    // DEFAULT mode generates an execution plan for reconciling current resource
    // state into expected resource state.
    DEFAULT = 1;

    // DELETE mode generates as execution plan for destroying current resources.
    DELETE = 2;
  }

  // Possible errors that can occur with previews.
  enum ErrorCode {
    // No error code was specified.
    ERROR_CODE_UNSPECIFIED = 0;

    // Cloud Build failed due to a permissions issue.
    CLOUD_BUILD_PERMISSION_DENIED = 1;

    // Cloud Storage bucket failed to create due to a permissions issue.
    BUCKET_CREATION_PERMISSION_DENIED = 2;

    // Cloud Storage bucket failed for a non-permissions-related issue.
    BUCKET_CREATION_FAILED = 3;

    // Acquiring lock on provided deployment reference failed.
    DEPLOYMENT_LOCK_ACQUIRE_FAILED = 4;

    // Preview encountered an error when trying to access Cloud Build API.
    PREVIEW_BUILD_API_FAILED = 5;

    // Preview created a build but build failed and logs were generated.
    PREVIEW_BUILD_RUN_FAILED = 6;
  }

  // Blueprint to preview.
  oneof blueprint {
    // The terraform blueprint to preview.
    TerraformBlueprint terraform_blueprint = 6;
  }

  // Identifier. Resource name of the preview. Resource name can be user
  // provided or server generated ID if unspecified. Format:
  // `projects/{project}/locations/{location}/previews/{preview}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Time the preview was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User-defined labels for the preview.
  map<string, string> labels = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Current state of the preview.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Optional deployment reference. If specified, the preview will be
  // performed using the provided deployment's current state and use any
  // relevant fields from the deployment unless explicitly specified in the
  // preview create request.
  string deployment = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/Deployment"
    }
  ];

  // Optional. Current mode of preview.
  PreviewMode preview_mode = 15 [(google.api.field_behavior) = OPTIONAL];

  // Required. User-specified Service Account (SA) credentials to be used when
  // previewing resources.
  // Format: `projects/{projectID}/serviceAccounts/{serviceAccount}`
  string service_account = 7 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // Optional. User-defined location of Cloud Build logs, artifacts, and
  // in Google Cloud Storage.
  // Format: `gs://{bucket}/{folder}`
  // A default bucket will be bootstrapped if the field is not set or empty
  // Default Bucket Format: `gs://<project number>-<region>-blueprint-config`
  // Constraints:
  // - The bucket needs to be in the same project as the deployment
  // - The path cannot be within the path of `gcs_source`
  // If omitted and deployment resource ref provided has artifacts_gcs_bucket
  // defined, that artifact bucket is used.
  optional string artifacts_gcs_bucket = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The user-specified Worker Pool resource in which the Cloud Build
  // job will execute. Format
  // projects/{project}/locations/{location}/workerPools/{workerPoolId} If this
  // field is unspecified, the default Cloud Build worker pool will be used. If
  // omitted and deployment resource ref provided has worker_pool defined, that
  // worker pool is used.
  optional string worker_pool = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }
  ];

  // Output only. Code describing any errors that may have occurred.
  ErrorCode error_code = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information regarding the current state.
  google.rpc.Status error_status = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud Build instance UUID associated with this preview.
  string build = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Summary of errors encountered during Terraform preview.
  // It has a size limit of 10, i.e. only top 10 errors will be summarized here.
  repeated TerraformError tf_errors = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Link to tf-error.ndjson file, which contains the full list of
  // the errors encountered during a Terraform preview.
  // Format: `gs://{bucket}/{object}`.
  string error_logs = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Artifacts from preview.
  PreviewArtifacts preview_artifacts = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of preview logs in `gs://{bucket}/{object}` format.
  string logs = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current Terraform version set on the preview.
  // It is in the format of "Major.Minor.Patch", for example, "1.3.10".
  string tf_version = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The user-specified Terraform version constraint.
  // Example: "=1.3.10".
  optional string tf_version_constraint = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Arbitrary key-value metadata storage e.g. to help client tools
  // identifiy preview during automation. See
  // https://google.aip.dev/148#annotations for details on format and size
  // limitations.
  map<string, string> annotations = 20 [(google.api.field_behavior) = OPTIONAL];
}

// Ephemeral metadata content describing the state of a preview operation.
message PreviewOperationMetadata {
  // The possible steps a preview may be running.
  enum PreviewStep {
    // Unspecified preview step.
    PREVIEW_STEP_UNSPECIFIED = 0;

    // Infra Manager is creating a Google Cloud Storage bucket to store
    // artifacts and metadata about the preview.
    PREPARING_STORAGE_BUCKET = 1;

    // Downloading the blueprint onto the Google Cloud Storage bucket.
    DOWNLOADING_BLUEPRINT = 2;

    // Initializing Terraform using `terraform init`.
    RUNNING_TF_INIT = 3;

    // Running `terraform plan`.
    RUNNING_TF_PLAN = 4;

    // Fetching a deployment.
    FETCHING_DEPLOYMENT = 5;

    // Locking a deployment.
    LOCKING_DEPLOYMENT = 6;

    // Unlocking a deployment.
    UNLOCKING_DEPLOYMENT = 7;

    // Operation was successful.
    SUCCEEDED = 8;

    // Operation failed.
    FAILED = 9;

    // Validating the provided repository.
    VALIDATING_REPOSITORY = 10;
  }

  // The current step the preview operation is running.
  PreviewStep step = 1;

  // Artifacts from preview.
  PreviewArtifacts preview_artifacts = 2;

  // Output only. Location of preview logs in `gs://{bucket}/{object}` format.
  string logs = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud Build instance UUID associated with this preview.
  string build = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Artifacts created by preview.
message PreviewArtifacts {
  // Output only. Location of a blueprint copy and other content in Google Cloud
  // Storage. Format: `gs://{bucket}/{object}`
  string content = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of artifacts in Google Cloud Storage.
  // Format: `gs://{bucket}/{object}`
  string artifacts = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A request to create a preview.
message CreatePreviewRequest {
  // Required. The parent in whose context the Preview is created. The parent
  // value is in the format: 'projects/{project_id}/locations/{location}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The preview ID.
  string preview_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. [Preview][google.cloud.config.v1.Preview] resource to be created.
  Preview preview = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// A request to get details about a preview.
message GetPreviewRequest {
  // Required. The name of the preview. Format:
  // 'projects/{project_id}/locations/{location}/previews/{preview}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "config.googleapis.com/Preview" }
  ];
}

// A request to list all previews for a given project and location.
message ListPreviewsRequest {
  // Required. The parent in whose context the Previews are listed. The parent
  // value is in the format: 'projects/{project_id}/locations/{location}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. When requesting a page of resources, 'page_size' specifies number
  // of resources to return. If unspecified, at most 500 will be returned. The
  // maximum value is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token returned by previous call to 'ListDeployments' which
  // specifies the position in the list from where to continue listing the
  // resources.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Lists the Deployments that match the filter expression. A filter
  // expression filters the resources listed in the response. The expression
  // must be of the form '{field} {operator} {value}' where operators: '<', '>',
  // '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS
  // operator which is roughly synonymous with equality). {field} can refer to a
  // proto or JSON field, or a synthetic field. Field names can be camelCase or
  // snake_case.
  //
  // Examples:
  // - Filter by name:
  //   name = "projects/foo/locations/us-central1/deployments/bar
  //
  // - Filter by labels:
  //   - Resources that have a key called 'foo'
  //     labels.foo:*
  //   - Resources that have a key called 'foo' whose value is 'bar'
  //     labels.foo = bar
  //
  // - Filter by state:
  //   - Deployments in CREATING state.
  //     state=CREATING
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field to use to sort the list.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A response to a `ListPreviews` call. Contains a list of Previews.
message ListPreviewsResponse {
  // List of [Previews][google.cloud.config.v1.Preview].
  repeated Preview previews = 1;

  // Token to be supplied to the next ListPreviews request via `page_token`
  // to obtain the next set of results.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to delete a preview.
message DeletePreviewRequest {
  // Required. The name of the Preview in the format:
  // 'projects/{project_id}/locations/{location}/previews/{preview}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "config.googleapis.com/Preview" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// A request to export preview results.
message ExportPreviewResultRequest {
  // Required. The preview whose results should be exported. The preview value
  // is in the format:
  // 'projects/{project_id}/locations/{location}/previews/{preview}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "config.googleapis.com/Preview" }
  ];
}

// A response to `ExportPreviewResult` call. Contains preview results.
message ExportPreviewResultResponse {
  // Output only. Signed URLs for accessing the plan files.
  PreviewResult result = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains a signed Cloud Storage URLs.
message PreviewResult {
  // Output only. Plan binary signed URL
  string binary_signed_uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Plan JSON signed URL
  string json_signed_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request message for the GetTerraformVersion method.
message GetTerraformVersionRequest {
  // Required. The name of the TerraformVersion. Format:
  // 'projects/{project_id}/locations/{location}/terraformVersions/{terraform_version}'
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "config.googleapis.com/TerraformVersion"
    }
  ];
}

// The request message for the ListTerraformVersions method.
message ListTerraformVersionsRequest {
  // Required. The parent in whose context the TerraformVersions are listed. The
  // parent value is in the format:
  // 'projects/{project_id}/locations/{location}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. When requesting a page of resources, 'page_size' specifies number
  // of resources to return. If unspecified, at most 500 will be returned. The
  // maximum value is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token returned by previous call to 'ListTerraformVersions' which
  // specifies the position in the list from where to continue listing the
  // resources.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Lists the TerraformVersions that match the filter expression. A
  // filter expression filters the resources listed in the response. The
  // expression must be of the form '{field} {operator} {value}' where
  // operators: '<', '>',
  // '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS
  // operator which is roughly synonymous with equality). {field} can refer to a
  // proto or JSON field, or a synthetic field. Field names can be camelCase or
  // snake_case.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field to use to sort the list.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for the `ListTerraformVersions` method.
message ListTerraformVersionsResponse {
  // List of [TerraformVersion][google.cloud.config.v1.TerraformVersion]s.
  repeated TerraformVersion terraform_versions = 1;

  // Token to be supplied to the next ListTerraformVersions request via
  // `page_token` to obtain the next set of results.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// A TerraformVersion represents the support state the corresponding
// Terraform version.
message TerraformVersion {
  option (google.api.resource) = {
    type: "config.googleapis.com/TerraformVersion"
    pattern: "projects/{project}/locations/{location}/terraformVersions/{terraform_version}"
    plural: "terraformVersions"
    singular: "terraformVersion"
  };

  // Possible states of a TerraformVersion.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The version is actively supported.
    ACTIVE = 1;

    // The version is deprecated.
    DEPRECATED = 2;

    // The version is obsolete.
    OBSOLETE = 3;
  }

  // Identifier. The version name is in the format:
  // 'projects/{project_id}/locations/{location}/terraformVersions/{terraform_version}'.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The state of the version, ACTIVE, DEPRECATED or OBSOLETE.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the version is supported.
  google.protobuf.Timestamp support_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the version is deprecated.
  optional google.protobuf.Timestamp deprecate_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the version is obsolete.
  optional google.protobuf.Timestamp obsolete_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
