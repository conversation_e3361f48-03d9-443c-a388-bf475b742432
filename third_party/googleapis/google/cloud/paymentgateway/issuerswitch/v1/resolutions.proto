// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.paymentgateway.issuerswitch.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/paymentgateway/issuerswitch/v1/common_fields.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option csharp_namespace = "Google.Cloud.PaymentGateway.IssuerSwitch.V1";
option go_package = "cloud.google.com/go/paymentgateway/issuerswitch/apiv1/issuerswitchpb;issuerswitchpb";
option java_multiple_files = true;
option java_outer_classname = "ResolutionsProto";
option java_package = "com.google.cloud.paymentgateway.issuerswitch.v1";
option php_namespace = "Google\\Cloud\\PaymentGateway\\IssuerSwitch\\V1";
option ruby_package = "Google::Cloud::PaymentGateway::IssuerSwitch::V1";

// Creates and resolves UPI complaints and disputes.
service IssuerSwitchResolutions {
  option (google.api.default_host) = "issuerswitch.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a complaint. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [CreateComplaintMetadata][google.cloud.paymentgateway.issuerswitch.v1.CreateComplaintMetadata]
  // - `response`:
  // [Complaint][google.cloud.paymentgateway.issuerswitch.v1.Complaint]
  rpc CreateComplaint(CreateComplaintRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/complaints"
      body: "complaint"
    };
    option (google.api.method_signature) = "parent,complaint";
    option (google.longrunning.operation_info) = {
      response_type: "Complaint"
      metadata_type: "CreateComplaintMetadata"
    };
  }

  // Resolve a complaint. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [ResolveComplaintMetadata][google.cloud.paymentgateway.issuerswitch.v1.ResolveComplaintMetadata]
  // - `response`:
  // [Complaint][google.cloud.paymentgateway.issuerswitch.v1.Complaint]
  rpc ResolveComplaint(ResolveComplaintRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{complaint.name=projects/*/complaints/*}:resolve"
      body: "complaint"
    };
    option (google.api.method_signature) = "complaint";
    option (google.longrunning.operation_info) = {
      response_type: "Complaint"
      metadata_type: "ResolveComplaintMetadata"
    };
  }

  // Create a dispute. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [CreateDisputeMetadata][google.cloud.paymentgateway.issuerswitch.v1.CreateDisputeMetadata]
  // - `response`:
  // [Dispute][google.cloud.paymentgateway.issuerswitch.v1.Dispute]
  rpc CreateDispute(CreateDisputeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/disputes"
      body: "dispute"
    };
    option (google.api.method_signature) = "parent,dispute";
    option (google.longrunning.operation_info) = {
      response_type: "Dispute"
      metadata_type: "CreateDisputeMetadata"
    };
  }

  // Resolve a dispute. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [ResolveDisputeMetadata][google.cloud.paymentgateway.issuerswitch.v1.ResolveDisputeMetadata]
  // - `response`:
  // [Dispute][google.cloud.paymentgateway.issuerswitch.v1.Dispute]
  rpc ResolveDispute(ResolveDisputeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{dispute.name=projects/*/disputes/*}:resolve"
      body: "dispute"
    };
    option (google.api.method_signature) = "dispute";
    option (google.longrunning.operation_info) = {
      response_type: "Dispute"
      metadata_type: "ResolveDisputeMetadata"
    };
  }
}

// A complaint processed by the issuer switch.
message Complaint {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/Complaint"
    pattern: "projects/{project}/complaints/{complaint}"
  };

  // The name of the complaint. This uniquely identifies the complaint.
  // Format of name is
  // projects/{project_id}/complaints/{complaint_id}.
  string name = 1;

  // The reason for raising the complaint. This maps adjustment flag
  // and reason code for the complaint to `reqAdjFlag` and `reqAdjCode` in
  // complaint request respectively while raising a complaint.
  RaiseComplaintAdjustment raise_complaint_adjustment = 2;

  // Required. Details required for raising / resolving a complaint.
  CaseDetails details = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. Response to the raised / resolved complaint.
  CaseResponse response = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The reason for resolving the complaint. It provides adjustment values while
  // resolving and for already resolved complaints. This maps adjustment flag
  // and reason code for the complaint to `reqAdjFlag` and `reqAdjCode` in
  // complaint request respectively when a complete resolution is done via
  // Resolve Complaint API otherwise maps to `respAdjFlag` and `respAdjCode` in
  // complaint response respectively when a complaint request from UPI is
  // directly resolved by issuer switch.
  ResolveComplaintAdjustment resolve_complaint_adjustment = 6;
}

// Request for the `CreateComplaint` method.
message CreateComplaintRequest {
  // Required. The parent resource for the complaint. The format is
  // `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/Complaint"
    }
  ];

  // Required. The complaint to be raised.
  Complaint complaint = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `ResolveComplaint` method.
message ResolveComplaintRequest {
  // Required. The complaint to be resolved.
  Complaint complaint = 1 [(google.api.field_behavior) = REQUIRED];
}

// A dispute processed by the issuer switch.
message Dispute {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/Dispute"
    pattern: "projects/{project}/disputes/{dispute}"
  };

  // The name of the dispute. This uniquely identifies the dispute.
  // Format of name is
  // projects/{project_id}/disputes/{dispute_id}.
  string name = 1;

  // The reason for raising the dispute. This maps adjustment flag
  // and reason code for the dispute to `reqAdjFlag` and `reqAdjCode` in
  // complaint request respectively while raising a dispute.
  RaiseDisputeAdjustment raise_dispute_adjustment = 2;

  // Required. Details required for raising/resolving dispute.
  CaseDetails details = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. Response to the raised/resolved dispute.
  CaseResponse response = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The reason for resolving the dispute. It provides adjustment values while
  // resolving and for already resolved disputes. This maps adjustment flag
  // and reason code for the dispute to `reqAdjFlag` and `reqAdjCode` in
  // dispute request respectively while resolving a dispute.
  ResolveDisputeAdjustment resolve_dispute_adjustment = 6;
}

// Request for the `CreateDispute` method.
message CreateDisputeRequest {
  // Required. The parent resource for the dispute. The format is
  // `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/Dispute"
    }
  ];

  // Required. The dispute to be raised.
  Dispute dispute = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `ResolveDispute` method.
message ResolveDisputeRequest {
  // Required. The dispute to be resolved.
  Dispute dispute = 1 [(google.api.field_behavior) = REQUIRED];
}

// Details of original transaction.
message OriginalTransaction {
  // Required. Uniquely identifies the original transaction. This maps to the
  // `Txn.Id` value of the original transaction in India's UPI system.
  string transaction_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Retrieval Reference Number (RRN) of the original transaction.
  string retrieval_reference_number = 2
      [(google.api.field_behavior) = REQUIRED];

  // Timestamp of the original transaction request.
  google.protobuf.Timestamp request_time = 3;
}

// Details of the complaint or dispute.
message CaseDetails {
  // Required. Details of original transaction.
  OriginalTransaction original_transaction = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Initiator of the complaint / dispute.
  TransactionSubType transaction_sub_type = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The adjustment amount in URCS for the complaint / dispute. This
  // maps to `reqAdjAmount` in complaint request.
  google.type.Money amount = 3 [(google.api.field_behavior) = REQUIRED];

  // The original response code which has been updated in the complaint
  // Response. This should map to settlement response code currently available
  // in URCS system.
  string original_settlement_response_code = 4;

  // Required. Set to true if the complaint / dispute belongs to current
  // settlement cycle, false otherwise.
  bool current_cycle = 5 [(google.api.field_behavior) = REQUIRED];
}

// Response to the complaint or dispute.
message CaseResponse {
  // The status of the complaint or dispute transaction. This maps to `result`
  // in complaint transaction response.
  enum Result {
    // Unspecified status.
    RESULT_UNSPECIFIED = 0;

    // The transaction has successfully completed.
    SUCCESS = 1;

    // The transaction has failed.
    FAILURE = 2;
  }

  // Complaint Reference Number(CRN) sent by UPI as a reference against the
  // generated complaint / dispute.
  string complaint_reference_number = 1;

  // The adjustment amount of the response. This maps to `adjAmt` in
  // complaint response.
  google.type.Money amount = 2;

  // The adjustment flag in response to the complaint. This maps adjustment flag
  // in URCS for the complaint transaction to `Resp.Ref.adjFlag` in complaint
  // response.
  string adjustment_flag = 3;

  // The adjustment code in response to the complaint. This maps reason code in
  // URCS for the complaint transaction to `Resp.Ref.adjCode` in complaint
  // response.
  string adjustment_code = 4;

  // It defines the Adjustment Reference ID which has been updated in the
  // complaint response. This maps to `adjRefID` in complaint response.
  string adjustment_reference_id = 5;

  // Adjustment Remarks. This maps to `adjRemarks` in complaint response.
  string adjustment_remarks = 6;

  // The Approval Reference Number. This maps to `approvalNum` in complaint
  // response.
  string approval_number = 7;

  // Process Status of the transaction. This maps to `procStatus` in complaint
  // response.
  string process_status = 8;

  // The adjustment timestamp when bank performs the adjustment for the received
  // complaint request. This maps to `adjTs` in complaint response.
  google.protobuf.Timestamp adjustment_time = 9;

  // The details of the participant of the original financial transaction.
  oneof participant {
    // The payer in the original financial transaction.
    Participant payer = 10;

    // The payee in the original financial transaction.
    Participant payee = 11;
  }

  // The result of the transaction.
  Result result = 12;
}

// The adjusment flag and reason code for raising complaint.
message RaiseComplaintAdjustment {
  // The adjusment flag for raising complaint.
  enum AdjustmentFlag {
    // Unspecified adjustment flag.
    ADJUSTMENT_FLAG_UNSPECIFIED = 0;

    // Complaint Raise. This flag maps to the `PBRB` adjustment flag as defined
    // in NPCI's `UDIR` specification.
    RAISE = 1;
  }

  // The reason for raising complaint.
  enum ReasonCode {
    // Unspecified reason code.
    REASON_CODE_UNSPECIFIED = 0;

    // Customer account has not yet reversed for a declined pay transaction.
    // This reason code maps to the `U005` reason code as defined in NPCI's
    // `UDIR` specification.
    CUSTOMER_ACCOUNT_NOT_REVERSED = 1;

    // Goods / services are not provided for approved transaction.
    // This reason code maps to the `U008` reason code as defined in NPCI's
    // `UDIR` specification.
    GOODS_SERVICES_NOT_PROVIDED = 2;

    // Customer account not credited back for declined transaction. This
    // reason code maps to the `U009` reason code as defined in NPCI's `UDIR`
    // specification.
    CUSTOMER_ACCOUNT_NOT_CREDITED_BACK = 3;

    // Beneficiary account is not credited for successful pay transaction. This
    // reason code maps to the `U010` reason code as defined in NPCI's `UDIR`
    // specification.
    BENEFICIARY_ACCOUNT_NOT_CREDITED = 4;

    // Credit not processed for cancelled or returned goods and services.
    // This reason code maps to the `U021` reason code as defined in NPCI's
    // `UDIR` specification.
    GOODS_SERVICES_CREDIT_NOT_PROCESSED = 5;

    // Account debited but transaction confirmation not received at merchant
    // location. This reason code maps to the `U022` reason code as defined in
    // NPCI's `UDIR` specification.
    MERCHANT_NOT_RECEIVED_CONFIRMATION = 6;

    // Paid by alternate means / Duplicate payment. This reason code maps to the
    // `U023` reason code as defined in NPCI's `UDIR` specification.
    PAID_BY_ALTERNATE_MEANS = 7;
  }

  // Required. The adjustment flag in URCS for the complaint transaction. This
  // maps to `reqAdjFlag` in complaint request and `respAdjFlag` in complaint
  // response.
  AdjustmentFlag adjustment_flag = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The adjustment code in URCS for the complaint transaction. This
  // maps to `reqAdjCode` in complaint request.
  ReasonCode adjustment_code = 2 [(google.api.field_behavior) = REQUIRED];
}

// The adjusment flag and reason code for resolving the complaint.
message ResolveComplaintAdjustment {
  // The adjusment flag for resolving the complaint.
  enum AdjustmentFlag {
    // Unspecified adjustment flag.
    ADJUSTMENT_FLAG_UNSPECIFIED = 0;

    // Debit Reversal Confirmation. This flag maps to the `DRC` adjustment flag
    // as defined in NPCI's `UDIR` specification.
    DEBIT_REVERSAL_CONFIRMATION = 1;

    // Return. This flag maps to the `RET` adjustment flag as defined in NPCI's
    // `UDIR` specification.
    RETURN = 2;

    // Refund Reversal Confirmation. This flag maps to the `RRC` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    REFUND_REVERSAL_CONFIRMATION = 3;

    // Transaction Credit Confirmation. This flag maps to the `TCC` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    TRANSACTION_CREDIT_CONFIRMATION = 4;
  }

  // The complaint resolution reason code.
  enum ReasonCode {
    // Unspecified reason code.
    REASON_CODE_UNSPECIFIED = 0;

    // Customer account has been reversed online for DRC dispute or beneficiary
    // account has been credited online for TCC dispute. This reason code maps
    // to the `102` reason code as defined in NPCI's `UDIR` specification.
    COMPLAINT_RESOLVED_ONLINE = 1;

    // Customer account has been reversed now or manually post reconciliation
    // for DRC dispute or beneficiary account has been credited now or manually
    // post reconciliation for TCC dispute. This reason code maps to the `103`
    // reason code as defined in NPCI's `UDIR` specification.
    COMPLAINT_RESOLVED_NOW_OR_MANUALLY = 2;

    // Online decline response failed. This reason code maps to the
    // `104` reason code as defined in NPCI's `UDIR` specification.
    ORIGINAL_TRANSACTION_NOT_DONE = 3;

    // Account closed. This reason code maps to the `114` reason code for
    // RET dispute as defined in NPCI's `UDIR` specification.
    RET_ACCOUNT_CLOSED = 4;

    // Account does not exist. This reason code maps to the `115` reason code
    // for RET dispute as defined in NPCI's `UDIR` specification.
    RET_ACCOUNT_DOES_NOT_EXIST = 5;

    // Party instructions. This reason code maps to the `116` reason code for
    // RET dispute as defined in NPCI's `UDIR` specification.
    RET_PARTY_INSTRUCTIONS = 6;

    // NRI account. This reason code maps to the `117` reason code for RET
    // dispute as defined in NPCI's `UDIR` specification.
    RET_NRI_ACCOUNT = 7;

    // Credit freezed. This reason code maps to the `118` reason code for RET
    // dispute as defined in NPCI's `UDIR` specification.
    RET_CREDIT_FREEZED = 8;

    // Invalid beneficiary details. This reason code maps to the `119` reason
    // code for RET dispute as defined in NPCI's `UDIR` specification.
    RET_INVALID_BENEFICIARY_DETAILS = 9;

    // Any other reason. This reason code maps to the `120` reason code for RET
    // dispute as defined in NPCI's `UDIR` specification.
    RET_ANY_OTHER_REASON = 10;

    // Beneficiary bank unable to credit their customer account.
    // This reason code maps to the `1094` reason code for RET dispute as
    // defined in NPCI's `UDIR` specification.
    RET_BENEFICIARY_CANNOT_CREDIT = 11;

    // Account debited but transaction confirmation not received at merchant
    // location. This reason code maps to the `1065` reason code for Credit
    // adjustment and RET dispute as defined in NPCI's `UDIR` specification.
    RET_MERCHANT_NOT_RECEIVED_CONFIRMATION = 12;

    // Customer account has been credited. This reason code maps to the `501`
    // reason code for Refund reversal confirmation dispute as defined in NPCI's
    // `UDIR` specification.
    RRC_CUSTOMER_ACCOUNT_CREDITED = 13;
  }

  // Required. The adjustment flag in URCS for the complaint transaction. This
  // maps to `reqAdjFlag` in complaint request and `respAdjFlag` in complaint
  // response.
  AdjustmentFlag adjustment_flag = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The adjustment code in URCS for the complaint transaction. This
  // maps to `reqAdjCode` in complaint request.
  ReasonCode adjustment_code = 2 [(google.api.field_behavior) = REQUIRED];
}

// The adjusment flag and reason code for raising dispute.
message RaiseDisputeAdjustment {
  // The adjusment flag for raising dispute.
  enum AdjustmentFlag {
    // Unspecified adjustment flag.
    ADJUSTMENT_FLAG_UNSPECIFIED = 0;

    // Chargeback Raise. This flag maps to the `B` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    CHARGEBACK_RAISE = 1;

    // Fraud Chargeback Raise. This flag maps to the `FC` adjustment flag
    // as defined in NPCI's `UDIR` specification.
    FRAUD_CHARGEBACK_RAISE = 2;

    // Wrong Credit Chargeback Raise. This flag maps to the `WC` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    WRONG_CREDIT_CHARGEBACK_RAISE = 3;

    // Deferred Chargeback Raise. This flag maps to the `FB` adjustment flag
    // as defined in NPCI's `UDIR` specification.
    DEFERRED_CHARGEBACK_RAISE = 4;

    // Pre-Arbitration Raise. This flag maps to the `P` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    PRE_ARBITRATION_RAISE = 5;

    // Deferred Pre-Arbitration Raise. This flag maps to the `FP` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    DEFERRED_PRE_ARBITRATION_RAISE = 6;

    // Arbitration Raise. This flag maps to the `AR` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    ARBITRATION_RAISE = 7;

    // Deferred Arbitration Raise. This flag maps to the `FAR` adjustment flag
    // as defined in NPCI's `UDIR` specification.
    DEFERRED_ARBITRATION_RAISE = 8;
  }

  // The reason for raising dispute.
  enum ReasonCode {
    // Unspecified reason code.
    REASON_CODE_UNSPECIFIED = 0;

    // Remitter account is debited but beneficiary account is not credited.
    // This reason code maps to the `108` reason code as defined in
    // NPCI's `UDIR` specification.
    CHARGEBACK_RAISE_REMITTER_DEBITED_BENEFICIARY_NOT_CREDITED = 1;

    // Remitter bank customer still disputes that beneficiary account is not
    // credited. This reason code maps to the `109` reason code as defined in
    // NPCI's `UDIR` specification.
    PRE_ARBITRATION_RAISE_BENEFICIARY_NOT_CREDITED = 2;

    // TCC has been raised but customer still complaining that beneficiary
    // account is not credited. This reason code maps to the `121` reason code
    // as defined in NPCI's `UDIR` specification.
    DEFERRED_CHARGEBACK_RAISE_BENEFICIARY_NOT_CREDITED = 3;

    // Customer is still complaining for not crediting the beneficiary
    // customer account. This reason code maps to the `124` reason code as
    // defined in NPCI's `UDIR` specification.
    DEFERRED_PRE_ARBITRATION_RAISE_BENEFICIARY_NOT_CREDITED = 4;

    // Customer is complaining even after raising Deferred Chargeback and
    // Pre-Arbitration on Deferred Chargeback where both have been rejected by
    // beneficiary bank. This reason code maps to the `127` reason code as
    // defined in NPCI's `UDIR` specification.
    DEFERRED_ARBITRATION_RAISE_DEFERRED_CHARGEBACK_PRE_ARBITRATION_REJECTED = 5;

    // Chargeback on fraudulent transaction. This reason code maps to the `128`
    // reason code as defined in NPCI's `UDIR` specification.
    CHARGEBACK_ON_FRAUD = 6;

    // Credit not processed for cancelled or returned goods and services. This
    // reason code maps to the `1061` reason code as defined in NPCI's `UDIR`
    // specification.
    GOODS_SERVICES_CREDIT_NOT_PROCESSED = 7;

    // Goods and services not as described / defective. This reason code maps to
    // the `1062` reason code as defined in NPCI's `UDIR` specification.
    GOODS_SERVICES_DEFECTIVE = 8;

    // Paid by alternate means. This reason code maps to the `1063` reason code
    // as defined in NPCI's `UDIR` specification.
    PAID_BY_ALTERNATE_MEANS = 9;

    // Goods or services not provided / not received. This reason code maps to
    // the `1064` reason code as defined in NPCI's `UDIR` specification.
    GOODS_SERVICES_NOT_RECEIVED = 10;

    // Account debited but transaction confirmation not received at merchant
    // location. This reason code maps to the `1065` reason code for chargeback
    // raise and deferred chargeback raise as defined in NPCI's `UDIR`
    // specification.
    MERCHANT_NOT_RECEIVED_CONFIRMATION = 11;

    // Transaction not steeled within the specified timeframes. This reason code
    // maps to the `1081` reason code as defined in NPCI's `UDIR` specification.
    TRANSACTION_NOT_STEELED = 12;

    // Duplicate / Multiple transaction. This reason code maps to the `1084`
    // reason code as defined in NPCI's `UDIR` specification.
    DUPLICATE_TRANSACTION = 13;

    // Card holder was charged more than the transaction amount.
    // This reason code maps to the `1085` reason code for Chargeback raise
    // dispute as defined in NPCI's `UDIR` specification.
    CHARGEBACK_CARD_HOLDER_CHARGED_MORE = 14;

    // Customer is still claiming that services are not delivered. This reason
    // code maps to the `1097` reason code as defined in NPCI's `UDIR`
    // specification.
    CUSTOMER_CLAIMING_GOODS_SERVICES_NOT_DELIVERED = 15;

    // Both the parties denied to agree. This reason code maps to the `1100`
    // reason code as defined in NPCI's `UDIR` specification.
    PARTIES_DENIED = 16;

    // Customer transferred funds to the unintended beneficiary account. This
    // reason code maps to the `WC1` reason code as defined in NPCI's `UDIR`
    // specification.
    FUNDS_TRANSFERRED_TO_UNINTENDED_BENEFICIARY = 17;
  }

  // Required. The adjustment flag in URCS for the complaint transaction. This
  // maps to `reqAdjFlag` in dispute request and `respAdjFlag` in dispute
  // response.
  AdjustmentFlag adjustment_flag = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The adjustment code in URCS for the complaint transaction. This
  // maps to `reqAdjCode` in dispute request.
  ReasonCode adjustment_code = 2 [(google.api.field_behavior) = REQUIRED];
}

// The adjusment flag and reason code for resolving the dispute.
message ResolveDisputeAdjustment {
  // The adjusment flag for resolving the dispute.
  enum AdjustmentFlag {
    // Unspecified adjustment flag.
    ADJUSTMENT_FLAG_UNSPECIFIED = 0;

    // Re-presentment Raise. This flag maps to the `R` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    RE_PRESENTMENT_RAISE = 1;

    // Deferred Re-presentment Raise. This flag maps to the `FR` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    DEFERRED_RE_PRESENTMENT_RAISE = 2;

    // Chargeback Acceptance. This flag maps to the `A` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    CHARGEBACK_ACCEPTANCE = 3;

    // Deferred Chargeback Acceptance. This flag maps to the `FA` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    DEFERRED_CHARGEBACK_ACCEPTANCE = 4;

    // Pre-Arbitration Acceptance. This flag maps to the `AP` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    PRE_ARBITRATION_ACCEPTANCE = 5;

    // Deferred Pre-Arbitration Acceptance. This flag maps to the `FAP`
    // adjustment flag as defined in NPCI's `UDIR` specification.
    DEFERRED_PRE_ARBITRATION_ACCEPTANCE = 6;

    // Pre-Arbitration Declined. This flag maps to the `PR` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    PRE_ARBITRATION_DECLINED = 7;

    // Deferred Pre-Arbitration Declined. This flag maps to the `FPR` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    DEFERRED_PRE_ARBITRATION_DECLINED = 8;

    // Arbitration Acceptance. This flag maps to the `ACA` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    ARBITRATION_ACCEPTANCE = 9;

    // Arbitration Continuation. This flag maps to the `ACC` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    ARBITRATION_CONTINUATION = 10;

    // Arbitration Withdrawn. This flag maps to the `ACW` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    ARBITRATION_WITHDRAWN = 11;

    // Arbitration Verdict. This flag maps to the `ACV` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    ARBITRATION_VERDICT = 12;

    // Credit Adjustment. This flag maps to the `C` adjustment flag as
    // defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT = 13;

    // Fraud Chargeback Representment. This flag maps to the `FCR` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    FRAUD_CHARGEBACK_REPRESENTMENT = 14;

    // Fraud Chargeback Accept. This flag maps to the `FCA` adjustment flag
    // as defined in NPCI's `UDIR` specification.
    FRAUD_CHARGEBACK_ACCEPT = 15;

    // Wrong Credit Representment. This flag maps to the `WR` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    WRONG_CREDIT_REPRESENTMENT = 16;

    // Wrong Credit Chargeback Acceptance. This flag maps to the `WA` adjustment
    // flag as defined in NPCI's `UDIR` specification.
    WRONG_CREDIT_CHARGEBACK_ACCEPTANCE = 17;

    // Manual Adjustment. This flag maps to the `MA` adjustment flag as defined
    // in NPCI's `UDIR` specification.
    MANUAL_ADJUSTMENT = 18;
  }

  // The dispute resolution reason code.
  enum ReasonCode {
    // Unspecified reason code.
    REASON_CODE_UNSPECIFIED = 0;

    // Beneficiary bank unable to credit their customer account for Chargeback
    // Acceptance dispute or duplicate processing for Pre Arbitration Acceptance
    // dispute. This reason code maps to the `111` reason code as defined in
    // NPCI's `UDIR` specification.
    CHARGEBACK_BENEFICIARY_CANNOT_CREDIT_OR_PRE_ARBITRATION_DUPLICATE_PROCESS =
        1;

    // Beneficiary account has been credited online. This reason code maps to
    // the `112` reason code for Pre-arbitration declined dispute as defined in
    // NPCI's `UDIR` specification.
    PRE_ARBITRATION_DECLINED_BENEFICIARY_CREDITED_ONLINE = 3;

    // Beneficiary account has been credited manually post reconciliation. This
    // reason code maps to the `113` reason code for Pre-arbitration declined
    // dispute as defined in NPCI's `UDIR` specification.
    PRE_ARBITRATION_DECLINED_BENEFICIARY_CREDITED_MANUALLY = 4;

    // Customer account is not credited, TCC raised inadvertently. This reason
    // code maps to the `122` reason code as defined in NPCI's `UDIR`
    // specification.
    DEFERRED_CHARGEBACK_ACCEPTANCE_ACCOUNT_NOT_CREDITED_TCC_RAISED = 5;

    // Customer account is credited successfully and TCC raised accordingly.
    // This reason code maps to the `123` reason code as defined in NPCI's
    // `UDIR` specification.
    DEFERRED_RE_PRESENTMENT_RAISE_ACCOUNT_CREDITED_TCC_RAISED = 6;

    // Customer account is not credited, TCC and Re-Presentment raised
    // inadvertently. This reason code maps to the `125` reason code as defined
    // in NPCI's `UDIR` specification.
    DEFERRED_PRE_ARBITRATION_ACCEPTANCE_ACCOUNT_NOT_CREDITED = 7;

    // Customer account is credited successfully and TCC and Re-Presentment
    // raised accordingly. This reason code maps to the `126` reason code as
    // defined in NPCI's `UDIR` specification.
    DEFERRED_PRE_ARBITRATION_DECLINED_ACCOUNT_CREDITED = 8;

    // Amount has been recovered successfully from the fraudulent customer
    // account. This reason code maps to the `129` reason code as defined
    // in NPCI's `UDIR` specification.
    FRAUD_CHARGEBACK_ACCEPT_AMOUNT_RECOVERED_FROM_FRAUDULENT_ACCOUNT = 9;

    // Lien marked however, customer account is not having sufficient balance to
    // debit. This reason code maps to the `130` reason code for
    // Fraud chargeback representment dispute as defined in NPCI's `UDIR`
    // specification.
    FRAUD_CHARGEBACK_REPRESENTMENT_LIEN_MARKED_INSUFFICIENT_BALANCE = 10;

    // FIR Copy not provided for the disputed transaction. This reason code maps
    // to the `131` reason code as defined in NPCI's `UDIR` specification.
    FRAUD_CHARGEBACK_REPRESENTMENT_FIR_NOT_PROVIDED = 11;

    // Other reason for Fraud chargeback representment dispute. This reason code
    // maps to the `132` reason code as defined in NPCI's `UDIR` specification.
    FRAUD_CHARGEBACK_REPRESENTMENT_REASON_OTHERS = 12;

    // Beneficiary account credited online. This reason code maps to the `208`
    // reason code for Re-presentment raise dispute as defined in NPCI's `UDIR`
    // specification.
    RE_PRESENTMENT_RAISE_BENEFICIARY_CREDITED_ONLINE = 13;

    // Beneficiary account credited manually post reconciliation. This reason
    // code maps to the `209` reason code for Re-presentment raise dispute as
    // defined in NPCI's `UDIR` specification.
    RE_PRESENTMENT_RAISE_BENEFICIARY_CREDITED_MANUALLY = 14;

    // Credit not processed for cancelled or returned goods and services. This
    // reason code maps to the `1061` reason code as defined in NPCI's `UDIR`
    // specification.
    CREDIT_ADJUSTMENT_GOODS_SERVICES_CREDIT_NOT_PROCESSED = 15;

    // Goods and Services not as described / defective. This reason code maps to
    // the `1062` reason code as defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_GOODS_SERVICES_DEFECTIVE = 16;

    // Paid by alternate means. This reason code maps to the `1063` reason code
    // as defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_PAID_BY_ALTERNATE_MEANS = 17;

    // Goods or Services Not Provided / Not Received. This reason code maps to
    // the `1064` reason code as defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_GOODS_SERVICES_NOT_RECEIVED = 18;

    // Account debited but transaction confirmation not received at merchant
    // location. This reason code maps to the `1065` reason code for Credit
    // adjustment as defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_MERCHANT_NOT_RECEIVED_CONFIRMATION = 19;

    // Duplicate /Multiple Transaction. This reason code maps to the `1084`
    // reason code as defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_DUPLICATE_TRANSACTION = 20;

    // Other reason for Credit adjustment. This reason code maps to the `1090`
    // reason code as defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_REASON_OTHERS = 21;

    // Non Matching account number. This reason code maps to the `1091`
    // reason code as defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_NON_MATCHING_ACCOUNT_NUMBER = 22;

    // Card holder was charged more than the transaction amount.
    // This reason code maps to the `1092` reason code as defined in NPCI's
    // `UDIR` specification.
    CREDIT_ADJUSTMENT_CARD_HOLDER_CHARGED_MORE = 23;

    // Credit not Processed. This reason code maps to the `1093` reason code as
    // defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_CREDIT_NOT_PROCESSED = 24;

    // Beneficiary bank unable to credit their customer account. This reason
    // code maps to the `1094` reason code for Credit Adjustment dispute as
    // defined in NPCI's `UDIR` specification.
    CREDIT_ADJUSTMENT_BENEFICIARY_CANNOT_CREDIT = 25;

    // Merchant was unable to provide the service. This reason code maps to the
    // `1095` reason code as defined in NPCI's `UDIR` specification.
    CHARGEBACK_ACCEPTANCE_MERCHANT_CANNOT_PROVIDE_SERVICE = 26;

    // Services/Goods provided see the supporting document. This reason code
    // maps to the `1096` reason code as defined in NPCI's `UDIR` specification.
    RE_PRESENTMENT_RAISE_GOODS_SERVICES_PROVIDED = 27;

    // Services provided later see supporting documents. This reason code maps
    // to the `1098` reason code as defined in NPCI's `UDIR` specification.
    PRE_ARBITRATION_DECLINED_SERVICES_PROVIDED_LATER = 28;

    // Services not provided by the merchant. This reason code maps to the
    // `1099` reason code as defined in NPCI's `UDIR` specification.
    PRE_ARBITRATION_ACCEPTANCE_SERVICES_NOT_PROVIDED_BY_MERCHANT = 29;

    // Illegible Fulfilment. This reason code maps to the `1101` reason code for
    // arbitration acceptance dispute as defined in NPCI's `UDIR` specification.
    ARBITRATION_ACCEPTANCE_ILLEGIBLE_FULFILMENT = 30;

    // Customer has still not received the service. This reason code maps to the
    // `1102` reason code as defined in NPCI's `UDIR` specification.
    ARBITRATION_CONTINUATION_CUSTOMER_STILL_NOT_RECEIVED_SERVICE = 31;

    // Customer has received the service later. This reason code maps to the
    // `1103` reason code as defined in NPCI's `UDIR` specification.
    ARBITRATION_WITHDRAWN_CUSTOMER_RECEIVED_SERVICE_LATER = 32;

    // Panel will give the verdict. This reason code maps to the `1104` reason
    // code as defined in NPCI's `UDIR` specification.
    ARBITRATION_VERDICT_PANEL_VERDICT = 33;

    // Manual adjustment. This reason code maps to the `2001` reason code as
    // defined in NPCI's `UDIR` specification.
    MANUAL_ADJUSTMENT_REASON = 34;

    // Attributing to the Customer. This reason code maps to the `AC` reason
    // code as defined in NPCI's `UDIR` specification.
    ATTRIBUTING_CUSTOMER = 35;

    // Attributing to the Technical issue at bank/aggregator/merchant. This
    // reason code maps to the `AT` reason code as defined in NPCI's `UDIR`
    // specification.
    ATTRIBUTING_TECHNICAL_ISSUE = 36;

    // Amount has been recovered successfully from the unintended customer
    // account. This reason code maps to the `WC2` reason code as defined in
    // NPCI's `UDIR` specification.
    WRONG_CREDIT_CHARGEBACK_ACCEPTANCE_AMOUNT_RECOVERED = 37;

    // Lien marked however customer account is not having sufficient balance to
    // debit the customer account. This reason code maps to the `WC3` reason
    // code for Wrong credit representment dispute as defined in NPCI's `UDIR`
    // specification.
    WRONG_CREDIT_REPRESENTMENT_LIEN_MARKED_INSUFFICIENT_BALANCE = 38;

    // Customer is not accessible for obtaining debit confirmation. This reason
    // code maps to the `WC4` reason code as defined in NPCI's `UDIR`
    // specification.
    WRONG_CREDIT_REPRESENTMENT_CUSTOMER_INACCESSIBLE = 39;

    // Other reason for Wrong credit representment. This reason code maps to the
    // `WC5` reason code as defined in NPCI's `UDIR` specification.
    WRONG_CREDIT_REPRESENTMENT_REASON_OTHERS = 40;
  }

  // Required. The adjustment flag in URCS for the complaint transaction. This
  // maps to `reqAdjFlag` in dispute request and `respAdjFlag` in dispute
  // response.
  AdjustmentFlag adjustment_flag = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The adjustment code in URCS for the complaint transaction. This
  // maps to `reqAdjCode` in dispute request.
  ReasonCode adjustment_code = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata for CreateComplaint.
message CreateComplaintMetadata {}

// Metadata for ResolveComplaint.
message ResolveComplaintMetadata {}

// Metadata for CreateDispute.
message CreateDisputeMetadata {}

// Metadata for ResolveDispute.
message ResolveDisputeMetadata {}

// The subtype of the complaint or dispute.
enum TransactionSubType {
  // Unspecified transaction subtype.
  TRANSACTION_SUB_TYPE_UNSPECIFIED = 0;

  // Beneficiary transaction subtype.
  TRANSACTION_SUB_TYPE_BENEFICIARY = 1;

  // Remitter transaction subtype.
  TRANSACTION_SUB_TYPE_REMITTER = 2;
}
