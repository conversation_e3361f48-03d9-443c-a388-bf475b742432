# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "tasks_proto",
    srcs = [
        "cloudtasks.proto",
        "queue.proto",
        "target.proto",
        "task.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "tasks_proto_with_info",
    deps = [
        ":tasks_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "tasks_java_proto",
    deps = [":tasks_proto"],
)

java_grpc_library(
    name = "tasks_java_grpc",
    srcs = [":tasks_proto"],
    deps = [":tasks_java_proto"],
)

java_gapic_library(
    name = "tasks_java_gapic",
    srcs = [":tasks_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloudtasks_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudtasks_v2.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":tasks_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":tasks_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "tasks_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.tasks.v2.CloudTasksClientHttpJsonTest",
        "com.google.cloud.tasks.v2.CloudTasksClientTest",
    ],
    runtime_deps = [":tasks_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-tasks-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":tasks_java_gapic",
        ":tasks_java_grpc",
        ":tasks_java_proto",
        ":tasks_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "tasks_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/cloudtasks/apiv2/cloudtaskspb",
    protos = [":tasks_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "tasks_go_gapic",
    srcs = [":tasks_proto_with_info"],
    grpc_service_config = "cloudtasks_grpc_service_config.json",
    importpath = "cloud.google.com/go/cloudtasks/apiv2;cloudtasks",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudtasks_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":tasks_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-tasks-v2-go",
    deps = [
        ":tasks_go_gapic",
        ":tasks_go_gapic_srcjar-metadata.srcjar",
        ":tasks_go_gapic_srcjar-snippets.srcjar",
        ":tasks_go_gapic_srcjar-test.srcjar",
        ":tasks_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "tasks_py_gapic",
    srcs = [":tasks_proto"],
    grpc_service_config = "cloudtasks_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudtasks_v2.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "tasks_py_gapic_test",
    srcs = [
        "tasks_py_gapic_pytest.py",
        "tasks_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":tasks_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "tasks-v2-py",
    deps = [
        ":tasks_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "tasks_php_proto",
    deps = [":tasks_proto"],
)

php_gapic_library(
    name = "tasks_php_gapic",
    srcs = [":tasks_proto_with_info"],
    grpc_service_config = "cloudtasks_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudtasks_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":tasks_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-tasks-v2-php",
    deps = [
        ":tasks_php_gapic",
        ":tasks_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "tasks_nodejs_gapic",
    package_name = "@google-cloud/tasks",
    src = ":tasks_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudtasks_grpc_service_config.json",
    main_service = "tasks",
    package = "google.cloud.tasks.v2",
    rest_numeric_enums = True,
    service_yaml = "cloudtasks_v2.yaml",
    transport = "grpc+rest",
    deps = [],
    format = "esm",
)

nodejs_gapic_assembly_pkg(
    name = "tasks-v2-nodejs",
    deps = [
        ":tasks_nodejs_gapic",
        ":tasks_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "tasks_ruby_proto",
    deps = [":tasks_proto"],
)

ruby_grpc_library(
    name = "tasks_ruby_grpc",
    srcs = [":tasks_proto"],
    deps = [":tasks_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "tasks_ruby_gapic",
    srcs = [":tasks_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=cloudtasks.googleapis.com",
        "ruby-cloud-api-shortname=cloudtasks",
        "ruby-cloud-env-prefix=TASKS",
        "ruby-cloud-gem-name=google-cloud-tasks-v2",
        "ruby-cloud-product-url=https://cloud.google.com/tasks",
    ],
    grpc_service_config = "cloudtasks_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Tasks is a fully managed service that allows you to manage the execution, dispatch and delivery of a large number of distributed tasks. You can asynchronously perform work outside of a user request. Your tasks can be executed on App Engine or any arbitrary HTTP endpoint.",
    ruby_cloud_title = "Cloud Tasks V2",
    service_yaml = "cloudtasks_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":tasks_ruby_grpc",
        ":tasks_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-tasks-v2-ruby",
    deps = [
        ":tasks_ruby_gapic",
        ":tasks_ruby_grpc",
        ":tasks_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "tasks_csharp_proto",
    extra_opts = [],
    deps = [":tasks_proto"],
)

csharp_grpc_library(
    name = "tasks_csharp_grpc",
    srcs = [":tasks_proto"],
    deps = [":tasks_csharp_proto"],
)

csharp_gapic_library(
    name = "tasks_csharp_gapic",
    srcs = [":tasks_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudtasks_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudtasks_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":tasks_csharp_grpc",
        ":tasks_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-tasks-v2-csharp",
    deps = [
        ":tasks_csharp_gapic",
        ":tasks_csharp_grpc",
        ":tasks_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "tasks_cc_proto",
    deps = [":tasks_proto"],
)

cc_grpc_library(
    name = "tasks_cc_grpc",
    srcs = [":tasks_proto"],
    grpc_only = True,
    deps = [":tasks_cc_proto"],
)
