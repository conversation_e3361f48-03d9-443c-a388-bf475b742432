# This build file includes a target for the Ruby wrapper library for
# google-cloud-recaptcha_enterprise.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for recaptchaenterprise.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "recaptchaenterprise_ruby_wrapper",
    srcs = ["//google/cloud/recaptchaenterprise/v1:recaptchaenterprise_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-recaptcha_enterprise",
        "ruby-cloud-env-prefix=RECAPTCHA_ENTERPRISE",
        "ruby-cloud-wrapper-of=v1:0.17;v1beta1:0.12",
        "ruby-cloud-product-url=https://cloud.google.com/recaptcha-enterprise",
        "ruby-cloud-api-id=recaptchaenterprise.googleapis.com",
        "ruby-cloud-api-shortname=recaptchaenterprise",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "reCAPTCHA Enterprise is a service that protects your site from spam and abuse.",
    ruby_cloud_title = "reCAPTCHA Enterprise",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-recaptchaenterprise-ruby",
    deps = [
        ":recaptchaenterprise_ruby_wrapper",
    ],
)
