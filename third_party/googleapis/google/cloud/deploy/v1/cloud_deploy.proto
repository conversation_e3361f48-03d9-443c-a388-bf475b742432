// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.deploy.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";

option go_package = "cloud.google.com/go/deploy/apiv1/deploypb;deploypb";
option java_multiple_files = true;
option java_outer_classname = "CloudDeployProto";
option java_package = "com.google.cloud.deploy.v1";
option (google.api.resource_definition) = {
  type: "cloudbuild.googleapis.com/Build"
  pattern: "projects/{project}/locations/{location}/builds/{build}"
};
option (google.api.resource_definition) = {
  type: "cloudbuild.googleapis.com/Repository"
  pattern: "projects/{project}/locations/{location}/connections/{connection}/repositories/{repository}"
};
option (google.api.resource_definition) = {
  type: "container.googleapis.com/Cluster"
  pattern: "projects/{project}/locations/{location}/clusters/{cluster}"
};
option (google.api.resource_definition) = {
  type: "clouddeploy.googleapis.com/Operation"
  pattern: "projects/{project}/locations/{location}/operations/{operation}"
};
option (google.api.resource_definition) = {
  type: "cloudbuild.googleapis.com/WorkerPool"
  pattern: "projects/{project}/locations/{location}/workerPools/{worker_pool}"
};
option (google.api.resource_definition) = {
  type: "gkehub.googleapis.com/Membership"
  pattern: "projects/{project}/locations/{location}/memberships/{membership}"
};
option (google.api.resource_definition) = {
  type: "run.googleapis.com/Service"
  pattern: "projects/{project}/locations/{location}/services/{service}"
};
option (google.api.resource_definition) = {
  type: "run.googleapis.com/Job"
  pattern: "projects/{project}/locations/{location}/jobs/{job}"
};

// CloudDeploy service creates and manages Continuous Delivery operations
// on Google Cloud Platform via Skaffold (https://skaffold.dev).
service CloudDeploy {
  option (google.api.default_host) = "clouddeploy.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists DeliveryPipelines in a given project and location.
  rpc ListDeliveryPipelines(ListDeliveryPipelinesRequest)
      returns (ListDeliveryPipelinesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/deliveryPipelines"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single DeliveryPipeline.
  rpc GetDeliveryPipeline(GetDeliveryPipelineRequest)
      returns (DeliveryPipeline) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deliveryPipelines/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new DeliveryPipeline in a given project and location.
  rpc CreateDeliveryPipeline(CreateDeliveryPipelineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/deliveryPipelines"
      body: "delivery_pipeline"
    };
    option (google.api.method_signature) =
        "parent,delivery_pipeline,delivery_pipeline_id";
    option (google.longrunning.operation_info) = {
      response_type: "DeliveryPipeline"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single DeliveryPipeline.
  rpc UpdateDeliveryPipeline(UpdateDeliveryPipelineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{delivery_pipeline.name=projects/*/locations/*/deliveryPipelines/*}"
      body: "delivery_pipeline"
    };
    option (google.api.method_signature) = "delivery_pipeline,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DeliveryPipeline"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single DeliveryPipeline.
  rpc DeleteDeliveryPipeline(DeleteDeliveryPipelineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/deliveryPipelines/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Targets in a given project and location.
  rpc ListTargets(ListTargetsRequest) returns (ListTargetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/targets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a `Rollout` to roll back the specified target.
  rpc RollbackTarget(RollbackTargetRequest) returns (RollbackTargetResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deliveryPipelines/*}:rollbackTarget"
      body: "*"
    };
    option (google.api.method_signature) = "name,target_id,rollout_id";
  }

  // Gets details of a single Target.
  rpc GetTarget(GetTargetRequest) returns (Target) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/targets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Target in a given project and location.
  rpc CreateTarget(CreateTargetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/targets"
      body: "target"
    };
    option (google.api.method_signature) = "parent,target,target_id";
    option (google.longrunning.operation_info) = {
      response_type: "Target"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Target.
  rpc UpdateTarget(UpdateTargetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{target.name=projects/*/locations/*/targets/*}"
      body: "target"
    };
    option (google.api.method_signature) = "target,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Target"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Target.
  rpc DeleteTarget(DeleteTargetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/targets/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists CustomTargetTypes in a given project and location.
  rpc ListCustomTargetTypes(ListCustomTargetTypesRequest)
      returns (ListCustomTargetTypesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/customTargetTypes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single CustomTargetType.
  rpc GetCustomTargetType(GetCustomTargetTypeRequest)
      returns (CustomTargetType) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/customTargetTypes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new CustomTargetType in a given project and location.
  rpc CreateCustomTargetType(CreateCustomTargetTypeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/customTargetTypes"
      body: "custom_target_type"
    };
    option (google.api.method_signature) =
        "parent,custom_target_type,custom_target_type_id";
    option (google.longrunning.operation_info) = {
      response_type: "CustomTargetType"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a single CustomTargetType.
  rpc UpdateCustomTargetType(UpdateCustomTargetTypeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{custom_target_type.name=projects/*/locations/*/customTargetTypes/*}"
      body: "custom_target_type"
    };
    option (google.api.method_signature) = "custom_target_type,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CustomTargetType"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single CustomTargetType.
  rpc DeleteCustomTargetType(DeleteCustomTargetTypeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/customTargetTypes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Releases in a given project and location.
  rpc ListReleases(ListReleasesRequest) returns (ListReleasesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/deliveryPipelines/*}/releases"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Release.
  rpc GetRelease(GetReleaseRequest) returns (Release) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/releases/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Release in a given project and location.
  rpc CreateRelease(CreateReleaseRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/deliveryPipelines/*}/releases"
      body: "release"
    };
    option (google.api.method_signature) = "parent,release,release_id";
    option (google.longrunning.operation_info) = {
      response_type: "Release"
      metadata_type: "OperationMetadata"
    };
  }

  // Abandons a Release in the Delivery Pipeline.
  rpc AbandonRelease(AbandonReleaseRequest) returns (AbandonReleaseResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/releases/*}:abandon"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new DeployPolicy in a given project and location.
  rpc CreateDeployPolicy(CreateDeployPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/deployPolicies"
      body: "deploy_policy"
    };
    option (google.api.method_signature) =
        "parent,deploy_policy,deploy_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "DeployPolicy"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single DeployPolicy.
  rpc UpdateDeployPolicy(UpdateDeployPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{deploy_policy.name=projects/*/locations/*/deployPolicies/*}"
      body: "deploy_policy"
    };
    option (google.api.method_signature) = "deploy_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DeployPolicy"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single DeployPolicy.
  rpc DeleteDeployPolicy(DeleteDeployPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/deployPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists DeployPolicies in a given project and location.
  rpc ListDeployPolicies(ListDeployPoliciesRequest)
      returns (ListDeployPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/deployPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single DeployPolicy.
  rpc GetDeployPolicy(GetDeployPolicyRequest) returns (DeployPolicy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deployPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Approves a Rollout.
  rpc ApproveRollout(ApproveRolloutRequest) returns (ApproveRolloutResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*}:approve"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Advances a Rollout in a given project and location.
  rpc AdvanceRollout(AdvanceRolloutRequest) returns (AdvanceRolloutResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*}:advance"
      body: "*"
    };
    option (google.api.method_signature) = "name,phase_id";
  }

  // Cancels a Rollout in a given project and location.
  rpc CancelRollout(CancelRolloutRequest) returns (CancelRolloutResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Rollouts in a given project and location.
  rpc ListRollouts(ListRolloutsRequest) returns (ListRolloutsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/deliveryPipelines/*/releases/*}/rollouts"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Rollout.
  rpc GetRollout(GetRolloutRequest) returns (Rollout) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Rollout in a given project and location.
  rpc CreateRollout(CreateRolloutRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/deliveryPipelines/*/releases/*}/rollouts"
      body: "rollout"
    };
    option (google.api.method_signature) = "parent,rollout,rollout_id";
    option (google.longrunning.operation_info) = {
      response_type: "Rollout"
      metadata_type: "OperationMetadata"
    };
  }

  // Ignores the specified Job in a Rollout.
  rpc IgnoreJob(IgnoreJobRequest) returns (IgnoreJobResponse) {
    option (google.api.http) = {
      post: "/v1/{rollout=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*}:ignoreJob"
      body: "*"
    };
    option (google.api.method_signature) = "rollout,phase_id,job_id";
  }

  // Retries the specified Job in a Rollout.
  rpc RetryJob(RetryJobRequest) returns (RetryJobResponse) {
    option (google.api.http) = {
      post: "/v1/{rollout=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*}:retryJob"
      body: "*"
    };
    option (google.api.method_signature) = "rollout,phase_id,job_id";
  }

  // Lists JobRuns in a given project and location.
  rpc ListJobRuns(ListJobRunsRequest) returns (ListJobRunsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*}/jobRuns"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single JobRun.
  rpc GetJobRun(GetJobRunRequest) returns (JobRun) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*/jobRuns/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Terminates a Job Run in a given project and location.
  rpc TerminateJobRun(TerminateJobRunRequest)
      returns (TerminateJobRunResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/releases/*/rollouts/*/jobRuns/*}:terminate"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the configuration for a location.
  rpc GetConfig(GetConfigRequest) returns (Config) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/config}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Automation in a given project and location.
  rpc CreateAutomation(CreateAutomationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/deliveryPipelines/*}/automations"
      body: "automation"
    };
    option (google.api.method_signature) = "parent,automation,automation_id";
    option (google.longrunning.operation_info) = {
      response_type: "Automation"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Automation resource.
  rpc UpdateAutomation(UpdateAutomationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{automation.name=projects/*/locations/*/deliveryPipelines/*/automations/*}"
      body: "automation"
    };
    option (google.api.method_signature) = "automation,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Automation"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Automation resource.
  rpc DeleteAutomation(DeleteAutomationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/automations/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single Automation.
  rpc GetAutomation(GetAutomationRequest) returns (Automation) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/automations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Automations in a given project and location.
  rpc ListAutomations(ListAutomationsRequest)
      returns (ListAutomationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/deliveryPipelines/*}/automations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single AutomationRun.
  rpc GetAutomationRun(GetAutomationRunRequest) returns (AutomationRun) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/automationRuns/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists AutomationRuns in a given project and location.
  rpc ListAutomationRuns(ListAutomationRunsRequest)
      returns (ListAutomationRunsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/deliveryPipelines/*}/automationRuns"
    };
    option (google.api.method_signature) = "parent";
  }

  // Cancels an AutomationRun. The `state` of the `AutomationRun` after
  // cancelling is `CANCELLED`. `CancelAutomationRun` can be called on
  // AutomationRun in the state `IN_PROGRESS` and `PENDING`; AutomationRun
  // in a different state returns an `FAILED_PRECONDITION` error.
  rpc CancelAutomationRun(CancelAutomationRunRequest)
      returns (CancelAutomationRunResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/deliveryPipelines/*/automationRuns/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// A `DeliveryPipeline` resource in the Cloud Deploy API.
//
// A `DeliveryPipeline` defines a pipeline through which a Skaffold
// configuration can progress.
message DeliveryPipeline {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/DeliveryPipeline"
    pattern: "projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}"
  };

  // Optional. Name of the `DeliveryPipeline`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}`.
  // The `deliveryPipeline` component must match
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Unique identifier of the `DeliveryPipeline`.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Description of the `DeliveryPipeline`. Max length is 255 characters.
  string description = 3;

  // User annotations. These attributes can only be set and used by the
  // user, and not by Cloud Deploy.
  map<string, string> annotations = 4;

  // Labels are attributes that can be set and used by both the
  // user and by Cloud Deploy. Labels must meet the following constraints:
  //
  // * Keys and values can contain only lowercase letters, numeric characters,
  // underscores, and dashes.
  // * All characters must use UTF-8 encoding, and international characters are
  // allowed.
  // * Keys must start with a lowercase letter or international character.
  // * Each resource is limited to a maximum of 64 labels.
  //
  // Both keys and values are additionally constrained to be <= 128 bytes.
  map<string, string> labels = 5;

  // Output only. Time at which the pipeline was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Most recent time at which the pipeline was updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The ordering configuration of the `DeliveryPipeline`.
  oneof pipeline {
    // SerialPipeline defines a sequential set of stages for a
    // `DeliveryPipeline`.
    SerialPipeline serial_pipeline = 8;
  }

  // Output only. Information around the state of the Delivery Pipeline.
  PipelineCondition condition = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 10;

  // When suspended, no new releases or rollouts can be created,
  // but in-progress ones will complete.
  bool suspended = 12;
}

// SerialPipeline defines a sequential set of stages for a `DeliveryPipeline`.
message SerialPipeline {
  // Each stage specifies configuration for a `Target`. The ordering
  // of this list defines the promotion flow.
  repeated Stage stages = 1;
}

// Stage specifies a location to which to deploy.
message Stage {
  // The target_id to which this stage points. This field refers exclusively to
  // the last segment of a target name. For example, this field would just be
  // `my-target` (rather than
  // `projects/project/locations/location/targets/my-target`). The location of
  // the `Target` is inferred to be the same as the location of the
  // `DeliveryPipeline` that contains this `Stage`.
  string target_id = 1;

  // Skaffold profiles to use when rendering the manifest for this stage's
  // `Target`.
  repeated string profiles = 2;

  // Optional. The strategy to use for a `Rollout` to this stage.
  Strategy strategy = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The deploy parameters to use for the target in this stage.
  repeated DeployParameters deploy_parameters = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// DeployParameters contains deploy parameters information.
message DeployParameters {
  // Required. Values are deploy parameters in key-value pairs.
  map<string, string> values = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Deploy parameters are applied to targets with match labels.
  // If unspecified, deploy parameters are applied to all targets (including
  // child targets of a multi-target).
  map<string, string> match_target_labels = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Strategy contains deployment strategy information.
message Strategy {
  // Deployment strategy details.
  oneof deployment_strategy {
    // Standard deployment strategy executes a single deploy and allows
    // verifying the deployment.
    Standard standard = 1;

    // Canary deployment strategy provides progressive percentage based
    // deployments to a Target.
    Canary canary = 2;
  }
}

// Predeploy contains the predeploy job configuration information.
message Predeploy {
  // Optional. A sequence of Skaffold custom actions to invoke during execution
  // of the predeploy job.
  repeated string actions = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Postdeploy contains the postdeploy job configuration information.
message Postdeploy {
  // Optional. A sequence of Skaffold custom actions to invoke during execution
  // of the postdeploy job.
  repeated string actions = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Standard represents the standard deployment strategy.
message Standard {
  // Whether to verify a deployment.
  bool verify = 1;

  // Optional. Configuration for the predeploy job. If this is not configured,
  // predeploy job will not be present.
  Predeploy predeploy = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration for the postdeploy job. If this is not configured,
  // postdeploy job will not be present.
  Postdeploy postdeploy = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Canary represents the canary deployment strategy.
message Canary {
  // Optional. Runtime specific configurations for the deployment strategy. The
  // runtime configuration is used to determine how Cloud Deploy will split
  // traffic to enable a progressive deployment.
  RuntimeConfig runtime_config = 1 [(google.api.field_behavior) = OPTIONAL];

  // The mode to use for the canary deployment strategy.
  oneof mode {
    // Configures the progressive based deployment for a Target.
    CanaryDeployment canary_deployment = 2;

    // Configures the progressive based deployment for a Target, but allows
    // customizing at the phase level where a phase represents each of the
    // percentage deployments.
    CustomCanaryDeployment custom_canary_deployment = 3;
  }
}

// CanaryDeployment represents the canary deployment configuration
message CanaryDeployment {
  // Required. The percentage based deployments that will occur as a part of a
  // `Rollout`. List is expected in ascending order and each integer n is
  // 0 <= n < 100.
  // If the GatewayServiceMesh is configured for Kubernetes, then the range for
  // n is 0 <= n <= 100.
  repeated int32 percentages = 1 [(google.api.field_behavior) = REQUIRED];

  // Whether to run verify tests after each percentage deployment.
  bool verify = 2;

  // Optional. Configuration for the predeploy job of the first phase. If this
  // is not configured, there will be no predeploy job for this phase.
  Predeploy predeploy = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration for the postdeploy job of the last phase. If this
  // is not configured, there will be no postdeploy job for this phase.
  Postdeploy postdeploy = 4 [(google.api.field_behavior) = OPTIONAL];
}

// CustomCanaryDeployment represents the custom canary deployment
// configuration.
message CustomCanaryDeployment {
  // PhaseConfig represents the configuration for a phase in the custom
  // canary deployment.
  message PhaseConfig {
    // Required. The ID to assign to the `Rollout` phase.
    // This value must consist of lower-case letters, numbers, and hyphens,
    // start with a letter and end with a letter or a number, and have a max
    // length of 63 characters. In other words, it must match the following
    // regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    string phase_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Percentage deployment for the phase.
    int32 percentage = 2 [(google.api.field_behavior) = REQUIRED];

    // Skaffold profiles to use when rendering the manifest for this phase.
    // These are in addition to the profiles list specified in the
    // `DeliveryPipeline` stage.
    repeated string profiles = 3;

    // Whether to run verify tests after the deployment.
    bool verify = 4;

    // Optional. Configuration for the predeploy job of this phase. If this is
    // not configured, there will be no predeploy job for this phase.
    Predeploy predeploy = 5 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Configuration for the postdeploy job of this phase. If this is
    // not configured, there will be no postdeploy job for this phase.
    Postdeploy postdeploy = 6 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Configuration for each phase in the canary deployment in the
  // order executed.
  repeated PhaseConfig phase_configs = 1
      [(google.api.field_behavior) = REQUIRED];
}

// KubernetesConfig contains the Kubernetes runtime configuration.
message KubernetesConfig {
  // Information about the Kubernetes Gateway API service mesh configuration.
  message GatewayServiceMesh {
    // Information about route destinations for the Gateway API service mesh.
    message RouteDestinations {
      // Required. The clusters where the Gateway API HTTPRoute resource will be
      // deployed to. Valid entries include the associated entities IDs
      // configured in the Target resource and "@self" to include the Target
      // cluster.
      repeated string destination_ids = 1
          [(google.api.field_behavior) = REQUIRED];

      // Optional. Whether to propagate the Kubernetes Service to the route
      // destination clusters. The Service will always be deployed to the Target
      // cluster even if the HTTPRoute is not. This option may be used to
      // facilitiate successful DNS lookup in the route destination clusters.
      // Can only be set to true if destinations are specified.
      bool propagate_service = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // Required. Name of the Gateway API HTTPRoute.
    string http_route = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Name of the Kubernetes Service.
    string service = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Name of the Kubernetes Deployment whose traffic is managed by
    // the specified HTTPRoute and Service.
    string deployment = 3 [(google.api.field_behavior) = REQUIRED];

    // Optional. The time to wait for route updates to propagate. The maximum
    // configurable time is 3 hours, in seconds format. If unspecified, there is
    // no wait time.
    google.protobuf.Duration route_update_wait_time = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The amount of time to migrate traffic back from the canary
    // Service to the original Service during the stable phase deployment. If
    // specified, must be between 15s and 3600s. If unspecified, there is no
    // cutback time.
    google.protobuf.Duration stable_cutback_duration = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The label to use when selecting Pods for the Deployment and
    // Service resources. This label must already be present in both resources.
    string pod_selector_label = 6 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Route destinations allow configuring the Gateway API HTTPRoute
    // to be deployed to additional clusters. This option is available for
    // multi-cluster service mesh set ups that require the route to exist in the
    // clusters that call the service. If unspecified, the HTTPRoute will only
    // be deployed to the Target cluster.
    RouteDestinations route_destinations = 8
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Information about the Kubernetes Service networking configuration.
  message ServiceNetworking {
    // Required. Name of the Kubernetes Service.
    string service = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Name of the Kubernetes Deployment whose traffic is managed by
    // the specified Service.
    string deployment = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. Whether to disable Pod overprovisioning. If Pod
    // overprovisioning is disabled then Cloud Deploy will limit the number of
    // total Pods used for the deployment strategy to the number of Pods the
    // Deployment has on the cluster.
    bool disable_pod_overprovisioning = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The label to use when selecting Pods for the Deployment
    // resource. This label must already be present in the Deployment.
    string pod_selector_label = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // The service definition configuration.
  oneof service_definition {
    // Kubernetes Gateway API service mesh configuration.
    GatewayServiceMesh gateway_service_mesh = 1;

    // Kubernetes Service networking configuration.
    ServiceNetworking service_networking = 2;
  }
}

// CloudRunConfig contains the Cloud Run runtime configuration.
message CloudRunConfig {
  // Whether Cloud Deploy should update the traffic stanza in a Cloud Run
  // Service on the user's behalf to facilitate traffic splitting. This is
  // required to be true for CanaryDeployments, but optional for
  // CustomCanaryDeployments.
  bool automatic_traffic_control = 1;

  // Optional. A list of tags that are added to the canary revision while the
  // canary phase is in progress.
  repeated string canary_revision_tags = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of tags that are added to the prior revision while the
  // canary phase is in progress.
  repeated string prior_revision_tags = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of tags that are added to the final stable revision when
  // the stable phase is applied.
  repeated string stable_revision_tags = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// RuntimeConfig contains the runtime specific configurations for a deployment
// strategy.
message RuntimeConfig {
  // The runtime configuration details.
  oneof runtime_config {
    // Kubernetes runtime configuration.
    KubernetesConfig kubernetes = 1;

    // Cloud Run runtime configuration.
    CloudRunConfig cloud_run = 2;
  }
}

// PipelineReadyCondition contains information around the status of the
// Pipeline.
message PipelineReadyCondition {
  // True if the Pipeline is in a valid state. Otherwise at least one condition
  // in `PipelineCondition` is in an invalid state. Iterate over those
  // conditions and see which condition(s) has status = false to find out what
  // is wrong with the Pipeline.
  bool status = 3;

  // Last time the condition was updated.
  google.protobuf.Timestamp update_time = 4;
}

// `TargetsPresentCondition` contains information on any Targets referenced in
// the Delivery Pipeline that do not actually exist.
message TargetsPresentCondition {
  // True if there aren't any missing Targets.
  bool status = 1;

  // The list of Target names that do not exist. For example,
  // `projects/{project_id}/locations/{location_name}/targets/{target_name}`.
  repeated string missing_targets = 2 [(google.api.resource_reference) = {
    type: "clouddeploy.googleapis.com/Target"
  }];

  // Last time the condition was updated.
  google.protobuf.Timestamp update_time = 4;
}

// TargetsTypeCondition contains information on whether the Targets defined in
// the Delivery Pipeline are of the same type.
message TargetsTypeCondition {
  // True if the targets are all a comparable type. For example this is true if
  // all targets are GKE clusters. This is false if some targets are Cloud Run
  // targets and others are GKE clusters.
  bool status = 1;

  // Human readable error message.
  string error_details = 2;
}

// PipelineCondition contains all conditions relevant to a Delivery Pipeline.
message PipelineCondition {
  // Details around the Pipeline's overall status.
  PipelineReadyCondition pipeline_ready_condition = 1;

  // Details around targets enumerated in the pipeline.
  TargetsPresentCondition targets_present_condition = 3;

  // Details on the whether the targets enumerated in the pipeline are of the
  // same type.
  TargetsTypeCondition targets_type_condition = 4;
}

// The request object for `ListDeliveryPipelines`.
message ListDeliveryPipelinesRequest {
  // Required. The parent, which owns this collection of pipelines. Format must
  // be `projects/{project_id}/locations/{location_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/DeliveryPipeline"
    }
  ];

  // The maximum number of pipelines to return. The service may return
  // fewer than this value. If unspecified, at most 50 pipelines will
  // be returned. The maximum value is 1000; values above 1000 will be set
  // to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListDeliveryPipelines` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match
  // the call that provided the page token.
  string page_token = 3;

  // Filter pipelines to be returned. See https://google.aip.dev/160 for more
  // details.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// The response object from `ListDeliveryPipelines`.
message ListDeliveryPipelinesResponse {
  // The `DeliveryPipeline` objects.
  repeated DeliveryPipeline delivery_pipelines = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// The request object for `GetDeliveryPipeline`
message GetDeliveryPipelineRequest {
  // Required. Name of the `DeliveryPipeline`. Format must be
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeliveryPipeline"
    }
  ];
}

// The request object for `CreateDeliveryPipeline`.
message CreateDeliveryPipelineRequest {
  // Required. The parent collection in which the `DeliveryPipeline` must be
  // created. The format is `projects/{project_id}/locations/{location_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/DeliveryPipeline"
    }
  ];

  // Required. ID of the `DeliveryPipeline`.
  string delivery_pipeline_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The `DeliveryPipeline` to create.
  DeliveryPipeline delivery_pipeline = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `UpdateDeliveryPipeline`.
message UpdateDeliveryPipelineRequest {
  // Required. Field mask is used to specify the fields to be overwritten by the
  // update in the `DeliveryPipeline` resource. The fields specified in the
  // update_mask are relative to the resource, not the full request. A field
  // will be overwritten if it's in the mask. If the user doesn't provide a mask
  // then all fields are overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The `DeliveryPipeline` to update.
  DeliveryPipeline delivery_pipeline = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, updating a `DeliveryPipeline` that does not exist
  // will result in the creation of a new `DeliveryPipeline`.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `DeleteDeliveryPipeline`.
message DeleteDeliveryPipelineRequest {
  // Required. The name of the `DeliveryPipeline` to delete. The format is
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeliveryPipeline"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, then deleting an already deleted or non-existing
  // `DeliveryPipeline` will succeed.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // actually post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, all child resources under this pipeline will also
  // be deleted. Otherwise, the request will only work if the pipeline has no
  // child resources.
  bool force = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Configs for the Rollback rollout.
message RollbackTargetConfig {
  // Optional. The rollback `Rollout` to create.
  Rollout rollout = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The starting phase ID for the `Rollout`. If unspecified, the
  // `Rollout` will start in the stable phase.
  string starting_phase_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `RollbackTarget`.
message RollbackTargetRequest {
  // Required. The `DeliveryPipeline` for which the rollback `Rollout` must be
  // created. The format is
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeliveryPipeline"
    }
  ];

  // Required. ID of the `Target` that is being rolled back.
  string target_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the rollback `Rollout` to create.
  string rollout_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. ID of the `Release` to roll back to. If this isn't specified, the
  // previous successful `Rollout` to the specified target will be used to
  // determine the `Release`.
  string release_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If provided, this must be the latest `Rollout` that is on the
  // `Target`.
  string rollout_to_roll_back = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configs for the rollback `Rollout`.
  RollbackTargetConfig rollback_config = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with a `RollbackTargetResponse`.
  bool validate_only = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deploy_policy}`.
  repeated string override_deploy_policy = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// The response object from `RollbackTarget`.
message RollbackTargetResponse {
  // The config of the rollback `Rollout` created or will be created.
  RollbackTargetConfig rollback_config = 1;
}

// A `Target` resource in the Cloud Deploy API.
//
// A `Target` defines a location to which a Skaffold configuration
// can be deployed.
message Target {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/Target"
    pattern: "projects/{project}/locations/{location}/targets/{target}"
  };

  // Optional. Name of the `Target`. Format is
  // `projects/{project}/locations/{location}/targets/{target}`.
  // The `target` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Resource id of the `Target`.
  string target_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unique identifier of the `Target`.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description of the `Target`. Max length is 255 characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User annotations. These attributes can only be set and used by
  // the user, and not by Cloud Deploy. See
  // https://google.aip.dev/128#annotations for more details such as format and
  // size limitations.
  map<string, string> annotations = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels are attributes that can be set and used by both the
  // user and by Cloud Deploy. Labels must meet the following constraints:
  //
  // * Keys and values can contain only lowercase letters, numeric characters,
  // underscores, and dashes.
  // * All characters must use UTF-8 encoding, and international characters are
  // allowed.
  // * Keys must start with a lowercase letter or international character.
  // * Each resource is limited to a maximum of 64 labels.
  //
  // Both keys and values are additionally constrained to be <= 128 bytes.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether or not the `Target` requires approval.
  bool require_approval = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time at which the `Target` was created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Most recent time at which the `Target` was updated.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Destination to which the Skaffold configuration is applied during a
  // rollout.
  oneof deployment_target {
    // Optional. Information specifying a GKE Cluster.
    GkeCluster gke = 15 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Information specifying an Anthos Cluster.
    AnthosCluster anthos_cluster = 17 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Information specifying a Cloud Run deployment target.
    CloudRunLocation run = 18 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Information specifying a multiTarget.
    MultiTarget multi_target = 19 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Information specifying a Custom Target.
    CustomTarget custom_target = 21 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. Map of entity IDs to their associated entities. Associated
  // entities allows specifying places other than the deployment target for
  // specific features. For example, the Gateway API canary can be configured to
  // deploy the HTTPRoute to a different cluster(s) than the deployment cluster
  // using associated entities. An entity ID must consist of lower-case letters,
  // numbers, and hyphens, start with a letter and end with a letter or a
  // number, and have a max length of 63 characters. In other words, it must
  // match the following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  map<string, AssociatedEntities> associated_entities = 23
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 12 [(google.api.field_behavior) = OPTIONAL];

  // Configurations for all execution that relates to this `Target`.
  // Each `ExecutionEnvironmentUsage` value may only be used in a single
  // configuration; using the same value multiple times is an error.
  // When one or more configurations are specified, they must include the
  // `RENDER` and `DEPLOY` `ExecutionEnvironmentUsage` values.
  // When no configurations are specified, execution will use the default
  // specified in `DefaultPool`.
  repeated ExecutionConfig execution_configs = 16;

  // Optional. The deploy parameters to use for this target.
  map<string, string> deploy_parameters = 20
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration of the environment to use when calling Skaffold.
message ExecutionConfig {
  // Possible usages of this configuration.
  enum ExecutionEnvironmentUsage {
    // Default value. This value is unused.
    EXECUTION_ENVIRONMENT_USAGE_UNSPECIFIED = 0;

    // Use for rendering.
    RENDER = 1;

    // Use for deploying and deployment hooks.
    DEPLOY = 2;

    // Use for deployment verification.
    VERIFY = 3;

    // Use for predeploy job execution.
    PREDEPLOY = 4;

    // Use for postdeploy job execution.
    POSTDEPLOY = 5;
  }

  // Required. Usages when this configuration should be applied.
  repeated ExecutionEnvironmentUsage usages = 1
      [(google.api.field_behavior) = REQUIRED];

  // Details of the environment.
  oneof execution_environment {
    // Optional. Use default Cloud Build pool.
    DefaultPool default_pool = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Use private Cloud Build pool.
    PrivatePool private_pool = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The resource name of the `WorkerPool`, with the format
  // `projects/{project}/locations/{location}/workerPools/{worker_pool}`.
  // If this optional field is unspecified, the default Cloud Build pool will be
  // used.
  string worker_pool = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }
  ];

  // Optional. Google service account to use for execution. If unspecified,
  // the project execution service account
  // (<PROJECT_NUMBER>-<EMAIL>) is used.
  string service_account = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Cloud Storage location in which to store execution outputs. This
  // can either be a bucket ("gs://my-bucket") or a path within a bucket
  // ("gs://my-bucket/my-dir").
  // If unspecified, a default bucket located in the same region will be used.
  string artifact_storage = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Execution timeout for a Cloud Build Execution. This must be
  // between 10m and 24h in seconds format. If unspecified, a default timeout of
  // 1h is used.
  google.protobuf.Duration execution_timeout = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, additional logging will be enabled when running builds
  // in this execution environment.
  bool verbose = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Execution using the default Cloud Build pool.
message DefaultPool {
  // Optional. Google service account to use for execution. If unspecified,
  // the project execution service account
  // (<PROJECT_NUMBER>-<EMAIL>) will be used.
  string service_account = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Cloud Storage location where execution outputs should be stored.
  // This can either be a bucket ("gs://my-bucket") or a path within a bucket
  // ("gs://my-bucket/my-dir").
  // If unspecified, a default bucket located in the same region will be used.
  string artifact_storage = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Execution using a private Cloud Build pool.
message PrivatePool {
  // Required. Resource name of the Cloud Build worker pool to use. The format
  // is `projects/{project}/locations/{location}/workerPools/{pool}`.
  string worker_pool = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }
  ];

  // Optional. Google service account to use for execution. If unspecified,
  // the project execution service account
  // (<PROJECT_NUMBER>-<EMAIL>) will be used.
  string service_account = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Cloud Storage location where execution outputs should be stored.
  // This can either be a bucket ("gs://my-bucket") or a path within a bucket
  // ("gs://my-bucket/my-dir").
  // If unspecified, a default bucket located in the same region will be used.
  string artifact_storage = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Information specifying a GKE Cluster.
message GkeCluster {
  // Optional. Information specifying a GKE Cluster. Format is
  // `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`.
  string cluster = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "container.googleapis.com/Cluster"
    }
  ];

  // Optional. If true, `cluster` is accessed using the private IP address of
  // the control plane endpoint. Otherwise, the default IP address of the
  // control plane endpoint is used. The default IP address is the private IP
  // address for clusters with private control-plane endpoints and the public IP
  // address otherwise.
  //
  // Only specify this option when `cluster` is a [private GKE
  // cluster](https://cloud.google.com/kubernetes-engine/docs/concepts/private-cluster-concept).
  // Note that `internal_ip` and `dns_endpoint` cannot both be set to true.
  bool internal_ip = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, used to configure a
  // [proxy](https://kubernetes.io/docs/concepts/configuration/organize-cluster-access-kubeconfig/#proxy)
  // to the Kubernetes server.
  string proxy_url = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the cluster will be accessed using the DNS endpoint. Note
  // that `dns_endpoint` and `internal_ip` cannot both be set to true.
  bool dns_endpoint = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Information specifying an Anthos Cluster.
message AnthosCluster {
  // Optional. Membership of the GKE Hub-registered cluster to which to apply
  // the Skaffold configuration. Format is
  // `projects/{project}/locations/{location}/memberships/{membership_name}`.
  string membership = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];
}

// Information specifying where to deploy a Cloud Run Service.
message CloudRunLocation {
  // Required. The location for the Cloud Run Service. Format must be
  // `projects/{project}/locations/{location}`.
  string location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Information specifying a multiTarget.
message MultiTarget {
  // Required. The target_ids of this multiTarget.
  repeated string target_ids = 1 [(google.api.field_behavior) = REQUIRED];
}

// Information specifying a Custom Target.
message CustomTarget {
  // Required. The name of the CustomTargetType. Format must be
  // `projects/{project}/locations/{location}/customTargetTypes/{custom_target_type}`.
  string custom_target_type = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/CustomTargetType"
    }
  ];
}

// Information about entities associated with a `Target`.
message AssociatedEntities {
  // Optional. Information specifying GKE clusters as associated entities.
  repeated GkeCluster gke_clusters = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Information specifying Anthos clusters as associated entities.
  repeated AnthosCluster anthos_clusters = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `ListTargets`.
message ListTargetsRequest {
  // Required. The parent, which owns this collection of targets. Format must be
  // `projects/{project_id}/locations/{location_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/Target"
    }
  ];

  // Optional. The maximum number of `Target` objects to return. The service may
  // return fewer than this value. If unspecified, at most 50 `Target` objects
  // will be returned. The maximum value is 1000; values above 1000 will be set
  // to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListTargets` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match
  // the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter targets to be returned. See https://google.aip.dev/160 for
  // more details.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field to sort by. See https://google.aip.dev/132#ordering for
  // more details.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response object from `ListTargets`.
message ListTargetsResponse {
  // The `Target` objects.
  repeated Target targets = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// The request object for `GetTarget`.
message GetTargetRequest {
  // Required. Name of the `Target`. Format must be
  // `projects/{project_id}/locations/{location_name}/targets/{target_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Target"
    }
  ];
}

// The request object for `CreateTarget`.
message CreateTargetRequest {
  // Required. The parent collection in which the `Target` must be created.
  // The format is
  // `projects/{project_id}/locations/{location_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/Target"
    }
  ];

  // Required. ID of the `Target`.
  string target_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The `Target` to create.
  Target target = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `UpdateTarget`.
message UpdateTargetRequest {
  // Required. Field mask is used to specify the fields to be overwritten by the
  // update in the `Target` resource. The fields specified in the update_mask
  // are relative to the resource, not the full request. A field will be
  // overwritten if it's in the mask. If the user doesn't provide a mask then
  // all fields are overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The `Target` to update.
  Target target = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, updating a `Target` that does not exist will
  // result in the creation of a new `Target`.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `DeleteTarget`.
message DeleteTargetRequest {
  // Required. The name of the `Target` to delete. The format is
  // `projects/{project_id}/locations/{location_name}/targets/{target_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Target"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, then deleting an already deleted or non-existing
  // `Target` will succeed.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // actually post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A `CustomTargetType` resource in the Cloud Deploy API.
//
// A `CustomTargetType` defines a type of custom target that can be referenced
// in a `Target` in order to facilitate deploying to other systems besides the
// supported runtimes.
message CustomTargetType {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/CustomTargetType"
    pattern: "projects/{project}/locations/{location}/customTargetTypes/{custom_target_type}"
  };

  // Optional. Name of the `CustomTargetType`. Format is
  // `projects/{project}/locations/{location}/customTargetTypes/{customTargetType}`.
  // The `customTargetType` component must match
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Resource id of the `CustomTargetType`.
  string custom_target_type_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unique identifier of the `CustomTargetType`.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description of the `CustomTargetType`. Max length is 255
  // characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User annotations. These attributes can only be set and used by
  // the user, and not by Cloud Deploy. See
  // https://google.aip.dev/128#annotations for more details such as format and
  // size limitations.
  map<string, string> annotations = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels are attributes that can be set and used by both the
  // user and by Cloud Deploy. Labels must meet the following constraints:
  //
  // * Keys and values can contain only lowercase letters, numeric characters,
  // underscores, and dashes.
  // * All characters must use UTF-8 encoding, and international characters are
  // allowed.
  // * Keys must start with a lowercase letter or international character.
  // * Each resource is limited to a maximum of 64 labels.
  //
  // Both keys and values are additionally constrained to be <= 128 bytes.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time at which the `CustomTargetType` was created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Most recent time at which the `CustomTargetType` was updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 9 [(google.api.field_behavior) = OPTIONAL];

  // Defines the `CustomTargetType` renderer and deployer.
  oneof definition {
    // Configures render and deploy for the `CustomTargetType` using Skaffold
    // custom actions.
    CustomTargetSkaffoldActions custom_actions = 10;
  }
}

// CustomTargetSkaffoldActions represents the `CustomTargetType` configuration
// using Skaffold custom actions.
message CustomTargetSkaffoldActions {
  // Optional. The Skaffold custom action responsible for render operations. If
  // not provided then Cloud Deploy will perform the render operations via
  // `skaffold render`.
  string render_action = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The Skaffold custom action responsible for deploy operations.
  string deploy_action = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. List of Skaffold modules Cloud Deploy will include in the
  // Skaffold Config as required before performing diagnose.
  repeated SkaffoldModules include_skaffold_modules = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Skaffold Config modules and their remote source.
message SkaffoldModules {
  // Git repository containing Skaffold Config modules.
  message SkaffoldGitSource {
    // Required. Git repository the package should be cloned from.
    string repo = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Relative path from the repository root to the Skaffold file.
    string path = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Git branch or tag to use when cloning the repository.
    string ref = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Cloud Storage bucket containing Skaffold Config modules.
  message SkaffoldGCSSource {
    // Required. Cloud Storage source paths to copy recursively. For example,
    // providing "gs://my-bucket/dir/configs/*" will result in Skaffold copying
    // all files within the "dir/configs" directory in the bucket "my-bucket".
    string source = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Relative path from the source to the Skaffold file.
    string path = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Cloud Build V2 Repository containing Skaffold Configs.
  message SkaffoldGCBRepoSource {
    // Required. Name of the Cloud Build V2 Repository.
    // Format is
    // projects/{project}/locations/{location}/connections/{connection}/repositories/{repository}.
    string repository = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "cloudbuild.googleapis.com/Repository"
      }
    ];

    // Optional. Relative path from the repository root to the Skaffold Config
    // file.
    string path = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Branch or tag to use when cloning the repository.
    string ref = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The Skaffold Config modules to use from the specified source.
  repeated string configs = 1 [(google.api.field_behavior) = OPTIONAL];

  // The source that contains the Skaffold Config modules.
  oneof source {
    // Remote git repository containing the Skaffold Config modules.
    SkaffoldGitSource git = 2;

    // Cloud Storage bucket containing the Skaffold Config modules.
    SkaffoldGCSSource google_cloud_storage = 3;

    // Cloud Build V2 repository containing the Skaffold Config modules.
    SkaffoldGCBRepoSource google_cloud_build_repo = 4;
  }
}

// The request object for `ListCustomTargetTypes`.
message ListCustomTargetTypesRequest {
  // Required. The parent that owns this collection of custom target types.
  // Format must be `projects/{project_id}/locations/{location_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/CustomTargetType"
    }
  ];

  // Optional. The maximum number of `CustomTargetType` objects to return. The
  // service may return fewer than this value. If unspecified, at most 50
  // `CustomTargetType` objects will be returned. The maximum value is 1000;
  // values above 1000 will be set to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListCustomTargetTypes`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match
  // the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter custom target types to be returned. See
  // https://google.aip.dev/160 for more details.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field to sort by. See https://google.aip.dev/132#ordering for
  // more details.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response object from `ListCustomTargetTypes.`
message ListCustomTargetTypesResponse {
  // The `CustomTargetType` objects.
  repeated CustomTargetType custom_target_types = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// The request object for `GetCustomTargetType`.
message GetCustomTargetTypeRequest {
  // Required. Name of the `CustomTargetType`. Format must be
  // `projects/{project_id}/locations/{location_name}/customTargetTypes/{custom_target_type}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/CustomTargetType"
    }
  ];
}

// The request object for `CreateCustomTargetType`.
message CreateCustomTargetTypeRequest {
  // Required. The parent collection in which the `CustomTargetType` must be
  // created. The format is `projects/{project_id}/locations/{location_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/CustomTargetType"
    }
  ];

  // Required. ID of the `CustomTargetType`.
  string custom_target_type_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The `CustomTargetType` to create.
  CustomTargetType custom_target_type = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `UpdateCustomTargetType`.
message UpdateCustomTargetTypeRequest {
  // Required. Field mask is used to specify the fields to be overwritten by the
  // update in the `CustomTargetType` resource. The fields specified in the
  // update_mask are relative to the resource, not the full request. A field
  // will be overwritten if it's in the mask. If the user doesn't provide a mask
  // then all fields are overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The `CustomTargetType` to update.
  CustomTargetType custom_target_type = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, updating a `CustomTargetType` that does not exist
  // will result in the creation of a new `CustomTargetType`.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `DeleteCustomTargetType`.
message DeleteCustomTargetTypeRequest {
  // Required. The name of the `CustomTargetType` to delete. Format must be
  // `projects/{project_id}/locations/{location_name}/customTargetTypes/{custom_target_type}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/CustomTargetType"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, then deleting an already deleted or non-existing
  // `CustomTargetType` will succeed.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated but no actual change is
  // made.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A `DeployPolicy` resource in the Cloud Deploy API.
//
// A `DeployPolicy` inhibits manual or automation-driven actions within a
// Delivery Pipeline or Target.
message DeployPolicy {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/DeployPolicy"
    pattern: "projects/{project}/locations/{location}/deployPolicies/{deploy_policy}"
    style: DECLARATIVE_FRIENDLY
  };

  // What invoked the action. Filters enforcing the policy depending on what
  // invoked the action.
  enum Invoker {
    // Unspecified.
    INVOKER_UNSPECIFIED = 0;

    // The action is user-driven. For example, creating a rollout manually via a
    // gcloud create command.
    USER = 1;

    // Automated action by Cloud Deploy.
    DEPLOY_AUTOMATION = 2;
  }

  // Output only. Name of the `DeployPolicy`. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  // The `deployPolicy` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unique identifier of the `DeployPolicy`.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Description of the `DeployPolicy`. Max length is 255 characters.
  string description = 3;

  // User annotations. These attributes can only be set and used by the
  // user, and not by Cloud Deploy. Annotations must meet the following
  // constraints:
  //
  // * Annotations are key/value pairs.
  // * Valid annotation keys have two segments: an optional prefix and name,
  // separated by a slash (`/`).
  // * The name segment is required and must be 63 characters or less,
  // beginning and ending with an alphanumeric character (`[a-z0-9A-Z]`) with
  // dashes (`-`), underscores (`_`), dots (`.`), and alphanumerics between.
  // * The prefix is optional. If specified, the prefix must be a DNS subdomain:
  // a series of DNS labels separated by dots(`.`), not longer than 253
  // characters in total, followed by a slash (`/`).
  //
  // See
  // https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/#syntax-and-character-set
  // for more details.
  map<string, string> annotations = 4;

  // Labels are attributes that can be set and used by both the
  // user and by Cloud Deploy. Labels must meet the following constraints:
  //
  // * Keys and values can contain only lowercase letters, numeric characters,
  // underscores, and dashes.
  // * All characters must use UTF-8 encoding, and international characters are
  // allowed.
  // * Keys must start with a lowercase letter or international character.
  // * Each resource is limited to a maximum of 64 labels.
  //
  // Both keys and values are additionally constrained to be <= 128 bytes.
  map<string, string> labels = 5;

  // Output only. Time at which the deploy policy was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Most recent time at which the deploy policy was updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // When suspended, the policy will not prevent actions from occurring, even
  // if the action violates the policy.
  bool suspended = 8;

  // Required. Selected resources to which the policy will be applied. At least
  // one selector is required. If one selector matches the resource the policy
  // applies. For example, if there are two selectors and the action being
  // attempted matches one of them, the policy will apply to that action.
  repeated DeployPolicyResourceSelector selectors = 12
      [(google.api.field_behavior) = REQUIRED];

  // Required. Rules to apply. At least one rule must be present.
  repeated PolicyRule rules = 10 [(google.api.field_behavior) = REQUIRED];

  // The weak etag of the `Automation` resource.
  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 11;
}

// Contains information on the resources to select for a deploy policy.
// Attributes provided must all match the resource in order for policy
// restrictions to apply. For example, if delivery pipelines attributes given
// are an id "prod" and labels "foo: bar", a delivery pipeline resource must
// match both that id and have that label in order to be subject to the policy.
message DeployPolicyResourceSelector {
  // Optional. Contains attributes about a delivery pipeline.
  DeliveryPipelineAttribute delivery_pipeline = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Contains attributes about a target.
  TargetAttribute target = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Contains criteria for selecting DeliveryPipelines.
message DeliveryPipelineAttribute {
  // ID of the `DeliveryPipeline`. The value of this field could be one of the
  // following:
  //
  // * The last segment of a pipeline name
  // * "*", all delivery pipelines in a location
  string id = 1;

  // DeliveryPipeline labels.
  map<string, string> labels = 2;
}

// Contains criteria for selecting Targets. This could be used to select targets
// for a Deploy Policy or for an Automation.
message TargetAttribute {
  // ID of the `Target`. The value of this field could be one of the
  // following:
  //
  // * The last segment of a target name
  // * "*", all targets in a location
  string id = 1;

  // Target labels.
  map<string, string> labels = 2;
}

// Deploy Policy rule.
message PolicyRule {
  oneof rule {
    // Rollout restrictions.
    RolloutRestriction rollout_restriction = 2;
  }
}

// Rollout restrictions.
message RolloutRestriction {
  // Rollout actions to be restricted as part of the policy.
  enum RolloutActions {
    // Unspecified.
    ROLLOUT_ACTIONS_UNSPECIFIED = 0;

    // Advance the rollout to the next phase.
    ADVANCE = 1;

    // Approve the rollout.
    APPROVE = 2;

    // Cancel the rollout.
    CANCEL = 3;

    // Create a rollout.
    CREATE = 4;

    // Ignore a job result on the rollout.
    IGNORE_JOB = 5;

    // Retry a job for a rollout.
    RETRY_JOB = 6;

    // Rollback a rollout.
    ROLLBACK = 7;

    // Terminate a jobrun.
    TERMINATE_JOBRUN = 8;
  }

  // Required. Restriction rule ID. Required and must be unique within a
  // DeployPolicy. The format is `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. What invoked the action. If left empty, all invoker types will be
  // restricted.
  repeated DeployPolicy.Invoker invokers = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Rollout actions to be restricted as part of the policy. If left
  // empty, all actions will be restricted.
  repeated RolloutActions actions = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Time window within which actions are restricted.
  TimeWindows time_windows = 4 [(google.api.field_behavior) = REQUIRED];
}

// Time windows within which actions are restricted. See the
// [documentation](https://cloud.google.com/deploy/docs/deploy-policy#dates_times)
// for more information on how to configure dates/times.
message TimeWindows {
  // Required. The time zone in IANA format [IANA Time Zone
  // Database](https://www.iana.org/time-zones) (e.g. America/New_York).
  string time_zone = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. One-time windows within which actions are restricted.
  repeated OneTimeWindow one_time_windows = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Recurring weekly windows within which actions are restricted.
  repeated WeeklyWindow weekly_windows = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// One-time window within which actions are restricted. For example, blocking
// actions over New Year's Eve from December 31st at 5pm to January 1st at 9am.
message OneTimeWindow {
  // Required. Start date.
  google.type.Date start_date = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Start time (inclusive). Use 00:00 for the beginning of the day.
  google.type.TimeOfDay start_time = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. End date.
  google.type.Date end_date = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. End time (exclusive). You may use 24:00 for the end of the day.
  google.type.TimeOfDay end_time = 4 [(google.api.field_behavior) = REQUIRED];
}

// Weekly windows. For example, blocking actions every Saturday and Sunday.
// Another example would be blocking actions every weekday from 5pm to midnight.
message WeeklyWindow {
  // Optional. Days of week. If left empty, all days of the week will be
  // included.
  repeated google.type.DayOfWeek days_of_week = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Start time (inclusive). Use 00:00 for the beginning of the day.
  // If you specify start_time you must also specify end_time. If left empty,
  // this will block for the entire day for the days specified in days_of_week.
  google.type.TimeOfDay start_time = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. End time (exclusive). Use 24:00 to indicate midnight. If you
  // specify end_time you must also specify start_time. If left empty, this will
  // block for the entire day for the days specified in days_of_week.
  google.type.TimeOfDay end_time = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Returned from an action if one or more policies were
// violated, and therefore the action was prevented. Contains information about
// what policies were violated and why.
message PolicyViolation {
  // Policy violation details.
  repeated PolicyViolationDetails policy_violation_details = 1;
}

// Policy violation details.
message PolicyViolationDetails {
  // Name of the policy that was violated.
  // Policy resource will be in the format of
  // `projects/{project}/locations/{location}/policies/{policy}`.
  string policy = 1;

  // Id of the rule that triggered the policy violation.
  string rule_id = 2;

  // User readable message about why the request violated a policy. This is not
  // intended for machine parsing.
  string failure_message = 3;
}

// A `Release` resource in the Cloud Deploy API.
//
// A `Release` defines a specific Skaffold configuration instance
// that can be deployed.
message Release {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/Release"
    pattern: "projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/releases/{release}"
  };

  // Valid states of the render operation.
  enum RenderState {
    // The render state is unspecified.
    RENDER_STATE_UNSPECIFIED = 0;

    // All rendering operations have completed successfully.
    SUCCEEDED = 1;

    // All rendering operations have completed, and one or more have failed.
    FAILED = 2;

    // Rendering has started and is not complete.
    IN_PROGRESS = 3;
  }

  // Details of rendering for a single target.
  message TargetRender {
    // Valid states of the render operation.
    enum TargetRenderState {
      // The render operation state is unspecified.
      TARGET_RENDER_STATE_UNSPECIFIED = 0;

      // The render operation has completed successfully.
      SUCCEEDED = 1;

      // The render operation has failed.
      FAILED = 2;

      // The render operation is in progress.
      IN_PROGRESS = 3;
    }

    // Well-known rendering failures.
    enum FailureCause {
      // No reason for failure is specified.
      FAILURE_CAUSE_UNSPECIFIED = 0;

      // Cloud Build is not available, either because it is not enabled or
      // because Cloud Deploy has insufficient permissions. See [required
      // permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).
      CLOUD_BUILD_UNAVAILABLE = 1;

      // The render operation did not complete successfully; check Cloud Build
      // logs.
      EXECUTION_FAILED = 2;

      // Cloud Build failed to fulfill Cloud Deploy's request. See
      // failure_message for additional details.
      CLOUD_BUILD_REQUEST_FAILED = 3;

      // The render operation did not complete successfully because the
      // verification stanza required for verify was not found on the Skaffold
      // configuration.
      VERIFICATION_CONFIG_NOT_FOUND = 4;

      // The render operation did not complete successfully because the custom
      // action required for predeploy or postdeploy was not found in the
      // Skaffold configuration. See failure_message for additional details.
      CUSTOM_ACTION_NOT_FOUND = 5;

      // Release failed during rendering because the release configuration is
      // not supported with the specified deployment strategy.
      DEPLOYMENT_STRATEGY_NOT_SUPPORTED = 6;

      // The render operation had a feature configured that is not supported.
      RENDER_FEATURE_NOT_SUPPORTED = 7;
    }

    // Output only. The resource name of the Cloud Build `Build` object that is
    // used to render the manifest for this target. Format is
    // `projects/{project}/locations/{location}/builds/{build}`.
    string rendering_build = 1 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "cloudbuild.googleapis.com/Build"
      }
    ];

    // Output only. Current state of the render operation for this Target.
    TargetRenderState rendering_state = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Metadata related to the `Release` render for this Target.
    RenderMetadata metadata = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Reason this render failed. This will always be unspecified
    // while the render in progress.
    FailureCause failure_cause = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Additional information about the render failure, if
    // available.
    string failure_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // ReleaseReadyCondition contains information around the status of the
  // Release. If a release is not ready, you cannot create a rollout with the
  // release.
  message ReleaseReadyCondition {
    // True if the Release is in a valid state. Otherwise at least one condition
    // in `ReleaseCondition` is in an invalid state. Iterate over those
    // conditions and see which condition(s) has status = false to find out what
    // is wrong with the Release.
    bool status = 1;
  }

  // SkaffoldSupportedCondition contains information about when support for the
  // release's version of Skaffold ends.
  message SkaffoldSupportedCondition {
    // True if the version of Skaffold used by this release is supported.
    bool status = 1;

    // The Skaffold support state for this release's version of Skaffold.
    SkaffoldSupportState skaffold_support_state = 2;

    // The time at which this release's version of Skaffold will enter
    // maintenance mode.
    google.protobuf.Timestamp maintenance_mode_time = 3;

    // The time at which this release's version of Skaffold will no longer be
    // supported.
    google.protobuf.Timestamp support_expiration_time = 4;
  }

  // ReleaseCondition contains all conditions relevant to a Release.
  message ReleaseCondition {
    // Details around the Releases's overall status.
    ReleaseReadyCondition release_ready_condition = 1;

    // Details around the support state of the release's Skaffold
    // version.
    SkaffoldSupportedCondition skaffold_supported_condition = 2;
  }

  // Optional. Name of the `Release`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}`.
  // The `release` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Unique identifier of the `Release`.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Description of the `Release`. Max length is 255 characters.
  string description = 3;

  // User annotations. These attributes can only be set and used by the
  // user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations
  // for more details such as format and size limitations.
  map<string, string> annotations = 4;

  // Labels are attributes that can be set and used by both the
  // user and by Cloud Deploy. Labels must meet the following constraints:
  //
  // * Keys and values can contain only lowercase letters, numeric characters,
  // underscores, and dashes.
  // * All characters must use UTF-8 encoding, and international characters are
  // allowed.
  // * Keys must start with a lowercase letter or international character.
  // * Each resource is limited to a maximum of 64 labels.
  //
  // Both keys and values are additionally constrained to be <= 128 bytes.
  map<string, string> labels = 5;

  // Output only. Indicates whether this is an abandoned release.
  bool abandoned = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `Release` was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the render began.
  google.protobuf.Timestamp render_start_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the render completed.
  google.protobuf.Timestamp render_end_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Cloud Storage URI of tar.gz archive containing Skaffold configuration.
  string skaffold_config_uri = 17;

  // Filepath of the Skaffold config inside of the config URI.
  string skaffold_config_path = 9;

  // List of artifacts to pass through to Skaffold command.
  repeated BuildArtifact build_artifacts = 10;

  // Output only. Snapshot of the parent pipeline taken at release creation
  // time.
  DeliveryPipeline delivery_pipeline_snapshot = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Snapshot of the targets taken at release creation time.
  repeated Target target_snapshots = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Snapshot of the custom target types referenced by the targets
  // taken at release creation time.
  repeated CustomTargetType custom_target_type_snapshots = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state of the render operation.
  RenderState render_state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 16;

  // Optional. The Skaffold version to use when operating on this release, such
  // as "1.20.0". Not all versions are valid; Cloud Deploy supports a specific
  // set of versions.
  //
  // If unset, the most recent supported Skaffold version will be used.
  string skaffold_version = 19 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Map from target ID to the target artifacts created
  // during the render operation.
  map<string, TargetArtifact> target_artifacts = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Map from target ID to details of the render operation for that
  // target.
  map<string, TargetRender> target_renders = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Information around the state of the Release.
  ReleaseCondition condition = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The deploy parameters to use for all targets in this release.
  map<string, string> deploy_parameters = 25
      [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `CreateDeployPolicy`.
message CreateDeployPolicyRequest {
  // Required. The parent collection in which the `DeployPolicy` must be
  // created. The format is `projects/{project_id}/locations/{location_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];

  // Required. ID of the `DeployPolicy`.
  string deploy_policy_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The `DeployPolicy` to create.
  DeployPolicy deploy_policy = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `UpdateDeployPolicy`.
message UpdateDeployPolicyRequest {
  // Required. Field mask is used to specify the fields to be overwritten by the
  // update in the `DeployPolicy` resource. The fields specified in the
  // update_mask are relative to the resource, not the full request. A field
  // will be overwritten if it's in the mask. If the user doesn't provide a mask
  // then all fields are overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The `DeployPolicy` to update.
  DeployPolicy deploy_policy = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, updating a `DeployPolicy` that does not exist
  // will result in the creation of a new `DeployPolicy`.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `DeleteDeployPolicy`.
message DeleteDeployPolicyRequest {
  // Required. The name of the `DeployPolicy` to delete. The format is
  // `projects/{project_id}/locations/{location_name}/deployPolicies/{deploy_policy_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, then deleting an already deleted or non-existing
  // `DeployPolicy` will succeed.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // actually post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `ListDeployPolicies`.
message ListDeployPoliciesRequest {
  // Required. The parent, which owns this collection of deploy policies. Format
  // must be `projects/{project_id}/locations/{location_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];

  // The maximum number of deploy policies to return. The service may return
  // fewer than this value. If unspecified, at most 50 deploy policies will
  // be returned. The maximum value is 1000; values above 1000 will be set
  // to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListDeployPolicies` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match
  // the call that provided the page token.
  string page_token = 3;

  // Filter deploy policies to be returned. See https://google.aip.dev/160 for
  // more details. All fields can be used in the filter.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// The response object from `ListDeployPolicies`.
message ListDeployPoliciesResponse {
  // The `DeployPolicy` objects.
  repeated DeployPolicy deploy_policies = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// The request object for `GetDeployPolicy`
message GetDeployPolicyRequest {
  // Required. Name of the `DeployPolicy`. Format must be
  // `projects/{project_id}/locations/{location_name}/deployPolicies/{deploy_policy_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// Description of an a image to use during Skaffold rendering.
message BuildArtifact {
  // Image name in Skaffold configuration.
  string image = 3;

  // Image tag to use. This will generally be the full path to an image, such
  // as "gcr.io/my-project/busybox:1.2.3" or
  // "gcr.io/my-project/busybox@sha256:abc123".
  string tag = 2;
}

// The artifacts produced by a target render operation.
message TargetArtifact {
  // Contains the paths to the artifacts, relative to the URI, for a phase.
  message PhaseArtifact {
    // Output only. File path of the resolved Skaffold configuration relative to
    // the URI.
    string skaffold_config_path = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. File path of the rendered manifest relative to the URI.
    string manifest_path = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. File path of the directory of rendered job manifests
    // relative to the URI. This is only set if it is applicable.
    string job_manifests_path = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  oneof uri {
    // Output only. URI of a directory containing the artifacts. This contains
    // deployment configuration used by Skaffold during a rollout, and all
    // paths are relative to this location.
    string artifact_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. File path of the resolved Skaffold configuration relative to
  // the URI.
  string skaffold_config_path = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. File path of the rendered manifest relative to the URI.
  string manifest_path = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Map from the phase ID to the phase artifacts for the `Target`.
  map<string, PhaseArtifact> phase_artifacts = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The artifacts produced by a deploy operation.
message DeployArtifact {
  // Output only. URI of a directory containing the artifacts. All paths are
  // relative to this location.
  string artifact_uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. File paths of the manifests applied during the deploy
  // operation relative to the URI.
  repeated string manifest_paths = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CloudRunRenderMetadata contains Cloud Run information associated with a
// `Release` render.
message CloudRunRenderMetadata {
  // Output only. The name of the Cloud Run Service in the rendered manifest.
  // Format is `projects/{project}/locations/{location}/services/{service}`.
  string service = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Service" }
  ];
}

// RenderMetadata includes information associated with a `Release` render.
message RenderMetadata {
  // Output only. Metadata associated with rendering for Cloud Run.
  CloudRunRenderMetadata cloud_run = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Custom metadata provided by user-defined render operation.
  CustomMetadata custom = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request object for `ListReleases`.
message ListReleasesRequest {
  // Required. The `DeliveryPipeline` which owns this collection of `Release`
  // objects.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeliveryPipeline"
    }
  ];

  // Optional. The maximum number of `Release` objects to return. The service
  // may return fewer than this value. If unspecified, at most 50 `Release`
  // objects will be returned. The maximum value is 1000; values above 1000 will
  // be set to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListReleases` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match
  // the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter releases to be returned. See https://google.aip.dev/160
  // for more details.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field to sort by. See https://google.aip.dev/132#ordering for
  // more details.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response object from `ListReleases`.
message ListReleasesResponse {
  // The `Release` objects.
  repeated Release releases = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// The request object for `GetRelease`.
message GetReleaseRequest {
  // Required. Name of the `Release`. Format must be
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{release_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Release"
    }
  ];
}

// The request object for `CreateRelease`,
message CreateReleaseRequest {
  // Required. The parent collection in which the `Release` is created.
  // The format is
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeliveryPipeline"
    }
  ];

  // Required. ID of the `Release`.
  string release_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The `Release` to create.
  Release release = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  repeated string override_deploy_policy = 6 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// A `Rollout` resource in the Cloud Deploy API.
//
// A `Rollout` contains information around a specific deployment to a `Target`.
message Rollout {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/Rollout"
    pattern: "projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/releases/{release}/rollouts/{rollout}"
  };

  // Valid approval states of a `Rollout`.
  enum ApprovalState {
    // The `Rollout` has an unspecified approval state.
    APPROVAL_STATE_UNSPECIFIED = 0;

    // The `Rollout` requires approval.
    NEEDS_APPROVAL = 1;

    // The `Rollout` does not require approval.
    DOES_NOT_NEED_APPROVAL = 2;

    // The `Rollout` has been approved.
    APPROVED = 3;

    // The `Rollout` has been rejected.
    REJECTED = 4;
  }

  // Valid states of a `Rollout`.
  enum State {
    // The `Rollout` has an unspecified state.
    STATE_UNSPECIFIED = 0;

    // The `Rollout` has completed successfully.
    SUCCEEDED = 1;

    // The `Rollout` has failed.
    FAILED = 2;

    // The `Rollout` is being deployed.
    IN_PROGRESS = 3;

    // The `Rollout` needs approval.
    PENDING_APPROVAL = 4;

    // An approver rejected the `Rollout`.
    APPROVAL_REJECTED = 5;

    // The `Rollout` is waiting for an earlier Rollout(s) to complete on this
    // `Target`.
    PENDING = 6;

    // The `Rollout` is waiting for the `Release` to be fully rendered.
    PENDING_RELEASE = 7;

    // The `Rollout` is in the process of being cancelled.
    CANCELLING = 8;

    // The `Rollout` has been cancelled.
    CANCELLED = 9;

    // The `Rollout` is halted.
    HALTED = 10;
  }

  // Well-known rollout failures.
  enum FailureCause {
    // No reason for failure is specified.
    FAILURE_CAUSE_UNSPECIFIED = 0;

    // Cloud Build is not available, either because it is not enabled or because
    // Cloud Deploy has insufficient permissions. See [required
    // permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).
    CLOUD_BUILD_UNAVAILABLE = 1;

    // The deploy operation did not complete successfully; check Cloud Build
    // logs.
    EXECUTION_FAILED = 2;

    // Deployment did not complete within the alloted time.
    DEADLINE_EXCEEDED = 3;

    // Release is in a failed state.
    RELEASE_FAILED = 4;

    // Release is abandoned.
    RELEASE_ABANDONED = 5;

    // No Skaffold verify configuration was found.
    VERIFICATION_CONFIG_NOT_FOUND = 6;

    // Cloud Build failed to fulfill Cloud Deploy's request. See failure_message
    // for additional details.
    CLOUD_BUILD_REQUEST_FAILED = 7;

    // A Rollout operation had a feature configured that is not supported.
    OPERATION_FEATURE_NOT_SUPPORTED = 8;
  }

  // Optional. Name of the `Rollout`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  // The `rollout` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Unique identifier of the `Rollout`.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Description of the `Rollout` for user purposes. Max length is 255
  // characters.
  string description = 3;

  // User annotations. These attributes can only be set and used by the
  // user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations
  // for more details such as format and size limitations.
  map<string, string> annotations = 4;

  // Labels are attributes that can be set and used by both the
  // user and by Cloud Deploy. Labels must meet the following constraints:
  //
  // * Keys and values can contain only lowercase letters, numeric characters,
  // underscores, and dashes.
  // * All characters must use UTF-8 encoding, and international characters are
  // allowed.
  // * Keys must start with a lowercase letter or international character.
  // * Each resource is limited to a maximum of 64 labels.
  //
  // Both keys and values are additionally constrained to be <= 128 bytes.
  map<string, string> labels = 5;

  // Output only. Time at which the `Rollout` was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `Rollout` was approved.
  google.protobuf.Timestamp approve_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `Rollout` was enqueued.
  google.protobuf.Timestamp enqueue_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `Rollout` started deploying.
  google.protobuf.Timestamp deploy_start_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `Rollout` finished deploying.
  google.protobuf.Timestamp deploy_end_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The ID of Target to which this `Rollout` is deploying.
  string target_id = 18 [(google.api.field_behavior) = REQUIRED];

  // Output only. Approval state of the `Rollout`.
  ApprovalState approval_state = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state of the `Rollout`.
  State state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the rollout failure, if
  // available.
  string failure_reason = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of the Cloud Build `Build` object that is
  // used to deploy the Rollout. Format is
  // `projects/{project}/locations/{location}/builds/{build}`.
  string deploying_build = 17 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Build"
    }
  ];

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 16;

  // Output only. The reason this rollout failed. This will always be
  // unspecified while the rollout is in progress.
  FailureCause deploy_failure_cause = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The phases that represent the workflows of this `Rollout`.
  repeated Phase phases = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Metadata contains information about the rollout.
  Metadata metadata = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the `ControllerRollout`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  string controller_rollout = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the `Rollout` that is rolled back by this `Rollout`.
  // Empty if this `Rollout` wasn't created as a rollback.
  string rollback_of_rollout = 26 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Names of `Rollouts` that rolled back this `Rollout`.
  repeated string rolled_back_by_rollouts = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The AutomationRun actively repairing the rollout.
  string active_repair_automation_run = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata includes information associated with a `Rollout`.
message Metadata {
  // Output only. The name of the Cloud Run Service that is associated with a
  // `Rollout`.
  CloudRunMetadata cloud_run = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. AutomationRolloutMetadata contains the information about the
  // interactions between Automation service and this rollout.
  AutomationRolloutMetadata automation = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Custom metadata provided by user-defined `Rollout` operations.
  CustomMetadata custom = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// DeployJobRunMetadata surfaces information associated with a `DeployJobRun` to
// the user.
message DeployJobRunMetadata {
  // Output only. The name of the Cloud Run Service that is associated with a
  // `DeployJobRun`.
  CloudRunMetadata cloud_run = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Custom Target metadata associated with a `DeployJobRun`.
  CustomTargetDeployMetadata custom_target = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Custom metadata provided by user-defined deploy operation.
  CustomMetadata custom = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CloudRunMetadata contains information from a Cloud Run deployment.
message CloudRunMetadata {
  // Output only. The name of the Cloud Run Service that is associated with a
  // `Rollout`. Format is
  // `projects/{project}/locations/{location}/services/{service}`.
  string service = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Service" }
  ];

  // Output only. The Cloud Run Service urls that are associated with a
  // `Rollout`.
  repeated string service_urls = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Cloud Run Revision id associated with a `Rollout`.
  string revision = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the Cloud Run job that is associated with a
  // `Rollout`. Format is
  // `projects/{project}/locations/{location}/jobs/{job_name}`.
  string job = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Job" }
  ];
}

// CustomTargetDeployMetadata contains information from a Custom Target
// deploy operation.
message CustomTargetDeployMetadata {
  // Output only. Skip message provided in the results of a custom deploy
  // operation.
  string skip_message = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// AutomationRolloutMetadata contains Automation-related actions that
// were performed on a rollout.
message AutomationRolloutMetadata {
  // Output only. The name of the AutomationRun initiated by a promote release
  // rule.
  string promote_automation_run = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of the AutomationRuns initiated by an advance
  // rollout rule.
  repeated string advance_automation_runs = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The names of the AutomationRuns initiated by a repair rollout
  // rule.
  repeated string repair_automation_runs = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CustomMetadata contains information from a user-defined operation.
message CustomMetadata {
  // Output only. Key-value pairs provided by the user-defined operation.
  map<string, string> values = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Phase represents a collection of jobs that are logically grouped together
// for a `Rollout`.
message Phase {
  // Valid states of a Phase.
  enum State {
    // The Phase has an unspecified state.
    STATE_UNSPECIFIED = 0;

    // The Phase is waiting for an earlier Phase(s) to complete.
    PENDING = 1;

    // The Phase is in progress.
    IN_PROGRESS = 2;

    // The Phase has succeeded.
    SUCCEEDED = 3;

    // The Phase has failed.
    FAILED = 4;

    // The Phase was aborted.
    ABORTED = 5;

    // The Phase was skipped.
    SKIPPED = 6;
  }

  // Output only. The ID of the Phase.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state of the Phase.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information on why the Phase was skipped, if
  // available.
  string skip_message = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The job composition of this Phase.
  oneof jobs {
    // Output only. Deployment job composition.
    DeploymentJobs deployment_jobs = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. ChildRollout job composition.
    ChildRolloutJobs child_rollout_jobs = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// Deployment job composition.
message DeploymentJobs {
  // Output only. The deploy Job. This is the deploy job in the phase.
  Job deploy_job = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The verify Job. Runs after a deploy if the deploy succeeds.
  Job verify_job = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The predeploy Job, which is the first job on the phase.
  Job predeploy_job = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The postdeploy Job, which is the last job on the phase.
  Job postdeploy_job = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ChildRollouts job composition
message ChildRolloutJobs {
  // Output only. List of CreateChildRolloutJobs
  repeated Job create_rollout_jobs = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of AdvanceChildRolloutJobs
  repeated Job advance_rollout_jobs = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Job represents an operation for a `Rollout`.
message Job {
  // Valid states of a Job.
  enum State {
    // The Job has an unspecified state.
    STATE_UNSPECIFIED = 0;

    // The Job is waiting for an earlier Phase(s) or Job(s) to complete.
    PENDING = 1;

    // The Job is disabled.
    DISABLED = 2;

    // The Job is in progress.
    IN_PROGRESS = 3;

    // The Job succeeded.
    SUCCEEDED = 4;

    // The Job failed.
    FAILED = 5;

    // The Job was aborted.
    ABORTED = 6;

    // The Job was skipped.
    SKIPPED = 7;

    // The Job was ignored.
    IGNORED = 8;
  }

  // Output only. The ID of the Job.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the Job.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information on why the Job was skipped, if
  // available.
  string skip_message = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the `JobRun` responsible for the most recent
  // invocation of this Job.
  string job_run = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/JobRun"
    }
  ];

  // The type of Job.
  oneof job_type {
    // Output only. A deploy Job.
    DeployJob deploy_job = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A verify Job.
    VerifyJob verify_job = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A predeploy Job.
    PredeployJob predeploy_job = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A postdeploy Job.
    PostdeployJob postdeploy_job = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A createChildRollout Job.
    CreateChildRolloutJob create_child_rollout_job = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. An advanceChildRollout Job.
    AdvanceChildRolloutJob advance_child_rollout_job = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// A deploy Job.
message DeployJob {}

// A verify Job.
message VerifyJob {}

// A predeploy Job.
message PredeployJob {
  // Output only. The custom actions that the predeploy Job executes.
  repeated string actions = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A postdeploy Job.
message PostdeployJob {
  // Output only. The custom actions that the postdeploy Job executes.
  repeated string actions = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A createChildRollout Job.
message CreateChildRolloutJob {}

// An advanceChildRollout Job.
message AdvanceChildRolloutJob {}

// ListRolloutsRequest is the request object used by `ListRollouts`.
message ListRolloutsRequest {
  // Required. The `Release` which owns this collection of `Rollout` objects.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Release"
    }
  ];

  // Optional. The maximum number of `Rollout` objects to return. The service
  // may return fewer than this value. If unspecified, at most 50 `Rollout`
  // objects will be returned. The maximum value is 1000; values above 1000 will
  // be set to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListRollouts` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match
  // the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter rollouts to be returned. See https://google.aip.dev/160
  // for more details.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field to sort by. See https://google.aip.dev/132#ordering for
  // more details.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// ListRolloutsResponse is the response object reutrned by `ListRollouts`.
message ListRolloutsResponse {
  // The `Rollout` objects.
  repeated Rollout rollouts = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// GetRolloutRequest is the request object used by `GetRollout`.
message GetRolloutRequest {
  // Required. Name of the `Rollout`. Format must be
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{release_name}/rollouts/{rollout_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Rollout"
    }
  ];
}

// CreateRolloutRequest is the request object used by `CreateRollout`.
message CreateRolloutRequest {
  // Required. The parent collection in which the `Rollout` must be created.
  // The format is
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{release_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Release"
    }
  ];

  // Required. ID of the `Rollout`.
  string rollout_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The `Rollout` to create.
  Rollout rollout = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  repeated string override_deploy_policy = 6 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];

  // Optional. The starting phase ID for the `Rollout`. If empty the `Rollout`
  // will start at the first phase.
  string starting_phase_id = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have
  // [google.longrunning.Operation.error][google.longrunning.Operation.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request object used by `ApproveRollout`.
message ApproveRolloutRequest {
  // Required. Name of the Rollout. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Rollout"
    }
  ];

  // Required. True = approve; false = reject
  bool approved = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  repeated string override_deploy_policy = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// The response object from `ApproveRollout`.
message ApproveRolloutResponse {}

// The request object used by `AdvanceRollout`.
message AdvanceRolloutRequest {
  // Required. Name of the Rollout. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Rollout"
    }
  ];

  // Required. The phase ID to advance the `Rollout` to.
  string phase_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  repeated string override_deploy_policy = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// The response object from `AdvanceRollout`.
message AdvanceRolloutResponse {}

// The request object used by `CancelRollout`.
message CancelRolloutRequest {
  // Required. Name of the Rollout. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Rollout"
    }
  ];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  repeated string override_deploy_policy = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// The response object from `CancelRollout`.
message CancelRolloutResponse {}

// The request object used by `IgnoreJob`.
message IgnoreJobRequest {
  // Required. Name of the Rollout. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  string rollout = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Rollout"
    }
  ];

  // Required. The phase ID the Job to ignore belongs to.
  string phase_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The job ID for the Job to ignore.
  string job_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  repeated string override_deploy_policy = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// The response object from `IgnoreJob`.
message IgnoreJobResponse {}

// RetryJobRequest is the request object used by `RetryJob`.
message RetryJobRequest {
  // Required. Name of the Rollout. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  string rollout = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Rollout"
    }
  ];

  // Required. The phase ID the Job to retry belongs to.
  string phase_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The job ID for the Job to retry.
  string job_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  repeated string override_deploy_policy = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// The response object from 'RetryJob'.
message RetryJobResponse {}

// The request object used by `AbandonRelease`.
message AbandonReleaseRequest {
  // Required. Name of the Release. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Release"
    }
  ];
}

// The response object for `AbandonRelease`.
message AbandonReleaseResponse {}

// A `JobRun` resource in the Cloud Deploy API.
//
// A `JobRun` contains information of a single `Rollout` job evaluation.
message JobRun {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/JobRun"
    pattern: "projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/releases/{release}/rollouts/{rollout}/jobRuns/{job_run}"
  };

  // Valid states of a `JobRun`.
  enum State {
    // The `JobRun` has an unspecified state.
    STATE_UNSPECIFIED = 0;

    // The `JobRun` is in progress.
    IN_PROGRESS = 1;

    // The `JobRun` has succeeded.
    SUCCEEDED = 2;

    // The `JobRun` has failed.
    FAILED = 3;

    // The `JobRun` is terminating.
    TERMINATING = 4;

    // The `JobRun` was terminated.
    TERMINATED = 5;
  }

  // Optional. Name of the `JobRun`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{releases}/rollouts/{rollouts}/jobRuns/{uuid}`.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Unique identifier of the `JobRun`.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the `Rollout` phase this `JobRun` belongs in.
  string phase_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the `Rollout` job this `JobRun` corresponds to.
  string job_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `JobRun` was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `JobRun` was started.
  google.protobuf.Timestamp start_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `JobRun` ended.
  google.protobuf.Timestamp end_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the `JobRun`.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The `JobRun` type and the information for that type.
  oneof job_run {
    // Output only. Information specific to a deploy `JobRun`.
    DeployJobRun deploy_job_run = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Information specific to a verify `JobRun`.
    VerifyJobRun verify_job_run = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Information specific to a predeploy `JobRun`.
    PredeployJobRun predeploy_job_run = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Information specific to a postdeploy `JobRun`.
    PostdeployJobRun postdeploy_job_run = 15
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Information specific to a createChildRollout `JobRun`.
    CreateChildRolloutJobRun create_child_rollout_job_run = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Information specific to an advanceChildRollout `JobRun`
    AdvanceChildRolloutJobRun advance_child_rollout_job_run = 13
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// DeployJobRun contains information specific to a deploy `JobRun`.
message DeployJobRun {
  // Well-known deploy failures.
  enum FailureCause {
    // No reason for failure is specified.
    FAILURE_CAUSE_UNSPECIFIED = 0;

    // Cloud Build is not available, either because it is not enabled or because
    // Cloud Deploy has insufficient permissions. See [Required
    // permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).
    CLOUD_BUILD_UNAVAILABLE = 1;

    // The deploy operation did not complete successfully; check Cloud Build
    // logs.
    EXECUTION_FAILED = 2;

    // The deploy job run did not complete within the alloted time.
    DEADLINE_EXCEEDED = 3;

    // There were missing resources in the runtime environment required for a
    // canary deployment. Check the Cloud Build logs for more information.
    MISSING_RESOURCES_FOR_CANARY = 4;

    // Cloud Build failed to fulfill Cloud Deploy's request. See failure_message
    // for additional details.
    CLOUD_BUILD_REQUEST_FAILED = 5;

    // The deploy operation had a feature configured that is not supported.
    DEPLOY_FEATURE_NOT_SUPPORTED = 6;
  }

  // Output only. The resource name of the Cloud Build `Build` object that is
  // used to deploy. Format is
  // `projects/{project}/locations/{location}/builds/{build}`.
  string build = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Build"
    }
  ];

  // Output only. The reason the deploy failed. This will always be unspecified
  // while the deploy is in progress or if it succeeded.
  FailureCause failure_cause = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the deploy failure, if available.
  string failure_message = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Metadata containing information about the deploy job run.
  DeployJobRunMetadata metadata = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The artifact of a deploy job run, if available.
  DeployArtifact artifact = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// VerifyJobRun contains information specific to a verify `JobRun`.
message VerifyJobRun {
  // Well-known verify failures.
  enum FailureCause {
    // No reason for failure is specified.
    FAILURE_CAUSE_UNSPECIFIED = 0;

    // Cloud Build is not available, either because it is not enabled or because
    // Cloud Deploy has insufficient permissions. See [required
    // permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).
    CLOUD_BUILD_UNAVAILABLE = 1;

    // The verify operation did not complete successfully; check Cloud Build
    // logs.
    EXECUTION_FAILED = 2;

    // The verify job run did not complete within the alloted time.
    DEADLINE_EXCEEDED = 3;

    // No Skaffold verify configuration was found.
    VERIFICATION_CONFIG_NOT_FOUND = 4;

    // Cloud Build failed to fulfill Cloud Deploy's request. See failure_message
    // for additional details.
    CLOUD_BUILD_REQUEST_FAILED = 5;
  }

  // Output only. The resource name of the Cloud Build `Build` object that is
  // used to verify. Format is
  // `projects/{project}/locations/{location}/builds/{build}`.
  string build = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Build"
    }
  ];

  // Output only. URI of a directory containing the verify artifacts. This
  // contains the Skaffold event log.
  string artifact_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. File path of the Skaffold event log relative to the artifact
  // URI.
  string event_log_path = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The reason the verify failed. This will always be unspecified
  // while the verify is in progress or if it succeeded.
  FailureCause failure_cause = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the verify failure, if available.
  string failure_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// PredeployJobRun contains information specific to a predeploy `JobRun`.
message PredeployJobRun {
  // Well-known predeploy failures.
  enum FailureCause {
    // No reason for failure is specified.
    FAILURE_CAUSE_UNSPECIFIED = 0;

    // Cloud Build is not available, either because it is not enabled or because
    // Cloud Deploy has insufficient permissions. See [required
    // permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).
    CLOUD_BUILD_UNAVAILABLE = 1;

    // The predeploy operation did not complete successfully; check Cloud Build
    // logs.
    EXECUTION_FAILED = 2;

    // The predeploy job run did not complete within the alloted time.
    DEADLINE_EXCEEDED = 3;

    // Cloud Build failed to fulfill Cloud Deploy's request. See failure_message
    // for additional details.
    CLOUD_BUILD_REQUEST_FAILED = 4;
  }

  // Output only. The resource name of the Cloud Build `Build` object that is
  // used to execute the custom actions associated with the predeploy Job.
  // Format is `projects/{project}/locations/{location}/builds/{build}`.
  string build = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Build"
    }
  ];

  // Output only. The reason the predeploy failed. This will always be
  // unspecified while the predeploy is in progress or if it succeeded.
  FailureCause failure_cause = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the predeploy failure, if
  // available.
  string failure_message = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// PostdeployJobRun contains information specific to a postdeploy `JobRun`.
message PostdeployJobRun {
  // Well-known postdeploy failures.
  enum FailureCause {
    // No reason for failure is specified.
    FAILURE_CAUSE_UNSPECIFIED = 0;

    // Cloud Build is not available, either because it is not enabled or because
    // Cloud Deploy has insufficient permissions. See [required
    // permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).
    CLOUD_BUILD_UNAVAILABLE = 1;

    // The postdeploy operation did not complete successfully; check Cloud Build
    // logs.
    EXECUTION_FAILED = 2;

    // The postdeploy job run did not complete within the alloted time.
    DEADLINE_EXCEEDED = 3;

    // Cloud Build failed to fulfill Cloud Deploy's request. See failure_message
    // for additional details.
    CLOUD_BUILD_REQUEST_FAILED = 4;
  }

  // Output only. The resource name of the Cloud Build `Build` object that is
  // used to execute the custom actions associated with the postdeploy Job.
  // Format is `projects/{project}/locations/{location}/builds/{build}`.
  string build = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Build"
    }
  ];

  // Output only. The reason the postdeploy failed. This will always be
  // unspecified while the postdeploy is in progress or if it succeeded.
  FailureCause failure_cause = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the postdeploy failure, if
  // available.
  string failure_message = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CreateChildRolloutJobRun contains information specific to a
// createChildRollout `JobRun`.
message CreateChildRolloutJobRun {
  // Output only. Name of the `ChildRollout`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  string rollout = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the childRollout Phase initiated by this JobRun.
  string rollout_phase_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// AdvanceChildRolloutJobRun contains information specific to a
// advanceChildRollout `JobRun`.
message AdvanceChildRolloutJobRun {
  // Output only. Name of the `ChildRollout`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.
  string rollout = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. the ID of the ChildRollout's Phase.
  string rollout_phase_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ListJobRunsRequest is the request object used by `ListJobRuns`.
message ListJobRunsRequest {
  // Required. The `Rollout` which owns this collection of `JobRun` objects.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Rollout"
    }
  ];

  // Optional. The maximum number of `JobRun` objects to return. The service may
  // return fewer than this value. If unspecified, at most 50 `JobRun` objects
  // will be returned. The maximum value is 1000; values above 1000 will be set
  // to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListJobRuns` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match the call that provided
  // the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter results to be returned. See https://google.aip.dev/160 for
  // more details.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field to sort by. See https://google.aip.dev/132#ordering for
  // more details.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// ListJobRunsResponse is the response object returned by `ListJobRuns`.
message ListJobRunsResponse {
  // The `JobRun` objects.
  repeated JobRun job_runs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page. If
  // this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached
  repeated string unreachable = 3;
}

// GetJobRunRequest is the request object used by `GetJobRun`.
message GetJobRunRequest {
  // Required. Name of the `JobRun`. Format must be
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{release_name}/rollouts/{rollout_name}/jobRuns/{job_run_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/JobRun"
    }
  ];
}

// The request object used by `TerminateJobRun`.
message TerminateJobRunRequest {
  // Required. Name of the `JobRun`. Format must be
  // `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}/jobRuns/{jobRun}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/JobRun"
    }
  ];

  // Optional. Deploy policies to override. Format is
  // `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.
  repeated string override_deploy_policy = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/DeployPolicy"
    }
  ];
}

// The response object from `TerminateJobRun`.
message TerminateJobRunResponse {}

// Service-wide configuration.
message Config {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/Config"
    pattern: "projects/{project}/locations/{location}/config"
  };

  // Name of the configuration.
  string name = 1;

  // All supported versions of Skaffold.
  repeated SkaffoldVersion supported_versions = 2;

  // Default Skaffold version that is assigned when a Release is created without
  // specifying a Skaffold version.
  string default_skaffold_version = 3;
}

// Details of a supported Skaffold version.
message SkaffoldVersion {
  // Release version number. For example, "1.20.3".
  string version = 1;

  // The time at which this version of Skaffold will enter maintenance mode.
  google.protobuf.Timestamp maintenance_mode_time = 3;

  // The time at which this version of Skaffold will no longer be supported.
  google.protobuf.Timestamp support_expiration_time = 4;

  // Date when this version is expected to no longer be supported.
  google.type.Date support_end_date = 2;
}

// Request to get a configuration.
message GetConfigRequest {
  // Required. Name of requested configuration.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Config"
    }
  ];
}

// An `Automation` resource in the Cloud Deploy API.
//
// An `Automation` enables the automation of manually driven actions for
// a Delivery Pipeline, which includes Release promotion among Targets,
// Rollout repair and Rollout deployment strategy advancement. The intention
// of Automation is to reduce manual intervention in the continuous delivery
// process.
message Automation {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/Automation"
    pattern: "projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automations/{automation}"
    style: DECLARATIVE_FRIENDLY
  };

  // Output only. Name of the `Automation`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automations/{automation}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unique identifier of the `Automation`.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description of the `Automation`. Max length is 255 characters.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time at which the automation was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the automation was updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User annotations. These attributes can only be set and used by
  // the user, and not by Cloud Deploy. Annotations must meet the following
  // constraints:
  //
  // * Annotations are key/value pairs.
  // * Valid annotation keys have two segments: an optional prefix and name,
  // separated by a slash (`/`).
  // * The name segment is required and must be 63 characters or less,
  // beginning and ending with an alphanumeric character (`[a-z0-9A-Z]`) with
  // dashes (`-`), underscores (`_`), dots (`.`), and alphanumerics between.
  // * The prefix is optional. If specified, the prefix must be a DNS subdomain:
  // a series of DNS labels separated by dots(`.`), not longer than 253
  // characters in total, followed by a slash (`/`).
  //
  // See
  // https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/#syntax-and-character-set
  // for more details.
  map<string, string> annotations = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels are attributes that can be set and used by both the
  // user and by Cloud Deploy. Labels must meet the following constraints:
  //
  // * Keys and values can contain only lowercase letters, numeric characters,
  // underscores, and dashes.
  // * All characters must use UTF-8 encoding, and international characters are
  // allowed.
  // * Keys must start with a lowercase letter or international character.
  // * Each resource is limited to a maximum of 64 labels.
  //
  // Both keys and values are additionally constrained to be <= 63 characters.
  map<string, string> labels = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The weak etag of the `Automation` resource.
  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When Suspended, automation is deactivated from execution.
  bool suspended = 9 [(google.api.field_behavior) = OPTIONAL];

  // Required. Email address of the user-managed IAM service account that
  // creates Cloud Deploy release and rollout resources.
  string service_account = 10 [(google.api.field_behavior) = REQUIRED];

  // Required. Selected resources to which the automation will be applied.
  AutomationResourceSelector selector = 11
      [(google.api.field_behavior) = REQUIRED];

  // Required. List of Automation rules associated with the Automation resource.
  // Must have at least one rule and limited to 250 rules per Delivery Pipeline.
  // Note: the order of the rules here is not the same as the order of
  // execution.
  repeated AutomationRule rules = 14 [(google.api.field_behavior) = REQUIRED];
}

// AutomationResourceSelector contains the information to select the resources
// to which an Automation is going to be applied.
message AutomationResourceSelector {
  // Contains attributes about a target.
  repeated TargetAttribute targets = 1;
}

// `AutomationRule` defines the automation activities.
message AutomationRule {
  // The configuration of the Automation rule.
  oneof rule {
    // Optional. `PromoteReleaseRule` will automatically promote a release from
    // the current target to a specified target.
    PromoteReleaseRule promote_release_rule = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The `AdvanceRolloutRule` will automatically advance a
    // successful Rollout.
    AdvanceRolloutRule advance_rollout_rule = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The `RepairRolloutRule` will automatically repair a failed
    // rollout.
    RepairRolloutRule repair_rollout_rule = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The `TimedPromoteReleaseRule` will automatically promote a
    // release from the current target(s) to the specified target(s) on a
    // configured schedule.
    TimedPromoteReleaseRule timed_promote_release_rule = 4
        [(google.api.field_behavior) = OPTIONAL];
  }
}

// The `TimedPromoteReleaseRule` will automatically promote a release from the
// current target(s) to the specified target(s) on a configured schedule.
message TimedPromoteReleaseRule {
  // Required. ID of the rule. This ID must be unique in the `Automation`
  // resource to which this rule belongs. The format is
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The ID of the stage in the pipeline to which this `Release` is
  // deploying. If unspecified, default it to the next stage in the promotion
  // flow. The value of this field could be one of the following:
  //
  // * The last segment of a target name
  // * "@next", the next target in the promotion sequence
  string destination_target_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Schedule in crontab format. e.g. "0 9 * * 1" for every Monday at
  // 9am.
  string schedule = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The time zone in IANA format [IANA Time Zone
  // Database](https://www.iana.org/time-zones) (e.g. America/New_York).
  string time_zone = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. Information around the state of the Automation rule.
  AutomationRuleCondition condition = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The starting phase of the rollout created by this rule. Default
  // to the first phase.
  string destination_phase = 6 [(google.api.field_behavior) = OPTIONAL];
}

// The `PromoteRelease` rule will automatically promote a release from the
// current target to a specified target.
message PromoteReleaseRule {
  // Required. ID of the rule. This id must be unique in the `Automation`
  // resource to which this rule belongs. The format is
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. How long the release need to be paused until being promoted to
  // the next target.
  google.protobuf.Duration wait = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The ID of the stage in the pipeline to which this `Release` is
  // deploying. If unspecified, default it to the next stage in the promotion
  // flow. The value of this field could be one of the following:
  //
  // * The last segment of a target name
  // * "@next", the next target in the promotion sequence
  string destination_target_id = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Information around the state of the Automation rule.
  AutomationRuleCondition condition = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The starting phase of the rollout created by this operation.
  // Default to the first phase.
  string destination_phase = 8 [(google.api.field_behavior) = OPTIONAL];
}

// The `AdvanceRollout` automation rule will automatically advance a successful
// Rollout to the next phase.
message AdvanceRolloutRule {
  // Required. ID of the rule. This id must be unique in the `Automation`
  // resource to which this rule belongs. The format is
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Proceeds only after phase name matched any one in the list.
  // This value must consist of lower-case letters, numbers, and hyphens,
  // start with a letter and end with a letter or a number, and have a max
  // length of 63 characters. In other words, it must match the following
  // regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  repeated string source_phases = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. How long to wait after a rollout is finished.
  google.protobuf.Duration wait = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Information around the state of the Automation rule.
  AutomationRuleCondition condition = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The `RepairRolloutRule` automation rule will automatically repair a failed
// `Rollout`.
message RepairRolloutRule {
  // Required. ID of the rule. This id must be unique in the `Automation`
  // resource to which this rule belongs. The format is
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Phases within which jobs are subject to automatic repair actions
  // on failure. Proceeds only after phase name matched any one in the list, or
  // for all phases if unspecified. This value must consist of lower-case
  // letters, numbers, and hyphens, start with a letter and end with a letter or
  // a number, and have a max length of 63 characters. In other words, it must
  // match the following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  repeated string phases = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Jobs to repair. Proceeds only after job name matched any one in
  // the list, or for all jobs if unspecified or empty. The phase that includes
  // the job must match the phase ID specified in `source_phase`. This value
  // must consist of lower-case letters, numbers, and hyphens, start with a
  // letter and end with a letter or a number, and have a max length of 63
  // characters. In other words, it must match the following regex:
  // `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  repeated string jobs = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Information around the state of the 'Automation' rule.
  AutomationRuleCondition condition = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Defines the types of automatic repair phases for failed jobs.
  repeated RepairPhaseConfig repair_phases = 8
      [(google.api.field_behavior) = REQUIRED];
}

// Configuration of the repair phase.
message RepairPhaseConfig {
  // The repair phase to perform.
  oneof repair_phase {
    // Optional. Retries a failed job.
    Retry retry = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Rolls back a `Rollout`.
    Rollback rollback = 2 [(google.api.field_behavior) = OPTIONAL];
  }
}

// Retries the failed job.
message Retry {
  // Required. Total number of retries. Retry is skipped if set to 0; The
  // minimum value is 1, and the maximum value is 10.
  int64 attempts = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. How long to wait for the first retry. Default is 0, and the
  // maximum value is 14d.
  google.protobuf.Duration wait = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The pattern of how wait time will be increased. Default is
  // linear. Backoff mode will be ignored if `wait` is 0.
  BackoffMode backoff_mode = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Rolls back a `Rollout`.
message Rollback {
  // Optional. The starting phase ID for the `Rollout`. If unspecified, the
  // `Rollout` will start in the stable phase.
  string destination_phase = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If pending rollout exists on the target, the rollback operation
  // will be aborted.
  bool disable_rollback_if_rollout_pending = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// `AutomationRuleCondition` contains conditions relevant to an
// `Automation` rule.
message AutomationRuleCondition {
  // Optional. Details around targets enumerated in the rule.
  TargetsPresentCondition targets_present_condition = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Details specific to the automation rule type.
  oneof rule_type_condition {
    // Optional. TimedPromoteReleaseCondition contains rule conditions specific
    // to a an Automation with a timed promote release rule defined.
    TimedPromoteReleaseCondition timed_promote_release_condition = 2
        [(google.api.field_behavior) = OPTIONAL];
  }
}

// `TimedPromoteReleaseCondition` contains conditions specific to an Automation
// with a Timed Promote Release rule defined.
message TimedPromoteReleaseCondition {
  // The targets involved in a single timed promotion.
  message Targets {
    // Optional. The source target ID.
    string source_target_id = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The destination target ID.
    string destination_target_id = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. When the next scheduled promotion(s) will occur.
  google.protobuf.Timestamp next_promotion_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of targets involved in the upcoming timed promotion(s).
  repeated Targets targets_list = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request object for `CreateAutomation`.
message CreateAutomationRequest {
  // Required. The parent collection in which the `Automation` must be created.
  // The format is
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/Automation"
    }
  ];

  // Required. ID of the `Automation`.
  string automation_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The `Automation` to create.
  Automation automation = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `UpdateAutomation`.
message UpdateAutomationRequest {
  // Required. Field mask is used to specify the fields to be overwritten by the
  // update in the `Automation` resource. The fields specified in the
  // update_mask are relative to the resource, not the full request. A field
  // will be overwritten if it's in the mask. If the user doesn't provide a mask
  // then all fields are overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The `Automation` to update.
  Automation automation = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, updating a `Automation` that does not exist will
  // result in the creation of a new `Automation`.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the request is validated and the user is provided
  // with an expected result, but no actual change is made.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `DeleteAutomation`.
message DeleteAutomationRequest {
  // Required. The name of the `Automation` to delete. The format is
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/automations/{automation_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Automation"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server knows to ignore the
  // request if it has already been completed. The server guarantees that for
  // at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, then deleting an already deleted or non-existing
  // `Automation` will succeed.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and verify whether the resource
  // exists, but do not actually post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The weak etag of the request.
  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The request object for `ListAutomations`.
message ListAutomationsRequest {
  // Required. The parent `Delivery Pipeline`, which owns this collection of
  // automations. Format must be
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/Automation"
    }
  ];

  // The maximum number of automations to return. The service may return
  // fewer than this value. If unspecified, at most 50 automations will
  // be returned. The maximum value is 1000; values above 1000 will be set
  // to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListAutomations` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match
  // the call that provided the page token.
  string page_token = 3;

  // Filter automations to be returned. All fields can be used in the
  // filter.
  string filter = 4;

  // Field to sort by.
  string order_by = 5;
}

// The response object from `ListAutomations`.
message ListAutomationsResponse {
  // The `Automation` objects.
  repeated Automation automations = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// The request object for `GetAutomation`
message GetAutomationRequest {
  // Required. Name of the `Automation`. Format must be
  // `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/automations/{automation_name}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/Automation"
    }
  ];
}

// An `AutomationRun` resource in the Cloud Deploy API.
//
// An `AutomationRun` represents an execution instance of an
// automation rule.
message AutomationRun {
  option (google.api.resource) = {
    type: "clouddeploy.googleapis.com/AutomationRun"
    pattern: "projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRuns/{automation_run}"
  };

  // Valid state of an `AutomationRun`.
  enum State {
    // The `AutomationRun` has an unspecified state.
    STATE_UNSPECIFIED = 0;

    // The `AutomationRun` has succeeded.
    SUCCEEDED = 1;

    // The `AutomationRun` was cancelled.
    CANCELLED = 2;

    // The `AutomationRun` has failed.
    FAILED = 3;

    // The `AutomationRun` is in progress.
    IN_PROGRESS = 4;

    // The `AutomationRun` is pending.
    PENDING = 5;

    // The `AutomationRun` was aborted.
    ABORTED = 6;
  }

  // Output only. Name of the `AutomationRun`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRuns/{automation_run}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the `AutomationRun` was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which the automationRun was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The weak etag of the `AutomationRun` resource.
  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the user-managed IAM service account that
  // performs the operations against Cloud Deploy resources.
  string service_account = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Snapshot of the Automation taken at AutomationRun creation
  // time.
  Automation automation_snapshot = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the source target that initiates the
  // `AutomationRun`. The value of this field is the last segment of a target
  // name.
  string target_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state of the `AutomationRun`.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Explains the current state of the `AutomationRun`. Present
  // only when an explanation is needed.
  string state_description = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Contains information about what policies prevented the
  // `AutomationRun` from proceeding.
  PolicyViolation policy_violation = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time the `AutomationRun` expires. An `AutomationRun` expires
  // after 14 days from its creation date.
  google.protobuf.Timestamp expire_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the automation rule that initiated the operation.
  string rule_id = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of the automation that initiated the operation.
  string automation_id = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The operation that the `AutomationRun` will perform.
  oneof operation {
    // Output only. Promotes a release to a specified 'Target'.
    PromoteReleaseOperation promote_release_operation = 13
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Advances a rollout to the next phase.
    AdvanceRolloutOperation advance_rollout_operation = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Repairs a failed 'Rollout'.
    RepairRolloutOperation repair_rollout_operation = 17
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Promotes a release to a specified 'Target' as defined in a
    // Timed Promote Release rule.
    TimedPromoteReleaseOperation timed_promote_release_operation = 19
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Earliest time the `AutomationRun` will attempt to resume.
  // Wait-time is configured by `wait` in automation rule.
  google.protobuf.Timestamp wait_until_time = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains the information of an automated promote-release operation.
message PromoteReleaseOperation {
  // Output only. The ID of the target that represents the promotion stage to
  // which the release will be promoted. The value of this field is the last
  // segment of a target name.
  string target_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. How long the operation will be paused.
  google.protobuf.Duration wait = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the rollout that initiates the `AutomationRun`.
  string rollout = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The starting phase of the rollout created by this operation.
  string phase = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains the information of an automated advance-rollout operation.
message AdvanceRolloutOperation {
  // Output only. The phase of a deployment that initiated the operation.
  string source_phase = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. How long the operation will be paused.
  google.protobuf.Duration wait = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the rollout that initiates the `AutomationRun`.
  string rollout = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The phase the rollout will be advanced to.
  string destination_phase = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains the information for an automated `repair rollout` operation.
message RepairRolloutOperation {
  // Output only. The name of the rollout that initiates the `AutomationRun`.
  string rollout = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The index of the current repair action in the repair sequence.
  int64 current_repair_phase_index = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Records of the repair attempts. Each repair phase may have
  // multiple retry attempts or single rollback attempt.
  repeated RepairPhase repair_phases = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The phase ID of the phase that includes the job being
  // repaired.
  string phase_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The job ID for the Job to repair.
  string job_id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains the information of an automated timed promote-release operation.
message TimedPromoteReleaseOperation {
  // Output only. The ID of the target that represents the promotion stage to
  // which the release will be promoted. The value of this field is the last
  // segment of a target name.
  string target_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the release to be promoted.
  string release = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The starting phase of the rollout created by this operation.
  string phase = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// RepairPhase tracks the repair attempts that have been made for
// each `RepairPhaseConfig` specified in the `Automation` resource.
message RepairPhase {
  // The `RepairPhase` type and the information for that type.
  oneof repair_phase {
    // Output only. Records of the retry attempts for retry repair mode.
    RetryPhase retry = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Rollback attempt for rollback repair mode .
    RollbackAttempt rollback = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// RetryPhase contains the retry attempts and the metadata for initiating a
// new attempt.
message RetryPhase {
  // Output only. The number of attempts that have been made.
  int64 total_attempts = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The pattern of how the wait time of the retry attempt is
  // calculated.
  BackoffMode backoff_mode = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Detail of a retry action.
  repeated RetryAttempt attempts = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// RetryAttempt represents an action of retrying the failed Cloud Deploy job.
message RetryAttempt {
  // Output only. The index of this retry attempt.
  int64 attempt = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. How long the operation will be paused.
  google.protobuf.Duration wait = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Valid state of this retry action.
  RepairState state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Description of the state of the Retry.
  string state_desc = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// RollbackAttempt represents an action of rolling back a Cloud Deploy 'Target'.
message RollbackAttempt {
  // Output only. The phase to which the rollout will be rolled back to.
  string destination_phase = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the rollback `Rollout` to create.
  string rollout_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Valid state of this rollback action.
  RepairState state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Description of the state of the Rollback.
  string state_desc = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If active rollout exists on the target, abort this rollback.
  bool disable_rollback_if_rollout_pending = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request object for `ListAutomationRuns`.
message ListAutomationRunsRequest {
  // Required. The parent `Delivery Pipeline`, which owns this collection of
  // automationRuns. Format must be
  // `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "clouddeploy.googleapis.com/AutomationRun"
    }
  ];

  // The maximum number of automationRuns to return. The service may return
  // fewer than this value. If unspecified, at most 50 automationRuns will
  // be returned. The maximum value is 1000; values above 1000 will be set
  // to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListAutomationRuns` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters match
  // the call that provided the page token.
  string page_token = 3;

  // Filter automationRuns to be returned. All fields can be used in the
  // filter.
  string filter = 4;

  // Field to sort by.
  string order_by = 5;
}

// The response object from `ListAutomationRuns`.
message ListAutomationRunsResponse {
  // The `AutomationRuns` objects.
  repeated AutomationRun automation_runs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// The request object for `GetAutomationRun`
message GetAutomationRunRequest {
  // Required. Name of the `AutomationRun`. Format must be
  // `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRuns/{automation_run}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/AutomationRun"
    }
  ];
}

// The request object used by `CancelAutomationRun`.
message CancelAutomationRunRequest {
  // Required. Name of the `AutomationRun`. Format is
  // `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRuns/{automation_run}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "clouddeploy.googleapis.com/AutomationRun"
    }
  ];
}

// The response object from `CancelAutomationRun`.
message CancelAutomationRunResponse {}

// The support state of a specific Skaffold version.
enum SkaffoldSupportState {
  // Default value. This value is unused.
  SKAFFOLD_SUPPORT_STATE_UNSPECIFIED = 0;

  // This Skaffold version is currently supported.
  SKAFFOLD_SUPPORT_STATE_SUPPORTED = 1;

  // This Skaffold version is in maintenance mode.
  SKAFFOLD_SUPPORT_STATE_MAINTENANCE_MODE = 2;

  // This Skaffold version is no longer supported.
  SKAFFOLD_SUPPORT_STATE_UNSUPPORTED = 3;
}

// The pattern of how wait time is increased.
enum BackoffMode {
  // No WaitMode is specified.
  BACKOFF_MODE_UNSPECIFIED = 0;

  // Increases the wait time linearly.
  BACKOFF_MODE_LINEAR = 1;

  // Increases the wait time exponentially.
  BACKOFF_MODE_EXPONENTIAL = 2;
}

// Valid state of a repair attempt.
enum RepairState {
  // The `repair` has an unspecified state.
  REPAIR_STATE_UNSPECIFIED = 0;

  // The `repair` action has succeeded.
  REPAIR_STATE_SUCCEEDED = 1;

  // The `repair` action was cancelled.
  REPAIR_STATE_CANCELLED = 2;

  // The `repair` action has failed.
  REPAIR_STATE_FAILED = 3;

  // The `repair` action is in progress.
  REPAIR_STATE_IN_PROGRESS = 4;

  // The `repair` action is pending.
  REPAIR_STATE_PENDING = 5;

  // The `repair` action was aborted.
  REPAIR_STATE_ABORTED = 7;
}
