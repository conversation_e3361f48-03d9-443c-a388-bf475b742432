type: google.api.Service
config_version: 3
name: connectgateway.googleapis.com
title: Connect Gateway API

apis:
- name: google.cloud.gkeconnect.gateway.v1.GatewayControl

documentation:
  summary: |-
    The Connect Gateway service allows connectivity from external parties to
    connected Kubernetes clusters.

authentication:
  rules:
  - selector: google.cloud.gkeconnect.gateway.v1.GatewayControl.GenerateCredentials
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1618911
  documentation_uri: https://cloud.google.com/kubernetes-engine/enterprise/multicluster-management/gateway
  api_short_name: connectgateway
  github_label: 'api: connectgateway'
  doc_tag_prefix: connectgateway
  organization: CLOUD
  library_settings:
  - version: google.cloud.gkeconnect.gateway.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
