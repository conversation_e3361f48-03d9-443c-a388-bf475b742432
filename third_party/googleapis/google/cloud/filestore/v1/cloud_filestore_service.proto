// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.filestore.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/common/operation_metadata.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.Filestore.V1";
option go_package = "cloud.google.com/go/filestore/apiv1/filestorepb;filestorepb";
option java_multiple_files = true;
option java_outer_classname = "CloudFilestoreServiceProto";
option java_package = "com.google.cloud.filestore.v1";
option php_namespace = "Google\\Cloud\\Filestore\\V1";

// Configures and manages Filestore resources.
//
// Filestore Manager v1.
//
// The `file.googleapis.com` service implements the Filestore API and
// defines the following resource model for managing instances:
// * The service works with a collection of cloud projects, named: `/projects/*`
// * Each project has a collection of available locations, named: `/locations/*`
// * Each location has a collection of instances and backups, named:
// `/instances/*` and `/backups/*` respectively.
// * As such, Filestore instances are resources of the form:
//   `/projects/{project_number}/locations/{location_id}/instances/{instance_id}`
//   and backups are resources of the form:
//   `/projects/{project_number}/locations/{location_id}/backup/{backup_id}`
//
// Note that location_id must be a Google Cloud `zone` for instances, but
// a Google Cloud `region` for backups; for example:
// * `projects/12345/locations/us-central1-c/instances/my-filestore`
// * `projects/12345/locations/us-central1/backups/my-backup`
service CloudFilestoreManager {
  option (google.api.default_host) = "file.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all instances in a project for either a specified location
  // or for all locations.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a specific instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an instance.
  // When creating from a backup, the capacity of the new instance needs to be
  // equal to or larger than the capacity of the backup (and also equal to or
  // larger than the minimum capacity of the tier).
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Updates the settings of a specific instance.
  rpc UpdateInstance(UpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{instance.name=projects/*/locations/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Restores an existing instance's file share from a backup.
  //
  // The capacity of the instance needs to be equal to or larger than the
  // capacity of the backup (and also equal to or larger than the minimum
  // capacity of the tier).
  rpc RestoreInstance(RestoreInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:restore"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Revert an existing instance's file system to a specified snapshot.
  rpc RevertInstance(RevertInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:revert"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Deletes an instance.
  rpc DeleteInstance(DeleteInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Lists all snapshots in a project for either a specified location
  // or for all locations.
  rpc ListSnapshots(ListSnapshotsRequest) returns (ListSnapshotsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/instances/*}/snapshots"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a specific snapshot.
  rpc GetSnapshot(GetSnapshotRequest) returns (Snapshot) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*/snapshots/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a snapshot.
  rpc CreateSnapshot(CreateSnapshotRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/instances/*}/snapshots"
      body: "snapshot"
    };
    option (google.api.method_signature) = "parent,snapshot,snapshot_id";
    option (google.longrunning.operation_info) = {
      response_type: "Snapshot"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Deletes a snapshot.
  rpc DeleteSnapshot(DeleteSnapshotRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/instances/*/snapshots/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Updates the settings of a specific snapshot.
  rpc UpdateSnapshot(UpdateSnapshotRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{snapshot.name=projects/*/locations/*/instances/*/snapshots/*}"
      body: "snapshot"
    };
    option (google.api.method_signature) = "snapshot,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Snapshot"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Lists all backups in a project for either a specified location or for all
  // locations.
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a specific backup.
  rpc GetBackup(GetBackupRequest) returns (Backup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a backup.
  rpc CreateBackup(CreateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/backups"
      body: "backup"
    };
    option (google.api.method_signature) = "parent,backup,backup_id";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Deletes a backup.
  rpc DeleteBackup(DeleteBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }

  // Updates the settings of a specific backup.
  rpc UpdateBackup(UpdateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup.name=projects/*/locations/*/backups/*}"
      body: "backup"
    };
    option (google.api.method_signature) = "backup,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "google.cloud.common.OperationMetadata"
    };
  }
}

// Network configuration for the instance.
message NetworkConfig {
  // Internet protocol versions supported by Filestore.
  enum AddressMode {
    // Internet protocol not set.
    ADDRESS_MODE_UNSPECIFIED = 0;

    // Use the IPv4 internet protocol.
    MODE_IPV4 = 1;
  }

  // Available connection modes.
  enum ConnectMode {
    // Not set.
    CONNECT_MODE_UNSPECIFIED = 0;

    // Connect via direct peering to the Filestore service.
    DIRECT_PEERING = 1;

    // Connect to your Filestore instance using Private Service
    // Access. Private services access provides an IP address range for multiple
    // Google Cloud services, including Filestore.
    PRIVATE_SERVICE_ACCESS = 2;
  }

  // The name of the Google Compute Engine
  // [VPC network](https://cloud.google.com/vpc/docs/vpc) to which the
  // instance is connected.
  string network = 1;

  // Internet protocol versions for which the instance has IP addresses
  // assigned. For this version, only MODE_IPV4 is supported.
  repeated AddressMode modes = 3;

  // Optional, reserved_ip_range can have one of the following two types of
  // values.
  //
  // * CIDR range value when using DIRECT_PEERING connect mode.
  // * [Allocated IP address
  // range](https://cloud.google.com/compute/docs/ip-addresses/reserve-static-internal-ip-address)
  // when using PRIVATE_SERVICE_ACCESS connect mode.
  //
  // When the name of an allocated IP address range is specified, it must be one
  // of the ranges associated with the private service access connection.
  // When specified as a direct CIDR value, it must be a /29 CIDR block for
  // Basic tier, a /24 CIDR block for High Scale tier, or a /26 CIDR block for
  // Enterprise tier in one of the [internal IP address
  // ranges](https://www.arin.net/reference/research/statistics/address_filters/)
  // that identifies the range of IP addresses reserved for this instance. For
  // example, 10.0.0.0/29, ***********/24 or ***********/26, respectively. The
  // range you specify can't overlap with either existing subnets or assigned IP
  // address ranges for other Filestore instances in the selected VPC
  // network.
  string reserved_ip_range = 4;

  // Output only. IPv4 addresses in the format
  // `{octet1}.{octet2}.{octet3}.{octet4}` or IPv6 addresses in the format
  // `{block1}:{block2}:{block3}:{block4}:{block5}:{block6}:{block7}:{block8}`.
  repeated string ip_addresses = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The network connect mode of the Filestore instance.
  // If not provided, the connect mode defaults to DIRECT_PEERING.
  ConnectMode connect_mode = 6;
}

// File share configuration for the instance.
message FileShareConfig {
  // Required. The name of the file share. Must use 1-16 characters for the
  // basic service tier and 1-63 characters for all other service tiers.
  // Must use lowercase letters, numbers, or underscores `[a-z0-9_]`. Must
  // start with a letter. Immutable.
  string name = 1;

  // File share capacity in gigabytes (GB).
  // Filestore defines 1 GB as 1024^3 bytes.
  int64 capacity_gb = 2;

  // The source that this file share has been restored from. Empty if the file
  // share is created from scratch.
  oneof source {
    // The resource name of the backup, in the format
    // `projects/{project_number}/locations/{location_id}/backups/{backup_id}`,
    // that this file share has been restored from.
    string source_backup = 8 [
      (google.api.resource_reference) = { type: "file.googleapis.com/Backup" }
    ];
  }

  // Nfs Export Options.
  // There is a limit of 10 export options per file share.
  repeated NfsExportOptions nfs_export_options = 7;
}

// NFS export options specifications.
message NfsExportOptions {
  // The access mode.
  enum AccessMode {
    // AccessMode not set.
    ACCESS_MODE_UNSPECIFIED = 0;

    // The client can only read the file share.
    READ_ONLY = 1;

    // The client can read and write the file share (default).
    READ_WRITE = 2;
  }

  // The squash mode.
  enum SquashMode {
    // SquashMode not set.
    SQUASH_MODE_UNSPECIFIED = 0;

    // The Root user has root access to the file share (default).
    NO_ROOT_SQUASH = 1;

    // The Root user has squashed access to the anonymous uid/gid.
    ROOT_SQUASH = 2;
  }

  // List of either an IPv4 addresses in the format
  // `{octet1}.{octet2}.{octet3}.{octet4}` or CIDR ranges in the format
  // `{octet1}.{octet2}.{octet3}.{octet4}/{mask size}` which may mount the
  // file share.
  // Overlapping IP ranges are not allowed, both within and across
  // NfsExportOptions. An error will be returned.
  // The limit is 64 IP ranges/addresses for each FileShareConfig among all
  // NfsExportOptions.
  repeated string ip_ranges = 1;

  // Either READ_ONLY, for allowing only read requests on the exported
  // directory, or READ_WRITE, for allowing both read and write requests.
  // The default is READ_WRITE.
  AccessMode access_mode = 2;

  // Either NO_ROOT_SQUASH, for allowing root access on the exported directory,
  // or ROOT_SQUASH, for not allowing root access. The default is
  // NO_ROOT_SQUASH.
  SquashMode squash_mode = 3;

  // An integer representing the anonymous user id with a default value of
  // 65534.
  // Anon_uid may only be set with squash_mode of ROOT_SQUASH.  An error will be
  // returned if this field is specified for other squash_mode settings.
  int64 anon_uid = 4;

  // An integer representing the anonymous group id with a default value of
  // 65534.
  // Anon_gid may only be set with squash_mode of ROOT_SQUASH.  An error will be
  // returned if this field is specified for other squash_mode settings.
  int64 anon_gid = 5;
}

// A Filestore instance.
message Instance {
  option (google.api.resource) = {
    type: "file.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
  };

  // The instance state.
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // The instance is being created.
    CREATING = 1;

    // The instance is available for use.
    READY = 2;

    // Work is being done on the instance. You can get further details from the
    // `statusMessage` field of the `Instance` resource.
    REPAIRING = 3;

    // The instance is shutting down.
    DELETING = 4;

    // The instance is experiencing an issue and might be unusable. You can get
    // further details from the `statusMessage` field of the `Instance`
    // resource.
    ERROR = 6;

    // The instance is restoring a backup to an existing file share and may be
    // unusable during this time.
    RESTORING = 7;

    // The instance is suspended. You can get further details from
    // the `suspension_reasons` field of the `Instance` resource.
    SUSPENDED = 8;

    // The instance is in the process of becoming suspended.
    SUSPENDING = 9;

    // The instance is in the process of becoming active.
    RESUMING = 10;

    // The instance is reverting to a snapshot.
    REVERTING = 12;
  }

  // Available service tiers.
  enum Tier {
    // Not set.
    TIER_UNSPECIFIED = 0;

    // STANDARD tier. BASIC_HDD is the preferred term for this tier.
    STANDARD = 1;

    // PREMIUM tier. BASIC_SSD is the preferred term for this tier.
    PREMIUM = 2;

    // BASIC instances offer a maximum capacity of 63.9 TB.
    // BASIC_HDD is an alias for STANDARD Tier, offering economical
    // performance backed by HDD.
    BASIC_HDD = 3;

    // BASIC instances offer a maximum capacity of 63.9 TB.
    // BASIC_SSD is an alias for PREMIUM Tier, and offers improved
    // performance backed by SSD.
    BASIC_SSD = 4;

    // HIGH_SCALE instances offer expanded capacity and performance scaling
    // capabilities.
    HIGH_SCALE_SSD = 5;

    // ENTERPRISE instances offer the features and availability needed for
    // mission-critical workloads.
    ENTERPRISE = 6;

    // ZONAL instances offer expanded capacity and performance scaling
    // capabilities.
    ZONAL = 7;

    // REGIONAL instances offer the features and availability needed for
    // mission-critical workloads.
    REGIONAL = 8;
  }

  // SuspensionReason contains the possible reasons for a suspension.
  enum SuspensionReason {
    // Not set.
    SUSPENSION_REASON_UNSPECIFIED = 0;

    // The KMS key used by the instance is either revoked or denied access to.
    KMS_KEY_ISSUE = 1;
  }

  // Output only. The resource name of the instance, in the format
  // `projects/{project}/locations/{location}/instances/{instance}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The description of the instance (2048 characters or less).
  string description = 2;

  // Output only. The instance state.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the instance state, if available.
  string status_message = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The service tier of the instance.
  Tier tier = 8;

  // Resource labels to represent user provided metadata.
  map<string, string> labels = 9;

  // File system shares on the instance.
  // For this version, only a single file share is supported.
  repeated FileShareConfig file_shares = 10;

  // VPC networks to which the instance is connected.
  // For this version, only a single network is supported.
  repeated NetworkConfig networks = 11;

  // Server-specified ETag for the instance resource to prevent simultaneous
  // updates from overwriting each other.
  string etag = 12;

  // Output only. Reserved for future use.
  google.protobuf.BoolValue satisfies_pzs = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // KMS key name used for data encryption.
  string kms_key_name = 14;

  // Output only. Field indicates all the reasons the instance is in "SUSPENDED"
  // state.
  repeated SuspensionReason suspension_reasons = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CreateInstanceRequest creates an instance.
message CreateInstanceRequest {
  // Required. The instance's project and location, in the format
  // `projects/{project_id}/locations/{location}`. In Filestore,
  // locations map to Google Cloud zones, for example **us-west1-b**.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The name of the instance to create.
  // The name must be unique for the specified project and location.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. An [instance resource][google.cloud.filestore.v1.Instance]
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];
}

// GetInstanceRequest gets the state of an instance.
message GetInstanceRequest {
  // Required. The instance resource name, in the format
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Instance" }
  ];
}

// UpdateInstanceRequest updates the settings of an instance.
message UpdateInstanceRequest {
  // Mask of fields to update.  At least one path must be supplied in this
  // field.  The elements of the repeated paths field may only include these
  // fields:
  //
  // * "description"
  // * "file_shares"
  // * "labels"
  google.protobuf.FieldMask update_mask = 1;

  // Only fields specified in update_mask are updated.
  Instance instance = 2;
}

// RestoreInstanceRequest restores an existing instance's file share from a
// backup.
message RestoreInstanceRequest {
  // Required. The resource name of the instance, in the format
  // `projects/{project_number}/locations/{location_id}/instances/{instance_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Instance" }
  ];

  // Required. Name of the file share in the Filestore instance that the backup
  // is being restored to.
  string file_share = 2 [(google.api.field_behavior) = REQUIRED];

  oneof source {
    // The resource name of the backup, in the format
    // `projects/{project_number}/locations/{location_id}/backups/{backup_id}`.
    string source_backup = 3 [
      (google.api.resource_reference) = { type: "file.googleapis.com/Backup" }
    ];
  }
}

// RevertInstanceRequest reverts the given instance's file share to the
// specified snapshot.
message RevertInstanceRequest {
  // Required.
  // `projects/{project_id}/locations/{location_id}/instances/{instance_id}`.
  // The resource name of the instance, in the format
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Instance" }
  ];

  // Required. The snapshot resource ID, in the format 'my-snapshot', where the
  // specified ID is the {snapshot_id} of the fully qualified name like
  // `projects/{project_id}/locations/{location_id}/instances/{instance_id}/snapshots/{snapshot_id}`
  string target_snapshot_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// DeleteInstanceRequest deletes an instance.
message DeleteInstanceRequest {
  // Required. The instance resource name, in the format
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Instance" }
  ];

  // If set to true, all snapshots of the instance will also be deleted.
  // (Otherwise, the request will only work if the instance has no snapshots.)
  bool force = 2;
}

// ListInstancesRequest lists instances.
message ListInstancesRequest {
  // Required. The project and location for which to retrieve instance
  // information, in the format `projects/{project_id}/locations/{location}`. In
  // Cloud Filestore, locations map to Google Cloud zones, for example
  // **us-west1-b**. To retrieve instance information for all locations, use "-"
  // for the
  // `{location}` value.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 4;

  // List filter.
  string filter = 5;
}

// ListInstancesResponse is the result of ListInstancesRequest.
message ListInstancesResponse {
  // A list of instances in the project for the specified location.
  //
  // If the `{location}` value in the request is "-", the response contains a
  // list of instances from all locations. If any location is unreachable, the
  // response will only return instances in reachable locations and the
  // "unreachable" field will be populated with a list of unreachable locations.
  repeated Instance instances = 1;

  // The token you can use to retrieve the next page of results. Not returned
  // if there are no more results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A Filestore snapshot.
message Snapshot {
  option (google.api.resource) = {
    type: "file.googleapis.com/Snapshot"
    pattern: "projects/{project}/locations/{location}/instances/{instance}/snapshots/{snapshot}"
  };

  // The snapshot state.
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // Snapshot is being created.
    CREATING = 1;

    // Snapshot is available for use.
    READY = 2;

    // Snapshot is being deleted.
    DELETING = 3;
  }

  // Output only. The resource name of the snapshot, in the format
  // `projects/{project_id}/locations/{location_id}/instances/{instance_id}/snapshots/{snapshot_id}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A description of the snapshot with 2048 characters or less.
  // Requests with longer descriptions will be rejected.
  string description = 2;

  // Output only. The snapshot state.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the snapshot was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource labels to represent user provided metadata.
  map<string, string> labels = 5;

  // Output only. The amount of bytes needed to allocate a full copy of the
  // snapshot content
  int64 filesystem_used_bytes = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CreateSnapshotRequest creates a snapshot.
message CreateSnapshotRequest {
  // Required. The Filestore Instance to create the snapshots of, in the format
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Instance" }
  ];

  // Required. The ID to use for the snapshot.
  // The ID must be unique within the specified instance.
  //
  // This value must start with a lowercase letter followed by up to 62
  // lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
  string snapshot_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A snapshot resource.
  Snapshot snapshot = 3 [(google.api.field_behavior) = REQUIRED];
}

// GetSnapshotRequest gets the state of a snapshot.
message GetSnapshotRequest {
  // Required. The snapshot resource name, in the format
  // `projects/{project_id}/locations/{location}/instances/{instance_id}/snapshots/{snapshot_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Snapshot" }
  ];
}

// DeleteSnapshotRequest deletes a snapshot.
message DeleteSnapshotRequest {
  // Required. The snapshot resource name, in the format
  // `projects/{project_id}/locations/{location}/instances/{instance_id}/snapshots/{snapshot_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Snapshot" }
  ];
}

// UpdateSnapshotRequest updates description and/or labels for a snapshot.
message UpdateSnapshotRequest {
  // Required. Mask of fields to update. At least one path must be supplied in
  // this field.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A snapshot resource.
  Snapshot snapshot = 2 [(google.api.field_behavior) = REQUIRED];
}

// ListSnapshotsRequest lists snapshots.
message ListSnapshotsRequest {
  // Required. The instance for which to retrieve snapshot information,
  // in the format
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Instance" }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 4;

  // List filter.
  string filter = 5;
}

// ListSnapshotsResponse is the result of ListSnapshotsRequest.
message ListSnapshotsResponse {
  // A list of snapshots in the project for the specified instance.
  repeated Snapshot snapshots = 1;

  // The token you can use to retrieve the next page of results. Not returned
  // if there are no more results in the list.
  string next_page_token = 2;
}

// A Filestore backup.
message Backup {
  option (google.api.resource) = {
    type: "file.googleapis.com/Backup"
    pattern: "projects/{project}/locations/{location}/backups/{backup}"
  };

  // The backup state.
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // Backup is being created.
    CREATING = 1;

    // Backup has been taken and the operation is being finalized. At this
    // point, changes to the file share will not be reflected in the backup.
    FINALIZING = 2;

    // Backup is available for use.
    READY = 3;

    // Backup is being deleted.
    DELETING = 4;

    // Backup is not valid and cannot be used for creating new instances or
    // restoring existing instances.
    INVALID = 5;
  }

  // Output only. The resource name of the backup, in the format
  // `projects/{project_number}/locations/{location_id}/backups/{backup_id}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A description of the backup with 2048 characters or less.
  // Requests with longer descriptions will be rejected.
  string description = 2;

  // Output only. The backup state.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the backup was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource labels to represent user provided metadata.
  map<string, string> labels = 5;

  // Output only. Capacity of the source file share when the backup was created.
  int64 capacity_gb = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The size of the storage used by the backup. As backups share
  // storage, this number is expected to change with backup creation/deletion.
  int64 storage_bytes = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The resource name of the source Filestore instance, in the format
  // `projects/{project_number}/locations/{location_id}/instances/{instance_id}`,
  // used to create this backup.
  string source_instance = 8 [
    (google.api.resource_reference) = { type: "file.googleapis.com/Instance" }
  ];

  // Name of the file share in the source Filestore instance that the
  // backup is created from.
  string source_file_share = 9;

  // Output only. The service tier of the source Filestore instance that this
  // backup is created from.
  Instance.Tier source_instance_tier = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Amount of bytes that will be downloaded if the backup is
  // restored. This may be different than storage bytes, since sequential
  // backups of the same disk will share storage.
  int64 download_bytes = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  google.protobuf.BoolValue satisfies_pzs = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. KMS key name used for data encryption.
  string kms_key = 13 [(google.api.field_behavior) = IMMUTABLE];
}

// CreateBackupRequest creates a backup.
message CreateBackupRequest {
  // Required. The backup's project and location, in the format
  // `projects/{project_number}/locations/{location}`. In Filestore,
  // backup locations map to Google Cloud regions, for example **us-west1**.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. A [backup resource][google.cloud.filestore.v1.Backup]
  Backup backup = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the backup.
  // The ID must be unique within the specified project and location.
  //
  // This value must start with a lowercase letter followed by up to 62
  // lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
  // Values that do not match this pattern will trigger an INVALID_ARGUMENT
  // error.
  string backup_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// DeleteBackupRequest deletes a backup.
message DeleteBackupRequest {
  // Required. The backup resource name, in the format
  // `projects/{project_number}/locations/{location}/backups/{backup_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Backup" }
  ];
}

// UpdateBackupRequest updates description and/or labels for a backup.
message UpdateBackupRequest {
  // Required. A [backup resource][google.cloud.filestore.v1.Backup]
  Backup backup = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Mask of fields to update.  At least one path must be supplied in
  // this field.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// GetBackupRequest gets the state of a backup.
message GetBackupRequest {
  // Required. The backup resource name, in the format
  // `projects/{project_number}/locations/{location}/backups/{backup_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "file.googleapis.com/Backup" }
  ];
}

// ListBackupsRequest lists backups.
message ListBackupsRequest {
  // Required. The project and location for which to retrieve backup
  // information, in the format
  // `projects/{project_number}/locations/{location}`. In Filestore, backup
  // locations map to Google Cloud regions, for example **us-west1**. To
  // retrieve backup information for all locations, use "-" for the
  // `{location}` value.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 4;

  // List filter.
  string filter = 5;
}

// ListBackupsResponse is the result of ListBackupsRequest.
message ListBackupsResponse {
  // A list of backups in the project for the specified location.
  //
  // If the `{location}` value in the request is "-", the response contains a
  // list of backups from all locations. If any location is unreachable, the
  // response will only return backups in reachable locations and the
  // "unreachable" field will be populated with a list of unreachable
  // locations.
  repeated Backup backups = 1;

  // The token you can use to retrieve the next page of results. Not returned
  // if there are no more results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}
