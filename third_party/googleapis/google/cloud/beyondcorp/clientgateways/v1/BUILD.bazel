# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "clientgateways_proto",
    srcs = [
        "client_gateways_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "clientgateways_proto_with_info",
    deps = [
        ":clientgateways_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

java_proto_library(
    name = "clientgateways_java_proto",
    deps = [":clientgateways_proto"],
)

java_grpc_library(
    name = "clientgateways_java_grpc",
    srcs = [":clientgateways_proto"],
    deps = [":clientgateways_java_proto"],
)

java_gapic_library(
    name = "clientgateways_java_gapic",
    srcs = [":clientgateways_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "beyondcorp-clientgateways_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    test_deps = [
        ":clientgateways_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":clientgateways_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "clientgateways_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.beyondcorp.clientgateways.v1.ClientGatewaysServiceClientTest",
    ],
    runtime_deps = [":clientgateways_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-beyondcorp-clientgateways-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":clientgateways_java_gapic",
        ":clientgateways_java_grpc",
        ":clientgateways_java_proto",
        ":clientgateways_proto",
    ],
)

go_proto_library(
    name = "clientgateways_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/beyondcorp/clientgateways/apiv1/clientgatewayspb",
    protos = [":clientgateways_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "clientgateways_go_gapic",
    srcs = [":clientgateways_proto_with_info"],
    grpc_service_config = "beyondcorp-clientgateways_grpc_service_config.json",
    importpath = "cloud.google.com/go/beyondcorp/clientgateways/apiv1;clientgateways",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    deps = [
        ":clientgateways_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-beyondcorp-clientgateways-v1-go",
    deps = [
        ":clientgateways_go_gapic",
        ":clientgateways_go_gapic_srcjar-metadata.srcjar",
        ":clientgateways_go_gapic_srcjar-snippets.srcjar",
        ":clientgateways_go_gapic_srcjar-test.srcjar",
        ":clientgateways_go_proto",
    ],
)

py_gapic_library(
    name = "clientgateways_py_gapic",
    srcs = [":clientgateways_proto"],
    grpc_service_config = "beyondcorp-clientgateways_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-beyondcorp-clientgateways",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=beyondcorp_clientgateways",
    ],
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "clientgateways_py_gapic_test",
    srcs = [
        "clientgateways_py_gapic_pytest.py",
        "clientgateways_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":clientgateways_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "beyondcorp-clientgateways-v1-py",
    deps = [
        ":clientgateways_py_gapic",
    ],
)

php_proto_library(
    name = "clientgateways_php_proto",
    deps = [":clientgateways_proto"],
)

php_gapic_library(
    name = "clientgateways_php_gapic",
    srcs = [":clientgateways_proto_with_info"],
    grpc_service_config = "beyondcorp-clientgateways_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [":clientgateways_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-clientgateways-v1-php",
    deps = [
        ":clientgateways_php_gapic",
        ":clientgateways_php_proto",
    ],
)

nodejs_gapic_library(
    name = "clientgateways_nodejs_gapic",
    package_name = "@google-cloud/clientgateways",
    src = ":clientgateways_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "beyondcorp-clientgateways_grpc_service_config.json",
    package = "google.cloud.beyondcorp.clientgateways.v1",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "beyondcorp-clientgateways-v1-nodejs",
    deps = [
        ":clientgateways_nodejs_gapic",
        ":clientgateways_proto",
    ],
)

ruby_proto_library(
    name = "clientgateways_ruby_proto",
    deps = [":clientgateways_proto"],
)

ruby_grpc_library(
    name = "clientgateways_ruby_grpc",
    srcs = [":clientgateways_proto"],
    deps = [":clientgateways_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "clientgateways_ruby_gapic",
    srcs = [":clientgateways_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-beyond_corp-client_gateways-v1",
        "ruby-cloud-product-url=https://cloud.google.com/beyondcorp/",
        "ruby-cloud-api-id=beyondcorp.googleapis.com",
        "ruby-cloud-api-shortname=beyondcorp",
        "ruby-cloud-wrapper-gem-override=google-cloud-beyond_corp",
    ],
    grpc_service_config = "beyondcorp-clientgateways_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Beyondcorp Enterprise provides identity and context aware access controls for enterprise resources and enables zero-trust access. Using the Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity using the App Connector hybrid connectivity solution.",
    ruby_cloud_title = "BeyondCorp ClientGateways V1",
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":clientgateways_ruby_grpc",
        ":clientgateways_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-clientgateways-v1-ruby",
    deps = [
        ":clientgateways_ruby_gapic",
        ":clientgateways_ruby_grpc",
        ":clientgateways_ruby_proto",
    ],
)

csharp_proto_library(
    name = "clientgateways_csharp_proto",
    deps = [":clientgateways_proto"],
)

csharp_grpc_library(
    name = "clientgateways_csharp_grpc",
    srcs = [":clientgateways_proto"],
    deps = [":clientgateways_csharp_proto"],
)

csharp_gapic_library(
    name = "clientgateways_csharp_gapic",
    srcs = [":clientgateways_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "beyondcorp-clientgateways_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":clientgateways_csharp_grpc",
        ":clientgateways_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-clientgateways-v1-csharp",
    deps = [
        ":clientgateways_csharp_gapic",
        ":clientgateways_csharp_grpc",
        ":clientgateways_csharp_proto",
    ],
)

cc_proto_library(
    name = "clientgateways_cc_proto",
    deps = [":clientgateways_proto"],
)

cc_grpc_library(
    name = "clientgateways_cc_grpc",
    srcs = [":clientgateways_proto"],
    grpc_only = True,
    deps = [":clientgateways_cc_proto"],
)
