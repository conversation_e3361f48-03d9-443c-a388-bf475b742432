// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.backupdr.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";

option csharp_namespace = "Google.Cloud.BackupDR.V1";
option go_package = "cloud.google.com/go/backupdr/apiv1/backupdrpb;backupdrpb";
option java_multiple_files = true;
option java_outer_classname = "BackupvaultGceProto";
option java_package = "com.google.cloud.backupdr.v1";
option php_namespace = "Google\\Cloud\\BackupDR\\V1";
option ruby_package = "Google::Cloud::BackupDR::V1";

// ComputeInstanceBackupProperties represents Compute Engine instance backup
// properties.
message ComputeInstanceBackupProperties {
  // An optional text description for the instances that are created from these
  // properties.
  optional string description = 1;

  // A list of tags to apply to the instances that are created from these
  // properties. The tags identify valid sources or targets for network
  // firewalls. The setTags method can modify this list of tags. Each tag within
  // the list must comply with RFC1035 (https://www.ietf.org/rfc/rfc1035.txt).
  optional Tags tags = 2;

  // The machine type to use for instances that are created from these
  // properties.
  optional string machine_type = 3;

  // Enables instances created based on these properties to send packets with
  // source IP addresses other than their own and receive packets with
  // destination IP addresses other than their own. If these instances will be
  // used as an IP gateway or it will be set as the next-hop in a Route
  // resource, specify `true`. If unsure, leave this set to `false`. See the
  // https://cloud.google.com/vpc/docs/using-routes#canipforward
  // documentation for more information.
  optional bool can_ip_forward = 4;

  // An array of network access configurations for this interface.
  repeated NetworkInterface network_interface = 5;

  // An array of disks that are associated with the instances that are created
  // from these properties.
  repeated AttachedDisk disk = 6;

  // The metadata key/value pairs to assign to instances that are created from
  // these properties. These pairs can consist of custom metadata or predefined
  // keys. See https://cloud.google.com/compute/docs/metadata/overview for more
  // information.
  optional Metadata metadata = 7;

  // A list of service accounts with specified scopes. Access tokens for these
  // service accounts are available to the instances that are created from
  // these properties. Use metadata queries to obtain the access tokens for
  // these instances.
  repeated ServiceAccount service_account = 8;

  // Specifies the scheduling options for the instances that are created from
  // these properties.
  optional Scheduling scheduling = 9;

  // A list of guest accelerator cards' type and count to use for instances
  // created from these properties.
  repeated AcceleratorConfig guest_accelerator = 10;

  // Minimum cpu/platform to be used by instances. The instance may be
  // scheduled on the specified or newer cpu/platform. Applicable values are the
  // friendly names of CPU platforms, such as
  // `minCpuPlatform: Intel Haswell` or `minCpuPlatform: Intel Sandy Bridge`.
  // For more information, read
  // https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform.
  optional string min_cpu_platform = 11;

  // KeyRevocationActionType of the instance. Supported options are "STOP" and
  // "NONE". The default value is "NONE" if it is not specified.
  optional KeyRevocationActionType key_revocation_action_type = 12;

  // The source instance used to create this backup. This can be a partial or
  // full URL to the resource. For example, the following are valid values:
  //   -https://www.googleapis.com/compute/v1/projects/project/zones/zone/instances/instance
  //   -projects/project/zones/zone/instances/instance
  optional string source_instance = 13;

  // Labels to apply to instances that are created from these properties.
  map<string, string> labels = 14;
}

// ComputeInstanceRestoreProperties represents Compute Engine instance
// properties to be overridden during restore.
message ComputeInstanceRestoreProperties {
  // The private IPv6 google access type for the VMs.
  enum InstancePrivateIpv6GoogleAccess {
    // Default value. This value is unused.
    INSTANCE_PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED = 0;

    // Each network interface inherits PrivateIpv6GoogleAccess from its
    // subnetwork.
    INHERIT_FROM_SUBNETWORK = 1;

    // Outbound private IPv6 access from VMs in this subnet to Google services.
    // If specified, the subnetwork who is attached to the instance's default
    // network interface will be assigned an internal IPv6 prefix if it doesn't
    // have before.
    ENABLE_OUTBOUND_VM_ACCESS_TO_GOOGLE = 2;

    // Bidirectional private IPv6 access to/from Google services. If
    // specified, the subnetwork who is attached to the instance's default
    // network interface will be assigned an internal IPv6 prefix if it doesn't
    // have before.
    ENABLE_BIDIRECTIONAL_ACCESS_TO_GOOGLE = 3;
  }

  // Required. Name of the compute instance.
  optional string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Controls for advanced machine-related behavior features.
  optional AdvancedMachineFeatures advanced_machine_features = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Allows this instance to send and receive packets with
  // non-matching destination or source IPs.
  optional bool can_ip_forward = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Controls Confidential compute options on the instance
  optional ConfidentialInstanceConfig confidential_instance_config = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether the resource should be protected against deletion.
  optional bool deletion_protection = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional description of this resource. Provide this property
  // when you create the resource.
  optional string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Array of disks associated with this instance. Persistent disks
  // must be created before you can assign them.
  repeated AttachedDisk disks = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Enables display device for the instance.
  optional DisplayDevice display_device = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of the type and count of accelerator cards attached to the
  // instance.
  repeated AcceleratorConfig guest_accelerators = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the hostname of the instance. The specified hostname
  // must be RFC1035 compliant. If hostname is not specified, the default
  // hostname is [INSTANCE_NAME].c.[PROJECT_ID].internal when using the global
  // DNS, and [INSTANCE_NAME].[ZONE].c.[PROJECT_ID].internal when using zonal
  // DNS.
  optional string hostname = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Encrypts suspended data for an instance with a
  // customer-managed encryption key.
  optional CustomerEncryptionKey instance_encryption_key = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. KeyRevocationActionType of the instance.
  optional KeyRevocationActionType key_revocation_action_type = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels to apply to this instance.
  map<string, string> labels = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Full or partial URL of the machine type resource to use for this
  // instance.
  optional string machine_type = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This includes custom metadata and predefined keys.
  optional Metadata metadata = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum CPU platform to use for this instance.
  optional string min_cpu_platform = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An array of network configurations for this instance. These
  // specify how interfaces are configured to interact with other network
  // services, such as connecting to the internet. Multiple interfaces are
  // supported per instance.
  repeated NetworkInterface network_interfaces = 17
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configure network performance such as egress bandwidth tier.
  optional NetworkPerformanceConfig network_performance_config = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Input only. Additional params passed with the request, but not persisted
  // as part of resource payload.
  optional InstanceParams params = 19
      [(google.api.field_behavior) = INPUT_ONLY];

  // Optional. The private IPv6 google access type for the VM.
  // If not specified, use INHERIT_FROM_SUBNETWORK as default.
  optional InstancePrivateIpv6GoogleAccess private_ipv6_google_access = 20
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the reservations that this instance can consume from.
  optional AllocationAffinity allocation_affinity = 21
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Resource policies applied to this instance.
  repeated string resource_policies = 22
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Sets the scheduling options for this instance.
  optional Scheduling scheduling = 23 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of service accounts, with their specified scopes,
  // authorized for this instance. Only one service account per VM instance is
  // supported.
  repeated ServiceAccount service_accounts = 24
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Tags to apply to this instance. Tags are used to identify valid
  // sources or targets for network firewalls and are specified by the client
  // during instance creation.
  optional Tags tags = 26 [(google.api.field_behavior) = OPTIONAL];
}

// ComputeInstanceTargetEnvironment represents Compute Engine target
// environment to be used during restore.
message ComputeInstanceTargetEnvironment {
  // Required. Target project for the Compute Engine instance.
  string project = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The zone of the Compute Engine instance.
  string zone = 2 [(google.api.field_behavior) = REQUIRED];
}

// ComputeInstanceDataSourceProperties represents the properties of a
// ComputeEngine resource that are stored in the DataSource.
message ComputeInstanceDataSourceProperties {
  // Name of the compute instance backed up by the datasource.
  string name = 1;

  // The description of the Compute Engine instance.
  string description = 2;

  // The machine type of the instance.
  string machine_type = 3;

  // The total number of disks attached to the Instance.
  int64 total_disk_count = 4;

  // The sum of all the disk sizes.
  int64 total_disk_size_gb = 5;
}

// Specifies options for controlling advanced machine features.
message AdvancedMachineFeatures {
  // Optional. Whether to enable nested virtualization or not (default is
  // false).
  optional bool enable_nested_virtualization = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of threads per physical core. To disable simultaneous
  // multithreading (SMT) set this to 1. If unset, the maximum number
  // of threads supported per core by the underlying processor is
  // assumed.
  optional int32 threads_per_core = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of physical cores to expose to an instance. Multiply
  // by the number of threads per core to compute the total number of virtual
  // CPUs to expose to the instance. If unset, the number of cores is
  // inferred from the instance's nominal CPU count and the underlying
  // platform's SMT width.
  optional int32 visible_core_count = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether to enable UEFI networking for instance creation.
  optional bool enable_uefi_networking = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// A set of Confidential Instance options.
message ConfidentialInstanceConfig {
  // Optional. Defines whether the instance should have confidential compute
  // enabled.
  optional bool enable_confidential_compute = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// A set of Display Device options
message DisplayDevice {
  // Optional. Enables display for the Compute Engine VM
  optional bool enable_display = 1 [(google.api.field_behavior) = OPTIONAL];
}

// A specification of the type and number of accelerator cards attached to the
// instance.
message AcceleratorConfig {
  // Optional. Full or partial URL of the accelerator type resource to attach to
  // this instance.
  optional string accelerator_type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The number of the guest accelerator cards exposed to this
  // instance.
  optional int32 accelerator_count = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A customer-supplied encryption key.
message CustomerEncryptionKey {
  // The key to use for encryption.
  oneof key {
    // Optional. Specifies a 256-bit customer-supplied
    // encryption key.
    string raw_key = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. RSA-wrapped 2048-bit
    // customer-supplied encryption key to either encrypt or decrypt this
    // resource.
    string rsa_encrypted_key = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The name of the encryption key that is stored in Google Cloud
    // KMS.
    string kms_key_name = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The service account being used for the encryption request for the
  // given KMS key. If absent, the Compute Engine default service account is
  // used.
  optional string kms_key_service_account = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// A key/value pair to be used for storing metadata.
message Entry {
  // Optional. Key for the metadata entry.
  optional string key = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Value for the metadata entry. These are free-form strings, and
  // only have meaning as interpreted by the image running in the instance. The
  // only restriction placed on values is that their size must be less than
  // or equal to 262144 bytes (256 KiB).
  optional string value = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A metadata key/value entry.
message Metadata {
  // Optional. Array of key/value pairs. The total size of all keys and values
  // must be less than 512 KB.
  repeated Entry items = 1 [(google.api.field_behavior) = OPTIONAL];
}

// A network interface resource attached to an instance.
// s
message NetworkInterface {
  // Stack type for this network interface.
  enum StackType {
    // Default should be STACK_TYPE_UNSPECIFIED.
    STACK_TYPE_UNSPECIFIED = 0;

    // The network interface will be assigned IPv4 address.
    IPV4_ONLY = 1;

    // The network interface can have both IPv4 and IPv6 addresses.
    IPV4_IPV6 = 2;
  }

  // IPv6 access type for this network interface.
  enum Ipv6AccessType {
    // IPv6 access type not set. Means this network interface hasn't been
    // turned on IPv6 yet.
    UNSPECIFIED_IPV6_ACCESS_TYPE = 0;

    // This network interface can have internal IPv6.
    INTERNAL = 1;

    // This network interface can have external IPv6.
    EXTERNAL = 2;
  }

  // Nic type for this network interface.
  enum NicType {
    // Default should be NIC_TYPE_UNSPECIFIED.
    NIC_TYPE_UNSPECIFIED = 0;

    // VIRTIO
    VIRTIO_NET = 1;

    // GVNIC
    GVNIC = 2;
  }

  // Optional. URL of the VPC network resource for this instance.
  optional string network = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The URL of the Subnetwork resource for this instance.
  optional string subnetwork = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An IPv4 internal IP address to assign to the instance for this
  // network interface. If not specified by the user, an unused internal IP is
  // assigned by the system.
  optional string ip_address = 3 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. An IPv6 internal network address for this network interface. To
  // use a static internal IP address, it must be unused and in the same region
  // as the instance's zone. If not specified, Google Cloud will automatically
  // assign an internal IPv6 address from the instance's subnetwork.
  optional string ipv6_address = 4 [
    (google.api.field_info).format = IPV6,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. The prefix length of the primary internal IPv6 range.
  optional int32 internal_ipv6_prefix_length = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. [Output Only] The name of the network interface, which is
  // generated by the server.
  optional string name = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. An array of configurations for this interface. Currently, only
  // one access config,ONE_TO_ONE_NAT is supported. If there are no
  // accessConfigs specified, then this instance will have
  // no external internet access.
  repeated AccessConfig access_configs = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An array of IPv6 access configurations for this interface.
  // Currently, only one IPv6 access config, DIRECT_IPV6, is supported. If there
  // is no ipv6AccessConfig specified, then this instance will
  // have no external IPv6 Internet access.
  repeated AccessConfig ipv6_access_configs = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. An array of alias IP ranges for this network interface.
  // You can only specify this field for network interfaces in VPC networks.
  repeated AliasIpRange alias_ip_ranges = 9
      [(google.api.field_behavior) = OPTIONAL];

  // The stack type for this network interface.
  optional StackType stack_type = 10;

  // Optional. [Output Only] One of EXTERNAL, INTERNAL to indicate whether the
  // IP can be accessed from the Internet. This field is always inherited from
  // its subnetwork.
  optional Ipv6AccessType ipv6_access_type = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The networking queue count that's specified by users for the
  // network interface. Both Rx and Tx queues will be set to this number. It'll
  // be empty if not specified by the users.
  optional int32 queue_count = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The type of vNIC to be used on this interface. This may be gVNIC
  // or VirtioNet.
  optional NicType nic_type = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The URL of the network attachment that this interface should
  // connect to in the following format:
  // projects/{project_number}/regions/{region_name}/networkAttachments/{network_attachment_name}.
  optional string network_attachment = 14
      [(google.api.field_behavior) = OPTIONAL];
}

// Network performance configuration.
message NetworkPerformanceConfig {
  // Network performance tier.
  enum Tier {
    // This value is unused.
    TIER_UNSPECIFIED = 0;

    // Default network performance config.
    DEFAULT = 1;

    // Tier 1 network performance config.
    TIER_1 = 2;
  }

  // Optional. The tier of the total egress bandwidth.
  optional Tier total_egress_bandwidth_tier = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// An access configuration attached to an instance's network interface.
// Only one access config per instance is supported.
message AccessConfig {
  // The type of configuration.
  enum AccessType {
    // Default value. This value is unused.
    ACCESS_TYPE_UNSPECIFIED = 0;

    // ONE_TO_ONE_NAT
    ONE_TO_ONE_NAT = 1;

    // Direct IPv6 access.
    DIRECT_IPV6 = 2;
  }

  // Network tier property used by addresses, instances and forwarding rules.
  enum NetworkTier {
    // Default value. This value is unused.
    NETWORK_TIER_UNSPECIFIED = 0;

    // High quality, Google-grade network tier, support for all networking
    // products.
    PREMIUM = 1;

    // Public internet quality, only limited support for other networking
    // products.
    STANDARD = 2;
  }

  // Optional. In accessConfigs (IPv4), the
  //  default and only option is ONE_TO_ONE_NAT. In
  //  ipv6AccessConfigs, the default and only option is
  //  DIRECT_IPV6.
  optional AccessType type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of this access configuration.
  optional string name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The external IP address of this access configuration.
  optional string external_ip = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The external IPv6 address of this access configuration.
  optional string external_ipv6 = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The prefix length of the external IPv6 range.
  optional int32 external_ipv6_prefix_length = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether a public DNS 'PTR' record should be created to
  // map the external IP address of the instance to a DNS domain name.
  optional bool set_public_ptr = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The DNS domain name for the public PTR record.
  optional string public_ptr_domain_name = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. This signifies the networking tier used for configuring this
  // access
  optional NetworkTier network_tier = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// An alias IP range attached to an instance's network interface.
message AliasIpRange {
  // Optional. The IP alias ranges to allocate for this interface.
  optional string ip_cidr_range = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of a subnetwork secondary IP range from which to
  // allocate an IP alias range. If not specified, the primary range of the
  // subnetwork is used.
  optional string subnetwork_range_name = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Additional instance params.
message InstanceParams {
  // Optional. Resource manager tags to be bound to the instance.
  map<string, string> resource_manager_tags = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// Specifies the reservations that this instance can consume from.
message AllocationAffinity {
  // Indicates whether to consume from a reservation or not.
  enum Type {
    // Default value. This value is unused.
    TYPE_UNSPECIFIED = 0;

    // Do not consume from any allocated capacity.
    NO_RESERVATION = 1;

    // Consume any allocation available.
    ANY_RESERVATION = 2;

    // Must consume from a specific reservation. Must specify key value fields
    // for specifying the reservations.
    SPECIFIC_RESERVATION = 3;
  }

  // Optional. Specifies the type of reservation from which this instance can
  // consume
  optional Type consume_allocation_type = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Corresponds to the label key of a reservation resource.
  optional string key = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Corresponds to the label values of a reservation resource.
  repeated string values = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Sets the scheduling options for an Instance.
message Scheduling {
  // Defines the maintenance behavior for this instance=
  enum OnHostMaintenance {
    // Default value. This value is unused.
    ON_HOST_MAINTENANCE_UNSPECIFIED = 0;

    // Tells Compute Engine to terminate and (optionally) restart the instance
    // away from the maintenance activity.
    TERMINATE = 1;

    // Default, Allows Compute Engine to automatically migrate instances
    // out of the way of maintenance events.
    MIGRATE = 1000;
  }

  // Node Affinity: the configuration of desired nodes onto which this Instance
  // could be scheduled.
  message NodeAffinity {
    // Defines the type of node selections.
    enum Operator {
      // Default value. This value is unused.
      OPERATOR_UNSPECIFIED = 0;

      // Requires Compute Engine to seek for matched nodes.
      IN = 1;

      // Requires Compute Engine to avoid certain nodes.
      NOT_IN = 2;
    }

    // Optional. Corresponds to the label key of Node resource.
    optional string key = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Defines the operation of node selection.
    optional Operator operator = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Corresponds to the label values of Node resource.
    repeated string values = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Defines the provisioning model for an instance.
  enum ProvisioningModel {
    // Default value. This value is not used.
    PROVISIONING_MODEL_UNSPECIFIED = 0;

    // Standard provisioning with user controlled runtime, no discounts.
    STANDARD = 1;

    // Heavily discounted, no guaranteed runtime.
    SPOT = 2;
  }

  // Defines the supported termination actions for an instance.
  enum InstanceTerminationAction {
    // Default value. This value is unused.
    INSTANCE_TERMINATION_ACTION_UNSPECIFIED = 0;

    // Delete the VM.
    DELETE = 1;

    // Stop the VM without storing in-memory content. default action.
    STOP = 2;
  }

  // Optional. Defines the maintenance behavior for this instance.
  optional OnHostMaintenance on_host_maintenance = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the instance should be automatically restarted
  // if it is terminated by Compute Engine (not terminated by a user).
  optional bool automatic_restart = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines whether the instance is preemptible.
  optional bool preemptible = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A set of node affinity and anti-affinity configurations.
  // Overrides reservationAffinity.
  repeated NodeAffinity node_affinities = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The minimum number of virtual CPUs this instance will consume
  // when running on a sole-tenant node.
  optional int32 min_node_cpus = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the provisioning model of the instance.
  optional ProvisioningModel provisioning_model = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the termination action for the instance.
  optional InstanceTerminationAction instance_termination_action = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the maximum amount of time a Local Ssd Vm should wait
  // while recovery of the Local Ssd state is attempted. Its value should be in
  // between 0 and 168 hours with hour granularity and the default value being 1
  // hour.
  optional SchedulingDuration local_ssd_recovery_timeout = 10
      [(google.api.field_behavior) = OPTIONAL];
}

// A SchedulingDuration represents a fixed-length span of time represented
// as a count of seconds and fractions of seconds at nanosecond
// resolution. It is independent of any calendar and concepts like "day"
// or "month". Range is approximately 10,000 years.
message SchedulingDuration {
  // Optional. Span of time at a resolution of a second.
  optional int64 seconds = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Span of time that's a fraction of a second at nanosecond
  // resolution.
  optional int32 nanos = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A service account.
message ServiceAccount {
  // Optional. Email address of the service account.
  optional string email = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of scopes to be made available for this service account.
  repeated string scopes = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A set of instance tags.
message Tags {
  // Optional. An array of tags. Each tag must be 1-63 characters long, and
  // comply with RFC1035.
  repeated string items = 1 [(google.api.field_behavior) = OPTIONAL];
}

// An instance-attached disk resource.
message AttachedDisk {
  // Specifies the parameters to initialize this disk.
  message InitializeParams {
    // Optional. Specifies the disk name. If not specified, the default is to
    // use the name of the instance.
    optional string disk_name = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. URL of the zone where the disk should be created.
    // Required for each regional disk associated with the instance.
    repeated string replica_zones = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // List of the Disk Types.
  enum DiskType {
    // Default value, which is unused.
    DISK_TYPE_UNSPECIFIED = 0;

    // A scratch disk type.
    SCRATCH = 1;

    // A persistent disk type.
    PERSISTENT = 2;
  }

  // List of the Disk Modes.
  enum DiskMode {
    // Default value, which is unused.
    DISK_MODE_UNSPECIFIED = 0;

    // Attaches this disk in read-write mode. Only one
    // virtual machine at a time can be attached to a disk in read-write mode.
    READ_WRITE = 1;

    // Attaches this disk in read-only mode. Multiple virtual machines can use
    // a disk in read-only mode at a time.
    READ_ONLY = 2;

    // The disk is locked for administrative reasons. Nobody else
    // can use the disk. This mode is used (for example) when taking
    // a snapshot of a disk to prevent mounting the disk while it is
    // being snapshotted.
    LOCKED = 3;
  }

  // List of the Disk Interfaces.
  enum DiskInterface {
    // Default value, which is unused.
    DISK_INTERFACE_UNSPECIFIED = 0;

    // SCSI Disk Interface.
    SCSI = 1;

    // NVME Disk Interface.
    NVME = 2;

    // NVDIMM Disk Interface.
    NVDIMM = 3;

    // ISCSI Disk Interface.
    ISCSI = 4;
  }

  // List of the states of the Disk.
  enum DiskSavedState {
    // Default Disk state has not been preserved.
    DISK_SAVED_STATE_UNSPECIFIED = 0;

    // Disk state has been preserved.
    PRESERVED = 1;
  }

  // Optional. Specifies the parameters to initialize this disk.
  optional InitializeParams initialize_params = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. This is used as an identifier for the disks. This is the unique
  // name has to provided to modify disk parameters like disk_name and
  // replica_zones (in case of RePDs)
  optional string device_name = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Type of the resource.
  optional string kind = 5 [(google.api.field_behavior) = OPTIONAL];

  // Specifies the type of the disk.
  optional DiskType disk_type_deprecated = 6 [deprecated = true];

  // Optional. The mode in which to attach this disk.
  optional DiskMode mode = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies a valid partial or full URL to an existing Persistent
  // Disk resource.
  optional string source = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A zero-based index to this disk, where 0 is reserved for the
  // boot disk.
  optional int64 index = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates that this is a boot disk. The virtual machine will use
  // the first partition of the disk for its root filesystem.
  optional bool boot = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the disk will be auto-deleted when the instance
  // is deleted (but not when the disk is detached from the instance).
  optional bool auto_delete = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Any valid publicly visible licenses.
  repeated string license = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the disk interface to use for attaching this disk.
  optional DiskInterface disk_interface = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of features to enable on the guest operating system.
  // Applicable only for bootable images.
  repeated GuestOsFeature guest_os_feature = 14
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Encrypts or decrypts a disk using a customer-supplied
  // encryption key.
  optional CustomerEncryptionKey disk_encryption_key = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The size of the disk in GB.
  optional int64 disk_size_gb = 16 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output only. The state of the disk.
  optional DiskSavedState saved_state = 17 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. Output only. The URI of the disk type resource. For example:
  // projects/project/zones/zone/diskTypes/pd-standard or pd-ssd
  optional string disk_type = 18 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. Specifies the type of the disk.
  optional DiskType type = 19 [(google.api.field_behavior) = OPTIONAL];
}

// Feature type of the Guest OS.
message GuestOsFeature {
  // List of the Feature Types.
  enum FeatureType {
    // Default value, which is unused.
    FEATURE_TYPE_UNSPECIFIED = 0;

    // VIRTIO_SCSI_MULTIQUEUE feature type.
    VIRTIO_SCSI_MULTIQUEUE = 1;

    // WINDOWS feature type.
    WINDOWS = 2;

    // MULTI_IP_SUBNET feature type.
    MULTI_IP_SUBNET = 3;

    // UEFI_COMPATIBLE feature type.
    UEFI_COMPATIBLE = 4;

    // SECURE_BOOT feature type.
    SECURE_BOOT = 5;

    // GVNIC feature type.
    GVNIC = 6;

    // SEV_CAPABLE feature type.
    SEV_CAPABLE = 7;

    // BARE_METAL_LINUX_COMPATIBLE feature type.
    BARE_METAL_LINUX_COMPATIBLE = 8;

    // SUSPEND_RESUME_COMPATIBLE feature type.
    SUSPEND_RESUME_COMPATIBLE = 9;

    // SEV_LIVE_MIGRATABLE feature type.
    SEV_LIVE_MIGRATABLE = 10;

    // SEV_SNP_CAPABLE feature type.
    SEV_SNP_CAPABLE = 11;

    // TDX_CAPABLE feature type.
    TDX_CAPABLE = 12;

    // IDPF feature type.
    IDPF = 13;

    // SEV_LIVE_MIGRATABLE_V2 feature type.
    SEV_LIVE_MIGRATABLE_V2 = 14;
  }

  // The ID of a supported feature.
  optional FeatureType type = 1;
}

// Specifies whether the virtual machine instance will be shut down on key
// revocation. It is currently used in instance, instance properties and GMI
// protos
enum KeyRevocationActionType {
  // Default value. This value is unused.
  KEY_REVOCATION_ACTION_TYPE_UNSPECIFIED = 0;

  // Indicates user chose no operation.
  NONE = 1;

  // Indicates user chose to opt for VM shutdown on key revocation.
  STOP = 2;
}
