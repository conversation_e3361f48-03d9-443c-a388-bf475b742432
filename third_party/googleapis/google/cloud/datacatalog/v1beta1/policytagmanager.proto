// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/datacatalog/v1beta1/common.proto";
import "google/cloud/datacatalog/v1beta1/timestamps.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1Beta1";
option go_package = "cloud.google.com/go/datacatalog/apiv1beta1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_outer_classname = "PolicyTagManagerProto";
option java_package = "com.google.cloud.datacatalog.v1beta1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1beta1";
option ruby_package = "Google::Cloud::DataCatalog::V1beta1";

// The policy tag manager API service allows clients to manage their taxonomies
// and policy tags.
service PolicyTagManager {
  option (google.api.default_host) = "datacatalog.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a taxonomy in the specified project.
  rpc CreateTaxonomy(CreateTaxonomyRequest) returns (Taxonomy) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/taxonomies"
      body: "taxonomy"
    };
    option (google.api.method_signature) = "parent,taxonomy";
  }

  // Deletes a taxonomy. This operation will also delete all
  // policy tags in this taxonomy along with their associated policies.
  rpc DeleteTaxonomy(DeleteTaxonomyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/taxonomies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a taxonomy.
  rpc UpdateTaxonomy(UpdateTaxonomyRequest) returns (Taxonomy) {
    option (google.api.http) = {
      patch: "/v1beta1/{taxonomy.name=projects/*/locations/*/taxonomies/*}"
      body: "taxonomy"
    };
    option (google.api.method_signature) = "taxonomy";
  }

  // Lists all taxonomies in a project in a particular location that the caller
  // has permission to view.
  rpc ListTaxonomies(ListTaxonomiesRequest) returns (ListTaxonomiesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/taxonomies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a taxonomy.
  rpc GetTaxonomy(GetTaxonomyRequest) returns (Taxonomy) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/taxonomies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a policy tag in the specified taxonomy.
  rpc CreatePolicyTag(CreatePolicyTagRequest) returns (PolicyTag) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/taxonomies/*}/policyTags"
      body: "policy_tag"
    };
    option (google.api.method_signature) = "parent,policy_tag";
  }

  // Deletes a policy tag. Also deletes all of its descendant policy tags.
  rpc DeletePolicyTag(DeletePolicyTagRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/taxonomies/*/policyTags/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a policy tag.
  rpc UpdatePolicyTag(UpdatePolicyTagRequest) returns (PolicyTag) {
    option (google.api.http) = {
      patch: "/v1beta1/{policy_tag.name=projects/*/locations/*/taxonomies/*/policyTags/*}"
      body: "policy_tag"
    };
    option (google.api.method_signature) = "policy_tag";
  }

  // Lists all policy tags in a taxonomy.
  rpc ListPolicyTags(ListPolicyTagsRequest) returns (ListPolicyTagsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/taxonomies/*}/policyTags"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a policy tag.
  rpc GetPolicyTag(GetPolicyTagRequest) returns (PolicyTag) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/taxonomies/*/policyTags/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the IAM policy for a taxonomy or a policy tag.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/taxonomies/*}:getIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/taxonomies/*/policyTags/*}:getIamPolicy"
        body: "*"
      }
    };
  }

  // Sets the IAM policy for a taxonomy or a policy tag.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/taxonomies/*}:setIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/taxonomies/*/policyTags/*}:setIamPolicy"
        body: "*"
      }
    };
  }

  // Returns the permissions that a caller has on the specified taxonomy or
  // policy tag.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/taxonomies/*}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/taxonomies/*/policyTags/*}:testIamPermissions"
        body: "*"
      }
    };
  }
}

// A taxonomy is a collection of policy tags that classify data along a common
// axis. For instance a data *sensitivity* taxonomy could contain policy tags
// denoting PII such as age, zipcode, and SSN. A data *origin* taxonomy could
// contain policy tags to distinguish user data, employee data, partner data,
// public data.
message Taxonomy {
  option (google.api.resource) = {
    type: "datacatalog.googleapis.com/Taxonomy"
    pattern: "projects/{project}/locations/{location}/taxonomies/{taxonomy}"
  };

  // Defines policy types where policy tag can be used for.
  enum PolicyType {
    // Unspecified policy type.
    POLICY_TYPE_UNSPECIFIED = 0;

    // Fine grained access control policy, which enables access control on
    // tagged resources.
    FINE_GRAINED_ACCESS_CONTROL = 1;
  }

  // The source system of the Taxonomy.
  message Service {
    // The Google Cloud service name.
    ManagingSystem name = 1;

    // The service agent for the service.
    string identity = 2;
  }

  // Identifier. Resource name of this taxonomy, whose format is:
  // "projects/{project_number}/locations/{location_id}/taxonomies/{id}".
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. User defined name of this taxonomy. It must: contain only unicode
  // letters, numbers, underscores, dashes and spaces; not start or end with
  // spaces; and be at most 200 bytes long when encoded in UTF-8.
  //
  // The taxonomy display name must be unique within an organization.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description of this taxonomy. It must: contain only unicode
  // characters, tabs, newlines, carriage returns and page breaks; and be at
  // most 2000 bytes long when encoded in UTF-8. If not set, defaults to an
  // empty description.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Number of policy tags contained in this taxonomy.
  int32 policy_tag_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamps about this taxonomy. Only create_time and
  // update_time are used.
  SystemTimestamps taxonomy_timestamps = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A list of policy types that are activated for this taxonomy. If
  // not set, defaults to an empty list.
  repeated PolicyType activated_policy_types = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Identity of the service which owns the Taxonomy. This field is
  // only populated when the taxonomy is created by a Google Cloud service.
  // Currently only 'DATAPLEX' is supported.
  Service service = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Denotes one policy tag in a taxonomy (e.g. ssn). Policy Tags can be defined
// in a hierarchy. For example, consider the following hierarchy:
// Geolocation -&gt; (LatLong, City, ZipCode). PolicyTag "Geolocation"
// contains three child policy tags: "LatLong", "City", and "ZipCode".
message PolicyTag {
  option (google.api.resource) = {
    type: "datacatalog.googleapis.com/PolicyTag"
    pattern: "projects/{project}/locations/{location}/taxonomies/{taxonomy}/policyTags/{policy_tag}"
  };

  // Identifier. Resource name of this policy tag, whose format is:
  // "projects/{project_number}/locations/{location_id}/taxonomies/{taxonomy_id}/policyTags/{id}".
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. User defined name of this policy tag. It must: be unique within
  // the parent taxonomy; contain only unicode letters, numbers, underscores,
  // dashes and spaces; not start or end with spaces; and be at most 200 bytes
  // long when encoded in UTF-8.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Description of this policy tag. It must: contain only unicode characters,
  // tabs, newlines, carriage returns and page breaks; and be at most 2000 bytes
  // long when encoded in UTF-8. If not set, defaults to an empty description.
  // If not set, defaults to an empty description.
  string description = 3;

  // Resource name of this policy tag's parent policy tag (e.g. for the
  // "LatLong" policy tag in the example above, this field contains the
  // resource name of the "Geolocation" policy tag). If empty, it means this
  // policy tag is a top level policy tag (e.g. this field is empty for the
  // "Geolocation" policy tag in the example above). If not set, defaults to an
  // empty string.
  string parent_policy_tag = 4;

  // Output only. Resource names of child policy tags of this policy tag.
  repeated string child_policy_tags = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [CreateTaxonomy][google.cloud.datacatalog.v1beta1.PolicyTagManager.CreateTaxonomy].
message CreateTaxonomyRequest {
  // Required. Resource name of the project that the taxonomy will belong to.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];

  // The taxonomy to be created.
  Taxonomy taxonomy = 2;
}

// Request message for
// [DeleteTaxonomy][google.cloud.datacatalog.v1beta1.PolicyTagManager.DeleteTaxonomy].
message DeleteTaxonomyRequest {
  // Required. Resource name of the taxonomy to be deleted. All policy tags in
  // this taxonomy will also be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];
}

// Request message for
// [UpdateTaxonomy][google.cloud.datacatalog.v1beta1.PolicyTagManager.UpdateTaxonomy].
message UpdateTaxonomyRequest {
  // The taxonomy to update. Only description, display_name, and activated
  // policy types can be updated.
  Taxonomy taxonomy = 1;

  // The update mask applies to the resource. For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  // If not set, defaults to all of the fields that are allowed to update.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [ListTaxonomies][google.cloud.datacatalog.v1beta1.PolicyTagManager.ListTaxonomies].
message ListTaxonomiesRequest {
  // Required. Resource name of the project to list the taxonomies of.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];

  // The maximum number of items to return. Must be a value between 1 and 1000.
  // If not set, defaults to 50.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any. If
  // not set, defaults to an empty string.
  string page_token = 3;

  // Supported field for filter is 'service' and value is 'dataplex'.
  // Eg: service=dataplex.
  string filter = 4;
}

// Response message for
// [ListTaxonomies][google.cloud.datacatalog.v1beta1.PolicyTagManager.ListTaxonomies].
message ListTaxonomiesResponse {
  // Taxonomies that the project contains.
  repeated Taxonomy taxonomies = 1;

  // Token used to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request message for
// [GetTaxonomy][google.cloud.datacatalog.v1beta1.PolicyTagManager.GetTaxonomy].
message GetTaxonomyRequest {
  // Required. Resource name of the requested taxonomy.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];
}

// Request message for
// [CreatePolicyTag][google.cloud.datacatalog.v1beta1.PolicyTagManager.CreatePolicyTag].
message CreatePolicyTagRequest {
  // Required. Resource name of the taxonomy that the policy tag will belong to.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/PolicyTag"
    }
  ];

  // The policy tag to be created.
  PolicyTag policy_tag = 2;
}

// Request message for
// [DeletePolicyTag][google.cloud.datacatalog.v1beta1.PolicyTagManager.DeletePolicyTag].
message DeletePolicyTagRequest {
  // Required. Resource name of the policy tag to be deleted. All of its
  // descendant policy tags will also be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/PolicyTag"
    }
  ];
}

// Request message for
// [UpdatePolicyTag][google.cloud.datacatalog.v1beta1.PolicyTagManager.UpdatePolicyTag].
message UpdatePolicyTagRequest {
  // The policy tag to update. Only the description, display_name, and
  // parent_policy_tag fields can be updated.
  PolicyTag policy_tag = 1;

  // The update mask applies to the resource. Only display_name, description and
  // parent_policy_tag can be updated and thus can be listed in the mask. If
  // update_mask is not provided, all allowed fields (i.e. display_name,
  // description and parent) will be updated. For more information including the
  // `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  // If not set, defaults to all of the fields that are allowed to update.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [ListPolicyTags][google.cloud.datacatalog.v1beta1.PolicyTagManager.ListPolicyTags].
message ListPolicyTagsRequest {
  // Required. Resource name of the taxonomy to list the policy tags of.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/PolicyTag"
    }
  ];

  // The maximum number of items to return. Must be a value between 1 and 1000.
  // If not set, defaults to 50.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any. If
  // not set, defaults to an empty string.
  string page_token = 3;
}

// Response message for
// [ListPolicyTags][google.cloud.datacatalog.v1beta1.PolicyTagManager.ListPolicyTags].
message ListPolicyTagsResponse {
  // The policy tags that are in the requested taxonomy.
  repeated PolicyTag policy_tags = 1;

  // Token used to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request message for
// [GetPolicyTag][google.cloud.datacatalog.v1beta1.PolicyTagManager.GetPolicyTag].
message GetPolicyTagRequest {
  // Required. Resource name of the requested policy tag.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/PolicyTag"
    }
  ];
}
