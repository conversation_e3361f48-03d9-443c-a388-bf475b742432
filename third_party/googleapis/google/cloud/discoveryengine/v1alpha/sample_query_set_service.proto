// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/sample_query_set.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SampleQuerySetServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Service for managing
// [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet]s,
service SampleQuerySetService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets a
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet].
  rpc GetSampleQuerySet(GetSampleQuerySetRequest) returns (SampleQuerySet) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/sampleQuerySets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a list of
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet]s.
  rpc ListSampleQuerySets(ListSampleQuerySetsRequest)
      returns (ListSampleQuerySetsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*}/sampleQuerySets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet]
  rpc CreateSampleQuerySet(CreateSampleQuerySetRequest)
      returns (SampleQuerySet) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*}/sampleQuerySets"
      body: "sample_query_set"
    };
    option (google.api.method_signature) =
        "parent,sample_query_set,sample_query_set_id";
  }

  // Updates a
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet].
  rpc UpdateSampleQuerySet(UpdateSampleQuerySetRequest)
      returns (SampleQuerySet) {
    option (google.api.http) = {
      patch: "/v1alpha/{sample_query_set.name=projects/*/locations/*/sampleQuerySets/*}"
      body: "sample_query_set"
    };
    option (google.api.method_signature) = "sample_query_set,update_mask";
  }

  // Deletes a
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet].
  rpc DeleteSampleQuerySet(DeleteSampleQuerySetRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=projects/*/locations/*/sampleQuerySets/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for
// [SampleQuerySetService.GetSampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySetService.GetSampleQuerySet]
// method.
message GetSampleQuerySetRequest {
  // Required. Full resource name of
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet], such
  // as
  // `projects/{project}/locations/{location}/sampleQuerySets/{sample_query_set}`.
  //
  // If the caller does not have permission to access the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet],
  // regardless of whether or not it exists, a PERMISSION_DENIED error is
  // returned.
  //
  // If the requested
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet] does
  // not exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SampleQuerySet"
    }
  ];
}

// Request message for
// [SampleQuerySetService.ListSampleQuerySets][google.cloud.discoveryengine.v1alpha.SampleQuerySetService.ListSampleQuerySets]
// method.
message ListSampleQuerySetsRequest {
  // Required. The parent location resource name, such as
  // `projects/{project}/locations/{location}`.
  //
  // If the caller does not have permission to list
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet]s
  // under this location, regardless of whether or not this location exists, a
  // `PERMISSION_DENIED` error is returned.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Location"
    }
  ];

  // Maximum number of
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet]s to
  // return. If unspecified, defaults to 100. The maximum allowed value is 1000.
  // Values above 1000 will be coerced to 1000.
  //
  // If this field is negative, an `INVALID_ARGUMENT` error is returned.
  int32 page_size = 2;

  // A page token
  // [ListSampleQuerySetsResponse.next_page_token][google.cloud.discoveryengine.v1alpha.ListSampleQuerySetsResponse.next_page_token],
  // received from a previous
  // [SampleQuerySetService.ListSampleQuerySets][google.cloud.discoveryengine.v1alpha.SampleQuerySetService.ListSampleQuerySets]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [SampleQuerySetService.ListSampleQuerySets][google.cloud.discoveryengine.v1alpha.SampleQuerySetService.ListSampleQuerySets]
  // must match the call that provided the page token. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string page_token = 3;
}

// Response message for
// [SampleQuerySetService.ListSampleQuerySets][google.cloud.discoveryengine.v1alpha.SampleQuerySetService.ListSampleQuerySets]
// method.
message ListSampleQuerySetsResponse {
  // The [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet]s.
  repeated SampleQuerySet sample_query_sets = 1;

  // A token that can be sent as
  // [ListSampleQuerySetsRequest.page_token][google.cloud.discoveryengine.v1alpha.ListSampleQuerySetsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [SampleQuerySetService.CreateSampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySetService.CreateSampleQuerySet]
// method.
message CreateSampleQuerySetRequest {
  // Required. The parent resource name, such as
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Location"
    }
  ];

  // Required. The
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet] to
  // create.
  SampleQuerySet sample_query_set = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet],
  // which will become the final component of the
  // [SampleQuerySet.name][google.cloud.discoveryengine.v1alpha.SampleQuerySet.name].
  //
  // If the caller does not have permission to create the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet],
  // regardless of whether or not it exists, a `PERMISSION_DENIED` error is
  // returned.
  //
  // This field must be unique among all
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet]s with
  // the same
  // [parent][google.cloud.discoveryengine.v1alpha.CreateSampleQuerySetRequest.parent].
  // Otherwise, an `ALREADY_EXISTS` error is returned.
  //
  // This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034)
  // standard with a length limit of 63 characters. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string sample_query_set_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [SampleQuerySetService.UpdateSampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySetService.UpdateSampleQuerySet]
// method.
message UpdateSampleQuerySetRequest {
  // Required. The sample query set to update.
  //
  // If the caller does not have permission to update the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet],
  // regardless of whether or not it exists, a `PERMISSION_DENIED` error is
  // returned.
  //
  // If the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet] to
  // update does not exist a `NOT_FOUND` error is returned.
  SampleQuerySet sample_query_set = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided imported 'sample query set' to
  // update. If not set, will by default update all fields.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [SampleQuerySetService.DeleteSampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySetService.DeleteSampleQuerySet]
// method.
message DeleteSampleQuerySetRequest {
  // Required. Full resource name of
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet], such
  // as
  // `projects/{project}/locations/{location}/sampleQuerySets/{sample_query_set}`.
  //
  // If the caller does not have permission to delete the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet],
  // regardless of whether or not it exists, a `PERMISSION_DENIED` error is
  // returned.
  //
  // If the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet] to
  // delete does not exist, a `NOT_FOUND` error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SampleQuerySet"
    }
  ];
}
