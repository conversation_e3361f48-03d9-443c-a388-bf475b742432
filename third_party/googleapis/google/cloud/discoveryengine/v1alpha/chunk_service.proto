// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/chunk.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "ChunkServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Service for displaying processed
// [Chunk][google.cloud.discoveryengine.v1alpha.Chunk] information of the
// customer's unstructured data.
service ChunkService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets a [Document][google.cloud.discoveryengine.v1alpha.Document].
  rpc GetChunk(GetChunkRequest) returns (Chunk) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/dataStores/*/branches/*/documents/*/chunks/*}"
      additional_bindings {
        get: "/v1alpha/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*/documents/*/chunks/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a list of [Chunk][google.cloud.discoveryengine.v1alpha.Chunk]s.
  rpc ListChunks(ListChunksRequest) returns (ListChunksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*/dataStores/*/branches/*/documents/*}/chunks"
      additional_bindings {
        get: "/v1alpha/{parent=projects/*/locations/*/collections/*/dataStores/*/branches/*/documents/*}/chunks"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [ChunkService.GetChunk][google.cloud.discoveryengine.v1alpha.ChunkService.GetChunk]
// method.
message GetChunkRequest {
  // Required. Full resource name of
  // [Chunk][google.cloud.discoveryengine.v1alpha.Chunk], such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document}/chunks/{chunk}`.
  //
  // If the caller does not have permission to access the
  // [Chunk][google.cloud.discoveryengine.v1alpha.Chunk], regardless of whether
  // or not it exists, a `PERMISSION_DENIED` error is returned.
  //
  // If the requested [Chunk][google.cloud.discoveryengine.v1alpha.Chunk] does
  // not exist, a `NOT_FOUND` error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Chunk"
    }
  ];
}

// Request message for
// [ChunkService.ListChunks][google.cloud.discoveryengine.v1alpha.ChunkService.ListChunks]
// method.
message ListChunksRequest {
  // Required. The parent document resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document}`.
  //
  // If the caller does not have permission to list
  // [Chunk][google.cloud.discoveryengine.v1alpha.Chunk]s under this document,
  // regardless of whether or not this document exists, a `PERMISSION_DENIED`
  // error is returned.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Document"
    }
  ];

  // Maximum number of [Chunk][google.cloud.discoveryengine.v1alpha.Chunk]s to
  // return. If unspecified, defaults to 100. The maximum allowed value is 1000.
  // Values above 1000 will be coerced to 1000.
  //
  // If this field is negative, an `INVALID_ARGUMENT` error is returned.
  int32 page_size = 2;

  // A page token
  // [ListChunksResponse.next_page_token][google.cloud.discoveryengine.v1alpha.ListChunksResponse.next_page_token],
  // received from a previous
  // [ChunkService.ListChunks][google.cloud.discoveryengine.v1alpha.ChunkService.ListChunks]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [ChunkService.ListChunks][google.cloud.discoveryengine.v1alpha.ChunkService.ListChunks]
  // must match the call that provided the page token. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string page_token = 3;
}

// Response message for
// [ChunkService.ListChunks][google.cloud.discoveryengine.v1alpha.ChunkService.ListChunks]
// method.
message ListChunksResponse {
  // The [Chunk][google.cloud.discoveryengine.v1alpha.Chunk]s.
  repeated Chunk chunks = 1;

  // A token that can be sent as
  // [ListChunksRequest.page_token][google.cloud.discoveryengine.v1alpha.ListChunksRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}
