# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "gsuiteaddons_proto",
    srcs = [
        "gsuiteaddons.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/apps/script/type:type_proto",
        "//google/apps/script/type/calendar:calendar_proto",
        "//google/apps/script/type/docs:docs_proto",
        "//google/apps/script/type/drive:drive_proto",
        "//google/apps/script/type/gmail:gmail_proto",
        "//google/apps/script/type/sheets:sheets_proto",
        "//google/apps/script/type/slides:slides_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "gsuiteaddons_proto_with_info",
    deps = [
        ":gsuiteaddons_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "gsuiteaddons_java_proto",
    deps = [":gsuiteaddons_proto"],
)

java_grpc_library(
    name = "gsuiteaddons_java_grpc",
    srcs = [":gsuiteaddons_proto"],
    deps = [":gsuiteaddons_java_proto"],
)

java_gapic_library(
    name = "gsuiteaddons_java_gapic",
    srcs = [":gsuiteaddons_proto_with_info"],
    grpc_service_config = "gsuiteaddons_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "gsuiteaddons_v1.yaml",
    test_deps = [
        ":gsuiteaddons_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":gsuiteaddons_java_proto",
    ],
)

java_gapic_test(
    name = "gsuiteaddons_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.gsuiteaddons.v1.GSuiteAddOnsClientHttpJsonTest",
        "com.google.cloud.gsuiteaddons.v1.GSuiteAddOnsClientTest",
    ],
    runtime_deps = [":gsuiteaddons_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-gsuiteaddons-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":gsuiteaddons_java_gapic",
        ":gsuiteaddons_java_grpc",
        ":gsuiteaddons_java_proto",
        ":gsuiteaddons_proto",
    ],
)

go_proto_library(
    name = "gsuiteaddons_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gsuiteaddons/apiv1/gsuiteaddonspb",
    protos = [":gsuiteaddons_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/apps/script/type:type_go_proto",
        "//google/apps/script/type/calendar:calendar_go_proto",
        "//google/apps/script/type/docs:docs_go_proto",
        "//google/apps/script/type/drive:drive_go_proto",
        "//google/apps/script/type/gmail:gmail_go_proto",
        "//google/apps/script/type/sheets:sheets_go_proto",
        "//google/apps/script/type/slides:slides_go_proto",
    ],
)

go_gapic_library(
    name = "gsuiteaddons_go_gapic",
    srcs = [":gsuiteaddons_proto_with_info"],
    grpc_service_config = "gsuiteaddons_grpc_service_config.json",
    importpath = "cloud.google.com/go/gsuiteaddons/apiv1;gsuiteaddons",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "gsuiteaddons_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gsuiteaddons_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-gsuiteaddons-v1-go",
    deps = [
        ":gsuiteaddons_go_gapic",
        ":gsuiteaddons_go_gapic_srcjar-snippets.srcjar",
        ":gsuiteaddons_go_gapic_srcjar-test.srcjar",
        ":gsuiteaddons_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
py_gapic_library(
    name = "gsuiteaddons_py_gapic",
    srcs = [":gsuiteaddons_proto"],
    grpc_service_config = "gsuiteaddons_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "gsuiteaddons_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/apps/script/type:type_py_gapic",
        "//google/apps/script/type/calendar:calendar_py_gapic",
        "//google/apps/script/type/docs:docs_py_gapic",
        "//google/apps/script/type/drive:drive_py_gapic",
        "//google/apps/script/type/gmail:gmail_py_gapic",
        "//google/apps/script/type/sheets:sheets_py_gapic",
        "//google/apps/script/type/slides:slides_py_gapic",
    ],
    opt_args = [
        "proto-plus-deps=\
google.apps.script.type.calendar+\
google.apps.script.type.docs+\
google.apps.script.type.drive+\
google.apps.script.type.gmail+\
google.apps.script.type.sheets+\
google.apps.script.type.slides+\
google.apps.script.type"
    ],
)

# TODO: Uncomment once
# https://github.com/googleapis/gapic-generator-python/issues/1376 is fixed
#py_test(
#    name = "gsuiteaddons_py_gapic_test",
#    srcs = [
#        "gsuiteaddons_py_gapic_pytest.py",
#        "gsuiteaddons_py_gapic_test.py",
#    ],
#    legacy_create_init = False,
#    deps = [":gsuiteaddons_py_gapic"],
#)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "gsuiteaddons-v1-py",
    deps = [
        ":gsuiteaddons_py_gapic",
    ],
)

php_proto_library(
    name = "gsuiteaddons_php_proto",
    deps = [":gsuiteaddons_proto"],
)

php_gapic_library(
    name = "gsuiteaddons_php_gapic",
    srcs = [":gsuiteaddons_proto_with_info"],
    grpc_service_config = "gsuiteaddons_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "gsuiteaddons_v1.yaml",
    transport = "grpc+rest",
    deps = [":gsuiteaddons_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-gsuiteaddons-v1-php",
    deps = [
        ":gsuiteaddons_php_gapic",
        ":gsuiteaddons_php_proto",
    ],
)

nodejs_gapic_library(
    name = "gsuiteaddons_nodejs_gapic",
    package_name = "@google-cloud/gsuiteaddons",
    src = ":gsuiteaddons_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "gsuiteaddons_grpc_service_config.json",
    package = "google.cloud.gsuiteaddons.v1",
    rest_numeric_enums = True,
    service_yaml = "gsuiteaddons_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "gsuiteaddons-v1-nodejs",
    deps = [
        ":gsuiteaddons_nodejs_gapic",
        ":gsuiteaddons_proto",
        "//google/apps/script/type:type_proto",
        "//google/apps/script/type/calendar:calendar_proto",
        "//google/apps/script/type/docs:docs_proto",
        "//google/apps/script/type/drive:drive_proto",
        "//google/apps/script/type/gmail:gmail_proto",
        "//google/apps/script/type/sheets:sheets_proto",
        "//google/apps/script/type/slides:slides_proto",
    ],
)

ruby_proto_library(
    name = "gsuiteaddons_ruby_proto",
    deps = [":gsuiteaddons_proto"],
)

ruby_grpc_library(
    name = "gsuiteaddons_ruby_grpc",
    srcs = [":gsuiteaddons_proto"],
    deps = [":gsuiteaddons_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "gsuiteaddons_ruby_gapic",
    srcs = [":gsuiteaddons_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-gsuite_add_ons-v1",
        "ruby-cloud-gem-namespace=Google::Cloud::GSuiteAddOns::V1",
        "ruby-cloud-product-url=https://developers.google.com/workspace/add-ons/",
        "ruby-cloud-api-id=gsuiteaddons.googleapis.com",
        "ruby-cloud-api-shortname=gsuiteaddons",
        "ruby-cloud-namespace-override=GsuiteAddOns=GSuiteAddOns",
        "ruby-cloud-path-override=g_suite_add_ons=gsuite_add_ons",
        "ruby-cloud-extra-dependencies=google-apps-script-type=>0.0+<2.a",
    ],
    grpc_service_config = "gsuiteaddons_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Add-ons are customized applications that integrate with Google Workspace productivity applications.",
    ruby_cloud_title = "Google Workspace Add-ons V1",
    service_yaml = "gsuiteaddons_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gsuiteaddons_ruby_grpc",
        ":gsuiteaddons_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-gsuiteaddons-v1-ruby",
    deps = [
        ":gsuiteaddons_ruby_gapic",
        ":gsuiteaddons_ruby_grpc",
        ":gsuiteaddons_ruby_proto",
    ],
)

csharp_proto_library(
    name = "gsuiteaddons_csharp_proto",
    deps = [":gsuiteaddons_proto"],
)

csharp_grpc_library(
    name = "gsuiteaddons_csharp_grpc",
    srcs = [":gsuiteaddons_proto"],
    deps = [":gsuiteaddons_csharp_proto"],
)

csharp_gapic_library(
    name = "gsuiteaddons_csharp_gapic",
    srcs = [":gsuiteaddons_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "gsuiteaddons_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "gsuiteaddons_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gsuiteaddons_csharp_grpc",
        ":gsuiteaddons_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-gsuiteaddons-v1-csharp",
    deps = [
        ":gsuiteaddons_csharp_gapic",
        ":gsuiteaddons_csharp_grpc",
        ":gsuiteaddons_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "gsuiteaddons_cc_proto",
    deps = [":gsuiteaddons_proto"],
)

cc_grpc_library(
    name = "gsuiteaddons_cc_grpc",
    srcs = [":gsuiteaddons_proto"],
    grpc_only = True,
    deps = [":gsuiteaddons_cc_proto"],
)
