// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommender.logging.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/recommender/v1/insight.proto";
import "google/cloud/recommender/v1/recommendation.proto";

option go_package = "cloud.google.com/go/recommender/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ActionLogProto";
option java_package = "com.google.cloud.recommender.logging.v1";

// Log content of an action on a recommendation. This includes Mark* actions.
message ActionLog {
  // Required. User that executed this action. Eg, <EMAIL>
  string actor = 1;

  // Required. State change that was made by the actor. Eg, SUCCEEDED.
  google.cloud.recommender.v1.RecommendationStateInfo.State state = 2;

  // Optional. Metadata that was included with the action that was taken.
  map<string, string> state_metadata = 3;

  // Required. Name of the recommendation which was acted on. Eg, :
  // 'projects/123/locations/global/recommenders/roleReco/recommendations/r1'
  string recommendation_name = 4;
}

// Log content of an action on an insight. This includes Mark* actions.
message InsightActionLog {
  // Required. User that executed this action. Eg, <EMAIL>
  string actor = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. State change that was made by the actor. Eg, ACCEPTED.
  google.cloud.recommender.v1.InsightStateInfo.State state = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Metadata that was included with the action that was taken.
  map<string, string> state_metadata = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Name of the insight which was acted on. Eg, :
  // 'projects/123/locations/global/insightTypes/roleInsight/insights/i1'
  string insight = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommender.googleapis.com/Insight"
    }
  ];
}
