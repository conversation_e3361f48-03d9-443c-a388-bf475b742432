// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/launch_stage.proto";
import "google/api/resource.proto";
import "google/api/routing.proto";
import "google/cloud/run/v2/condition.proto";
import "google/cloud/run/v2/k8s.min.proto";
import "google/cloud/run/v2/status.proto";
import "google/cloud/run/v2/vendor_settings.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "RevisionProto";
option java_package = "com.google.cloud.run.v2";

// Cloud Run Revision Control Plane API.
service Revisions {
  option (google.api.default_host) = "run.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets information about a Revision.
  rpc GetRevision(GetRevisionRequest) returns (Revision) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/services/*/revisions/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Revisions from a given Service, or from a given location.  Results
  // are sorted by creation time, descending.
  rpc ListRevisions(ListRevisionsRequest) returns (ListRevisionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/services/*}/revisions"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a Revision.
  rpc DeleteRevision(DeleteRevisionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/services/*/revisions/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Revision"
      metadata_type: "Revision"
    };
  }
}

// Request message for obtaining a Revision by its full name.
message GetRevisionRequest {
  // Required. The full name of the Revision.
  // Format:
  // projects/{project}/locations/{location}/services/{service}/revisions/{revision}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Revision" }
  ];
}

// Request message for retrieving a list of Revisions.
message ListRevisionsRequest {
  // Required. The Service from which the Revisions should be listed.
  // To list all Revisions across Services, use "-" instead of Service name.
  // Format:
  // projects/{project}/locations/{location}/services/{service}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "run.googleapis.com/Revision"
    }
  ];

  // Maximum number of revisions to return in this call.
  int32 page_size = 2;

  // A page token received from a previous call to ListRevisions.
  // All other parameters must match.
  string page_token = 3;

  // If true, returns deleted (but unexpired) resources along with active ones.
  bool show_deleted = 4;
}

// Response message containing a list of Revisions.
message ListRevisionsResponse {
  // The resulting list of Revisions.
  repeated Revision revisions = 1;

  // A token indicating there are more items than page_size. Use it in the next
  // ListRevisions request to continue.
  string next_page_token = 2;
}

// Request message for deleting a retired Revision.
// Revision lifecycle is usually managed by making changes to the parent
// Service. Only retired revisions can be deleted with this API.
message DeleteRevisionRequest {
  // Required. The name of the Revision to delete.
  // Format:
  // projects/{project}/locations/{location}/services/{service}/revisions/{revision}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Revision" }
  ];

  // Indicates that the request should be validated without actually
  // deleting any resources.
  bool validate_only = 2;

  // A system-generated fingerprint for this version of the
  // resource. This may be used to detect modification conflict during updates.
  string etag = 3;
}

// A Revision is an immutable snapshot of code and configuration.  A Revision
// references a container image. Revisions are only created by updates to its
// parent Service.
message Revision {
  option (google.api.resource) = {
    type: "run.googleapis.com/Revision"
    pattern: "projects/{project}/locations/{location}/services/{service}/revisions/{revision}"
    style: DECLARATIVE_FRIENDLY
  };

  // Output only. The unique name of this Revision.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server assigned unique identifier for the Revision. The value
  // is a UUID4 string and guaranteed to remain unchanged until the resource is
  // deleted.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A number that monotonically increases every time the user
  // modifies the desired state.
  int64 generation = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unstructured key value map that can be used to organize and
  // categorize objects. User-provided labels are shared with Google's billing
  // system, so they can be used to filter, or break down billing charges by
  // team, component, environment, state, etc. For more information, visit
  // https://cloud.google.com/resource-manager/docs/creating-managing-labels or
  // https://cloud.google.com/run/docs/configuring/labels.
  map<string, string> labels = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unstructured key value map that may
  // be set by external tools to store and arbitrary metadata.
  // They are not queryable and should be preserved
  // when modifying objects.
  map<string, string> annotations = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a deleted resource, the deletion time. It is only
  // populated as a response to a Delete request.
  google.protobuf.Timestamp delete_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a deleted resource, the time after which it will be
  // permamently deleted. It is only populated as a response to a Delete
  // request.
  google.protobuf.Timestamp expire_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The least stable launch stage needed to create this resource, as defined by
  // [Google Cloud Platform Launch
  // Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports
  // `ALPHA`, `BETA`, and `GA`.
  // <p>Note that this value might not be what was used
  // as input. For example, if ALPHA was provided as input in the parent
  // resource, but only BETA and GA-level features are were, this field will be
  // BETA.
  google.api.LaunchStage launch_stage = 10;

  // Output only. The name of the parent service.
  string service = 11 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Service" }
  ];

  // Scaling settings for this revision.
  RevisionScaling scaling = 12;

  // VPC Access configuration for this Revision. For more information, visit
  // https://cloud.google.com/run/docs/configuring/connecting-vpc.
  VpcAccess vpc_access = 13;

  // Sets the maximum number of requests that each serving instance can receive.
  int32 max_instance_request_concurrency = 34;

  // Max allowed time for an instance to respond to a request.
  google.protobuf.Duration timeout = 15;

  // Email address of the IAM service account associated with the revision of
  // the service. The service account represents the identity of the running
  // revision, and determines what permissions the revision has.
  string service_account = 16;

  // Holds the single container that defines the unit of execution for this
  // Revision.
  repeated Container containers = 17;

  // A list of Volumes to make available to containers.
  repeated Volume volumes = 18;

  // The execution environment being used to host this Revision.
  ExecutionEnvironment execution_environment = 20;

  // A reference to a customer managed encryption key (CMEK) to use to encrypt
  // this container image. For more information, go to
  // https://cloud.google.com/run/docs/securing/using-cmek
  string encryption_key = 21 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // Enables service mesh connectivity.
  ServiceMesh service_mesh = 22;

  // The action to take if the encryption key is revoked.
  EncryptionKeyRevocationAction encryption_key_revocation_action = 23;

  // If encryption_key_revocation_action is SHUTDOWN, the duration before
  // shutting down all instances. The minimum increment is 1 hour.
  google.protobuf.Duration encryption_key_shutdown_duration = 24;

  // Output only. Indicates whether the resource's reconciliation is still in
  // progress. See comments in `Service.reconciling` for additional information
  // on reconciliation process in Cloud Run.
  bool reconciling = 30 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Condition of this Revision, containing its readiness
  // status, and detailed error information in case it did not reach a serving
  // state.
  repeated Condition conditions = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The generation of this Revision currently serving traffic. See
  // comments in `reconciling` for additional information on reconciliation
  // process in Cloud Run.
  int64 observed_generation = 32 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Google Console URI to obtain logs for the Revision.
  string log_uri = 33 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 37 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Enable session affinity.
  bool session_affinity = 38;

  // Output only. The current effective scaling settings for the revision.
  RevisionScalingStatus scaling_status = 39
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The node selector for the revision.
  NodeSelector node_selector = 40;

  // Output only. A system-generated fingerprint for this version of the
  // resource. May be used to detect modification conflict during updates.
  string etag = 99 [(google.api.field_behavior) = OUTPUT_ONLY];
}
