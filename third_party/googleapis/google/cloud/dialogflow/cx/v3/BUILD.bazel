# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "cx_proto",
    srcs = [
        "advanced_settings.proto",
        "agent.proto",
        "audio_config.proto",
        "changelog.proto",
        "data_store_connection.proto",
        "deployment.proto",
        "entity_type.proto",
        "environment.proto",
        "experiment.proto",
        "flow.proto",
        "fulfillment.proto",
        "gcs.proto",
        "generative_settings.proto",
        "generator.proto",
        "import_strategy.proto",
        "inline.proto",
        "intent.proto",
        "page.proto",
        "response_message.proto",
        "safety_settings.proto",
        "security_settings.proto",
        "session.proto",
        "session_entity_type.proto",
        "test_case.proto",
        "transition_route_group.proto",
        "validation_message.proto",
        "version.proto",
        "webhook.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "cx_proto_with_info",
    deps = [
        ":cx_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "cx_java_proto",
    deps = [":cx_proto"],
)

java_grpc_library(
    name = "cx_java_grpc",
    srcs = [":cx_proto"],
    deps = [":cx_java_proto"],
)

java_gapic_library(
    name = "cx_java_gapic",
    srcs = [":cx_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "dialogflow_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v3.yaml",
    test_deps = [
        ":cx_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":cx_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "cx_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.dialogflow.cx.v3.AgentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.AgentsClientTest",
        "com.google.cloud.dialogflow.cx.v3.ChangelogsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.ChangelogsClientTest",
        "com.google.cloud.dialogflow.cx.v3.DeploymentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.DeploymentsClientTest",
        "com.google.cloud.dialogflow.cx.v3.EntityTypesClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.EntityTypesClientTest",
        "com.google.cloud.dialogflow.cx.v3.EnvironmentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.EnvironmentsClientTest",
        "com.google.cloud.dialogflow.cx.v3.ExperimentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.ExperimentsClientTest",
        "com.google.cloud.dialogflow.cx.v3.FlowsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.FlowsClientTest",
        "com.google.cloud.dialogflow.cx.v3.GeneratorsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.GeneratorsClientTest",
        "com.google.cloud.dialogflow.cx.v3.IntentsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.IntentsClientTest",
        "com.google.cloud.dialogflow.cx.v3.PagesClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.PagesClientTest",
        "com.google.cloud.dialogflow.cx.v3.SecuritySettingsServiceClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.SecuritySettingsServiceClientTest",
        "com.google.cloud.dialogflow.cx.v3.SessionEntityTypesClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.SessionEntityTypesClientTest",
        "com.google.cloud.dialogflow.cx.v3.SessionsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.SessionsClientTest",
        "com.google.cloud.dialogflow.cx.v3.TestCasesClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.TestCasesClientTest",
        "com.google.cloud.dialogflow.cx.v3.TransitionRouteGroupsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.TransitionRouteGroupsClientTest",
        "com.google.cloud.dialogflow.cx.v3.VersionsClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.VersionsClientTest",
        "com.google.cloud.dialogflow.cx.v3.WebhooksClientHttpJsonTest",
        "com.google.cloud.dialogflow.cx.v3.WebhooksClientTest",
    ],
    runtime_deps = [":cx_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-dialogflow-cx-v3-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":cx_java_gapic",
        ":cx_java_grpc",
        ":cx_java_proto",
        ":cx_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "cx_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb",
    protos = [":cx_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "cx_go_gapic",
    srcs = [":cx_proto_with_info"],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    importpath = "cloud.google.com/go/dialogflow/cx/apiv3;cx",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":cx_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-dialogflow-cx-v3-go",
    deps = [
        ":cx_go_gapic",
        ":cx_go_gapic_srcjar-metadata.srcjar",
        ":cx_go_gapic_srcjar-snippets.srcjar",
        ":cx_go_gapic_srcjar-test.srcjar",
        ":cx_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "cx_py_gapic",
    srcs = [":cx_proto"],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=dialogflowcx",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-dialogflow-cx",
    ],
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v3.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "cx_py_gapic_test",
    srcs = [
        "cx_py_gapic_pytest.py",
        "cx_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":cx_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "dialogflow-cx-v3-py",
    deps = [
        ":cx_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "cx_php_proto",
    deps = [":cx_proto"],
)

php_gapic_library(
    name = "cx_php_gapic",
    srcs = [":cx_proto_with_info"],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":cx_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-dialogflow-cx-v3-php",
    deps = [
        ":cx_php_gapic",
        ":cx_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "cx_nodejs_gapic",
    package_name = "@google-cloud/dialogflow-cx",
    src = ":cx_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    mixins = "google.longrunning.Operations;google.cloud.location.Locations",
    package = "google.cloud.dialogflow.cx.v3",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v3.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "dialogflow-cx-v3-nodejs",
    deps = [
        ":cx_nodejs_gapic",
        ":cx_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "cx_ruby_proto",
    deps = [":cx_proto"],
)

ruby_grpc_library(
    name = "cx_ruby_grpc",
    srcs = [":cx_proto"],
    deps = [":cx_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "cx_ruby_gapic",
    srcs = [":cx_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=dialogflow.googleapis.com",
        "ruby-cloud-api-shortname=dialogflow",
        "ruby-cloud-env-prefix=DIALOGFLOW",
        "ruby-cloud-gem-name=google-cloud-dialogflow-cx-v3",
        "ruby-cloud-namespace-override=Cx=CX",
        "ruby-cloud-product-url=https://cloud.google.com/dialogflow",
    ],
    grpc_service_config = "dialogflow_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Dialogflow is an end-to-end, build-once deploy-everywhere development suite for creating conversational interfaces for websites, mobile applications, popular messaging platforms, and IoT devices. You can use it to build interfaces (such as chatbots and conversational IVR) that enable natural and rich interactions between your users and your business. This client is for Dialogflow CX, providing an advanced agent type suitable for large or very complex agents.",
    ruby_cloud_title = "Dialogflow CX V3",
    service_yaml = "dialogflow_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":cx_ruby_grpc",
        ":cx_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-dialogflow-cx-v3-ruby",
    deps = [
        ":cx_ruby_gapic",
        ":cx_ruby_grpc",
        ":cx_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "cx_csharp_proto",
    deps = [":cx_proto"],
)

csharp_grpc_library(
    name = "cx_csharp_grpc",
    srcs = [":cx_proto"],
    deps = [":cx_csharp_proto"],
)

csharp_gapic_library(
    name = "cx_csharp_gapic",
    srcs = [":cx_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "dialogflow_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dialogflow_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":cx_csharp_grpc",
        ":cx_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-dialogflow-cx-v3-csharp",
    deps = [
        ":cx_csharp_gapic",
        ":cx_csharp_grpc",
        ":cx_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "cx_cc_proto",
    deps = [":cx_proto"],
)

cc_grpc_library(
    name = "cx_cc_grpc",
    srcs = [":cx_proto"],
    grpc_only = True,
    deps = [":cx_cc_proto"],
)
