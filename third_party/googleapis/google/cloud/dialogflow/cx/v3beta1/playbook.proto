// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3beta1/example.proto";
import "google/cloud/dialogflow/cx/v3beta1/generative_settings.proto";
import "google/cloud/dialogflow/cx/v3beta1/parameter_definition.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "PlaybookProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";

// Service for managing
// [Playbooks][google.cloud.dialogflow.cx.v3beta1.Playbook].
service Playbooks {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Creates a playbook in a specified agent.
  rpc CreatePlaybook(CreatePlaybookRequest) returns (Playbook) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*}/playbooks"
      body: "playbook"
    };
    option (google.api.method_signature) = "parent,playbook";
  }

  // Deletes a specified playbook.
  rpc DeletePlaybook(DeletePlaybookRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/agents/*/playbooks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of playbooks in the specified agent.
  rpc ListPlaybooks(ListPlaybooksRequest) returns (ListPlaybooksResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*}/playbooks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified Playbook.
  rpc GetPlaybook(GetPlaybookRequest) returns (Playbook) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/playbooks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the specified Playbook.
  rpc UpdatePlaybook(UpdatePlaybookRequest) returns (Playbook) {
    option (google.api.http) = {
      patch: "/v3beta1/{playbook.name=projects/*/locations/*/agents/*/playbooks/*}"
      body: "playbook"
    };
    option (google.api.method_signature) = "playbook,update_mask";
  }

  // Creates a version for the specified Playbook.
  rpc CreatePlaybookVersion(CreatePlaybookVersionRequest)
      returns (PlaybookVersion) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*/playbooks/*}/versions"
      body: "playbook_version"
    };
    option (google.api.method_signature) = "parent,playbook_version";
  }

  // Retrieves the specified version of the Playbook.
  rpc GetPlaybookVersion(GetPlaybookVersionRequest) returns (PlaybookVersion) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/playbooks/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists versions for the specified Playbook.
  rpc ListPlaybookVersions(ListPlaybookVersionsRequest)
      returns (ListPlaybookVersionsResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*/playbooks/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes the specified version of the Playbook.
  rpc DeletePlaybookVersion(DeletePlaybookVersionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/agents/*/playbooks/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// The request message for
// [Playbooks.CreatePlaybook][google.cloud.dialogflow.cx.v3beta1.Playbooks.CreatePlaybook].
message CreatePlaybookRequest {
  // Required. The agent to create a playbook for.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Playbook"
    }
  ];

  // Required. The playbook to create.
  Playbook playbook = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Playbooks.DeletePlaybook][google.cloud.dialogflow.cx.v3beta1.Playbooks.DeletePlaybook].
message DeletePlaybookRequest {
  // Required. The name of the playbook to delete.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Playbook"
    }
  ];
}

// The request message for
// [Playbooks.ListPlaybooks][google.cloud.dialogflow.cx.v3beta1.Playbooks.ListPlaybooks].
message ListPlaybooksRequest {
  // Required. The agent to list playbooks from.
  // Format: `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Playbook"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Playbooks.ListPlaybooks][google.cloud.dialogflow.cx.v3beta1.Playbooks.ListPlaybooks].
message ListPlaybooksResponse {
  // The list of playbooks. There will be a maximum number of items returned
  // based on the page_size field in the request.
  repeated Playbook playbooks = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Playbooks.GetPlaybook][google.cloud.dialogflow.cx.v3beta1.Playbooks.GetPlaybook].
message GetPlaybookRequest {
  // Required. The name of the playbook.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Playbook"
    }
  ];
}

// The request message for
// [Playbooks.UpdatePlaybook][google.cloud.dialogflow.cx.v3beta1.Playbooks.UpdatePlaybook].
message UpdatePlaybookRequest {
  // Required. The playbook to update.
  Playbook playbook = 1 [(google.api.field_behavior) = REQUIRED];

  // The mask to control which fields get updated. If the mask is not present,
  // all fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Playbook is the basic building block to instruct the LLM how to execute a
// certain task.
//
// A playbook consists of a goal to accomplish, an optional list of step by step
// instructions (the step instruction may refers to name of the custom or
// default plugin tools to use) to perform the task,
// a list of contextual input data to be passed in at the beginning of the
// invoked, and a list of output parameters to store the playbook result.
message Playbook {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Playbook"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/playbooks/{playbook}"
  };

  // Message of single step execution.
  message Step {
    // Instruction on how to execute current step.
    oneof instruction {
      // Step instruction in text format.
      string text = 1;
    }

    // Sub-processing needed to execute the current step.
    repeated Step steps = 2;
  }

  // Message of the Instruction of the playbook.
  message Instruction {
    // Ordered list of step by step execution instructions to accomplish
    // target goal.
    repeated Step steps = 2;
  }

  // The unique identifier of the playbook.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>`.
  string name = 1;

  // Required. The human-readable name of the playbook, unique within an agent.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. High level description of the goal the playbook intend to
  // accomplish.
  string goal = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Defined structured input parameters for this playbook.
  repeated google.cloud.dialogflow.cx.v3beta1.ParameterDefinition
      input_parameter_definitions = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defined structured output parameters for this playbook.
  repeated google.cloud.dialogflow.cx.v3beta1.ParameterDefinition
      output_parameter_definitions = 6 [(google.api.field_behavior) = OPTIONAL];

  // Instruction to accomplish target goal.
  Instruction instruction = 17;

  // Output only. Estimated number of tokes current playbook takes when sent to
  // the LLM.
  int64 token_count = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of initial playbook creation.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last time the playbook version was updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource name of other playbooks referenced by the current
  // playbook in the instructions.
  repeated string referenced_playbooks = 11 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Playbook"
    }
  ];

  // Output only. The resource name of flows referenced by the current playbook
  // in the instructions.
  repeated string referenced_flows = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Flow" }
  ];

  // Optional. The resource name of tools referenced by the current playbook in
  // the instructions. If not provided explicitly, they are will
  // be implied using the tool being referenced in goal and steps.
  repeated string referenced_tools = 13 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "dialogflow.googleapis.com/Tool" }
  ];

  // Optional. Llm model settings for the playbook.
  LlmModelSettings llm_model_settings = 14
      [(google.api.field_behavior) = OPTIONAL];
}

// The request message for
// [Playbooks.CreatePlaybookVersion][google.cloud.dialogflow.cx.v3beta1.Playbooks.CreatePlaybookVersion].
message CreatePlaybookVersionRequest {
  // Required. The playbook to create a version for.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/PlaybookVersion"
    }
  ];

  // Required. The playbook version to create.
  PlaybookVersion playbook_version = 2 [(google.api.field_behavior) = REQUIRED];
}

// Playbook version is a snapshot of the playbook at certain timestamp.
message PlaybookVersion {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/PlaybookVersion"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/playbooks/{playbook}/versions/{version}"
    plural: "playbookVersions"
    singular: "playbookVersion"
  };

  // The unique identifier of the playbook version.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>/versions/<VersionID>`.
  string name = 1;

  // Optional. The description of the playbook version.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Snapshot of the playbook when the playbook version is created.
  Playbook playbook = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Snapshot of the examples belonging to the playbook when the
  // playbook version is created.
  repeated Example examples = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last time the playbook version was created or modified.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request message for
// [Playbooks.GetPlaybookVersion][google.cloud.dialogflow.cx.v3beta1.Playbooks.GetPlaybookVersion].
message GetPlaybookVersionRequest {
  // Required. The name of the playbook version.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>/versions/<VersionID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/PlaybookVersion"
    }
  ];
}

// The request message for
// [Playbooks.ListPlaybookVersions][google.cloud.dialogflow.cx.v3beta1.Playbooks.ListPlaybookVersions].
message ListPlaybookVersionsRequest {
  // Required. The playbook to list versions for.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/PlaybookVersion"
    }
  ];

  // Optional. The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous list request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [Playbooks.ListPlaybookVersions][google.cloud.dialogflow.cx.v3beta1.Playbooks.ListPlaybookVersions].
message ListPlaybookVersionsResponse {
  // The list of playbook version. There will be a maximum number of items
  // returned based on the page_size field in the request.
  repeated PlaybookVersion playbook_versions = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Playbooks.DeletePlaybookVersion][google.cloud.dialogflow.cx.v3beta1.Playbooks.DeletePlaybookVersion].
message DeletePlaybookVersionRequest {
  // Required. The name of the playbook version to delete.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>/versions/<VersionID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/PlaybookVersion"
    }
  ];
}
