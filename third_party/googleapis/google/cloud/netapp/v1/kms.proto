// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.netapp.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetApp.V1";
option go_package = "cloud.google.com/go/netapp/apiv1/netapppb;netapppb";
option java_multiple_files = true;
option java_outer_classname = "KmsProto";
option java_package = "com.google.cloud.netapp.v1";
option php_namespace = "Google\\Cloud\\NetApp\\V1";
option ruby_package = "Google::Cloud::NetApp::V1";

// GetKmsConfigRequest gets a KMS Config.
message GetKmsConfigRequest {
  // Required. Name of the KmsConfig
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/KmsConfig"
    }
  ];
}

// ListKmsConfigsRequest lists KMS Configs.
message ListKmsConfigsRequest {
  // Required. Parent value
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/KmsConfig"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 4;

  // List filter.
  string filter = 5;
}

// ListKmsConfigsResponse is the response to a ListKmsConfigsRequest.
message ListKmsConfigsResponse {
  // The list of KmsConfigs
  repeated KmsConfig kms_configs = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// CreateKmsConfigRequest creates a KMS Config.
message CreateKmsConfigRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "netapp.googleapis.com/KmsConfig"
    }
  ];

  // Required. Id of the requesting KmsConfig. Must be unique within the parent
  // resource. Must contain only letters, numbers and hyphen, with the first
  // character a letter, the last a letter or a
  // number, and a 63 character maximum.
  string kms_config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The required parameters to create a new KmsConfig.
  KmsConfig kms_config = 3 [(google.api.field_behavior) = REQUIRED];
}

// UpdateKmsConfigRequest updates a KMS Config.
message UpdateKmsConfigRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // KmsConfig resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The KmsConfig being updated
  KmsConfig kms_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// DeleteKmsConfigRequest deletes a KMS Config.
message DeleteKmsConfigRequest {
  // Required. Name of the KmsConfig.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/KmsConfig"
    }
  ];
}

// EncryptVolumesRequest specifies the KMS config to encrypt existing volumes.
message EncryptVolumesRequest {
  // Required. Name of the KmsConfig.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/KmsConfig"
    }
  ];
}

// VerifyKmsConfigRequest specifies the KMS config to be validated.
message VerifyKmsConfigRequest {
  // Required. Name of the KMS Config to be verified.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "netapp.googleapis.com/KmsConfig"
    }
  ];
}

// VerifyKmsConfigResponse contains the information if the config is correctly
// and error message.
message VerifyKmsConfigResponse {
  // Output only. If the customer key configured correctly to the encrypt
  // volume.
  bool healthy = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Error message if config is not healthy.
  string health_error = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Instructions for the customers to provide the access to the
  // encryption key.
  string instructions = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// KmsConfig is the customer managed encryption key(CMEK) configuration.
message KmsConfig {
  option (google.api.resource) = {
    type: "netapp.googleapis.com/KmsConfig"
    pattern: "projects/{project}/locations/{location}/kmsConfigs/{kms_config}"
    plural: "kmsConfigs"
    singular: "kmsConfig"
  };

  // The KmsConfig States
  enum State {
    // Unspecified KmsConfig State
    STATE_UNSPECIFIED = 0;

    // KmsConfig State is Ready
    READY = 1;

    // KmsConfig State is Creating
    CREATING = 2;

    // KmsConfig State is Deleting
    DELETING = 3;

    // KmsConfig State is Updating
    UPDATING = 4;

    // KmsConfig State is In Use.
    IN_USE = 5;

    // KmsConfig State is Error
    ERROR = 6;

    // KmsConfig State is Pending to verify crypto key access.
    KEY_CHECK_PENDING = 7;

    // KmsConfig State is Not accessbile by the SDE service account to the
    // crypto key.
    KEY_NOT_REACHABLE = 8;

    // KmsConfig State is Disabling.
    DISABLING = 9;

    // KmsConfig State is Disabled.
    DISABLED = 10;

    // KmsConfig State is Migrating.
    // The existing volumes are migrating from SMEK to CMEK.
    MIGRATING = 11;
  }

  // Identifier. Name of the KmsConfig.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Customer managed crypto key resource full name. Format:
  // projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{key}.
  string crypto_key_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. State of the KmsConfig.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State details of the KmsConfig.
  string state_details = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create time of the KmsConfig.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Description of the KmsConfig.
  string description = 6;

  // Labels as key value pairs
  map<string, string> labels = 7;

  // Output only. Instructions to provide the access to the customer provided
  // encryption key.
  string instructions = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Service account which will have access to the customer
  // provided encryption key.
  string service_account = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}
