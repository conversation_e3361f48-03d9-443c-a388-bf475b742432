type: google.api.Service
config_version: 3
name: documentai.googleapis.com
title: Cloud Document AI API

apis:
- name: google.cloud.documentai.v1beta3.DocumentProcessorService
- name: google.cloud.documentai.v1beta3.DocumentService
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.documentai.v1beta3.BatchDeleteDocumentsMetadata
- name: google.cloud.documentai.v1beta3.BatchDeleteDocumentsResponse
- name: google.cloud.documentai.v1beta3.BatchProcessMetadata
- name: google.cloud.documentai.v1beta3.BatchProcessResponse
- name: google.cloud.documentai.v1beta3.Dataset
- name: google.cloud.documentai.v1beta3.DeleteProcessorMetadata
- name: google.cloud.documentai.v1beta3.DeleteProcessorVersionMetadata
- name: google.cloud.documentai.v1beta3.DeployProcessorVersionMetadata
- name: google.cloud.documentai.v1beta3.DeployProcessorVersionResponse
- name: google.cloud.documentai.v1beta3.DisableProcessorMetadata
- name: google.cloud.documentai.v1beta3.DisableProcessorResponse
- name: google.cloud.documentai.v1beta3.EnableProcessorMetadata
- name: google.cloud.documentai.v1beta3.EnableProcessorResponse
- name: google.cloud.documentai.v1beta3.EvaluateProcessorVersionMetadata
- name: google.cloud.documentai.v1beta3.EvaluateProcessorVersionResponse
- name: google.cloud.documentai.v1beta3.ImportDocumentsMetadata
- name: google.cloud.documentai.v1beta3.ImportDocumentsResponse
- name: google.cloud.documentai.v1beta3.ImportProcessorVersionMetadata
- name: google.cloud.documentai.v1beta3.ImportProcessorVersionResponse
- name: google.cloud.documentai.v1beta3.ReviewDocumentOperationMetadata
- name: google.cloud.documentai.v1beta3.ReviewDocumentResponse
- name: google.cloud.documentai.v1beta3.SetDefaultProcessorVersionMetadata
- name: google.cloud.documentai.v1beta3.SetDefaultProcessorVersionResponse
- name: google.cloud.documentai.v1beta3.TrainProcessorVersionMetadata
- name: google.cloud.documentai.v1beta3.TrainProcessorVersionResponse
- name: google.cloud.documentai.v1beta3.UndeployProcessorVersionMetadata
- name: google.cloud.documentai.v1beta3.UndeployProcessorVersionResponse
- name: google.cloud.documentai.v1beta3.UpdateDatasetOperationMetadata

documentation:
  summary: |-
    Service to parse structured information from unstructured or
    semi-structured documents using state-of-the-art Google AI such as natural
    language, computer vision, translation, and AutoML.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1beta3/{name=projects/*/locations/*}'
    additional_bindings:
    - get: '/uiv1beta3/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1beta3/{name=projects/*}/locations'
    additional_bindings:
    - get: '/uiv1beta3/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1beta3/{name=projects/*/locations/*/operations/*}:cancel'
    additional_bindings:
    - post: '/uiv1beta3/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta3/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/uiv1beta3/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1beta3/{name=projects/*/locations/*/operations}'
    additional_bindings:
    - get: '/uiv1beta3/{name=projects/*/locations/*/operations}'

authentication:
  rules:
  - selector: 'google.cloud.documentai.v1beta3.DocumentProcessorService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.documentai.v1beta3.DocumentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1132231&template=1639002
  documentation_uri: https://cloud.google.com/document-ai/docs
  api_short_name: documentai
  github_label: 'api: documentai'
  organization: CLOUD
  library_settings:
  - version: google.cloud.documentai.v1beta3
    java_settings:
      common: {}
    cpp_settings:
      common: {}
    php_settings:
      common: {}
    python_settings:
      common: {}
    node_settings:
      common: {}
    dotnet_settings:
      common: {}
      ignored_resources:
      - documentai.googleapis.com/Location
    ruby_settings:
      common: {}
    go_settings:
      common: {}
  proto_reference_documentation_uri: https://cloud.google.com/document-ai/docs/reference/rpc
