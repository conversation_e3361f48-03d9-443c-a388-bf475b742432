// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.sql.v1beta4;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/sql/v1beta4/cloud_sql_resources.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/sql/apiv1beta4/sqlpb;sqlpb";
option java_multiple_files = true;
option java_outer_classname = "CloudSqlUsersProto";
option java_package = "com.google.cloud.sql.v1beta4";

service SqlUsersService {
  option (google.api.default_host) = "sqladmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/sqlservice.admin";

  // Deletes a user from a Cloud SQL instance.
  rpc Delete(SqlUsersDeleteRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/sql/v1beta4/projects/{project}/instances/{instance}/users"
    };
  }

  // Retrieves a resource containing information about a user.
  rpc Get(SqlUsersGetRequest) returns (User) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/users/{name}"
    };
  }

  // Creates a new user in a Cloud SQL instance.
  rpc Insert(SqlUsersInsertRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/sql/v1beta4/projects/{project}/instances/{instance}/users"
      body: "body"
    };
  }

  // Lists users in the specified Cloud SQL instance.
  rpc List(SqlUsersListRequest) returns (UsersListResponse) {
    option (google.api.http) = {
      get: "/sql/v1beta4/projects/{project}/instances/{instance}/users"
    };
  }

  // Updates an existing user in a Cloud SQL instance.
  rpc Update(SqlUsersUpdateRequest) returns (Operation) {
    option (google.api.http) = {
      put: "/sql/v1beta4/projects/{project}/instances/{instance}/users"
      body: "body"
    };
  }
}

message SqlUsersDeleteRequest {
  // Host of the user in the instance.
  string host = 1;

  // Database instance ID. This does not include the project ID.
  string instance = 2;

  // Name of the user in the instance.
  string name = 3;

  // Project ID of the project that contains the instance.
  string project = 4;
}

// Request message for Users Get RPC
message SqlUsersGetRequest {
  // Database instance ID. This does not include the project ID.
  string instance = 1;

  // User of the instance.
  string name = 2;

  // Project ID of the project that contains the instance.
  string project = 3;

  // Host of a user of the instance.
  string host = 4;
}

message SqlUsersInsertRequest {
  // Database instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;

  User body = 100;
}

message SqlUsersListRequest {
  // Database instance ID. This does not include the project ID.
  string instance = 1;

  // Project ID of the project that contains the instance.
  string project = 2;
}

message SqlUsersUpdateRequest {
  // Optional. Host of the user in the instance.
  string host = 1 [(google.api.field_behavior) = OPTIONAL];

  // Database instance ID. This does not include the project ID.
  string instance = 2;

  // Name of the user in the instance.
  string name = 3;

  // Project ID of the project that contains the instance.
  string project = 4;

  User body = 100;
}

// User level password validation policy.
message UserPasswordValidationPolicy {
  // Number of failed login attempts allowed before user get locked.
  int32 allowed_failed_attempts = 1;

  // Expiration duration after password is updated.
  google.protobuf.Duration password_expiration_duration = 2;

  // If true, failed login attempts check will be enabled.
  bool enable_failed_attempts_check = 3;

  // Output only. Read-only password status.
  PasswordStatus status = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // If true, the user must specify the current password before changing the
  // password. This flag is supported only for MySQL.
  bool enable_password_verification = 5;
}

// Read-only password status.
message PasswordStatus {
  // If true, user does not have login privileges.
  bool locked = 1;

  // The expiration time of the current password.
  google.protobuf.Timestamp password_expiration_time = 2;
}

// A Cloud SQL user resource.
message User {
  // The user type.
  enum SqlUserType {
    // The database's built-in user type.
    BUILT_IN = 0;

    // Cloud IAM user.
    CLOUD_IAM_USER = 1;

    // Cloud IAM service account.
    CLOUD_IAM_SERVICE_ACCOUNT = 2;

    // Cloud IAM group non-login user.
    CLOUD_IAM_GROUP = 3;

    // Cloud IAM group login user.
    CLOUD_IAM_GROUP_USER = 4;

    // Cloud IAM group service account.
    CLOUD_IAM_GROUP_SERVICE_ACCOUNT = 5;
  }

  // The type of retained password.
  enum DualPasswordType {
    // The default value.
    DUAL_PASSWORD_TYPE_UNSPECIFIED = 0;

    // Do not update the user's dual password status.
    NO_MODIFY_DUAL_PASSWORD = 1;

    // No dual password usable for connecting using this user.
    NO_DUAL_PASSWORD = 2;

    // Dual password usable for connecting using this user.
    DUAL_PASSWORD = 3;
  }

  // This is always `sql#user`.
  string kind = 1;

  // The password for the user.
  string password = 2;

  // This field is deprecated and will be removed from a future version of the
  // API.
  string etag = 3;

  // The name of the user in the Cloud SQL instance. Can be omitted for
  // `update` because it is already specified in the URL.
  string name = 4;

  // Optional. The host from which the user can connect. For `insert`
  // operations, host defaults to an empty string. For `update`
  // operations, host is specified as part of the request URL. The host name
  // cannot be updated after insertion.  For a MySQL instance, it's required;
  // for a PostgreSQL or SQL Server instance, it's optional.
  string host = 5 [(google.api.field_behavior) = OPTIONAL];

  // The name of the Cloud SQL instance. This does not include the project ID.
  // Can be omitted for <b>update</b> because it is already specified on the
  // URL.
  string instance = 6;

  // The project ID of the project containing the Cloud SQL database. The Google
  // apps domain is prefixed if applicable. Can be omitted for
  // <b>update</b> because it is already specified on the URL.
  string project = 7;

  // The user type. It determines the method to authenticate the user during
  // login. The default is the database's built-in user type.
  SqlUserType type = 8;

  // User details for specific database type
  oneof user_details {
    SqlServerUserDetails sqlserver_user_details = 9;
  }

  // User level password validation policy.
  UserPasswordValidationPolicy password_policy = 12;

  // Dual password status for the user.
  optional DualPasswordType dual_password_type = 13;
}

// Represents a Sql Server user on the Cloud SQL instance.
message SqlServerUserDetails {
  // If the user has been disabled
  bool disabled = 1;

  // The server roles for this user
  repeated string server_roles = 2;
}

// User list response.
message UsersListResponse {
  // This is always <b>sql#usersList</b>.
  string kind = 1;

  // List of user resources in the instance.
  repeated User items = 2;

  // Unused.
  string next_page_token = 3 [deprecated = true];
}
