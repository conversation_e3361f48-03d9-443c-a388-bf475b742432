// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.security.privateca.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/security/privateca/v1/resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Security.PrivateCA.V1";
option go_package = "cloud.google.com/go/security/privateca/apiv1/privatecapb;privatecapb";
option java_multiple_files = true;
option java_outer_classname = "PrivateCaProto";
option java_package = "com.google.cloud.security.privateca.v1";
option php_namespace = "Google\\Cloud\\Security\\PrivateCA\\V1";
option ruby_package = "Google::Cloud::Security::PrivateCA::V1";

// [Certificate Authority
// Service][google.cloud.security.privateca.v1.CertificateAuthorityService]
// manages private certificate authorities and issued certificates.
service CertificateAuthorityService {
  option (google.api.default_host) = "privateca.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a new [Certificate][google.cloud.security.privateca.v1.Certificate]
  // in a given Project, Location from a particular
  // [CaPool][google.cloud.security.privateca.v1.CaPool].
  rpc CreateCertificate(CreateCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/caPools/*}/certificates"
      body: "certificate"
    };
    option (google.api.method_signature) = "parent,certificate,certificate_id";
  }

  // Returns a [Certificate][google.cloud.security.privateca.v1.Certificate].
  rpc GetCertificate(GetCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/caPools/*/certificates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [Certificates][google.cloud.security.privateca.v1.Certificate].
  rpc ListCertificates(ListCertificatesRequest)
      returns (ListCertificatesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/caPools/*}/certificates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Revoke a [Certificate][google.cloud.security.privateca.v1.Certificate].
  rpc RevokeCertificate(RevokeCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/caPools/*/certificates/*}:revoke"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Update a [Certificate][google.cloud.security.privateca.v1.Certificate].
  // Currently, the only field you can update is the
  // [labels][google.cloud.security.privateca.v1.Certificate.labels] field.
  rpc UpdateCertificate(UpdateCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      patch: "/v1/{certificate.name=projects/*/locations/*/caPools/*/certificates/*}"
      body: "certificate"
    };
    option (google.api.method_signature) = "certificate,update_mask";
  }

  // Activate a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // that is in state
  // [AWAITING_USER_ACTIVATION][google.cloud.security.privateca.v1.CertificateAuthority.State.AWAITING_USER_ACTIVATION]
  // and is of type
  // [SUBORDINATE][google.cloud.security.privateca.v1.CertificateAuthority.Type.SUBORDINATE].
  // After the parent Certificate Authority signs a certificate signing request
  // from
  // [FetchCertificateAuthorityCsr][google.cloud.security.privateca.v1.CertificateAuthorityService.FetchCertificateAuthorityCsr],
  // this method can complete the activation process.
  rpc ActivateCertificateAuthority(ActivateCertificateAuthorityRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/caPools/*/certificateAuthorities/*}:activate"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Create a new
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in a given Project and Location.
  rpc CreateCertificateAuthority(CreateCertificateAuthorityRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/caPools/*}/certificateAuthorities"
      body: "certificate_authority"
    };
    option (google.api.method_signature) =
        "parent,certificate_authority,certificate_authority_id";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Disable a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  rpc DisableCertificateAuthority(DisableCertificateAuthorityRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/caPools/*/certificateAuthorities/*}:disable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Enable a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  rpc EnableCertificateAuthority(EnableCertificateAuthorityRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/caPools/*/certificateAuthorities/*}:enable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Fetch a certificate signing request (CSR) from a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // that is in state
  // [AWAITING_USER_ACTIVATION][google.cloud.security.privateca.v1.CertificateAuthority.State.AWAITING_USER_ACTIVATION]
  // and is of type
  // [SUBORDINATE][google.cloud.security.privateca.v1.CertificateAuthority.Type.SUBORDINATE].
  // The CSR must then be signed by the desired parent Certificate Authority,
  // which could be another
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // resource, or could be an on-prem certificate authority. See also
  // [ActivateCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.ActivateCertificateAuthority].
  rpc FetchCertificateAuthorityCsr(FetchCertificateAuthorityCsrRequest)
      returns (FetchCertificateAuthorityCsrResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/caPools/*/certificateAuthorities/*}:fetch"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  rpc GetCertificateAuthority(GetCertificateAuthorityRequest)
      returns (CertificateAuthority) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/caPools/*/certificateAuthorities/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists
  // [CertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthority].
  rpc ListCertificateAuthorities(ListCertificateAuthoritiesRequest)
      returns (ListCertificateAuthoritiesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/caPools/*}/certificateAuthorities"
    };
    option (google.api.method_signature) = "parent";
  }

  // Undelete a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // that has been deleted.
  rpc UndeleteCertificateAuthority(UndeleteCertificateAuthorityRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/caPools/*/certificateAuthorities/*}:undelete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  rpc DeleteCertificateAuthority(DeleteCertificateAuthorityRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/caPools/*/certificateAuthorities/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  rpc UpdateCertificateAuthority(UpdateCertificateAuthorityRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{certificate_authority.name=projects/*/locations/*/caPools/*/certificateAuthorities/*}"
      body: "certificate_authority"
    };
    option (google.api.method_signature) = "certificate_authority,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Create a [CaPool][google.cloud.security.privateca.v1.CaPool].
  rpc CreateCaPool(CreateCaPoolRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/caPools"
      body: "ca_pool"
    };
    option (google.api.method_signature) = "parent,ca_pool,ca_pool_id";
    option (google.longrunning.operation_info) = {
      response_type: "CaPool"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a [CaPool][google.cloud.security.privateca.v1.CaPool].
  rpc UpdateCaPool(UpdateCaPoolRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{ca_pool.name=projects/*/locations/*/caPools/*}"
      body: "ca_pool"
    };
    option (google.api.method_signature) = "ca_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CaPool"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns a [CaPool][google.cloud.security.privateca.v1.CaPool].
  rpc GetCaPool(GetCaPoolRequest) returns (CaPool) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/caPools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [CaPools][google.cloud.security.privateca.v1.CaPool].
  rpc ListCaPools(ListCaPoolsRequest) returns (ListCaPoolsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/caPools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Delete a [CaPool][google.cloud.security.privateca.v1.CaPool].
  rpc DeleteCaPool(DeleteCaPoolRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/caPools/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // FetchCaCerts returns the current trust anchor for the
  // [CaPool][google.cloud.security.privateca.v1.CaPool]. This will include CA
  // certificate chains for all certificate authorities in the ENABLED,
  // DISABLED, or STAGED states.
  rpc FetchCaCerts(FetchCaCertsRequest) returns (FetchCaCertsResponse) {
    option (google.api.http) = {
      post: "/v1/{ca_pool=projects/*/locations/*/caPools/*}:fetchCaCerts"
      body: "*"
    };
    option (google.api.method_signature) = "ca_pool";
  }

  // Returns a
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList].
  rpc GetCertificateRevocationList(GetCertificateRevocationListRequest)
      returns (CertificateRevocationList) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/caPools/*/certificateAuthorities/*/certificateRevocationLists/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists
  // [CertificateRevocationLists][google.cloud.security.privateca.v1.CertificateRevocationList].
  rpc ListCertificateRevocationLists(ListCertificateRevocationListsRequest)
      returns (ListCertificateRevocationListsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/caPools/*/certificateAuthorities/*}/certificateRevocationLists"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update a
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList].
  rpc UpdateCertificateRevocationList(UpdateCertificateRevocationListRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{certificate_revocation_list.name=projects/*/locations/*/caPools/*/certificateAuthorities/*/certificateRevocationLists/*}"
      body: "certificate_revocation_list"
    };
    option (google.api.method_signature) =
        "certificate_revocation_list,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateRevocationList"
      metadata_type: "OperationMetadata"
    };
  }

  // Create a new
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // in a given Project and Location.
  rpc CreateCertificateTemplate(CreateCertificateTemplateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/certificateTemplates"
      body: "certificate_template"
    };
    option (google.api.method_signature) =
        "parent,certificate_template,certificate_template_id";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateTemplate"
      metadata_type: "OperationMetadata"
    };
  }

  // DeleteCertificateTemplate deletes a
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate].
  rpc DeleteCertificateTemplate(DeleteCertificateTemplateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/certificateTemplates/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.security.privateca.v1.OperationMetadata"
    };
  }

  // Returns a
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate].
  rpc GetCertificateTemplate(GetCertificateTemplateRequest)
      returns (CertificateTemplate) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/certificateTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists
  // [CertificateTemplates][google.cloud.security.privateca.v1.CertificateTemplate].
  rpc ListCertificateTemplates(ListCertificateTemplatesRequest)
      returns (ListCertificateTemplatesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/certificateTemplates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update a
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate].
  rpc UpdateCertificateTemplate(UpdateCertificateTemplateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{certificate_template.name=projects/*/locations/*/certificateTemplates/*}"
      body: "certificate_template"
    };
    option (google.api.method_signature) = "certificate_template,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateTemplate"
      metadata_type: "OperationMetadata"
    };
  }
}

// Request message for
// [CertificateAuthorityService.CreateCertificate][google.cloud.security.privateca.v1.CertificateAuthorityService.CreateCertificate].
message CreateCertificateRequest {
  // Required. The resource name of the
  // [CaPool][google.cloud.security.privateca.v1.CaPool] associated with the
  // [Certificate][google.cloud.security.privateca.v1.Certificate], in the
  // format `projects/*/locations/*/caPools/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CaPool"
    }
  ];

  // Optional. It must be unique within a location and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`. This field is required when using a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the Enterprise [CertificateAuthority.Tier][], but is optional and its
  // value is ignored otherwise.
  string certificate_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A [Certificate][google.cloud.security.privateca.v1.Certificate]
  // with initial field values.
  Certificate certificate = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If this is true, no
  // [Certificate][google.cloud.security.privateca.v1.Certificate] resource will
  // be persisted regardless of the
  // [CaPool][google.cloud.security.privateca.v1.CaPool]'s
  // [tier][google.cloud.security.privateca.v1.CaPool.tier], and the returned
  // [Certificate][google.cloud.security.privateca.v1.Certificate] will not
  // contain the
  // [pem_certificate][google.cloud.security.privateca.v1.Certificate.pem_certificate]
  // field.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The resource ID of the
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // that should issue the certificate.  This optional field will ignore the
  // load-balancing scheme of the Pool and directly issue the certificate from
  // the CA with the specified ID, contained in the same
  // [CaPool][google.cloud.security.privateca.v1.CaPool] referenced by `parent`.
  // Per-CA quota rules apply. If left empty, a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // will be chosen from the [CaPool][google.cloud.security.privateca.v1.CaPool]
  // by the service. For example, to issue a
  // [Certificate][google.cloud.security.privateca.v1.Certificate] from a
  // Certificate Authority with resource name
  // "projects/my-project/locations/us-central1/caPools/my-pool/certificateAuthorities/my-ca",
  // you can set the
  // [parent][google.cloud.security.privateca.v1.CreateCertificateRequest.parent]
  // to "projects/my-project/locations/us-central1/caPools/my-pool" and the
  // [issuing_certificate_authority_id][google.cloud.security.privateca.v1.CreateCertificateRequest.issuing_certificate_authority_id]
  // to "my-ca".
  string issuing_certificate_authority_id = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.GetCertificate][google.cloud.security.privateca.v1.CertificateAuthorityService.GetCertificate].
message GetCertificateRequest {
  // Required. The [name][google.cloud.security.privateca.v1.Certificate.name]
  // of the [Certificate][google.cloud.security.privateca.v1.Certificate] to
  // get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/Certificate"
    }
  ];
}

// Request message for
// [CertificateAuthorityService.ListCertificates][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCertificates].
message ListCertificatesRequest {
  // Required. The resource name of the location associated with the
  // [Certificates][google.cloud.security.privateca.v1.Certificate], in the
  // format `projects/*/locations/*/caPools/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CaPool"
    }
  ];

  // Optional. Limit on the number of
  // [Certificates][google.cloud.security.privateca.v1.Certificate] to include
  // in the response. Further
  // [Certificates][google.cloud.security.privateca.v1.Certificate] can
  // subsequently be obtained by including the
  // [ListCertificatesResponse.next_page_token][google.cloud.security.privateca.v1.ListCertificatesResponse.next_page_token]
  // in a subsequent request. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListCertificatesResponse.next_page_token][google.cloud.security.privateca.v1.ListCertificatesResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response. For
  // details on supported filters and syntax, see [Certificates Filtering
  // documentation](https://cloud.google.com/certificate-authority-service/docs/sorting-filtering-certificates#filtering_support).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted. For details on
  // supported fields and syntax, see [Certificates Sorting
  // documentation](https://cloud.google.com/certificate-authority-service/docs/sorting-filtering-certificates#sorting_support).
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.ListCertificates][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCertificates].
message ListCertificatesResponse {
  // The list of [Certificates][google.cloud.security.privateca.v1.Certificate].
  repeated Certificate certificates = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCertificatesRequest.next_page_token][] to retrieve the
  // next page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [CertificateAuthorityService.RevokeCertificate][google.cloud.security.privateca.v1.CertificateAuthorityService.RevokeCertificate].
message RevokeCertificateRequest {
  // Required. The resource name for this
  // [Certificate][google.cloud.security.privateca.v1.Certificate] in the format
  // `projects/*/locations/*/caPools/*/certificates/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/Certificate"
    }
  ];

  // Required. The
  // [RevocationReason][google.cloud.security.privateca.v1.RevocationReason] for
  // revoking this certificate.
  RevocationReason reason = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.UpdateCertificate][google.cloud.security.privateca.v1.CertificateAuthorityService.UpdateCertificate].
message UpdateCertificateRequest {
  // Required. [Certificate][google.cloud.security.privateca.v1.Certificate]
  // with updated values.
  Certificate certificate = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.ActivateCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.ActivateCertificateAuthority].
message ActivateCertificateAuthorityRequest {
  // Required. The resource name for this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Required. The signed CA certificate issued from
  // [FetchCertificateAuthorityCsrResponse.pem_csr][google.cloud.security.privateca.v1.FetchCertificateAuthorityCsrResponse.pem_csr].
  string pem_ca_certificate = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Must include information about the issuer of
  // 'pem_ca_certificate', and any further issuers until the self-signed CA.
  SubordinateConfig subordinate_config = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.CreateCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.CreateCertificateAuthority].
message CreateCertificateAuthorityRequest {
  // Required. The resource name of the
  // [CaPool][google.cloud.security.privateca.v1.CaPool] associated with the
  // [CertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthority],
  // in the format `projects/*/locations/*/caPools/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CaPool"
    }
  ];

  // Required. It must be unique within a location and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`
  string certificate_authority_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // with initial field values.
  CertificateAuthority certificate_authority = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.DisableCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.DisableCertificateAuthority].
message DisableCertificateAuthorityRequest {
  // Required. The resource name for this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field allows this CA to be disabled even if it's being
  // depended on by another resource. However, doing so may result in unintended
  // and unrecoverable effects on any dependent resources since the CA will
  // no longer be able to issue certificates.
  bool ignore_dependent_resources = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.EnableCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.EnableCertificateAuthority].
message EnableCertificateAuthorityRequest {
  // Required. The resource name for this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.FetchCertificateAuthorityCsr][google.cloud.security.privateca.v1.CertificateAuthorityService.FetchCertificateAuthorityCsr].
message FetchCertificateAuthorityCsrRequest {
  // Required. The resource name for this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];
}

// Response message for
// [CertificateAuthorityService.FetchCertificateAuthorityCsr][google.cloud.security.privateca.v1.CertificateAuthorityService.FetchCertificateAuthorityCsr].
message FetchCertificateAuthorityCsrResponse {
  // Output only. The PEM-encoded signed certificate signing request (CSR).
  string pem_csr = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [CertificateAuthorityService.GetCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.GetCertificateAuthority].
message GetCertificateAuthorityRequest {
  // Required. The
  // [name][google.cloud.security.privateca.v1.CertificateAuthority.name] of the
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];
}

// Request message for
// [CertificateAuthorityService.ListCertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCertificateAuthorities].
message ListCertificateAuthoritiesRequest {
  // Required. The resource name of the
  // [CaPool][google.cloud.security.privateca.v1.CaPool] associated with the
  // [CertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthority],
  // in the format `projects/*/locations/*/caPools/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CaPool"
    }
  ];

  // Optional. Limit on the number of
  // [CertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthority]
  // to include in the response. Further
  // [CertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthority]
  // can subsequently be obtained by including the
  // [ListCertificateAuthoritiesResponse.next_page_token][google.cloud.security.privateca.v1.ListCertificateAuthoritiesResponse.next_page_token]
  // in a subsequent request. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListCertificateAuthoritiesResponse.next_page_token][google.cloud.security.privateca.v1.ListCertificateAuthoritiesResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.ListCertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCertificateAuthorities].
message ListCertificateAuthoritiesResponse {
  // The list of
  // [CertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthority].
  repeated CertificateAuthority certificate_authorities = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCertificateAuthoritiesRequest.next_page_token][] to retrieve the next
  // page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [CertificateAuthorityService.UndeleteCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.UndeleteCertificateAuthority].
message UndeleteCertificateAuthorityRequest {
  // Required. The resource name for this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.DeleteCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.DeleteCertificateAuthority].
message DeleteCertificateAuthorityRequest {
  // Required. The resource name for this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field allows the CA to be deleted even if the CA has
  // active certs. Active certs include both unrevoked and unexpired certs.
  bool ignore_active_certificates = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If this flag is set, the Certificate Authority will be deleted as
  // soon as possible without a 30-day grace period where undeletion would have
  // been allowed. If you proceed, there will be no way to recover this CA.
  bool skip_grace_period = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field allows this CA to be deleted even if it's being
  // depended on by another resource. However, doing so may result in unintended
  // and unrecoverable effects on any dependent resources since the CA will
  // no longer be able to issue certificates.
  bool ignore_dependent_resources = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.UpdateCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.UpdateCertificateAuthority].
message UpdateCertificateAuthorityRequest {
  // Required.
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // with updated values.
  CertificateAuthority certificate_authority = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.CreateCaPool][google.cloud.security.privateca.v1.CertificateAuthorityService.CreateCaPool].
message CreateCaPoolRequest {
  // Required. The resource name of the location associated with the
  // [CaPool][google.cloud.security.privateca.v1.CaPool], in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. It must be unique within a location and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`
  string ca_pool_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A [CaPool][google.cloud.security.privateca.v1.CaPool] with
  // initial field values.
  CaPool ca_pool = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.UpdateCaPool][google.cloud.security.privateca.v1.CertificateAuthorityService.UpdateCaPool].
message UpdateCaPoolRequest {
  // Required. [CaPool][google.cloud.security.privateca.v1.CaPool] with updated
  // values.
  CaPool ca_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.DeleteCaPool][google.cloud.security.privateca.v1.CertificateAuthorityService.DeleteCaPool].
message DeleteCaPoolRequest {
  // Required. The resource name for this
  // [CaPool][google.cloud.security.privateca.v1.CaPool] in the format
  // `projects/*/locations/*/caPools/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CaPool"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field allows this pool to be deleted even if it's being
  // depended on by another resource. However, doing so may result in unintended
  // and unrecoverable effects on any dependent resources since the pool will
  // no longer be able to issue certificates.
  bool ignore_dependent_resources = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.FetchCaCerts][google.cloud.security.privateca.v1.CertificateAuthorityService.FetchCaCerts].
message FetchCaCertsRequest {
  // Required. The resource name for the
  // [CaPool][google.cloud.security.privateca.v1.CaPool] in the format
  // `projects/*/locations/*/caPools/*`.
  string ca_pool = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CaPool"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.FetchCaCerts][google.cloud.security.privateca.v1.CertificateAuthorityService.FetchCaCerts].
message FetchCaCertsResponse {
  message CertChain {
    // The certificates that form the CA chain, from leaf to root order.
    repeated string certificates = 1;
  }

  // The PEM encoded CA certificate chains of all certificate authorities in
  // this [CaPool][google.cloud.security.privateca.v1.CaPool] in the ENABLED,
  // DISABLED, or STAGED states.
  repeated CertChain ca_certs = 1;
}

// Request message for
// [CertificateAuthorityService.GetCaPool][google.cloud.security.privateca.v1.CertificateAuthorityService.GetCaPool].
message GetCaPoolRequest {
  // Required. The [name][google.cloud.security.privateca.v1.CaPool.name] of the
  // [CaPool][google.cloud.security.privateca.v1.CaPool] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CaPool"
    }
  ];
}

// Request message for
// [CertificateAuthorityService.ListCaPools][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCaPools].
message ListCaPoolsRequest {
  // Required. The resource name of the location associated with the
  // [CaPools][google.cloud.security.privateca.v1.CaPool], in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Limit on the number of
  // [CaPools][google.cloud.security.privateca.v1.CaPool] to include in the
  // response. Further [CaPools][google.cloud.security.privateca.v1.CaPool] can
  // subsequently be obtained by including the
  // [ListCaPoolsResponse.next_page_token][google.cloud.security.privateca.v1.ListCaPoolsResponse.next_page_token]
  // in a subsequent request. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListCaPoolsResponse.next_page_token][google.cloud.security.privateca.v1.ListCaPoolsResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.ListCaPools][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCaPools].
message ListCaPoolsResponse {
  // The list of [CaPools][google.cloud.security.privateca.v1.CaPool].
  repeated CaPool ca_pools = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCertificateAuthoritiesRequest.next_page_token][] to retrieve the next
  // page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [CertificateAuthorityService.GetCertificateRevocationList][google.cloud.security.privateca.v1.CertificateAuthorityService.GetCertificateRevocationList].
message GetCertificateRevocationListRequest {
  // Required. The
  // [name][google.cloud.security.privateca.v1.CertificateRevocationList.name]
  // of the
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList]
  // to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateRevocationList"
    }
  ];
}

// Request message for
// [CertificateAuthorityService.ListCertificateRevocationLists][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCertificateRevocationLists].
message ListCertificateRevocationListsRequest {
  // Required. The resource name of the location associated with the
  // [CertificateRevocationLists][google.cloud.security.privateca.v1.CertificateRevocationList],
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. Limit on the number of
  // [CertificateRevocationLists][google.cloud.security.privateca.v1.CertificateRevocationList]
  // to include in the response. Further
  // [CertificateRevocationLists][google.cloud.security.privateca.v1.CertificateRevocationList]
  // can subsequently be obtained by including the
  // [ListCertificateRevocationListsResponse.next_page_token][google.cloud.security.privateca.v1.ListCertificateRevocationListsResponse.next_page_token]
  // in a subsequent request. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListCertificateRevocationListsResponse.next_page_token][google.cloud.security.privateca.v1.ListCertificateRevocationListsResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.ListCertificateRevocationLists][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCertificateRevocationLists].
message ListCertificateRevocationListsResponse {
  // The list of
  // [CertificateRevocationLists][google.cloud.security.privateca.v1.CertificateRevocationList].
  repeated CertificateRevocationList certificate_revocation_lists = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCertificateRevocationListsRequest.next_page_token][] to retrieve the
  // next page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [CertificateAuthorityService.UpdateCertificateRevocationList][google.cloud.security.privateca.v1.CertificateAuthorityService.UpdateCertificateRevocationList].
message UpdateCertificateRevocationListRequest {
  // Required.
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList]
  // with updated values.
  CertificateRevocationList certificate_revocation_list = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.CreateCertificateTemplate][google.cloud.security.privateca.v1.CertificateAuthorityService.CreateCertificateTemplate].
message CreateCertificateTemplateRequest {
  // Required. The resource name of the location associated with the
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate],
  // in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. It must be unique within a location and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`
  string certificate_template_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // with initial field values.
  CertificateTemplate certificate_template = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.DeleteCertificateTemplate][google.cloud.security.privateca.v1.CertificateAuthorityService.DeleteCertificateTemplate].
message DeleteCertificateTemplateRequest {
  // Required. The resource name for this
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // in the format `projects/*/locations/*/certificateTemplates/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateTemplate"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.GetCertificateTemplate][google.cloud.security.privateca.v1.CertificateAuthorityService.GetCertificateTemplate].
message GetCertificateTemplateRequest {
  // Required. The
  // [name][google.cloud.security.privateca.v1.CertificateTemplate.name] of the
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateTemplate"
    }
  ];
}

// Request message for
// [CertificateAuthorityService.ListCertificateTemplates][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCertificateTemplates].
message ListCertificateTemplatesRequest {
  // Required. The resource name of the location associated with the
  // [CertificateTemplates][google.cloud.security.privateca.v1.CertificateTemplate],
  // in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Limit on the number of
  // [CertificateTemplates][google.cloud.security.privateca.v1.CertificateTemplate]
  // to include in the response. Further
  // [CertificateTemplates][google.cloud.security.privateca.v1.CertificateTemplate]
  // can subsequently be obtained by including the
  // [ListCertificateTemplatesResponse.next_page_token][google.cloud.security.privateca.v1.ListCertificateTemplatesResponse.next_page_token]
  // in a subsequent request. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListCertificateTemplatesResponse.next_page_token][google.cloud.security.privateca.v1.ListCertificateTemplatesResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.ListCertificateTemplates][google.cloud.security.privateca.v1.CertificateAuthorityService.ListCertificateTemplates].
message ListCertificateTemplatesResponse {
  // The list of
  // [CertificateTemplates][google.cloud.security.privateca.v1.CertificateTemplate].
  repeated CertificateTemplate certificate_templates = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCertificateTemplatesRequest.next_page_token][] to retrieve
  // the next page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [CertificateAuthorityService.UpdateCertificateTemplate][google.cloud.security.privateca.v1.CertificateAuthorityService.UpdateCertificateTemplate].
message UpdateCertificateTemplateRequest {
  // Required.
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // with updated values.
  CertificateTemplate certificate_template = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that
  // if you must retry your request, the server will know to ignore the request
  // if it has already been completed. The server will guarantee that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
