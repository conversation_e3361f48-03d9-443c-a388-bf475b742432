// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/job_state.proto";
import "google/cloud/aiplatform/v1beta1/model_monitoring_spec.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/interval.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ModelMonitoringJobProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Represents a model monitoring job that analyze dataset using different
// monitoring algorithm.
message ModelMonitoringJob {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/ModelMonitoringJob"
    pattern: "projects/{project}/locations/{location}/modelMonitors/{model_monitor}/modelMonitoringJobs/{model_monitoring_job}"
  };

  // Output only. Resource name of a ModelMonitoringJob. Format:
  // `projects/{project_id}/locations/{location_id}/modelMonitors/{model_monitor_id}/modelMonitoringJobs/{model_monitoring_job_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The display name of the ModelMonitoringJob.
  // The name can be up to 128 characters long and can consist of any UTF-8.
  string display_name = 2;

  // Monitoring monitoring job spec. It outlines the specifications for
  // monitoring objectives, notifications, and result exports. If left blank,
  // the default monitoring specifications from the top-level resource
  // 'ModelMonitor' will be applied. If provided, we will use the specification
  // defined here rather than the default one.
  ModelMonitoringSpec model_monitoring_spec = 3;

  // Output only. Timestamp when this ModelMonitoringJob was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this ModelMonitoringJob was updated most
  // recently.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the monitoring job.
  //  * When the job is still creating, the state will be 'JOB_STATE_PENDING'.
  //  * Once the job is successfully created, the state will be
  //    'JOB_STATE_RUNNING'.
  //  * Once the job is finished, the state will be one of
  //    'JOB_STATE_FAILED', 'JOB_STATE_SUCCEEDED',
  //    'JOB_STATE_PARTIALLY_SUCCEEDED'.
  JobState state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Schedule resource name. It will only appear when this job is
  // triggered by a schedule.
  string schedule = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Schedule"
    }
  ];

  // Output only. Execution results for all the monitoring objectives.
  ModelMonitoringJobExecutionDetail job_execution_detail = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this ModelMonitoringJob was scheduled. It will
  // only appear when this job is triggered by a schedule.
  google.protobuf.Timestamp schedule_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represent the execution details of the job.
message ModelMonitoringJobExecutionDetail {
  // Processed dataset information.
  message ProcessedDataset {
    // Actual data location of the processed dataset.
    string location = 1;

    // Dataset time range information if any.
    google.type.Interval time_range = 2;
  }

  // Processed baseline datasets.
  repeated ProcessedDataset baseline_datasets = 1;

  // Processed target datasets.
  repeated ProcessedDataset target_datasets = 2;

  // Status of data processing for each monitoring objective.
  // Key is the objective.
  map<string, google.rpc.Status> objective_status = 3;

  // Additional job error status.
  google.rpc.Status error = 4;
}
