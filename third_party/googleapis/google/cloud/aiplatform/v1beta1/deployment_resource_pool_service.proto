// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/deployed_model_ref.proto";
import "google/cloud/aiplatform/v1beta1/deployment_resource_pool.proto";
import "google/cloud/aiplatform/v1beta1/endpoint.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "DeploymentResourcePoolServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service that manages the DeploymentResourcePool resource.
service DeploymentResourcePoolService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a DeploymentResourcePool.
  rpc CreateDeploymentResourcePool(CreateDeploymentResourcePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/deploymentResourcePools"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,deployment_resource_pool,deployment_resource_pool_id";
    option (google.longrunning.operation_info) = {
      response_type: "DeploymentResourcePool"
      metadata_type: "CreateDeploymentResourcePoolOperationMetadata"
    };
  }

  // Get a DeploymentResourcePool.
  rpc GetDeploymentResourcePool(GetDeploymentResourcePoolRequest)
      returns (DeploymentResourcePool) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/deploymentResourcePools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List DeploymentResourcePools in a location.
  rpc ListDeploymentResourcePools(ListDeploymentResourcePoolsRequest)
      returns (ListDeploymentResourcePoolsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/deploymentResourcePools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update a DeploymentResourcePool.
  rpc UpdateDeploymentResourcePool(UpdateDeploymentResourcePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{deployment_resource_pool.name=projects/*/locations/*/deploymentResourcePools/*}"
      body: "deployment_resource_pool"
    };
    option (google.api.method_signature) =
        "deployment_resource_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DeploymentResourcePool"
      metadata_type: "UpdateDeploymentResourcePoolOperationMetadata"
    };
  }

  // Delete a DeploymentResourcePool.
  rpc DeleteDeploymentResourcePool(DeleteDeploymentResourcePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/deploymentResourcePools/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // List DeployedModels that have been deployed on this DeploymentResourcePool.
  rpc QueryDeployedModels(QueryDeployedModelsRequest)
      returns (QueryDeployedModelsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{deployment_resource_pool=projects/*/locations/*/deploymentResourcePools/*}:queryDeployedModels"
    };
    option (google.api.method_signature) = "deployment_resource_pool";
  }
}

// Request message for CreateDeploymentResourcePool method.
message CreateDeploymentResourcePoolRequest {
  // Required. The parent location resource where this DeploymentResourcePool
  // will be created. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The DeploymentResourcePool to create.
  DeploymentResourcePool deployment_resource_pool = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the DeploymentResourcePool, which
  // will become the final component of the DeploymentResourcePool's resource
  // name.
  //
  // The maximum length is 63 characters, and valid characters
  // are `/^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$/`.
  string deployment_resource_pool_id = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation information for CreateDeploymentResourcePool method.
message CreateDeploymentResourcePoolOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for GetDeploymentResourcePool method.
message GetDeploymentResourcePoolRequest {
  // Required. The name of the DeploymentResourcePool to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/deploymentResourcePools/{deployment_resource_pool}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DeploymentResourcePool"
    }
  ];
}

// Request message for ListDeploymentResourcePools method.
message ListDeploymentResourcePoolsRequest {
  // Required. The parent Location which owns this collection of
  // DeploymentResourcePools. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of DeploymentResourcePools to return. The service may
  // return fewer than this value.
  int32 page_size = 2;

  // A page token, received from a previous `ListDeploymentResourcePools` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListDeploymentResourcePools` must match the call that provided the page
  // token.
  string page_token = 3;
}

// Response message for ListDeploymentResourcePools method.
message ListDeploymentResourcePoolsResponse {
  // The DeploymentResourcePools from the specified location.
  repeated DeploymentResourcePool deployment_resource_pools = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for UpdateDeploymentResourcePool method.
message UpdateDeploymentResourcePoolRequest {
  // Required. The DeploymentResourcePool to update.
  //
  // The DeploymentResourcePool's `name` field is used to identify the
  // DeploymentResourcePool to update.
  // Format:
  // `projects/{project}/locations/{location}/deploymentResourcePools/{deployment_resource_pool}`
  DeploymentResourcePool deployment_resource_pool = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation information for UpdateDeploymentResourcePool method.
message UpdateDeploymentResourcePoolOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for DeleteDeploymentResourcePool method.
message DeleteDeploymentResourcePoolRequest {
  // Required. The name of the DeploymentResourcePool to delete.
  // Format:
  // `projects/{project}/locations/{location}/deploymentResourcePools/{deployment_resource_pool}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DeploymentResourcePool"
    }
  ];
}

// Request message for QueryDeployedModels method.
message QueryDeployedModelsRequest {
  // Required. The name of the target DeploymentResourcePool to query.
  // Format:
  // `projects/{project}/locations/{location}/deploymentResourcePools/{deployment_resource_pool}`
  string deployment_resource_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of DeployedModels to return. The service may return
  // fewer than this value.
  int32 page_size = 2;

  // A page token, received from a previous `QueryDeployedModels` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `QueryDeployedModels` must match the call that provided the page
  // token.
  string page_token = 3;
}

// Response message for QueryDeployedModels method.
message QueryDeployedModelsResponse {
  // DEPRECATED Use deployed_model_refs instead.
  repeated DeployedModel deployed_models = 1 [deprecated = true];

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // References to the DeployedModels that share the specified
  // deploymentResourcePool.
  repeated DeployedModelRef deployed_model_refs = 3;

  // The total number of DeployedModels on this DeploymentResourcePool.
  int32 total_deployed_model_count = 4;

  // The total number of Endpoints that have DeployedModels on this
  // DeploymentResourcePool.
  int32 total_endpoint_count = 5;
}
