// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ContextProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Instance of a general context.
message Context {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/Context"
    pattern: "projects/{project}/locations/{location}/metadataStores/{metadata_store}/contexts/{context}"
  };

  // Immutable. The resource name of the Context.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // User provided display name of the Context.
  // May be up to 128 Unicode characters.
  string display_name = 2;

  // An eTag used to perform consistent read-modify-write updates. If not set, a
  // blind "overwrite" update happens.
  string etag = 8;

  // The labels with user-defined metadata to organize your Contexts.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one Context (System
  // labels are excluded).
  map<string, string> labels = 9;

  // Output only. Timestamp when this Context was created.
  google.protobuf.Timestamp create_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Context was last updated.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of resource names of Contexts that are parents of this
  // Context. A Context may have at most 10 parent_contexts.
  repeated string parent_contexts = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Context"
    }
  ];

  // The title of the schema describing the metadata.
  //
  // Schema title and version is expected to be registered in earlier Create
  // Schema calls. And both are used together as unique identifiers to identify
  // schemas within the local metadata store.
  string schema_title = 13;

  // The version of the schema in schema_name to use.
  //
  // Schema title and version is expected to be registered in earlier Create
  // Schema calls. And both are used together as unique identifiers to identify
  // schemas within the local metadata store.
  string schema_version = 14;

  // Properties of the Context.
  // Top level metadata keys' heading and trailing spaces will be trimmed.
  // The size of this field should not exceed 200KB.
  google.protobuf.Struct metadata = 15;

  // Description of the Context
  string description = 16;
}
