// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.transcoder.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/video/transcoder/v1/resources.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.Video.Transcoder.V1";
option go_package = "cloud.google.com/go/video/transcoder/apiv1/transcoderpb;transcoderpb";
option java_multiple_files = true;
option java_outer_classname = "ServicesProto";
option java_package = "com.google.cloud.video.transcoder.v1";
option php_namespace = "Google\\Cloud\\Video\\Transcoder\\V1";
option ruby_package = "Google::Cloud::Video::Transcoder::V1";

// Using the Transcoder API, you can queue asynchronous jobs for transcoding
// media into various output formats. Output formats may include different
// streaming standards such as HTTP Live Streaming (HLS) and Dynamic Adaptive
// Streaming over HTTP (DASH). You can also customize jobs using advanced
// features such as Digital Rights Management (DRM), audio equalization, content
// concatenation, and digital ad-stitch ready content generation.
service TranscoderService {
  option (google.api.default_host) = "transcoder.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a job in the specified region.
  rpc CreateJob(CreateJobRequest) returns (Job) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/jobs"
      body: "job"
    };
    option (google.api.method_signature) = "parent,job";
  }

  // Lists jobs in the specified region.
  rpc ListJobs(ListJobsRequest) returns (ListJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/jobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the job data.
  rpc GetJob(GetJobRequest) returns (Job) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/jobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a job.
  rpc DeleteJob(DeleteJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/jobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a job template in the specified region.
  rpc CreateJobTemplate(CreateJobTemplateRequest) returns (JobTemplate) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/jobTemplates"
      body: "job_template"
    };
    option (google.api.method_signature) =
        "parent,job_template,job_template_id";
  }

  // Lists job templates in the specified region.
  rpc ListJobTemplates(ListJobTemplatesRequest)
      returns (ListJobTemplatesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/jobTemplates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the job template data.
  rpc GetJobTemplate(GetJobTemplateRequest) returns (JobTemplate) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/jobTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a job template.
  rpc DeleteJobTemplate(DeleteJobTemplateRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/jobTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for `TranscoderService.CreateJob`.
message CreateJobRequest {
  // Required. The parent location to create and process this job.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Parameters for creating transcoding job.
  Job job = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for `TranscoderService.ListJobs`.
// The parent location from which to retrieve the collection of jobs.
message ListJobsRequest {
  // Required. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The `next_page_token` value returned from a previous List request, if
  // any.
  string page_token = 3;

  // The filter expression, following the syntax outlined in
  // https://google.aip.dev/160.
  string filter = 4;

  // One or more fields to compare and use to sort the output.
  // See https://google.aip.dev/132#ordering.
  string order_by = 5;
}

// Request message for `TranscoderService.GetJob`.
message GetJobRequest {
  // Required. The name of the job to retrieve.
  // Format: `projects/{project}/locations/{location}/jobs/{job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "transcoder.googleapis.com/Job" }
  ];
}

// Request message for `TranscoderService.DeleteJob`.
message DeleteJobRequest {
  // Required. The name of the job to delete.
  // Format: `projects/{project}/locations/{location}/jobs/{job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "transcoder.googleapis.com/Job" }
  ];

  // If set to true, and the job is not found, the request will succeed but no
  // action will be taken on the server.
  bool allow_missing = 2;
}

// Response message for `TranscoderService.ListJobs`.
message ListJobsResponse {
  // List of jobs in the specified region.
  repeated Job jobs = 1;

  // The pagination token.
  string next_page_token = 2;

  // List of regions that could not be reached.
  repeated string unreachable = 3;
}

// Request message for `TranscoderService.CreateJobTemplate`.
message CreateJobTemplateRequest {
  // Required. The parent location to create this job template.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Parameters for creating job template.
  JobTemplate job_template = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the job template, which will become the final
  // component of the job template's resource name.
  //
  // This value should be 4-63 characters, and valid characters must match the
  // regular expression `[a-zA-Z][a-zA-Z0-9_-]*`.
  string job_template_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for `TranscoderService.ListJobTemplates`.
message ListJobTemplatesRequest {
  // Required. The parent location from which to retrieve the collection of job
  // templates. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The `next_page_token` value returned from a previous List request, if
  // any.
  string page_token = 3;

  // The filter expression, following the syntax outlined in
  // https://google.aip.dev/160.
  string filter = 4;

  // One or more fields to compare and use to sort the output.
  // See https://google.aip.dev/132#ordering.
  string order_by = 5;
}

// Request message for `TranscoderService.GetJobTemplate`.
message GetJobTemplateRequest {
  // Required. The name of the job template to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/jobTemplates/{job_template}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "transcoder.googleapis.com/JobTemplate"
    }
  ];
}

// Request message for `TranscoderService.DeleteJobTemplate`.
message DeleteJobTemplateRequest {
  // Required. The name of the job template to delete.
  // `projects/{project}/locations/{location}/jobTemplates/{job_template}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "transcoder.googleapis.com/JobTemplate"
    }
  ];

  // If set to true, and the job template is not found, the request will succeed
  // but no action will be taken on the server.
  bool allow_missing = 2;
}

// Response message for `TranscoderService.ListJobTemplates`.
message ListJobTemplatesResponse {
  // List of job templates in the specified region.
  repeated JobTemplate job_templates = 1;

  // The pagination token.
  string next_page_token = 2;

  // List of regions that could not be reached.
  repeated string unreachable = 3;
}
