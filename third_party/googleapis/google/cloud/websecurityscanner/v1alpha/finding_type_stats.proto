// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.websecurityscanner.v1alpha;

import "google/cloud/websecurityscanner/v1alpha/finding.proto";

option go_package = "cloud.google.com/go/websecurityscanner/apiv1alpha/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "FindingTypeStatsProto";
option java_package = "com.google.cloud.websecurityscanner.v1alpha";

// A FindingTypeStats resource represents stats regarding a specific FindingType
// of Findings under a given ScanRun.
message FindingTypeStats {
  // The finding type associated with the stats.
  Finding.FindingType finding_type = 1;

  // The count of findings belonging to this finding type.
  int32 finding_count = 2;
}
