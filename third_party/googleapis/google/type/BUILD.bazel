load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
proto_library(
    name = "calendar_period_proto",
    srcs = ["calendar_period.proto"],
)

proto_library(
    name = "color_proto",
    srcs = ["color.proto"],
    deps = [
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library(
    name = "date_proto",
    srcs = ["date.proto"],
)

proto_library(
    name = "datetime_proto",
    srcs = ["datetime.proto"],
    deps = [
        "@com_google_protobuf//:duration_proto",
    ],
)

proto_library(
    name = "dayofweek_proto",
    srcs = ["dayofweek.proto"],
)

proto_library(
    name = "decimal_proto",
    srcs = ["decimal.proto"],
)

proto_library(
    name = "expr_proto",
    srcs = ["expr.proto"],
)

proto_library(
    name = "fraction_proto",
    srcs = ["fraction.proto"],
)

proto_library(
    name = "interval_proto",
    srcs = ["interval.proto"],
    deps = [
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "latlng_proto",
    srcs = ["latlng.proto"],
)

proto_library(
    name = "localized_text_proto",
    srcs = ["localized_text.proto"],
)

proto_library(
    name = "money_proto",
    srcs = ["money.proto"],
)

proto_library(
    name = "month_proto",
    srcs = ["month.proto"],
)

proto_library(
    name = "phone_number_proto",
    srcs = ["phone_number.proto"],
)

proto_library(
    name = "postal_address_proto",
    srcs = ["postal_address.proto"],
)

proto_library(
    name = "quaternion_proto",
    srcs = ["quaternion.proto"],
)

proto_library(
    name = "timeofday_proto",
    srcs = ["timeofday.proto"],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "type_java_proto",
    deps = [
        ":calendar_period_proto",
        ":color_proto",
        ":date_proto",
        ":datetime_proto",
        ":dayofweek_proto",
        ":decimal_proto",
        ":expr_proto",
        ":fraction_proto",
        ":interval_proto",
        ":latlng_proto",
        ":localized_text_proto",
        ":money_proto",
        ":month_proto",
        ":phone_number_proto",
        ":postal_address_proto",
        ":quaternion_proto",
        ":timeofday_proto",
    ],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-type-java",
    transport = "grpc+rest",
    deps = [
        ":calendar_period_proto",
        ":color_proto",
        ":date_proto",
        ":datetime_proto",
        ":dayofweek_proto",
        ":decimal_proto",
        ":expr_proto",
        ":fraction_proto",
        ":interval_proto",
        ":latlng_proto",
        ":localized_text_proto",
        ":money_proto",
        ":month_proto",
        ":phone_number_proto",
        ":postal_address_proto",
        ":quaternion_proto",
        ":timeofday_proto",
        ":type_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "go_proto_library")

go_proto_library(
    name = "calendar_period_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/calendarperiod",
    protos = [":calendar_period_proto"],
)

go_proto_library(
    name = "color_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/color",
    protos = [":color_proto"],
)

go_proto_library(
    name = "date_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/date",
    protos = [":date_proto"],
)

go_proto_library(
    name = "datetime_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/datetime",
    protos = [":datetime_proto"],
)

go_proto_library(
    name = "dayofweek_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/dayofweek",
    protos = [":dayofweek_proto"],
)

go_proto_library(
    name = "decimal_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/decimal",
    protos = [":decimal_proto"],
)

go_proto_library(
    name = "expr_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/expr",
    protos = [":expr_proto"],
)

go_proto_library(
    name = "fraction_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/fraction",
    protos = [":fraction_proto"],
)

go_proto_library(
    name = "interval_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/interval",
    protos = [":interval_proto"],
)

go_proto_library(
    name = "latlng_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/latlng",
    protos = [":latlng_proto"],
)

go_proto_library(
    name = "localized_text_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/localized_text",
    protos = [":localized_text_proto"],
)

go_proto_library(
    name = "money_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/money",
    protos = [":money_proto"],
)

go_proto_library(
    name = "month_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/month",
    protos = [":month_proto"],
)

go_proto_library(
    name = "phone_number_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/phone_number",
    protos = [":phone_number_proto"],
)

go_proto_library(
    name = "postaladdress_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/postaladdress",
    protos = [":postal_address_proto"],
)

go_proto_library(
    name = "quaternion_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/quaternion",
    protos = [":quaternion_proto"],
)

go_proto_library(
    name = "timeofday_go_proto",
    importpath = "google.golang.org/genproto/googleapis/type/timeofday",
    protos = [":timeofday_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_proto_library",
)

cc_proto_library(
    name = "calendar_period_cc_proto",
    deps = [":calendar_period_proto"],
)

cc_proto_library(
    name = "color_cc_proto",
    deps = [":color_proto"],
)

cc_proto_library(
    name = "date_cc_proto",
    deps = [":date_proto"],
)

cc_proto_library(
    name = "datetime_cc_proto",
    deps = [":datetime_proto"],
)

cc_proto_library(
    name = "dayofweek_cc_proto",
    deps = [":dayofweek_proto"],
)

cc_proto_library(
    name = "decimal_cc_proto",
    deps = [":decimal_proto"],
)

cc_proto_library(
    name = "expr_cc_proto",
    deps = [":expr_proto"],
)

cc_proto_library(
    name = "fraction_cc_proto",
    deps = [":fraction_proto"],
)

cc_proto_library(
    name = "interval_cc_proto",
    deps = [":interval_proto"],
)

cc_proto_library(
    name = "latlng_cc_proto",
    deps = [":latlng_proto"],
)

cc_proto_library(
    name = "money_cc_proto",
    deps = [":money_proto"],
)

cc_proto_library(
    name = "month_cc_proto",
    deps = [":month_proto"],
)

cc_proto_library(
    name = "phone_number_cc_proto",
    deps = [":phone_number_proto"],
)

cc_proto_library(
    name = "postal_address_cc_proto",
    deps = [":postal_address_proto"],
)

cc_proto_library(
    name = "quaternion_cc_proto",
    deps = [":quaternion_proto"],
)

cc_proto_library(
    name = "timeofday_cc_proto",
    deps = [":timeofday_proto"],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_proto_library",
)

py_proto_library(
    name = "calendar_period_py_proto",
    deps = [":calendar_period_proto"],
)

py_proto_library(
    name = "color_py_proto",
    deps = [":color_proto"],
)

py_proto_library(
    name = "date_py_proto",
    deps = [":date_proto"],
)

py_proto_library(
    name = "datetime_py_proto",
    deps = [":datetime_proto"],
)

py_proto_library(
    name = "dayofweek_py_proto",
    deps = [":dayofweek_proto"],
)

py_proto_library(
    name = "decimal_py_proto",
    deps = [":decimal_proto"],
)

py_proto_library(
    name = "expr_py_proto",
    deps = [":expr_proto"],
)

py_proto_library(
    name = "fraction_py_proto",
    deps = [":fraction_proto"],
)

py_proto_library(
    name = "interval_py_proto",
    deps = [":interval_proto"],
)

py_proto_library(
    name = "latlng_py_proto",
    deps = [":latlng_proto"],
)

py_proto_library(
    name = "localized_text_py_proto",
    deps = [":localized_text_proto"],
)

py_proto_library(
    name = "money_py_proto",
    deps = [":money_proto"],
)

py_proto_library(
    name = "month_py_proto",
    deps = [":month_proto"],
)

py_proto_library(
    name = "phone_number_py_proto",
    deps = [":phone_number_proto"],
)

py_proto_library(
    name = "postal_address_py_proto",
    deps = [":postal_address_proto"],
)

py_proto_library(
    name = "quaternion_py_proto",
    deps = [":quaternion_proto"],
)

py_proto_library(
    name = "timeofday_py_proto",
    deps = [":timeofday_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "type-py",
    deps = [
        ":calendar_period_py_proto",
        ":color_py_proto",
        ":date_py_proto",
        ":datetime_py_proto",
        ":dayofweek_py_proto",
        ":decimal_py_proto",
        ":expr_py_proto",
        ":fraction_py_proto",
        ":interval_py_proto",
        ":latlng_py_proto",
        ":localized_text_py_proto",
        ":money_py_proto",
        ":month_py_proto",
        ":phone_number_py_proto",
        ":postal_address_py_proto",
        ":quaternion_py_proto",
        ":timeofday_py_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "type_php_proto",
    deps = [
        ":calendar_period_proto",
        ":color_proto",
        ":date_proto",
        ":datetime_proto",
        ":dayofweek_proto",
        ":decimal_proto",
        ":expr_proto",
        ":fraction_proto",
        ":interval_proto",
        ":latlng_proto",
        ":localized_text_proto",
        ":money_proto",
        ":month_proto",
        ":phone_number_proto",
        ":postal_address_proto",
        ":quaternion_proto",
        ":timeofday_proto",
    ],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate PHP files for these protos.
# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-type-php",
    deps = [":type_php_proto"],
)

##############################################################################
# C#
##############################################################################

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "calendar_period_csharp_proto",
    deps = [":calendar_period_proto"],
)

csharp_proto_library(
    name = "color_csharp_proto",
    deps = [":color_proto"],
)

csharp_proto_library(
    name = "date_csharp_proto",
    deps = [":date_proto"],
)

csharp_proto_library(
    name = "datetime_csharp_proto",
    deps = [":datetime_proto"],
)

csharp_proto_library(
    name = "dayofweek_csharp_proto",
    deps = [":dayofweek_proto"],
)

csharp_proto_library(
    name = "decimal_csharp_proto",
    deps = [":decimal_proto"],
)

csharp_proto_library(
    name = "expr_csharp_proto",
    deps = [":expr_proto"],
)

csharp_proto_library(
    name = "fraction_csharp_proto",
    deps = [":fraction_proto"],
)

csharp_proto_library(
    name = "interval_csharp_proto",
    deps = [":interval_proto"],
)

csharp_proto_library(
    name = "latlng_csharp_proto",
    deps = [":latlng_proto"],
)

csharp_proto_library(
    name = "localized_text_csharp_proto",
    deps = [":localized_text_proto"],
)

csharp_proto_library(
    name = "money_csharp_proto",
    deps = [":money_proto"],
)

csharp_proto_library(
    name = "month_csharp_proto",
    deps = [":month_proto"],
)

csharp_proto_library(
    name = "phone_number_csharp_proto",
    deps = [":phone_number_proto"],
)

csharp_proto_library(
    name = "postal_address_csharp_proto",
    deps = [":postal_address_proto"],
)

csharp_proto_library(
    name = "quaternion_csharp_proto",
    deps = [":quaternion_proto"],
)
