# This build file includes a target for the Ruby wrapper library for
# google-cloud-datastore-admin.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for datastore admin.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "admin_ruby_wrapper",
    srcs = ["//google/datastore/admin/v1:admin_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-datastore-admin",
        "ruby-cloud-env-prefix=DATASTORE",
        "ruby-cloud-wrapper-of=v1:0.11",
        "ruby-cloud-product-url=https://cloud.google.com/datastore",
        "ruby-cloud-api-id=datastore.googleapis.com",
        "ruby-cloud-api-shortname=datastore",
    ],
    ruby_cloud_description = "Firestore in Datastore mode is a NoSQL document database built for automatic scaling, high performance, and ease of application development.",
    ruby_cloud_title = "Firestore in Datastore mode Admin",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-datastore-admin-ruby",
    deps = [
        ":admin_ruby_wrapper",
    ],
)
