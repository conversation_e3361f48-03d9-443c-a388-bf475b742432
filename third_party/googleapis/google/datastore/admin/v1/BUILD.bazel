# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "admin_proto",
    srcs = [
        "datastore_admin.proto",
        "index.proto",
        "migration.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "admin_proto_with_info",
    deps = [
        ":admin_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "admin_java_proto",
    deps = [":admin_proto"],
)

java_grpc_library(
    name = "admin_java_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_java_proto"],
)

java_gapic_library(
    name = "admin_java_gapic",
    srcs = [":admin_proto_with_info"],
    gapic_yaml = "datastore_admin_gapic.yaml",
    grpc_service_config = "datastore_admin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    test_deps = [
        ":admin_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":admin_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "admin_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.datastore.admin.v1.DatastoreAdminClientHttpJsonTest",
        "com.google.cloud.datastore.admin.v1.DatastoreAdminClientTest",
    ],
    runtime_deps = [":admin_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-datastore-admin-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":admin_java_gapic",
        ":admin_java_grpc",
        ":admin_java_proto",
        ":admin_proto",
    ],
)

go_proto_library(
    name = "admin_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/datastore/admin/apiv1/adminpb",
    protos = [":admin_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "admin_go_gapic",
    srcs = [":admin_proto_with_info"],
    grpc_service_config = "datastore_admin_grpc_service_config.json",
    importpath = "cloud.google.com/go/datastore/admin/apiv1;admin",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":admin_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-datastore-admin-v1-go",
    deps = [
        ":admin_go_gapic",
        ":admin_go_gapic_srcjar-metadata.srcjar",
        ":admin_go_gapic_srcjar-snippets.srcjar",
        ":admin_go_gapic_srcjar-test.srcjar",
        ":admin_go_proto",
    ],
)

py_gapic_library(
    name = "admin_py_gapic",
    srcs = [":admin_proto"],
    grpc_service_config = "datastore_admin_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=datastore_admin",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-datastore",
    ],
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "admin_py_gapic_test",
    srcs = [
        "admin_py_gapic_pytest.py",
        "admin_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":admin_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "datastore-admin-v1-py",
    deps = [
        ":admin_py_gapic",
    ],
)

php_proto_library(
    name = "admin_php_proto",
    deps = [":admin_proto"],
)

php_gapic_library(
    name = "admin_php_gapic",
    srcs = [":admin_proto_with_info"],
    grpc_service_config = "datastore_admin_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [":admin_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-datastore-admin-v1-php",
    deps = [
        ":admin_php_gapic",
        ":admin_php_proto",
    ],
)

nodejs_gapic_library(
    name = "admin_nodejs_gapic",
    package_name = "@google-cloud/datastore-admin",
    src = ":admin_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "datastore_admin_grpc_service_config.json",
    package = "google.datastore.admin.v1",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "datastore-admin-v1-nodejs",
    deps = [
        ":admin_nodejs_gapic",
        ":admin_proto",
    ],
)

ruby_proto_library(
    name = "admin_ruby_proto",
    deps = [":admin_proto"],
)

ruby_grpc_library(
    name = "admin_ruby_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "admin_ruby_gapic",
    srcs = [":admin_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-datastore-admin-v1",
        "ruby-cloud-env-prefix=DATASTORE",
        "ruby-cloud-product-url=https://cloud.google.com/datastore",
        "ruby-cloud-api-id=datastore.googleapis.com",
        "ruby-cloud-api-shortname=datastore",
    ],
    grpc_service_config = "datastore_admin_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Firestore in Datastore mode is a NoSQL document database built for automatic scaling, high performance, and ease of application development.",
    ruby_cloud_title = "Firestore in Datastore mode Admin V1",
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":admin_ruby_grpc",
        ":admin_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-datastore-admin-v1-ruby",
    deps = [
        ":admin_ruby_gapic",
        ":admin_ruby_grpc",
        ":admin_ruby_proto",
    ],
)

csharp_proto_library(
    name = "admin_csharp_proto",
    deps = [":admin_proto"],
)

csharp_grpc_library(
    name = "admin_csharp_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_csharp_proto"],
)

csharp_gapic_library(
    name = "admin_csharp_gapic",
    srcs = [":admin_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "datastore_admin_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":admin_csharp_grpc",
        ":admin_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-datastore-admin-v1-csharp",
    deps = [
        ":admin_csharp_gapic",
        ":admin_csharp_grpc",
        ":admin_csharp_proto",
    ],
)

cc_proto_library(
    name = "admin_cc_proto",
    deps = [":admin_proto"],
)

cc_grpc_library(
    name = "admin_cc_grpc",
    srcs = [":admin_proto"],
    grpc_only = True,
    deps = [":admin_cc_proto"],
)
