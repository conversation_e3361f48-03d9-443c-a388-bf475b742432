type: google.api.Service
config_version: 3
name: datastore.googleapis.com
title: Cloud Datastore API

apis:
- name: google.datastore.v1.Datastore
- name: google.longrunning.Operations

documentation:
  summary: |-
    Accesses the schemaless NoSQL database to provide fully managed, robust,
    scalable storage for your application.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*}/operations'

authentication:
  rules:
  - selector: 'google.datastore.v1.Datastore.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
