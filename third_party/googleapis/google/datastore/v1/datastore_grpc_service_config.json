{"methodConfig": [{"name": [{"service": "google.datastore.v1.Datastore", "method": "Lookup"}, {"service": "google.datastore.v1.Datastore", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.datastore.v1.Datastore", "method": "RunAggregationQuery"}, {"service": "google.datastore.v1.Datastore", "method": "ReserveIds"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.datastore.v1.Datastore", "method": "BeginTransaction"}, {"service": "google.datastore.v1.Datastore", "method": "Commit"}, {"service": "google.datastore.v1.Datastore", "method": "Rollback"}, {"service": "google.datastore.v1.Datastore", "method": "AllocateIds"}], "timeout": "60s"}, {"name": [{"service": "google.datastore.v1.Datastore", "method": "Execute"}], "timeout": "0s"}]}