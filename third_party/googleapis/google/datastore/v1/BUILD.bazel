# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "datastore_proto",
    srcs = [
        "aggregation_result.proto",
        "datastore.proto",
        "entity.proto",
        "query.proto",
        "query_profile.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:routing_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "datastore_proto_with_info",
    deps = [
        ":datastore_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "datastore_java_proto",
    deps = [":datastore_proto"],
)

java_grpc_library(
    name = "datastore_java_grpc",
    srcs = [":datastore_proto"],
    deps = [":datastore_java_proto"],
)

java_gapic_library(
    name = "datastore_java_gapic",
    srcs = [":datastore_proto_with_info"],
    gapic_yaml = "datastore_gapic.yaml",
    grpc_service_config = "datastore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    test_deps = [
        ":datastore_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":datastore_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "datastore_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.datastore.v1.DatastoreClientHttpJsonTest",
        "com.google.cloud.datastore.v1.DatastoreClientTest",
    ],
    runtime_deps = [":datastore_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-datastore-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":datastore_java_gapic",
        ":datastore_java_grpc",
        ":datastore_java_proto",
        ":datastore_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "datastore_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/datastore/apiv1/datastorepb",
    protos = [":datastore_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "datastore_go_gapic",
    srcs = [":datastore_proto_with_info"],
    grpc_service_config = "datastore_grpc_service_config.json",
    importpath = "cloud.google.com/go/datastore/apiv1;datastore",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datastore_go_proto",
        "//google/longrunning:longrunning_go_gapic",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-datastore-v1-go",
    deps = [
        ":datastore_go_gapic",
        ":datastore_go_gapic_srcjar-metadata.srcjar",
        ":datastore_go_gapic_srcjar-snippets.srcjar",
        ":datastore_go_gapic_srcjar-test.srcjar",
        ":datastore_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "datastore_py_gapic",
    srcs = [":datastore_proto"],
    grpc_service_config = "datastore_grpc_service_config.json",
    opt_args = ["python-gapic-namespace=google.cloud"],
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "datastore_py_gapic_test",
    srcs = [
        "datastore_py_gapic_pytest.py",
        "datastore_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":datastore_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "datastore-v1-py",
    deps = [
        ":datastore_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "datastore_php_proto",
    deps = [":datastore_proto"],
)

php_gapic_library(
    name = "datastore_php_gapic",
    srcs = [":datastore_proto_with_info"],
    grpc_service_config = "datastore_grpc_service_config.json",
    migration_mode = "MIGRATING",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [":datastore_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-datastore-v1-php",
    deps = [
        ":datastore_php_gapic",
        ":datastore_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "datastore_nodejs_gapic",
    package_name = "@google-cloud/datastore",
    src = ":datastore_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "datastore_grpc_service_config.json",
    main_service = "datastore",
    mixins = "google.longrunning.Operations",
    package = "google.datastore.v1",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "datastore-v1-nodejs",
    deps = [
        ":datastore_nodejs_gapic",
        ":datastore_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "datastore_ruby_proto",
    deps = [":datastore_proto"],
)

ruby_grpc_library(
    name = "datastore_ruby_grpc",
    srcs = [":datastore_proto"],
    deps = [":datastore_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "datastore_ruby_gapic",
    srcs = [":datastore_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=datastore.googleapis.com",
        "ruby-cloud-api-shortname=datastore",
        "ruby-cloud-env-prefix=DATASTORE",
        "ruby-cloud-gem-name=google-cloud-datastore-v1",
        "ruby-cloud-product-url=https://cloud.google.com/datastore",
    ],
    grpc_service_config = "datastore_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Firestore in Datastore mode is a NoSQL document database built for automatic scaling, high performance, and ease of application development.",
    ruby_cloud_title = "Firestore in Datastore mode V1",
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datastore_ruby_grpc",
        ":datastore_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-datastore-v1-ruby",
    deps = [
        ":datastore_ruby_gapic",
        ":datastore_ruby_grpc",
        ":datastore_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "datastore_csharp_proto",
    deps = [":datastore_proto"],
)

csharp_grpc_library(
    name = "datastore_csharp_grpc",
    srcs = [":datastore_proto"],
    deps = [":datastore_csharp_proto"],
)

csharp_gapic_library(
    name = "datastore_csharp_gapic",
    srcs = [":datastore_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "datastore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datastore_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datastore_csharp_grpc",
        ":datastore_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-datastore-v1-csharp",
    deps = [
        ":datastore_csharp_gapic",
        ":datastore_csharp_grpc",
        ":datastore_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "datastore_cc_proto",
    deps = [":datastore_proto"],
)

cc_grpc_library(
    name = "datastore_cc_grpc",
    srcs = [":datastore_proto"],
    grpc_only = True,
    deps = [":datastore_cc_proto"],
)
