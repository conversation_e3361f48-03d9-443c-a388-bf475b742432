// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.logging.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Logging.V2";
option go_package = "cloud.google.com/go/logging/apiv2/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggingConfigProto";
option java_package = "com.google.logging.v2";
option php_namespace = "Google\\Cloud\\Logging\\V2";
option ruby_package = "Google::Cloud::Logging::V2";
option (google.api.resource_definition) = {
  type: "logging.googleapis.com/OrganizationLocation"
  pattern: "organizations/{organization}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "logging.googleapis.com/FolderLocation"
  pattern: "folders/{folder}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "logging.googleapis.com/BillingAccountLocation"
  pattern: "billingAccounts/{billing_account}/locations/{location}"
};

// Service for configuring sinks used to route log entries.
service ConfigServiceV2 {
  option (google.api.default_host) = "logging.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only,"
      "https://www.googleapis.com/auth/logging.admin,"
      "https://www.googleapis.com/auth/logging.read";

  // Lists log buckets.
  rpc ListBuckets(ListBucketsRequest) returns (ListBucketsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=*/*/locations/*}/buckets"
      additional_bindings { get: "/v2/{parent=projects/*/locations/*}/buckets" }
      additional_bindings {
        get: "/v2/{parent=organizations/*/locations/*}/buckets"
      }
      additional_bindings { get: "/v2/{parent=folders/*/locations/*}/buckets" }
      additional_bindings {
        get: "/v2/{parent=billingAccounts/*/locations/*}/buckets"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a log bucket.
  rpc GetBucket(GetBucketRequest) returns (LogBucket) {
    option (google.api.http) = {
      get: "/v2/{name=*/*/locations/*/buckets/*}"
      additional_bindings { get: "/v2/{name=projects/*/locations/*/buckets/*}" }
      additional_bindings {
        get: "/v2/{name=organizations/*/locations/*/buckets/*}"
      }
      additional_bindings { get: "/v2/{name=folders/*/locations/*/buckets/*}" }
      additional_bindings {
        get: "/v2/{name=billingAccounts/*/locations/*/buckets/*}"
      }
    };
  }

  // Creates a log bucket asynchronously that can be used to store log entries.
  //
  // After a bucket has been created, the bucket's location cannot be changed.
  rpc CreateBucketAsync(CreateBucketRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=*/*/locations/*}/buckets:createAsync"
      body: "bucket"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/buckets:createAsync"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{parent=organizations/*/locations/*}/buckets:createAsync"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*/locations/*}/buckets:createAsync"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{parent=billingAccounts/*/locations/*}/buckets:createAsync"
        body: "bucket"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "LogBucket"
      metadata_type: "BucketMetadata"
    };
  }

  // Updates a log bucket asynchronously.
  //
  // If the bucket has a `lifecycle_state` of `DELETE_REQUESTED`, then
  // `FAILED_PRECONDITION` will be returned.
  //
  // After a bucket has been created, the bucket's location cannot be changed.
  rpc UpdateBucketAsync(UpdateBucketRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=*/*/locations/*/buckets/*}:updateAsync"
      body: "bucket"
      additional_bindings {
        post: "/v2/{name=projects/*/locations/*/buckets/*}:updateAsync"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{name=organizations/*/locations/*/buckets/*}:updateAsync"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{name=folders/*/locations/*/buckets/*}:updateAsync"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{name=billingAccounts/*/locations/*/buckets/*}:updateAsync"
        body: "bucket"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "LogBucket"
      metadata_type: "BucketMetadata"
    };
  }

  // Creates a log bucket that can be used to store log entries. After a bucket
  // has been created, the bucket's location cannot be changed.
  rpc CreateBucket(CreateBucketRequest) returns (LogBucket) {
    option (google.api.http) = {
      post: "/v2/{parent=*/*/locations/*}/buckets"
      body: "bucket"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/buckets"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{parent=organizations/*/locations/*}/buckets"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*/locations/*}/buckets"
        body: "bucket"
      }
      additional_bindings {
        post: "/v2/{parent=billingAccounts/*/locations/*}/buckets"
        body: "bucket"
      }
    };
  }

  // Updates a log bucket.
  //
  // If the bucket has a `lifecycle_state` of `DELETE_REQUESTED`, then
  // `FAILED_PRECONDITION` will be returned.
  //
  // After a bucket has been created, the bucket's location cannot be changed.
  rpc UpdateBucket(UpdateBucketRequest) returns (LogBucket) {
    option (google.api.http) = {
      patch: "/v2/{name=*/*/locations/*/buckets/*}"
      body: "bucket"
      additional_bindings {
        patch: "/v2/{name=projects/*/locations/*/buckets/*}"
        body: "bucket"
      }
      additional_bindings {
        patch: "/v2/{name=organizations/*/locations/*/buckets/*}"
        body: "bucket"
      }
      additional_bindings {
        patch: "/v2/{name=folders/*/locations/*/buckets/*}"
        body: "bucket"
      }
      additional_bindings {
        patch: "/v2/{name=billingAccounts/*/locations/*/buckets/*}"
        body: "bucket"
      }
    };
  }

  // Deletes a log bucket.
  //
  // Changes the bucket's `lifecycle_state` to the `DELETE_REQUESTED` state.
  // After 7 days, the bucket will be purged and all log entries in the bucket
  // will be permanently deleted.
  rpc DeleteBucket(DeleteBucketRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=*/*/locations/*/buckets/*}"
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/buckets/*}"
      }
      additional_bindings {
        delete: "/v2/{name=organizations/*/locations/*/buckets/*}"
      }
      additional_bindings {
        delete: "/v2/{name=folders/*/locations/*/buckets/*}"
      }
      additional_bindings {
        delete: "/v2/{name=billingAccounts/*/locations/*/buckets/*}"
      }
    };
  }

  // Undeletes a log bucket. A bucket that has been deleted can be undeleted
  // within the grace period of 7 days.
  rpc UndeleteBucket(UndeleteBucketRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2/{name=*/*/locations/*/buckets/*}:undelete"
      body: "*"
      additional_bindings {
        post: "/v2/{name=projects/*/locations/*/buckets/*}:undelete"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=organizations/*/locations/*/buckets/*}:undelete"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=folders/*/locations/*/buckets/*}:undelete"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=billingAccounts/*/locations/*/buckets/*}:undelete"
        body: "*"
      }
    };
  }

  // Lists views on a log bucket.
  rpc ListViews(ListViewsRequest) returns (ListViewsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=*/*/locations/*/buckets/*}/views"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*/buckets/*}/views"
      }
      additional_bindings {
        get: "/v2/{parent=organizations/*/locations/*/buckets/*}/views"
      }
      additional_bindings {
        get: "/v2/{parent=folders/*/locations/*/buckets/*}/views"
      }
      additional_bindings {
        get: "/v2/{parent=billingAccounts/*/locations/*/buckets/*}/views"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a view on a log bucket..
  rpc GetView(GetViewRequest) returns (LogView) {
    option (google.api.http) = {
      get: "/v2/{name=*/*/locations/*/buckets/*/views/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/buckets/*/views/*}"
      }
      additional_bindings {
        get: "/v2/{name=organizations/*/locations/*/buckets/*/views/*}"
      }
      additional_bindings {
        get: "/v2/{name=folders/*/locations/*/buckets/*/views/*}"
      }
      additional_bindings {
        get: "/v2/{name=billingAccounts/*/locations/*/buckets/*/views/*}"
      }
    };
  }

  // Creates a view over log entries in a log bucket. A bucket may contain a
  // maximum of 30 views.
  rpc CreateView(CreateViewRequest) returns (LogView) {
    option (google.api.http) = {
      post: "/v2/{parent=*/*/locations/*/buckets/*}/views"
      body: "view"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*/buckets/*}/views"
        body: "view"
      }
      additional_bindings {
        post: "/v2/{parent=organizations/*/locations/*/buckets/*}/views"
        body: "view"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*/locations/*/buckets/*}/views"
        body: "view"
      }
      additional_bindings {
        post: "/v2/{parent=billingAccounts/*/locations/*/buckets/*}/views"
        body: "view"
      }
    };
  }

  // Updates a view on a log bucket. This method replaces the following fields
  // in the existing view with values from the new view: `filter`.
  // If an `UNAVAILABLE` error is returned, this indicates that system is not in
  // a state where it can update the view. If this occurs, please try again in a
  // few minutes.
  rpc UpdateView(UpdateViewRequest) returns (LogView) {
    option (google.api.http) = {
      patch: "/v2/{name=*/*/locations/*/buckets/*/views/*}"
      body: "view"
      additional_bindings {
        patch: "/v2/{name=projects/*/locations/*/buckets/*/views/*}"
        body: "view"
      }
      additional_bindings {
        patch: "/v2/{name=organizations/*/locations/*/buckets/*/views/*}"
        body: "view"
      }
      additional_bindings {
        patch: "/v2/{name=folders/*/locations/*/buckets/*/views/*}"
        body: "view"
      }
      additional_bindings {
        patch: "/v2/{name=billingAccounts/*/locations/*/buckets/*/views/*}"
        body: "view"
      }
    };
  }

  // Deletes a view on a log bucket.
  // If an `UNAVAILABLE` error is returned, this indicates that system is not in
  // a state where it can delete the view. If this occurs, please try again in a
  // few minutes.
  rpc DeleteView(DeleteViewRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=*/*/locations/*/buckets/*/views/*}"
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/buckets/*/views/*}"
      }
      additional_bindings {
        delete: "/v2/{name=organizations/*/locations/*/buckets/*/views/*}"
      }
      additional_bindings {
        delete: "/v2/{name=folders/*/locations/*/buckets/*/views/*}"
      }
      additional_bindings {
        delete: "/v2/{name=billingAccounts/*/locations/*/buckets/*/views/*}"
      }
    };
  }

  // Lists sinks.
  rpc ListSinks(ListSinksRequest) returns (ListSinksResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=*/*}/sinks"
      additional_bindings { get: "/v2/{parent=projects/*}/sinks" }
      additional_bindings { get: "/v2/{parent=organizations/*}/sinks" }
      additional_bindings { get: "/v2/{parent=folders/*}/sinks" }
      additional_bindings { get: "/v2/{parent=billingAccounts/*}/sinks" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a sink.
  rpc GetSink(GetSinkRequest) returns (LogSink) {
    option (google.api.http) = {
      get: "/v2/{sink_name=*/*/sinks/*}"
      additional_bindings { get: "/v2/{sink_name=projects/*/sinks/*}" }
      additional_bindings { get: "/v2/{sink_name=organizations/*/sinks/*}" }
      additional_bindings { get: "/v2/{sink_name=folders/*/sinks/*}" }
      additional_bindings { get: "/v2/{sink_name=billingAccounts/*/sinks/*}" }
    };
    option (google.api.method_signature) = "sink_name";
  }

  // Creates a sink that exports specified log entries to a destination. The
  // export of newly-ingested log entries begins immediately, unless the sink's
  // `writer_identity` is not permitted to write to the destination. A sink can
  // export log entries only from the resource owning the sink.
  rpc CreateSink(CreateSinkRequest) returns (LogSink) {
    option (google.api.http) = {
      post: "/v2/{parent=*/*}/sinks"
      body: "sink"
      additional_bindings { post: "/v2/{parent=projects/*}/sinks" body: "sink" }
      additional_bindings {
        post: "/v2/{parent=organizations/*}/sinks"
        body: "sink"
      }
      additional_bindings { post: "/v2/{parent=folders/*}/sinks" body: "sink" }
      additional_bindings {
        post: "/v2/{parent=billingAccounts/*}/sinks"
        body: "sink"
      }
    };
    option (google.api.method_signature) = "parent,sink";
  }

  // Updates a sink. This method replaces the following fields in the existing
  // sink with values from the new sink: `destination`, and `filter`.
  //
  // The updated sink might also have a new `writer_identity`; see the
  // `unique_writer_identity` field.
  rpc UpdateSink(UpdateSinkRequest) returns (LogSink) {
    option (google.api.http) = {
      put: "/v2/{sink_name=*/*/sinks/*}"
      body: "sink"
      additional_bindings {
        put: "/v2/{sink_name=projects/*/sinks/*}"
        body: "sink"
      }
      additional_bindings {
        put: "/v2/{sink_name=organizations/*/sinks/*}"
        body: "sink"
      }
      additional_bindings {
        put: "/v2/{sink_name=folders/*/sinks/*}"
        body: "sink"
      }
      additional_bindings {
        put: "/v2/{sink_name=billingAccounts/*/sinks/*}"
        body: "sink"
      }
      additional_bindings {
        patch: "/v2/{sink_name=projects/*/sinks/*}"
        body: "sink"
      }
      additional_bindings {
        patch: "/v2/{sink_name=organizations/*/sinks/*}"
        body: "sink"
      }
      additional_bindings {
        patch: "/v2/{sink_name=folders/*/sinks/*}"
        body: "sink"
      }
      additional_bindings {
        patch: "/v2/{sink_name=billingAccounts/*/sinks/*}"
        body: "sink"
      }
    };
    option (google.api.method_signature) = "sink_name,sink,update_mask";
    option (google.api.method_signature) = "sink_name,sink";
  }

  // Deletes a sink. If the sink has a unique `writer_identity`, then that
  // service account is also deleted.
  rpc DeleteSink(DeleteSinkRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{sink_name=*/*/sinks/*}"
      additional_bindings { delete: "/v2/{sink_name=projects/*/sinks/*}" }
      additional_bindings { delete: "/v2/{sink_name=organizations/*/sinks/*}" }
      additional_bindings { delete: "/v2/{sink_name=folders/*/sinks/*}" }
      additional_bindings {
        delete: "/v2/{sink_name=billingAccounts/*/sinks/*}"
      }
    };
    option (google.api.method_signature) = "sink_name";
  }

  // Asynchronously creates a linked dataset in BigQuery which makes it possible
  // to use BigQuery to read the logs stored in the log bucket. A log bucket may
  // currently only contain one link.
  rpc CreateLink(CreateLinkRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=*/*/locations/*/buckets/*}/links"
      body: "link"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*/buckets/*}/links"
        body: "link"
      }
      additional_bindings {
        post: "/v2/{parent=organizations/*/locations/*/buckets/*}/links"
        body: "link"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*/locations/*/buckets/*}/links"
        body: "link"
      }
      additional_bindings {
        post: "/v2/{parent=billingAccounts/*/locations/*/buckets/*}/links"
        body: "link"
      }
    };
    option (google.api.method_signature) = "parent,link,link_id";
    option (google.longrunning.operation_info) = {
      response_type: "Link"
      metadata_type: "LinkMetadata"
    };
  }

  // Deletes a link. This will also delete the corresponding BigQuery linked
  // dataset.
  rpc DeleteLink(DeleteLinkRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=*/*/locations/*/buckets/*/links/*}"
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/buckets/*/links/*}"
      }
      additional_bindings {
        delete: "/v2/{name=organizations/*/locations/*/buckets/*/links/*}"
      }
      additional_bindings {
        delete: "/v2/{name=folders/*/locations/*/buckets/*/links/*}"
      }
      additional_bindings {
        delete: "/v2/{name=billingAccounts/*/locations/*/buckets/*/links/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "LinkMetadata"
    };
  }

  // Lists links.
  rpc ListLinks(ListLinksRequest) returns (ListLinksResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=*/*/locations/*/buckets/*}/links"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*/buckets/*}/links"
      }
      additional_bindings {
        get: "/v2/{parent=organizations/*/locations/*/buckets/*}/links"
      }
      additional_bindings {
        get: "/v2/{parent=folders/*/locations/*/buckets/*}/links"
      }
      additional_bindings {
        get: "/v2/{parent=billingAccounts/*/locations/*/buckets/*}/links"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a link.
  rpc GetLink(GetLinkRequest) returns (Link) {
    option (google.api.http) = {
      get: "/v2/{name=*/*/locations/*/buckets/*/links/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/buckets/*/links/*}"
      }
      additional_bindings {
        get: "/v2/{name=organizations/*/locations/*/buckets/*/links/*}"
      }
      additional_bindings {
        get: "/v2/{name=folders/*/locations/*/buckets/*/links/*}"
      }
      additional_bindings {
        get: "/v2/{name=billingAccounts/*/locations/*/buckets/*/links/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all the exclusions on the _Default sink in a parent resource.
  rpc ListExclusions(ListExclusionsRequest) returns (ListExclusionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=*/*}/exclusions"
      additional_bindings { get: "/v2/{parent=projects/*}/exclusions" }
      additional_bindings { get: "/v2/{parent=organizations/*}/exclusions" }
      additional_bindings { get: "/v2/{parent=folders/*}/exclusions" }
      additional_bindings { get: "/v2/{parent=billingAccounts/*}/exclusions" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the description of an exclusion in the _Default sink.
  rpc GetExclusion(GetExclusionRequest) returns (LogExclusion) {
    option (google.api.http) = {
      get: "/v2/{name=*/*/exclusions/*}"
      additional_bindings { get: "/v2/{name=projects/*/exclusions/*}" }
      additional_bindings { get: "/v2/{name=organizations/*/exclusions/*}" }
      additional_bindings { get: "/v2/{name=folders/*/exclusions/*}" }
      additional_bindings { get: "/v2/{name=billingAccounts/*/exclusions/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new exclusion in the _Default sink in a specified parent
  // resource. Only log entries belonging to that resource can be excluded. You
  // can have up to 10 exclusions in a resource.
  rpc CreateExclusion(CreateExclusionRequest) returns (LogExclusion) {
    option (google.api.http) = {
      post: "/v2/{parent=*/*}/exclusions"
      body: "exclusion"
      additional_bindings {
        post: "/v2/{parent=projects/*}/exclusions"
        body: "exclusion"
      }
      additional_bindings {
        post: "/v2/{parent=organizations/*}/exclusions"
        body: "exclusion"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*}/exclusions"
        body: "exclusion"
      }
      additional_bindings {
        post: "/v2/{parent=billingAccounts/*}/exclusions"
        body: "exclusion"
      }
    };
    option (google.api.method_signature) = "parent,exclusion";
  }

  // Changes one or more properties of an existing exclusion in the _Default
  // sink.
  rpc UpdateExclusion(UpdateExclusionRequest) returns (LogExclusion) {
    option (google.api.http) = {
      patch: "/v2/{name=*/*/exclusions/*}"
      body: "exclusion"
      additional_bindings {
        patch: "/v2/{name=projects/*/exclusions/*}"
        body: "exclusion"
      }
      additional_bindings {
        patch: "/v2/{name=organizations/*/exclusions/*}"
        body: "exclusion"
      }
      additional_bindings {
        patch: "/v2/{name=folders/*/exclusions/*}"
        body: "exclusion"
      }
      additional_bindings {
        patch: "/v2/{name=billingAccounts/*/exclusions/*}"
        body: "exclusion"
      }
    };
    option (google.api.method_signature) = "name,exclusion,update_mask";
  }

  // Deletes an exclusion in the _Default sink.
  rpc DeleteExclusion(DeleteExclusionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=*/*/exclusions/*}"
      additional_bindings { delete: "/v2/{name=projects/*/exclusions/*}" }
      additional_bindings { delete: "/v2/{name=organizations/*/exclusions/*}" }
      additional_bindings { delete: "/v2/{name=folders/*/exclusions/*}" }
      additional_bindings {
        delete: "/v2/{name=billingAccounts/*/exclusions/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the Logging CMEK settings for the given resource.
  //
  // Note: CMEK for the Log Router can be configured for Google Cloud projects,
  // folders, organizations and billing accounts. Once configured for an
  // organization, it applies to all projects and folders in the Google Cloud
  // organization.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  rpc GetCmekSettings(GetCmekSettingsRequest) returns (CmekSettings) {
    option (google.api.http) = {
      get: "/v2/{name=*/*}/cmekSettings"
      additional_bindings { get: "/v2/{name=projects/*}/cmekSettings" }
      additional_bindings { get: "/v2/{name=organizations/*}/cmekSettings" }
      additional_bindings { get: "/v2/{name=folders/*}/cmekSettings" }
      additional_bindings { get: "/v2/{name=billingAccounts/*}/cmekSettings" }
    };
  }

  // Updates the Log Router CMEK settings for the given resource.
  //
  // Note: CMEK for the Log Router can currently only be configured for Google
  // Cloud organizations. Once configured, it applies to all projects and
  // folders in the Google Cloud organization.
  //
  // [UpdateCmekSettings][google.logging.v2.ConfigServiceV2.UpdateCmekSettings]
  // will fail if 1) `kms_key_name` is invalid, or 2) the associated service
  // account does not have the required
  // `roles/cloudkms.cryptoKeyEncrypterDecrypter` role assigned for the key, or
  // 3) access to the key is disabled.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  rpc UpdateCmekSettings(UpdateCmekSettingsRequest) returns (CmekSettings) {
    option (google.api.http) = {
      patch: "/v2/{name=*/*}/cmekSettings"
      body: "cmek_settings"
      additional_bindings {
        patch: "/v2/{name=organizations/*}/cmekSettings"
        body: "cmek_settings"
      }
    };
  }

  // Gets the Log Router settings for the given resource.
  //
  // Note: Settings for the Log Router can be get for Google Cloud projects,
  // folders, organizations and billing accounts. Currently it can only be
  // configured for organizations. Once configured for an organization, it
  // applies to all projects and folders in the Google Cloud organization.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  rpc GetSettings(GetSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      get: "/v2/{name=*/*}/settings"
      additional_bindings { get: "/v2/{name=projects/*}/settings" }
      additional_bindings { get: "/v2/{name=organizations/*}/settings" }
      additional_bindings { get: "/v2/{name=folders/*}/settings" }
      additional_bindings { get: "/v2/{name=billingAccounts/*}/settings" }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the Log Router settings for the given resource.
  //
  // Note: Settings for the Log Router can currently only be configured for
  // Google Cloud organizations. Once configured, it applies to all projects and
  // folders in the Google Cloud organization.
  //
  // [UpdateSettings][google.logging.v2.ConfigServiceV2.UpdateSettings]
  // will fail if 1) `kms_key_name` is invalid, or 2) the associated service
  // account does not have the required
  // `roles/cloudkms.cryptoKeyEncrypterDecrypter` role assigned for the key, or
  // 3) access to the key is disabled. 4) `location_id` is not supported by
  // Logging. 5) `location_id` violate OrgPolicy.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  rpc UpdateSettings(UpdateSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      patch: "/v2/{name=*/*}/settings"
      body: "settings"
      additional_bindings {
        patch: "/v2/{name=organizations/*}/settings"
        body: "settings"
      }
      additional_bindings {
        patch: "/v2/{name=folders/*}/settings"
        body: "settings"
      }
    };
    option (google.api.method_signature) = "settings,update_mask";
  }

  // Copies a set of log entries from a log bucket to a Cloud Storage bucket.
  rpc CopyLogEntries(CopyLogEntriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/entries:copy"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "CopyLogEntriesResponse"
      metadata_type: "CopyLogEntriesMetadata"
    };
  }
}

// Configuration for an indexed field.
message IndexConfig {
  // Required. The LogEntry field path to index.
  //
  // Note that some paths are automatically indexed, and other paths are not
  // eligible for indexing. See [indexing documentation](
  // https://cloud.google.com/logging/docs/view/advanced-queries#indexed-fields)
  // for details.
  //
  // For example: `jsonPayload.request.status`
  string field_path = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of data in this index.
  IndexType type = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The timestamp when the index was last modified.
  //
  // This is used to return the timestamp, and will be ignored if supplied
  // during update.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Describes a repository in which log entries are stored.
message LogBucket {
  option (google.api.resource) = {
    type: "logging.googleapis.com/LogBucket"
    pattern: "projects/{project}/locations/{location}/buckets/{bucket}"
    pattern: "organizations/{organization}/locations/{location}/buckets/{bucket}"
    pattern: "folders/{folder}/locations/{location}/buckets/{bucket}"
    pattern: "billingAccounts/{billing_account}/locations/{location}/buckets/{bucket}"
  };

  // Output only. The resource name of the bucket.
  //
  // For example:
  //
  //   `projects/my-project/locations/global/buckets/my-bucket`
  //
  // For a list of supported locations, see [Supported
  // Regions](https://cloud.google.com/logging/docs/region-support)
  //
  // For the location of `global` it is unspecified where log entries are
  // actually stored.
  //
  // After a bucket has been created, the location cannot be changed.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Describes this bucket.
  string description = 3;

  // Output only. The creation timestamp of the bucket. This is not set for any
  // of the default buckets.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of the bucket.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Logs will be retained by default for this amount of time, after which they
  // will automatically be deleted. The minimum retention period is 1 day. If
  // this value is set to zero at bucket creation time, the default time of 30
  // days will be used.
  int32 retention_days = 11;

  // Whether the bucket is locked.
  //
  // The retention period on a locked bucket cannot be changed. Locked buckets
  // may only be deleted if they are empty.
  bool locked = 9;

  // Output only. The bucket lifecycle state.
  LifecycleState lifecycle_state = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether log analytics is enabled for this bucket.
  //
  // Once enabled, log analytics features cannot be disabled.
  bool analytics_enabled = 14;

  // Log entry field paths that are denied access in this bucket.
  //
  // The following fields and their children are eligible: `textPayload`,
  // `jsonPayload`, `protoPayload`, `httpRequest`, `labels`, `sourceLocation`.
  //
  // Restricting a repeated field will restrict all values. Adding a parent will
  // block all child fields. (e.g. `foo.bar` will block `foo.bar.baz`)
  repeated string restricted_fields = 15;

  // A list of indexed fields and related configuration data.
  repeated IndexConfig index_configs = 17;

  // The CMEK settings of the log bucket. If present, new log entries written to
  // this log bucket are encrypted using the CMEK key provided in this
  // configuration. If a log bucket has CMEK settings, the CMEK settings cannot
  // be disabled later by updating the log bucket. Changing the KMS key is
  // allowed.
  CmekSettings cmek_settings = 19;
}

// Describes a view over log entries in a bucket.
message LogView {
  option (google.api.resource) = {
    type: "logging.googleapis.com/LogView"
    pattern: "projects/{project}/locations/{location}/buckets/{bucket}/views/{view}"
    pattern: "organizations/{organization}/locations/{location}/buckets/{bucket}/views/{view}"
    pattern: "folders/{folder}/locations/{location}/buckets/{bucket}/views/{view}"
    pattern: "billingAccounts/{billing_account}/locations/{location}/buckets/{bucket}/views/{view}"
  };

  // The resource name of the view.
  //
  // For example:
  //
  //   `projects/my-project/locations/global/buckets/my-bucket/views/my-view`
  string name = 1;

  // Describes this view.
  string description = 3;

  // Output only. The creation timestamp of the view.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of the view.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Filter that restricts which log entries in a bucket are visible in this
  // view.
  //
  // Filters are restricted to be a logical AND of ==/!= of any of the
  // following:
  //
  //   - originating project/folder/organization/billing account.
  //   - resource type
  //   - log id
  //
  // For example:
  //
  //   SOURCE("projects/myproject") AND resource.type = "gce_instance"
  //                                AND LOG_ID("stdout")
  string filter = 7;
}

// Describes a sink used to export log entries to one of the following
// destinations in any project: a Cloud Storage bucket, a BigQuery dataset, a
// Pub/Sub topic or a Cloud Logging log bucket. A logs filter controls which log
// entries are exported. The sink must be created within a project,
// organization, billing account, or folder.
message LogSink {
  option (google.api.resource) = {
    type: "logging.googleapis.com/LogSink"
    pattern: "projects/{project}/sinks/{sink}"
    pattern: "organizations/{organization}/sinks/{sink}"
    pattern: "folders/{folder}/sinks/{sink}"
    pattern: "billingAccounts/{billing_account}/sinks/{sink}"
  };

  // Deprecated. This is unused.
  enum VersionFormat {
    // An unspecified format version that will default to V2.
    VERSION_FORMAT_UNSPECIFIED = 0;

    // `LogEntry` version 2 format.
    V2 = 1;

    // `LogEntry` version 1 format.
    V1 = 2;
  }

  // Required. The client-assigned sink identifier, unique within the project.
  //
  // For example: `"my-syslog-errors-to-pubsub"`. Sink identifiers are limited
  // to 100 characters and can include only the following characters: upper and
  // lower-case alphanumeric characters, underscores, hyphens, and periods.
  // First character has to be alphanumeric.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The export destination:
  //
  //     "storage.googleapis.com/[GCS_BUCKET]"
  //     "bigquery.googleapis.com/projects/[PROJECT_ID]/datasets/[DATASET]"
  //     "pubsub.googleapis.com/projects/[PROJECT_ID]/topics/[TOPIC_ID]"
  //
  // The sink's `writer_identity`, set when the sink is created, must have
  // permission to write to the destination or else the log entries are not
  // exported. For more information, see
  // [Exporting Logs with
  // Sinks](https://cloud.google.com/logging/docs/api/tasks/exporting-logs).
  string destination = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // Optional. An [advanced logs
  // filter](https://cloud.google.com/logging/docs/view/advanced-queries). The
  // only exported log entries are those that are in the resource owning the
  // sink and that match the filter.
  //
  // For example:
  //
  //   `logName="projects/[PROJECT_ID]/logs/[LOG_ID]" AND severity>=ERROR`
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A description of this sink.
  //
  // The maximum length of the description is 8000 characters.
  string description = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, then this sink is disabled and it does not export
  // any log entries.
  bool disabled = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Log entries that match any of these exclusion filters will not be
  // exported.
  //
  // If a log entry is matched by both `filter` and one of `exclusion_filters`
  // it will not be exported.
  repeated LogExclusion exclusions = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Deprecated. This field is unused.
  VersionFormat output_version_format = 6 [deprecated = true];

  // Output only. An IAM identity&mdash;a service account or group&mdash;under
  // which Cloud Logging writes the exported log entries to the sink's
  // destination. This field is either set by specifying
  // `custom_writer_identity` or set automatically by
  // [sinks.create][google.logging.v2.ConfigServiceV2.CreateSink] and
  // [sinks.update][google.logging.v2.ConfigServiceV2.UpdateSink] based on the
  // value of `unique_writer_identity` in those methods.
  //
  // Until you grant this identity write-access to the destination, log entry
  // exports from this sink will fail. For more information, see [Granting
  // Access for a
  // Resource](https://cloud.google.com/iam/docs/granting-roles-to-service-accounts#granting_access_to_a_service_account_for_a_resource).
  // Consult the destination service's documentation to determine the
  // appropriate IAM roles to assign to the identity.
  //
  // Sinks that have a destination that is a log bucket in the same project as
  // the sink cannot have a writer_identity and no additional permissions are
  // required.
  string writer_identity = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. This field applies only to sinks owned by organizations and
  // folders. If the field is false, the default, only the logs owned by the
  // sink's parent resource are available for export. If the field is true, then
  // log entries from all the projects, folders, and billing accounts contained
  // in the sink's parent resource are also available for export. Whether a
  // particular log entry from the children is exported depends on the sink's
  // filter expression.
  //
  // For example, if this field is true, then the filter
  // `resource.type=gce_instance` would export all Compute Engine VM instance
  // log entries from all projects in the sink's parent.
  //
  // To only export entries from certain child projects, filter on the project
  // part of the log name:
  //
  //   logName:("projects/test-project1/" OR "projects/test-project2/") AND
  //   resource.type=gce_instance
  bool include_children = 9 [(google.api.field_behavior) = OPTIONAL];

  // Destination dependent options.
  oneof options {
    // Optional. Options that affect sinks exporting data to BigQuery.
    BigQueryOptions bigquery_options = 12
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. The creation timestamp of the sink.
  //
  // This field may not be present for older sinks.
  google.protobuf.Timestamp create_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of the sink.
  //
  // This field may not be present for older sinks.
  google.protobuf.Timestamp update_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Describes a BigQuery dataset that was created by a link.
message BigQueryDataset {
  // Output only. The full resource name of the BigQuery dataset. The DATASET_ID
  // will match the ID of the link, so the link must match the naming
  // restrictions of BigQuery datasets (alphanumeric characters and underscores
  // only).
  //
  // The dataset will have a resource path of
  //   "bigquery.googleapis.com/projects/[PROJECT_ID]/datasets/[DATASET_ID]"
  string dataset_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Describes a link connected to an analytics enabled bucket.
message Link {
  option (google.api.resource) = {
    type: "logging.googleapis.com/Link"
    pattern: "projects/{project}/locations/{location}/buckets/{bucket}/links/{link}"
    pattern: "organizations/{organization}/locations/{location}/buckets/{bucket}/links/{link}"
    pattern: "folders/{folder}/locations/{location}/buckets/{bucket}/links/{link}"
    pattern: "billingAccounts/{billing_account}/locations/{location}/buckets/{bucket}/links/{link}"
  };

  // The resource name of the link. The name can have up to 100 characters.
  // A valid link id (at the end of the link name) must only have alphanumeric
  // characters and underscores within it.
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //     "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //     "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //
  // For example:
  //
  //   `projects/my-project/locations/global/buckets/my-bucket/links/my_link
  string name = 1;

  // Describes this link.
  //
  // The maximum length of the description is 8000 characters.
  string description = 2;

  // Output only. The creation timestamp of the link.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The resource lifecycle state.
  LifecycleState lifecycle_state = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The information of a BigQuery Dataset. When a link is created, a BigQuery
  // dataset is created along with it, in the same project as the LogBucket it's
  // linked to. This dataset will also have BigQuery Views corresponding to the
  // LogViews in the bucket.
  BigQueryDataset bigquery_dataset = 5;
}

// Options that change functionality of a sink exporting data to BigQuery.
message BigQueryOptions {
  // Optional. Whether to use [BigQuery's partition
  // tables](https://cloud.google.com/bigquery/docs/partitioned-tables). By
  // default, Cloud Logging creates dated tables based on the log entries'
  // timestamps, e.g. syslog_20170523. With partitioned tables the date suffix
  // is no longer present and [special query
  // syntax](https://cloud.google.com/bigquery/docs/querying-partitioned-tables)
  // has to be used instead. In both cases, tables are sharded based on UTC
  // timezone.
  bool use_partitioned_tables = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. True if new timestamp column based partitioning is in use,
  // false if legacy ingestion-time partitioning is in use.
  //
  // All new sinks will have this field set true and will use timestamp column
  // based partitioning. If use_partitioned_tables is false, this value has no
  // meaning and will be false. Legacy sinks using partitioned tables will have
  // this field set to false.
  bool uses_timestamp_column_partitioning = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The parameters to `ListBuckets`.
message ListBucketsRequest {
  // Required. The parent resource whose buckets are to be listed:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]"
  //     "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]"
  //     "folders/[FOLDER_ID]/locations/[LOCATION_ID]"
  //
  // Note: The locations portion of the resource must be specified, but
  // supplying the character `-` in place of [LOCATION_ID] will return all
  // buckets.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/LogBucket"
    }
  ];

  // Optional. If present, then retrieve the next batch of results from the
  // preceding call to this method. `pageToken` must be the value of
  // `nextPageToken` from the previous response. The values of other method
  // parameters should be identical to those in the previous call.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of results to return from this request.
  // Non-positive values are ignored. The presence of `nextPageToken` in the
  // response indicates that more results might be available.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response from ListBuckets.
message ListBucketsResponse {
  // A list of buckets.
  repeated LogBucket buckets = 1;

  // If there might be more results than appear in this response, then
  // `nextPageToken` is included. To get the next set of results, call the same
  // method again using the value of `nextPageToken` as `pageToken`.
  string next_page_token = 2;
}

// The parameters to `CreateBucket`.
message CreateBucketRequest {
  // Required. The resource in which to create the log bucket:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/global"`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/LogBucket"
    }
  ];

  // Required. A client-assigned identifier such as `"my-bucket"`. Identifiers
  // are limited to 100 characters and can include only letters, digits,
  // underscores, hyphens, and periods.
  string bucket_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The new bucket. The region specified in the new bucket must be
  // compliant with any Location Restriction Org Policy. The name field in the
  // bucket is ignored.
  LogBucket bucket = 3 [(google.api.field_behavior) = REQUIRED];
}

// The parameters to `UpdateBucket`.
message UpdateBucketRequest {
  // Required. The full resource name of the bucket to update.
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/global/buckets/my-bucket"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogBucket"
    }
  ];

  // Required. The updated bucket.
  LogBucket bucket = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Field mask that specifies the fields in `bucket` that need an
  // update. A bucket field will be overwritten if, and only if, it is in the
  // update mask. `name` and output only fields cannot be updated.
  //
  // For a detailed `FieldMask` definition, see:
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#google.protobuf.FieldMask
  //
  // For example: `updateMask=retention_days`
  google.protobuf.FieldMask update_mask = 4
      [(google.api.field_behavior) = REQUIRED];
}

// The parameters to `GetBucket`.
message GetBucketRequest {
  // Required. The resource name of the bucket:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/global/buckets/my-bucket"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogBucket"
    }
  ];
}

// The parameters to `DeleteBucket`.
message DeleteBucketRequest {
  // Required. The full resource name of the bucket to delete.
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/global/buckets/my-bucket"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogBucket"
    }
  ];
}

// The parameters to `UndeleteBucket`.
message UndeleteBucketRequest {
  // Required. The full resource name of the bucket to undelete.
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/global/buckets/my-bucket"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogBucket"
    }
  ];
}

// The parameters to `ListViews`.
message ListViewsRequest {
  // Required. The bucket whose views are to be listed:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. If present, then retrieve the next batch of results from the
  // preceding call to this method. `pageToken` must be the value of
  // `nextPageToken` from the previous response. The values of other method
  // parameters should be identical to those in the previous call.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of results to return from this request.
  //
  // Non-positive values are ignored. The presence of `nextPageToken` in the
  // response indicates that more results might be available.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response from ListViews.
message ListViewsResponse {
  // A list of views.
  repeated LogView views = 1;

  // If there might be more results than appear in this response, then
  // `nextPageToken` is included. To get the next set of results, call the same
  // method again using the value of `nextPageToken` as `pageToken`.
  string next_page_token = 2;
}

// The parameters to `CreateView`.
message CreateViewRequest {
  // Required. The bucket in which to create the view
  //
  //     `"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"`
  //
  // For example:
  //
  //   `"projects/my-project/locations/global/buckets/my-bucket"`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A client-assigned identifier such as `"my-view"`. Identifiers are
  // limited to 100 characters and can include only letters, digits,
  // underscores, hyphens, and periods.
  string view_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The new view.
  LogView view = 3 [(google.api.field_behavior) = REQUIRED];
}

// The parameters to `UpdateView`.
message UpdateViewRequest {
  // Required. The full resource name of the view to update
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/global/buckets/my-bucket/views/my-view"`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The updated view.
  LogView view = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Field mask that specifies the fields in `view` that need
  // an update. A field will be overwritten if, and only if, it is
  // in the update mask. `name` and output only fields cannot be updated.
  //
  // For a detailed `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#google.protobuf.FieldMask
  //
  // For example: `updateMask=filter`
  google.protobuf.FieldMask update_mask = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// The parameters to `GetView`.
message GetViewRequest {
  // Required. The resource name of the policy:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/global/buckets/my-bucket/views/my-view"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "logging.googleapis.com/LogView" }
  ];
}

// The parameters to `DeleteView`.
message DeleteViewRequest {
  // Required. The full resource name of the view to delete:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
  //
  // For example:
  //
  //    `"projects/my-project/locations/global/buckets/my-bucket/views/my-view"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "logging.googleapis.com/LogView" }
  ];
}

// The parameters to `ListSinks`.
message ListSinksRequest {
  // Required. The parent resource whose sinks are to be listed:
  //
  //     "projects/[PROJECT_ID]"
  //     "organizations/[ORGANIZATION_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]"
  //     "folders/[FOLDER_ID]"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/LogSink"
    }
  ];

  // Optional. If present, then retrieve the next batch of results from the
  // preceding call to this method. `pageToken` must be the value of
  // `nextPageToken` from the previous response. The values of other method
  // parameters should be identical to those in the previous call.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of results to return from this request.
  // Non-positive values are ignored. The presence of `nextPageToken` in the
  // response indicates that more results might be available.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Result returned from `ListSinks`.
message ListSinksResponse {
  // A list of sinks.
  repeated LogSink sinks = 1;

  // If there might be more results than appear in this response, then
  // `nextPageToken` is included. To get the next set of results, call the same
  // method again using the value of `nextPageToken` as `pageToken`.
  string next_page_token = 2;
}

// The parameters to `GetSink`.
message GetSinkRequest {
  // Required. The resource name of the sink:
  //
  //     "projects/[PROJECT_ID]/sinks/[SINK_ID]"
  //     "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
  //     "folders/[FOLDER_ID]/sinks/[SINK_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/sinks/my-sink"`
  string sink_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "logging.googleapis.com/LogSink" }
  ];
}

// The parameters to `CreateSink`.
message CreateSinkRequest {
  // Required. The resource in which to create the sink:
  //
  //     "projects/[PROJECT_ID]"
  //     "organizations/[ORGANIZATION_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]"
  //     "folders/[FOLDER_ID]"
  //
  // For examples:
  //
  //   `"projects/my-project"`
  //   `"organizations/*********"`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/LogSink"
    }
  ];

  // Required. The new sink, whose `name` parameter is a sink identifier that
  // is not already in use.
  LogSink sink = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Determines the kind of IAM identity returned as `writer_identity`
  // in the new sink. If this value is omitted or set to false, and if the
  // sink's parent is a project, then the value returned as `writer_identity` is
  // the same group or service account used by Cloud Logging before the addition
  // of writer identities to this API. The sink's destination must be in the
  // same project as the sink itself.
  //
  // If this field is set to true, or if the sink is owned by a non-project
  // resource such as an organization, then the value of `writer_identity` will
  // be a unique service account used only for exports from the new sink. For
  // more information, see `writer_identity` in
  // [LogSink][google.logging.v2.LogSink].
  bool unique_writer_identity = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The parameters to `UpdateSink`.
message UpdateSinkRequest {
  // Required. The full resource name of the sink to update, including the
  // parent resource and the sink identifier:
  //
  //     "projects/[PROJECT_ID]/sinks/[SINK_ID]"
  //     "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
  //     "folders/[FOLDER_ID]/sinks/[SINK_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/sinks/my-sink"`
  string sink_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "logging.googleapis.com/LogSink" }
  ];

  // Required. The updated sink, whose name is the same identifier that appears
  // as part of `sink_name`.
  LogSink sink = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. See [sinks.create][google.logging.v2.ConfigServiceV2.CreateSink]
  // for a description of this field. When updating a sink, the effect of this
  // field on the value of `writer_identity` in the updated sink depends on both
  // the old and new values of this field:
  //
  // +   If the old and new values of this field are both false or both true,
  //     then there is no change to the sink's `writer_identity`.
  // +   If the old value is false and the new value is true, then
  //     `writer_identity` is changed to a unique service account.
  // +   It is an error if the old value is true and the new value is
  //     set to false or defaulted to false.
  bool unique_writer_identity = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field mask that specifies the fields in `sink` that need
  // an update. A sink field will be overwritten if, and only if, it is
  // in the update mask. `name` and output only fields cannot be updated.
  //
  // An empty `updateMask` is temporarily treated as using the following mask
  // for backwards compatibility purposes:
  //
  //   `destination,filter,includeChildren`
  //
  // At some point in the future, behavior will be removed and specifying an
  // empty `updateMask` will be an error.
  //
  // For a detailed `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#google.protobuf.FieldMask
  //
  // For example: `updateMask=filter`
  google.protobuf.FieldMask update_mask = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// The parameters to `DeleteSink`.
message DeleteSinkRequest {
  // Required. The full resource name of the sink to delete, including the
  // parent resource and the sink identifier:
  //
  //     "projects/[PROJECT_ID]/sinks/[SINK_ID]"
  //     "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
  //     "folders/[FOLDER_ID]/sinks/[SINK_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/sinks/my-sink"`
  string sink_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "logging.googleapis.com/LogSink" }
  ];
}

// The parameters to CreateLink.
message CreateLinkRequest {
  // Required. The full resource name of the bucket to create a link for.
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  //     "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/Link"
    }
  ];

  // Required. The new link.
  Link link = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the link. The link_id can have up to 100
  // characters. A valid link_id must only have alphanumeric characters and
  // underscores within it.
  string link_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// The parameters to DeleteLink.
message DeleteLinkRequest {
  // Required. The full resource name of the link to delete.
  //
  //  "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //   "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //   "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //   "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "logging.googleapis.com/Link" }
  ];
}

// The parameters to ListLinks.
message ListLinksRequest {
  // Required. The parent resource whose links are to be listed:
  //
  //   "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/"
  //   "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/"
  //   "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/"
  //   "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/Link"
    }
  ];

  // Optional. If present, then retrieve the next batch of results from the
  // preceding call to this method. `pageToken` must be the value of
  // `nextPageToken` from the previous response.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of results to return from this request.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response from ListLinks.
message ListLinksResponse {
  // A list of links.
  repeated Link links = 1;

  // If there might be more results than those appearing in this response, then
  // `nextPageToken` is included. To get the next set of results, call the same
  // method again using the value of `nextPageToken` as `pageToken`.
  string next_page_token = 2;
}

// The parameters to GetLink.
message GetLinkRequest {
  // Required. The resource name of the link:
  //
  //   "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //   "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //   "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  //   "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "logging.googleapis.com/Link" }
  ];
}

// Specifies a set of log entries that are filtered out by a sink. If
// your Google Cloud resource receives a large volume of log entries, you can
// use exclusions to reduce your chargeable logs. Note that exclusions on
// organization-level and folder-level sinks don't apply to child resources.
// Note also that you cannot modify the _Required sink or exclude logs from it.
message LogExclusion {
  option (google.api.resource) = {
    type: "logging.googleapis.com/LogExclusion"
    pattern: "projects/{project}/exclusions/{exclusion}"
    pattern: "organizations/{organization}/exclusions/{exclusion}"
    pattern: "folders/{folder}/exclusions/{exclusion}"
    pattern: "billingAccounts/{billing_account}/exclusions/{exclusion}"
  };

  // Required. A client-assigned identifier, such as
  // `"load-balancer-exclusion"`. Identifiers are limited to 100 characters and
  // can include only letters, digits, underscores, hyphens, and periods. First
  // character has to be alphanumeric.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A description of this exclusion.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. An [advanced logs
  // filter](https://cloud.google.com/logging/docs/view/advanced-queries) that
  // matches the log entries to be excluded. By using the [sample
  // function](https://cloud.google.com/logging/docs/view/advanced-queries#sample),
  // you can exclude less than 100% of the matching log entries.
  //
  // For example, the following query matches 99% of low-severity log entries
  // from Google Cloud Storage buckets:
  //
  //   `resource.type=gcs_bucket severity<ERROR sample(insertId, 0.99)`
  string filter = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set to True, then this exclusion is disabled and it does not
  // exclude any log entries. You can [update an
  // exclusion][google.logging.v2.ConfigServiceV2.UpdateExclusion] to change the
  // value of this field.
  bool disabled = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The creation timestamp of the exclusion.
  //
  // This field may not be present for older exclusions.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of the exclusion.
  //
  // This field may not be present for older exclusions.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The parameters to `ListExclusions`.
message ListExclusionsRequest {
  // Required. The parent resource whose exclusions are to be listed.
  //
  //     "projects/[PROJECT_ID]"
  //     "organizations/[ORGANIZATION_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]"
  //     "folders/[FOLDER_ID]"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/LogExclusion"
    }
  ];

  // Optional. If present, then retrieve the next batch of results from the
  // preceding call to this method. `pageToken` must be the value of
  // `nextPageToken` from the previous response. The values of other method
  // parameters should be identical to those in the previous call.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of results to return from this request.
  // Non-positive values are ignored. The presence of `nextPageToken` in the
  // response indicates that more results might be available.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Result returned from `ListExclusions`.
message ListExclusionsResponse {
  // A list of exclusions.
  repeated LogExclusion exclusions = 1;

  // If there might be more results than appear in this response, then
  // `nextPageToken` is included. To get the next set of results, call the same
  // method again using the value of `nextPageToken` as `pageToken`.
  string next_page_token = 2;
}

// The parameters to `GetExclusion`.
message GetExclusionRequest {
  // Required. The resource name of an existing exclusion:
  //
  //     "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
  //     "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
  //     "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/exclusions/my-exclusion"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogExclusion"
    }
  ];
}

// The parameters to `CreateExclusion`.
message CreateExclusionRequest {
  // Required. The parent resource in which to create the exclusion:
  //
  //     "projects/[PROJECT_ID]"
  //     "organizations/[ORGANIZATION_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]"
  //     "folders/[FOLDER_ID]"
  //
  // For examples:
  //
  //   `"projects/my-logging-project"`
  //   `"organizations/*********"`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/LogExclusion"
    }
  ];

  // Required. The new exclusion, whose `name` parameter is an exclusion name
  // that is not already used in the parent resource.
  LogExclusion exclusion = 2 [(google.api.field_behavior) = REQUIRED];
}

// The parameters to `UpdateExclusion`.
message UpdateExclusionRequest {
  // Required. The resource name of the exclusion to update:
  //
  //     "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
  //     "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
  //     "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/exclusions/my-exclusion"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogExclusion"
    }
  ];

  // Required. New values for the existing exclusion. Only the fields specified
  // in `update_mask` are relevant.
  LogExclusion exclusion = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A non-empty list of fields to change in the existing exclusion.
  // New values for the fields are taken from the corresponding fields in the
  // [LogExclusion][google.logging.v2.LogExclusion] included in this request.
  // Fields not mentioned in `update_mask` are not changed and are ignored in
  // the request.
  //
  // For example, to change the filter and description of an exclusion,
  // specify an `update_mask` of `"filter,description"`.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = REQUIRED];
}

// The parameters to `DeleteExclusion`.
message DeleteExclusionRequest {
  // Required. The resource name of an existing exclusion to delete:
  //
  //     "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
  //     "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
  //     "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]"
  //
  // For example:
  //
  //   `"projects/my-project/exclusions/my-exclusion"`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogExclusion"
    }
  ];
}

// The parameters to
// [GetCmekSettings][google.logging.v2.ConfigServiceV2.GetCmekSettings].
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
message GetCmekSettingsRequest {
  // Required. The resource for which to retrieve CMEK settings.
  //
  //     "projects/[PROJECT_ID]/cmekSettings"
  //     "organizations/[ORGANIZATION_ID]/cmekSettings"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
  //     "folders/[FOLDER_ID]/cmekSettings"
  //
  // For example:
  //
  //   `"organizations/12345/cmekSettings"`
  //
  // Note: CMEK for the Log Router can be configured for Google Cloud projects,
  // folders, organizations and billing accounts. Once configured for an
  // organization, it applies to all projects and folders in the Google Cloud
  // organization.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/CmekSettings"
    }
  ];
}

// The parameters to
// [UpdateCmekSettings][google.logging.v2.ConfigServiceV2.UpdateCmekSettings].
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
message UpdateCmekSettingsRequest {
  // Required. The resource name for the CMEK settings to update.
  //
  //     "projects/[PROJECT_ID]/cmekSettings"
  //     "organizations/[ORGANIZATION_ID]/cmekSettings"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
  //     "folders/[FOLDER_ID]/cmekSettings"
  //
  // For example:
  //
  //   `"organizations/12345/cmekSettings"`
  //
  // Note: CMEK for the Log Router can currently only be configured for Google
  // Cloud organizations. Once configured, it applies to all projects and
  // folders in the Google Cloud organization.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The CMEK settings to update.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  CmekSettings cmek_settings = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Field mask identifying which fields from `cmek_settings` should
  // be updated. A field will be overwritten if and only if it is in the update
  // mask. Output only fields cannot be updated.
  //
  // See [FieldMask][google.protobuf.FieldMask] for more information.
  //
  // For example: `"updateMask=kmsKeyName"`
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Describes the customer-managed encryption key (CMEK) settings associated with
// a project, folder, organization, billing account, or flexible resource.
//
// Note: CMEK for the Log Router can currently only be configured for Google
// Cloud organizations. Once configured, it applies to all projects and folders
// in the Google Cloud organization.
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
message CmekSettings {
  option (google.api.resource) = {
    type: "logging.googleapis.com/CmekSettings"
    pattern: "projects/{project}/cmekSettings"
    pattern: "organizations/{organization}/cmekSettings"
    pattern: "folders/{folder}/cmekSettings"
    pattern: "billingAccounts/{billing_account}/cmekSettings"
  };

  // Output only. The resource name of the CMEK settings.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The resource name for the configured Cloud KMS key.
  //
  // KMS key name format:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION]/keyRings/[KEYRING]/cryptoKeys/[KEY]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/us-central1/keyRings/my-ring/cryptoKeys/my-key"`
  //
  //
  //
  // To enable CMEK for the Log Router, set this field to a valid
  // `kms_key_name` for which the associated service account has the required
  // cloudkms.cryptoKeyEncrypterDecrypter roles assigned for the key.
  //
  // The Cloud KMS key used by the Log Router can be updated by changing the
  // `kms_key_name` to a new valid key name or disabled by setting the key name
  // to an empty string. Encryption operations that are in progress will be
  // completed with the key that was in use when they started. Decryption
  // operations will be completed using the key that was used at the time of
  // encryption unless access to that key has been revoked.
  //
  // To disable CMEK for the Log Router, set this field to an empty string.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  string kms_key_name = 2;

  // The CryptoKeyVersion resource name for the configured Cloud KMS key.
  //
  // KMS key name format:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION]/keyRings/[KEYRING]/cryptoKeys/[KEY]/cryptoKeyVersions/[VERSION]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/us-central1/keyRings/my-ring/cryptoKeys/my-key/cryptoKeyVersions/1"`
  //
  // This is a read-only field used to convey the specific configured
  // CryptoKeyVersion of `kms_key` that has been configured. It will be
  // populated in cases where the CMEK settings are bound to a single key
  // version.
  //
  // If this field is populated, the `kms_key` is tied to a specific
  // CryptoKeyVersion.
  string kms_key_version_name = 4;

  // Output only. The service account that will be used by the Log Router to
  // access your Cloud KMS key.
  //
  // Before enabling CMEK for Log Router, you must first assign the
  // cloudkms.cryptoKeyEncrypterDecrypter role to the service account that
  // the Log Router will use to access your Cloud KMS key. Use
  // [GetCmekSettings][google.logging.v2.ConfigServiceV2.GetCmekSettings] to
  // obtain the service account ID.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  string service_account_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The parameters to
// [GetSettings][google.logging.v2.ConfigServiceV2.GetSettings].
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
message GetSettingsRequest {
  // Required. The resource for which to retrieve settings.
  //
  //     "projects/[PROJECT_ID]/settings"
  //     "organizations/[ORGANIZATION_ID]/settings"
  //     "billingAccounts/[BILLING_ACCOUNT_ID]/settings"
  //     "folders/[FOLDER_ID]/settings"
  //
  // For example:
  //
  //   `"organizations/12345/settings"`
  //
  // Note: Settings for the Log Router can be get for Google Cloud projects,
  // folders, organizations and billing accounts. Currently it can only be
  // configured for organizations. Once configured for an organization, it
  // applies to all projects and folders in the Google Cloud organization.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/Settings"
    }
  ];
}

// The parameters to
// [UpdateSettings][google.logging.v2.ConfigServiceV2.UpdateSettings].
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
message UpdateSettingsRequest {
  // Required. The resource name for the settings to update.
  //
  //     "organizations/[ORGANIZATION_ID]/settings"
  //
  // For example:
  //
  //   `"organizations/12345/settings"`
  //
  // Note: Settings for the Log Router can currently only be configured for
  // Google Cloud organizations. Once configured, it applies to all projects and
  // folders in the Google Cloud organization.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The settings to update.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  Settings settings = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Field mask identifying which fields from `settings` should
  // be updated. A field will be overwritten if and only if it is in the update
  // mask. Output only fields cannot be updated.
  //
  // See [FieldMask][google.protobuf.FieldMask] for more information.
  //
  // For example: `"updateMask=kmsKeyName"`
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Describes the settings associated with a project, folder, organization,
// billing account, or flexible resource.
message Settings {
  option (google.api.resource) = {
    type: "logging.googleapis.com/Settings"
    pattern: "projects/{project}/settings"
    pattern: "organizations/{organization}/settings"
    pattern: "folders/{folder}/settings"
    pattern: "billingAccounts/{billing_account}/settings"
  };

  // Output only. The resource name of the settings.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The resource name for the configured Cloud KMS key.
  //
  // KMS key name format:
  //
  //     "projects/[PROJECT_ID]/locations/[LOCATION]/keyRings/[KEYRING]/cryptoKeys/[KEY]"
  //
  // For example:
  //
  //   `"projects/my-project/locations/us-central1/keyRings/my-ring/cryptoKeys/my-key"`
  //
  //
  //
  // To enable CMEK for the Log Router, set this field to a valid
  // `kms_key_name` for which the associated service account has the required
  // `roles/cloudkms.cryptoKeyEncrypterDecrypter` role assigned for the key.
  //
  // The Cloud KMS key used by the Log Router can be updated by changing the
  // `kms_key_name` to a new valid key name. Encryption operations that are in
  // progress will be completed with the key that was in use when they started.
  // Decryption operations will be completed using the key that was used at the
  // time of encryption unless access to that key has been revoked.
  //
  // To disable CMEK for the Log Router, set this field to an empty string.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  string kms_key_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The service account that will be used by the Log Router to
  // access your Cloud KMS key.
  //
  // Before enabling CMEK for Log Router, you must first assign the role
  // `roles/cloudkms.cryptoKeyEncrypterDecrypter` to the service account that
  // the Log Router will use to access your Cloud KMS key. Use
  // [GetSettings][google.logging.v2.ConfigServiceV2.GetSettings] to
  // obtain the service account ID.
  //
  // See [Enabling CMEK for Log
  // Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
  // for more information.
  string kms_service_account_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The Cloud region that will be used for _Default and _Required log
  // buckets for newly created projects and folders. For example `europe-west1`.
  // This setting does not affect the location of custom log buckets.
  string storage_location = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the _Default sink in newly created projects and
  // folders will created in a disabled state. This can be used to automatically
  // disable log ingestion if there is already an aggregated sink configured in
  // the hierarchy. The _Default sink can be re-enabled manually if needed.
  bool disable_default_sink = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The parameters to CopyLogEntries.
message CopyLogEntriesRequest {
  // Required. Log bucket from which to copy log entries.
  //
  // For example:
  //
  //   `"projects/my-project/locations/global/buckets/my-source-bucket"`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A filter specifying which log entries to copy. The filter must be
  // no more than 20k characters. An empty filter matches all log entries.
  string filter = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Destination to which to copy log entries.
  string destination = 4 [(google.api.field_behavior) = REQUIRED];
}

// Metadata for CopyLogEntries long running operations.
message CopyLogEntriesMetadata {
  // The create time of an operation.
  google.protobuf.Timestamp start_time = 1;

  // The end time of an operation.
  google.protobuf.Timestamp end_time = 2;

  // State of an operation.
  OperationState state = 3;

  // Identifies whether the user has requested cancellation of the operation.
  bool cancellation_requested = 4;

  // CopyLogEntries RPC request.
  CopyLogEntriesRequest request = 5;

  // Estimated progress of the operation (0 - 100%).
  int32 progress = 6;

  // The IAM identity of a service account that must be granted access to the
  // destination.
  //
  // If the service account is not granted permission to the destination within
  // an hour, the operation will be cancelled.
  //
  // For example: `"serviceAccount:<EMAIL>"`
  string writer_identity = 7;
}

// Response type for CopyLogEntries long running operations.
message CopyLogEntriesResponse {
  // Number of log entries copied.
  int64 log_entries_copied_count = 1;
}

// Metadata for LongRunningUpdateBucket Operations.
message BucketMetadata {
  // The create time of an operation.
  google.protobuf.Timestamp start_time = 1;

  // The end time of an operation.
  google.protobuf.Timestamp end_time = 2;

  // State of an operation.
  OperationState state = 3;

  oneof request {
    // LongRunningCreateBucket RPC request.
    CreateBucketRequest create_bucket_request = 4;

    // LongRunningUpdateBucket RPC request.
    UpdateBucketRequest update_bucket_request = 5;
  }
}

// Metadata for long running Link operations.
message LinkMetadata {
  // The start time of an operation.
  google.protobuf.Timestamp start_time = 1;

  // The end time of an operation.
  google.protobuf.Timestamp end_time = 2;

  // State of an operation.
  OperationState state = 3;

  oneof request {
    // CreateLink RPC request.
    CreateLinkRequest create_link_request = 4;

    // DeleteLink RPC request.
    DeleteLinkRequest delete_link_request = 5;
  }
}

// List of different operation states.
// High level state of the operation. This is used to report the job's
// current state to the user. Once a long running operation is created,
// the current state of the operation can be queried even before the
// operation is finished and the final result is available.
enum OperationState {
  // Should not be used.
  OPERATION_STATE_UNSPECIFIED = 0;

  // The operation is scheduled.
  OPERATION_STATE_SCHEDULED = 1;

  // Waiting for necessary permissions.
  OPERATION_STATE_WAITING_FOR_PERMISSIONS = 2;

  // The operation is running.
  OPERATION_STATE_RUNNING = 3;

  // The operation was completed successfully.
  OPERATION_STATE_SUCCEEDED = 4;

  // The operation failed.
  OPERATION_STATE_FAILED = 5;

  // The operation was cancelled by the user.
  OPERATION_STATE_CANCELLED = 6;
}

// LogBucket lifecycle states.
enum LifecycleState {
  // Unspecified state. This is only used/useful for distinguishing unset
  // values.
  LIFECYCLE_STATE_UNSPECIFIED = 0;

  // The normal and active state.
  ACTIVE = 1;

  // The resource has been marked for deletion by the user. For some resources
  // (e.g. buckets), this can be reversed by an un-delete operation.
  DELETE_REQUESTED = 2;

  // The resource has been marked for an update by the user. It will remain in
  // this state until the update is complete.
  UPDATING = 3;

  // The resource has been marked for creation by the user. It will remain in
  // this state until the creation is complete.
  CREATING = 4;

  // The resource is in an INTERNAL error state.
  FAILED = 5;
}

// IndexType is used for custom indexing. It describes the type of an indexed
// field.
enum IndexType {
  // The index's type is unspecified.
  INDEX_TYPE_UNSPECIFIED = 0;

  // The index is a string-type index.
  INDEX_TYPE_STRING = 1;

  // The index is a integer-type index.
  INDEX_TYPE_INTEGER = 2;
}

// Cloud Logging specific location metadata.
message LocationMetadata {
  // Indicates whether or not Log Analytics features are supported in the given
  // location.
  bool log_analytics_enabled = 1;
}
