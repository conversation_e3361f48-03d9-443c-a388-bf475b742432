// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.storagetransfer.logging;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/genproto/googleapis/storagetransfer/logging;logging";
option java_multiple_files = true;
option java_outer_classname = "TransferActivityLogProto";
option java_package = "com.google.storagetransfer.logging";
option csharp_namespace = "Google.StorageTransfer.Logging";
option php_namespace = "Google\\StorageTransfer\\Logging";
option ruby_package = "Google::StorageTransfer::Logging";

// AWS S3 object metadata.
message AwsS3ObjectMetadata {
  // Required. Name of the S3 bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name/key of the object.
  string object_key = 2 [(google.api.field_behavior) = REQUIRED];

  // Last modified time of the object.
  google.protobuf.Timestamp last_modified_time = 3;

  // The MD5 checksum of the object's content.
  string md5 = 4;

  // Required. Size of the object in bytes.
  int64 size = 5 [(google.api.field_behavior) = REQUIRED];
}

// AWS S3 bucket metadata.
message AwsS3BucketMetadata {
  // Required. Name of the S3 bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // The path of transfer objects as an object key prefix ending with "/".
  string path = 2;
}

// Google Cloud Storage object metadata.
message GcsObjectMetadata {
  // Required. Name of the Cloud Storage bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name/key of the object.
  string object_key = 2 [(google.api.field_behavior) = REQUIRED];

  // Last modified time of the object.
  google.protobuf.Timestamp last_modified_time = 3;

  // The MD5 checksum of the object's content.
  string md5 = 4;

  // The CRC32C checksum of the object's content.
  string crc32c = 5;

  // Required. Size of the object in bytes.
  int64 size = 6 [(google.api.field_behavior) = REQUIRED];
}

// Google Cloud Storage bucket metadata.
message GcsBucketMetadata {
  // Required. Name of the Cloud Storage bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // The path of transfer objects as an object key prefix ending with "/".
  string path = 2;
}

// Azure Blob Storage blob metadata.
message AzureBlobMetadata {
  // Required. Name of the Azure Blob storage account.
  string account = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the container.
  string container = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the blob.
  string blob_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Last modified time of the blob.
  google.protobuf.Timestamp last_modified_time = 4;

  // The MD5 checksum of the object's content.
  string md5 = 5;

  // Required. Size of the blob in bytes.
  int64 size = 6 [(google.api.field_behavior) = REQUIRED];
}

// Azure Blob Storage container metadata.
message AzureBlobContainerMetadata {
  // Required. Name of the Azure Blob storage account.
  string account = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the container.
  string container = 2 [(google.api.field_behavior) = REQUIRED];

  // The path of transfer blobs as a blob name prefix ending with "/".
  string path = 3;
}

// POSIX file metadata.
message PosixFileMetadata {
  // Required. Path of a file.
  string path = 1 [(google.api.field_behavior) = REQUIRED];

  // Last modified time (mtime) of the file.
  google.protobuf.Timestamp last_modified_time = 2;

  // The CRC32C checksum of the object's content.
  string crc32c = 3;

  // Required. Size of the file in bytes.
  int64 size = 4 [(google.api.field_behavior) = REQUIRED];
}

// HTTP file metadata.
message HttpFileMetadata {
  // Required. Url of the HTTP file.
  string url = 1 [(google.api.field_behavior) = REQUIRED];

  // The MD5 checksum of the file's content.
  string md5 = 2;

  // Size of the file in bytes.
  int64 size = 3;
}

// HTTP manifest file metadata.
message HttpManifestMetadata {
  // Required. Url of the HTTP manifest which contains the list of HTTP files to
  // transfer.
  string url = 1 [(google.api.field_behavior) = REQUIRED];
}

// Metadata of a blob/file/object.
message ObjectMetadata {
  // Required. Storage system type of the object.
  StorageSystemType type = 1 [(google.api.field_behavior) = REQUIRED];

  oneof metadata {
    // Object metadata of AWS S3.
    AwsS3ObjectMetadata aws_s3_object = 3;

    // Blob metadata of Azure Blob Storage.
    AzureBlobMetadata azure_blob = 4;

    // Object metadata of Google Cloud Storage.
    GcsObjectMetadata gcs_object = 5;

    // File/directory metadata of POSIX file system.
    PosixFileMetadata posix_file = 6;

    // Metadata of a file on a HTTP server.
    HttpFileMetadata http_file = 7;
  }
}

// Metadata of a bucket/container/directory
message ContainerMetadata {
  // Required. Storage system type of the object.
  StorageSystemType type = 1 [(google.api.field_behavior) = REQUIRED];

  oneof metadata {
    // Bucket metadata of AWS S3.
    AwsS3BucketMetadata aws_s3_bucket = 3;

    // Container metadata of Azure Blob Storage.
    AzureBlobContainerMetadata azure_blob_container = 4;

    // Bucket metadata of Google Cloud Storage.
    GcsBucketMetadata gcs_bucket = 5;

    // Directory metadata of POSIX file system.
    PosixFileMetadata posix_directory = 6;

    // Metadata of a manifest file on a HTTP server.
    HttpManifestMetadata http_manifest = 7;
  }
}

// Schema of log payload of transfer activity.
message TransferActivityLog {
  // Possible actions which a transfer operation can make.
  enum Action {
    // Unspeficied action.
    ACTION_UNSPECIFIED = 0;

    // Finding work to do, such as listing files in a directory or listing
    // objects in a bucket.
    FIND = 1;

    // Copying files or objects.
    COPY = 2;

    // Deleting files or objects at destination.
    DELETE = 3;
  }

  // Status of an action.
  message Status {
    // Required. A string value of the Google RPC code as the status of the
    // action. The action succeeded if it's `OK`, and failed otherwise.
    string status_code = 1 [(google.api.field_behavior) = REQUIRED];

    // A string that represents the type of error encountered. Populated only if
    // status_code is not `OK`.
    string error_type = 2;

    // A human-readable error message for the failure. Populated only if
    // status_code is not `OK`.
    string error_message = 3;
  }

  // Required. Name of the transfer operation.
  string operation = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The action which the transfer operation made.
  Action action = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Status of the action.
  Status status = 3 [(google.api.field_behavior) = REQUIRED];

  // Metadata of source bucket/container/directory. Only set if the action is
  // FIND.
  ContainerMetadata source_container = 4;

  // Metadata of destination bucket/container/directory. Only set if the action
  // is FIND.
  ContainerMetadata destination_container = 5;

  // Metadata of the source blob/file/object. Only set if the action is COPY or
  // DELETE when deletion is applied to source.
  ObjectMetadata source_object = 6;

  // Metadata of the destination blob/file/object. Only set if the action is
  // or DELETE when deletion is applied to destination.
  ObjectMetadata destination_object = 7;

  // Required. Completion time of the action.
  google.protobuf.Timestamp complete_time = 8
      [(google.api.field_behavior) = REQUIRED];
}

// Type of the storage system.
enum StorageSystemType {
  // Unspecified.
  STORAGE_SYSTEM_TYPE_UNSPECIFIED = 0;

  // AWS S3.
  AWS_S3 = 1;

  // Azure Blob Storage.
  AZURE_BLOB = 2;

  // Google Cloud Storage.
  GCS = 3;

  // POSIX file system.
  POSIX_FS = 4;

  // HTTP/HTTPS servers.
  HTTP = 5;
}
