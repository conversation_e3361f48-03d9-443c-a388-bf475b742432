// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.storagetransfer.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/storagetransfer/v1/transfer_types.proto";

option csharp_namespace = "Google.Cloud.StorageTransfer.V1";
option go_package = "cloud.google.com/go/storagetransfer/apiv1/storagetransferpb;storagetransferpb";
option java_outer_classname = "TransferProto";
option java_package = "com.google.storagetransfer.v1.proto";
option php_namespace = "Google\\Cloud\\StorageTransfer\\V1";
option ruby_package = "Google::Cloud::StorageTransfer::V1";

// Storage Transfer Service and its protos.
// Transfers data between between Google Cloud Storage buckets or from a data
// source external to Google to a Cloud Storage bucket.
service StorageTransferService {
  option (google.api.default_host) = "storagetransfer.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns the Google service account that is used by Storage Transfer
  // Service to access buckets in the project where transfers
  // run or in other projects. Each Google service account is associated
  // with one Google Cloud project. Users
  // should add this service account to the Google Cloud Storage bucket
  // ACLs to grant access to Storage Transfer Service. This service
  // account is created and owned by Storage Transfer Service and can
  // only be used by Storage Transfer Service.
  rpc GetGoogleServiceAccount(GetGoogleServiceAccountRequest)
      returns (GoogleServiceAccount) {
    option (google.api.http) = {
      get: "/v1/googleServiceAccounts/{project_id}"
    };
  }

  // Creates a transfer job that runs periodically.
  rpc CreateTransferJob(CreateTransferJobRequest) returns (TransferJob) {
    option (google.api.http) = {
      post: "/v1/transferJobs"
      body: "transfer_job"
    };
  }

  // Updates a transfer job. Updating a job's transfer spec does not affect
  // transfer operations that are running already.
  //
  // **Note:** The job's [status][google.storagetransfer.v1.TransferJob.status]
  // field can be modified using this RPC (for example, to set a job's status to
  // [DELETED][google.storagetransfer.v1.TransferJob.Status.DELETED],
  // [DISABLED][google.storagetransfer.v1.TransferJob.Status.DISABLED], or
  // [ENABLED][google.storagetransfer.v1.TransferJob.Status.ENABLED]).
  rpc UpdateTransferJob(UpdateTransferJobRequest) returns (TransferJob) {
    option (google.api.http) = {
      patch: "/v1/{job_name=transferJobs/**}"
      body: "*"
    };
  }

  // Gets a transfer job.
  rpc GetTransferJob(GetTransferJobRequest) returns (TransferJob) {
    option (google.api.http) = {
      get: "/v1/{job_name=transferJobs/**}"
    };
  }

  // Lists transfer jobs.
  rpc ListTransferJobs(ListTransferJobsRequest)
      returns (ListTransferJobsResponse) {
    option (google.api.http) = {
      get: "/v1/transferJobs"
    };
  }

  // Pauses a transfer operation.
  rpc PauseTransferOperation(PauseTransferOperationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=transferOperations/**}:pause"
      body: "*"
    };
  }

  // Resumes a transfer operation that is paused.
  rpc ResumeTransferOperation(ResumeTransferOperationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=transferOperations/**}:resume"
      body: "*"
    };
  }

  // Starts a new operation for the specified transfer job.
  // A `TransferJob` has a maximum of one active `TransferOperation`. If this
  // method is called while a `TransferOperation` is active, an error is
  // returned.
  rpc RunTransferJob(RunTransferJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{job_name=transferJobs/**}:run"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "TransferOperation"
    };
  }

  // Deletes a transfer job. Deleting a transfer job sets its status to
  // [DELETED][google.storagetransfer.v1.TransferJob.Status.DELETED].
  rpc DeleteTransferJob(DeleteTransferJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{job_name=transferJobs/**}"
    };
  }

  // Creates an agent pool resource.
  rpc CreateAgentPool(CreateAgentPoolRequest) returns (AgentPool) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id=*}/agentPools"
      body: "agent_pool"
    };
    option (google.api.method_signature) =
        "project_id,agent_pool,agent_pool_id";
  }

  // Updates an existing agent pool resource.
  rpc UpdateAgentPool(UpdateAgentPoolRequest) returns (AgentPool) {
    option (google.api.http) = {
      patch: "/v1/{agent_pool.name=projects/*/agentPools/*}"
      body: "agent_pool"
    };
    option (google.api.method_signature) = "agent_pool,update_mask";
  }

  // Gets an agent pool.
  rpc GetAgentPool(GetAgentPoolRequest) returns (AgentPool) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/agentPools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists agent pools.
  rpc ListAgentPools(ListAgentPoolsRequest) returns (ListAgentPoolsResponse) {
    option (google.api.http) = {
      get: "/v1/projects/{project_id=*}/agentPools"
    };
    option (google.api.method_signature) = "project_id";
  }

  // Deletes an agent pool.
  rpc DeleteAgentPool(DeleteAgentPoolRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/agentPools/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request passed to GetGoogleServiceAccount.
message GetGoogleServiceAccountRequest {
  // Required. The ID of the Google Cloud project that the Google service
  // account is associated with.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request passed to CreateTransferJob.
message CreateTransferJobRequest {
  // Required. The job to create.
  TransferJob transfer_job = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request passed to UpdateTransferJob.
message UpdateTransferJobRequest {
  // Required. The name of job to update.
  string job_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the Google Cloud project that owns the
  // job.
  string project_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The job to update. `transferJob` is expected to specify one or
  // more of five fields:
  // [description][google.storagetransfer.v1.TransferJob.description],
  // [transfer_spec][google.storagetransfer.v1.TransferJob.transfer_spec],
  // [notification_config][google.storagetransfer.v1.TransferJob.notification_config],
  // [logging_config][google.storagetransfer.v1.TransferJob.logging_config], and
  // [status][google.storagetransfer.v1.TransferJob.status].  An
  // `UpdateTransferJobRequest` that specifies other fields are rejected with
  // the error [INVALID_ARGUMENT][google.rpc.Code.INVALID_ARGUMENT]. Updating a
  // job status to
  // [DELETED][google.storagetransfer.v1.TransferJob.Status.DELETED] requires
  // `storagetransfer.jobs.delete` permission.
  TransferJob transfer_job = 3 [(google.api.field_behavior) = REQUIRED];

  // The field mask of the fields in `transferJob` that are to be updated in
  // this request.  Fields in `transferJob` that can be updated are:
  // [description][google.storagetransfer.v1.TransferJob.description],
  // [transfer_spec][google.storagetransfer.v1.TransferJob.transfer_spec],
  // [notification_config][google.storagetransfer.v1.TransferJob.notification_config],
  // [logging_config][google.storagetransfer.v1.TransferJob.logging_config], and
  // [status][google.storagetransfer.v1.TransferJob.status].  To update the
  // `transfer_spec` of the job, a complete transfer specification must be
  // provided. An incomplete specification missing any required fields is
  // rejected with the error
  // [INVALID_ARGUMENT][google.rpc.Code.INVALID_ARGUMENT].
  google.protobuf.FieldMask update_transfer_job_field_mask = 4;
}

// Request passed to GetTransferJob.
message GetTransferJobRequest {
  // Required. The job to get.
  string job_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the Google Cloud project that owns the
  // job.
  string project_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request passed to DeleteTransferJob.
message DeleteTransferJobRequest {
  // Required. The job to delete.
  string job_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the Google Cloud project that owns the
  // job.
  string project_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// `projectId`, `jobNames`, and `jobStatuses` are query parameters that can
// be specified when listing transfer jobs.
message ListTransferJobsRequest {
  // Required. A list of query parameters specified as JSON text in the form of:
  //
  // ```
  // {
  //   "projectId":"my_project_id",
  //   "jobNames":["jobid1","jobid2",...],
  //   "jobStatuses":["status1","status2",...],
  //   "dataBackend":"QUERY_REPLICATION_CONFIGS",
  //   "sourceBucket":"source-bucket-name",
  //   "sinkBucket":"sink-bucket-name",
  // }
  // ```
  //
  // The JSON formatting in the example is for display only; provide the
  // query parameters without spaces or line breaks.
  //
  // * `projectId` is required.
  // * Since `jobNames` and `jobStatuses` support multiple values, their values
  //   must be specified with array notation. `jobNames` and `jobStatuses` are
  //   optional. Valid values are case-insensitive:
  //     * [ENABLED][google.storagetransfer.v1.TransferJob.Status.ENABLED]
  //     * [DISABLED][google.storagetransfer.v1.TransferJob.Status.DISABLED]
  //     * [DELETED][google.storagetransfer.v1.TransferJob.Status.DELETED]
  // * Specify `"dataBackend":"QUERY_REPLICATION_CONFIGS"` to return a list of
  //   cross-bucket replication jobs.
  // * Limit the results to jobs from a particular bucket with `sourceBucket`
  //   and/or to a particular bucket with `sinkBucket`.
  string filter = 1 [(google.api.field_behavior) = REQUIRED];

  // The list page size. The max allowed value is 256.
  int32 page_size = 4;

  // The list page token.
  string page_token = 5;
}

// Response from ListTransferJobs.
message ListTransferJobsResponse {
  // A list of transfer jobs.
  repeated TransferJob transfer_jobs = 1;

  // The list next page token.
  string next_page_token = 2;
}

// Request passed to PauseTransferOperation.
message PauseTransferOperationRequest {
  // Required. The name of the transfer operation.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request passed to ResumeTransferOperation.
message ResumeTransferOperationRequest {
  // Required. The name of the transfer operation.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request passed to RunTransferJob.
message RunTransferJobRequest {
  // Required. The name of the transfer job.
  string job_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the Google Cloud project that owns the transfer
  // job.
  string project_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Specifies the request passed to CreateAgentPool.
message CreateAgentPoolRequest {
  // Required. The ID of the Google Cloud project that owns the
  // agent pool.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The agent pool to create.
  AgentPool agent_pool = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the agent pool to create.
  //
  // The `agent_pool_id` must meet the following requirements:
  //
  // *   Length of 128 characters or less.
  // *   Not start with the string `goog`.
  // *   Start with a lowercase ASCII character, followed by:
  //     *   Zero or more: lowercase Latin alphabet characters, numerals,
  //         hyphens (`-`), periods (`.`), underscores (`_`), or tildes (`~`).
  //     *   One or more numerals or lowercase ASCII characters.
  //
  // As expressed by the regular expression:
  // `^(?!goog)[a-z]([a-z0-9-._~]*[a-z0-9])?$`.
  string agent_pool_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Specifies the request passed to UpdateAgentPool.
message UpdateAgentPoolRequest {
  // Required. The agent pool to update. `agent_pool` is expected to specify
  // following fields:
  //
  // *  [name][google.storagetransfer.v1.AgentPool.name]
  //
  // *  [display_name][google.storagetransfer.v1.AgentPool.display_name]
  //
  // *  [bandwidth_limit][google.storagetransfer.v1.AgentPool.bandwidth_limit]
  // An `UpdateAgentPoolRequest` with any other fields is rejected
  // with the error [INVALID_ARGUMENT][google.rpc.Code.INVALID_ARGUMENT].
  AgentPool agent_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // The [field mask]
  // (https://developers.google.com/protocol-buffers/docs/reference/google.protobuf)
  // of the fields in `agentPool` to update in this request.
  // The following `agentPool` fields can be updated:
  //
  // *  [display_name][google.storagetransfer.v1.AgentPool.display_name]
  //
  // *  [bandwidth_limit][google.storagetransfer.v1.AgentPool.bandwidth_limit]
  google.protobuf.FieldMask update_mask = 2;
}

// Specifies the request passed to GetAgentPool.
message GetAgentPoolRequest {
  // Required. The name of the agent pool to get.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Specifies the request passed to DeleteAgentPool.
message DeleteAgentPoolRequest {
  // Required. The name of the agent pool to delete.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request passed to ListAgentPools.
message ListAgentPoolsRequest {
  // Required. The ID of the Google Cloud project that owns the job.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // An optional list of query parameters specified as JSON text in the
  // form of:
  //
  // `{"agentPoolNames":["agentpool1","agentpool2",...]}`
  //
  // Since `agentPoolNames` support multiple values, its values must be
  // specified with array notation. When the filter is either empty or not
  // provided, the list returns all agent pools for the project.
  string filter = 2;

  // The list page size. The max allowed value is `256`.
  int32 page_size = 3;

  // The list page token.
  string page_token = 4;
}

// Response from ListAgentPools.
message ListAgentPoolsResponse {
  // A list of agent pools.
  repeated AgentPool agent_pools = 1;

  // The list next page token.
  string next_page_token = 2;
}
