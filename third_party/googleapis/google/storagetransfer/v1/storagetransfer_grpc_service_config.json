{"methodConfig": [{"name": [{"service": "google.storagetransfer.v1.StorageTransferService"}, {"service": "google.longrunning.Operations"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 2, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.storagetransfer.v1.StorageTransferService", "method": "CreateTransferJob"}], "timeout": "60s"}]}