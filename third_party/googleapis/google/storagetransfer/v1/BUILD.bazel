# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "storagetransfer_proto",
    srcs = [
        "transfer.proto",
        "transfer_types.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:code_proto",
        "//google/type:date_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "storagetransfer_proto_with_info",
    deps = [
        ":storagetransfer_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "storagetransfer_java_proto",
    deps = [":storagetransfer_proto"],
)

java_grpc_library(
    name = "storagetransfer_java_grpc",
    srcs = [":storagetransfer_proto"],
    deps = [":storagetransfer_java_proto"],
)

java_gapic_library(
    name = "storagetransfer_java_gapic",
    srcs = [":storagetransfer_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "storagetransfer_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storagetransfer_v1.yaml",
    test_deps = [
        ":storagetransfer_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":storagetransfer_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "storagetransfer_java_gapic_test_suite",
    test_classes = [
        "com.google.storagetransfer.v1.proto.StorageTransferServiceClientHttpJsonTest",
        "com.google.storagetransfer.v1.proto.StorageTransferServiceClientTest",
    ],
    runtime_deps = [":storagetransfer_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-storagetransfer-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":storagetransfer_java_gapic",
        ":storagetransfer_java_grpc",
        ":storagetransfer_java_proto",
        ":storagetransfer_proto",
    ],
)

go_proto_library(
    name = "storagetransfer_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/storagetransfer/apiv1/storagetransferpb",
    protos = [":storagetransfer_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:code_go_proto",
        "//google/type:date_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "storagetransfer_go_gapic",
    srcs = [":storagetransfer_proto_with_info"],
    grpc_service_config = "storagetransfer_grpc_service_config.json",
    importpath = "cloud.google.com/go/storagetransfer/apiv1;storagetransfer",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "storagetransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":storagetransfer_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-storagetransfer-v1-go",
    deps = [
        ":storagetransfer_go_gapic",
        ":storagetransfer_go_gapic_srcjar-metadata.srcjar",
        ":storagetransfer_go_gapic_srcjar-snippets.srcjar",
        ":storagetransfer_go_gapic_srcjar-test.srcjar",
        ":storagetransfer_go_proto",
    ],
)

py_gapic_library(
    name = "storagetransfer_py_gapic",
    srcs = [":storagetransfer_proto"],
    grpc_service_config = "storagetransfer_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=storage_transfer",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-storage-transfer",
    ],
    rest_numeric_enums = True,
    service_yaml = "storagetransfer_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "storagetransfer_py_gapic_test",
    srcs = [
        "storagetransfer_py_gapic_pytest.py",
        "storagetransfer_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":storagetransfer_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "storagetransfer-v1-py",
    deps = [
        ":storagetransfer_py_gapic",
    ],
)

php_proto_library(
    name = "storagetransfer_php_proto",
    deps = [":storagetransfer_proto"],
)

php_gapic_library(
    name = "storagetransfer_php_gapic",
    srcs = [":storagetransfer_proto_with_info"],
    grpc_service_config = "storagetransfer_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "storagetransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [":storagetransfer_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-storagetransfer-v1-php",
    deps = [
        ":storagetransfer_php_gapic",
        ":storagetransfer_php_proto",
    ],
)

nodejs_gapic_library(
    name = "storagetransfer_nodejs_gapic",
    package_name = "@google-cloud/storage-transfer",
    src = ":storagetransfer_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "storagetransfer_grpc_service_config.json",
    package = "google.storagetransfer.v1",
    rest_numeric_enums = True,
    service_yaml = "storagetransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "storagetransfer-v1-nodejs",
    deps = [
        ":storagetransfer_nodejs_gapic",
        ":storagetransfer_proto",
    ],
)

ruby_proto_library(
    name = "storagetransfer_ruby_proto",
    deps = [":storagetransfer_proto"],
)

ruby_grpc_library(
    name = "storagetransfer_ruby_grpc",
    srcs = [":storagetransfer_proto"],
    deps = [":storagetransfer_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "storagetransfer_ruby_gapic",
    srcs = [":storagetransfer_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=storagetransfer.googleapis.com",
        "ruby-cloud-api-shortname=storagetransfer",
        "ruby-cloud-gem-name=google-cloud-storage_transfer-v1",
        "ruby-cloud-product-url=https://cloud.google.com/storage-transfer-service/",
    ],
    grpc_service_config = "storagetransfer_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Storage Transfer Service allows you to quickly import online data into Cloud Storage. You can also set up a repeating schedule for transferring data, as well as transfer data within Cloud Storage, from one bucket to another.",
    ruby_cloud_title = "Storage Transfer Service V1",
    service_yaml = "storagetransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":storagetransfer_ruby_grpc",
        ":storagetransfer_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-storagetransfer-v1-ruby",
    deps = [
        ":storagetransfer_ruby_gapic",
        ":storagetransfer_ruby_grpc",
        ":storagetransfer_ruby_proto",
    ],
)

csharp_proto_library(
    name = "storagetransfer_csharp_proto",
    deps = [":storagetransfer_proto"],
)

csharp_grpc_library(
    name = "storagetransfer_csharp_grpc",
    srcs = [":storagetransfer_proto"],
    deps = [":storagetransfer_csharp_proto"],
)

csharp_gapic_library(
    name = "storagetransfer_csharp_gapic",
    srcs = [":storagetransfer_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "storagetransfer_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storagetransfer_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":storagetransfer_csharp_grpc",
        ":storagetransfer_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-storagetransfer-v1-csharp",
    deps = [
        ":storagetransfer_csharp_gapic",
        ":storagetransfer_csharp_grpc",
        ":storagetransfer_csharp_proto",
    ],
)

cc_proto_library(
    name = "storagetransfer_cc_proto",
    deps = [":storagetransfer_proto"],
)

cc_grpc_library(
    name = "storagetransfer_cc_grpc",
    srcs = [":storagetransfer_proto"],
    grpc_only = True,
    deps = [":storagetransfer_cc_proto"],
)
