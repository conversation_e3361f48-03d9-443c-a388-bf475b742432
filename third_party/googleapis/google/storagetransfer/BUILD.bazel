# This build file includes a target for the Ruby wrapper library for
# google-cloud-storage_transfer.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for storagetransfer.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "storagetransfer_ruby_wrapper",
    srcs = ["//google/storagetransfer/v1:storagetransfer_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-storage_transfer",
        "ruby-cloud-wrapper-of=v1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/storage-transfer-service/",
        "ruby-cloud-api-id=storagetransfer.googleapis.com",
        "ruby-cloud-api-shortname=storagetransfer",
    ],
    ruby_cloud_description = "Storage Transfer Service allows you to quickly import online data into Cloud Storage. You can also set up a repeating schedule for transferring data, as well as transfer data within Cloud Storage, from one bucket to another.",
    ruby_cloud_title = "Storage Transfer Service",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-storagetransfer-ruby",
    deps = [
        ":storagetransfer_ruby_wrapper",
    ],
)
