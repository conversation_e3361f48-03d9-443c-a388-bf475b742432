// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.actions.sdk.v2;

import "google/actions/sdk/v2/account_linking_secret.proto";
import "google/actions/sdk/v2/files.proto";
import "google/actions/sdk/v2/release_channel.proto";
import "google/actions/sdk/v2/validation_results.proto";
import "google/actions/sdk/v2/version.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/wrappers.proto";

option go_package = "google.golang.org/genproto/googleapis/actions/sdk/v2;sdk";
option java_multiple_files = true;
option java_outer_classname = "ActionsSdkProto";
option java_package = "com.google.actions.sdk.v2";

// Actions SDK API which allows developers to build projects using the SDK.
service ActionsSdk {
  option (google.api.default_host) = "actions.googleapis.com";

  // Updates the project draft based on the model.
  rpc WriteDraft(stream WriteDraftRequest) returns (Draft) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/draft:write"
      body: "*"
    };
  }

  // Updates the user's project preview based on the model.
  rpc WritePreview(stream WritePreviewRequest) returns (Preview) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/preview:write"
      body: "*"
    };
  }

  // Creates a project version based on the model and triggers deployment to the
  // specified release channel, if specified.
  rpc CreateVersion(stream CreateVersionRequest) returns (Version) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/versions:create"
      body: "*"
    };
  }

  // Reads the entire content of the project draft.
  rpc ReadDraft(ReadDraftRequest) returns (stream ReadDraftResponse) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/draft}:read"
      body: "*"
    };
  }

  // Reads the entire content of a project version.
  rpc ReadVersion(ReadVersionRequest) returns (stream ReadVersionResponse) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/versions/*}:read"
      body: "*"
    };
  }

  // Encrypts the OAuth client secret used in account linking flows.
  // This can be used to encrypt the client secret for the first time (e.g.
  // before the first push or after changing the client secret) or to re-encrypt
  // a client secret using the latest primary key version (considering key
  // rotation).
  rpc EncryptSecret(EncryptSecretRequest) returns (EncryptSecretResponse) {
    option (google.api.http) = {
      post: "/v2:encryptSecret"
      body: "*"
    };
  }

  // Decrypts the OAuth client secret used in account linking flows.
  // This can be used to view the client secret (e.g. after pulling a project).
  rpc DecryptSecret(DecryptSecretRequest) returns (DecryptSecretResponse) {
    option (google.api.http) = {
      post: "/v2:decryptSecret"
      body: "*"
    };
  }

  // Lists all the sample projects supported by the gactions CLI.
  rpc ListSampleProjects(ListSampleProjectsRequest) returns (ListSampleProjectsResponse) {
    option (google.api.http) = {
      get: "/v2/sampleProjects"
    };
  }

  // Lists all release channels and corresponding versions, if any.
  rpc ListReleaseChannels(ListReleaseChannelsRequest) returns (ListReleaseChannelsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/releaseChannels"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all versions and their current states.
  rpc ListVersions(ListVersionsRequest) returns (ListVersionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Streaming RPC request for WriteDraft.
message WriteDraftRequest {
  // Required. The parent resource name in the format `projects/{project}`. The
  // `{project}` is the cloud project ID associated with the project.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "actions.googleapis.com/Draft"
    }
  ];

  // Required. List of files sent to the server at a time. This is a list of config files
  // or data files.
  // 1. The first request must be a ConfigFiles.
  // 2. The first request must have a ConfigFile with 'settings'.
  // 3. The first request must have a ConfigFile with 'manifest'.
  // 4. The webhook ConfigFile corresponding to inline cloud function must be
  //    streamed before the DataFile corresponding to its source code.
  Files files = 4 [(google.api.field_behavior) = REQUIRED];
}

// Definition of draft resource.
message Draft {
  option (google.api.resource) = {
    type: "actions.googleapis.com/Draft"
    pattern: "projects/{project}/draft"
  };

  // The unique identifier of the draft in the following format.
  // `projects/{project}/draft`
  string name = 1;

  // Validation results associated with the project draft content. Note that
  // WriteDraft updates the draft despite the warnings as warnings are not draft
  // blocking.
  ValidationResults validation_results = 2;
}

// Streaming RPC request for WritePreview.
message WritePreviewRequest {
  // Indicates the preview content will be coming from the Draft.
  message ContentFromDraft {

  }

  // Indicates the preview content will be coming from an exiting version.
  message ContentFromSubmittedVersion {
    // Required. Submitted version of the project to be used to create a preview.
    // Format: `projects/{project}/versions/{version}`
    string version = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "actions.googleapis.com/Version"
      }
    ];
  }

  // Settings for updating the preview.
  message PreviewSettings {
    // Indicates whether or not to run certain operations, such as transactions,
    // in sandbox mode. By default, preview requests run these operations in
    // sandbox mode. In other words, the default value for `sandbox` is `true`.
    google.protobuf.BoolValue sandbox = 1;
  }

  // Required. The parent resource name in the format `projects/{project}`. The
  // `{project}` is the cloud project ID associated with the project.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "actions.googleapis.com/Preview"
    }
  ];

  // Data source used to created the preview.
  oneof source {
    // List of files sent to the server at a time. This is a list of config
    // files or data files.
    // 1. The first request must be a ConfigFiles.
    // 2. The first request must have a ConfigFile with 'settings'.
    // 3. The first request must have a ConfigFile with 'manifest'.
    // 4. The webhook ConfigFile corresponding to inline cloud function must be
    //    streamed before the DataFile corresponding to its source code.
    Files files = 5;

    // Content sourced from the project draft.
    ContentFromDraft draft = 6;

    // Content sourced from the an exiting version.
    ContentFromSubmittedVersion submitted_version = 7;
  }

  // Required. The settings for updating the user's preview.
  PreviewSettings preview_settings = 4 [(google.api.field_behavior) = REQUIRED];
}

// Definition of preview resource.
message Preview {
  option (google.api.resource) = {
    type: "actions.googleapis.com/Preview"
    pattern: "projects/{project}/previews/{preview}"
  };

  // The unique identifier of the preview.
  // Format: `projects/{project}/preview`
  string name = 1;

  // Validation results associated with the user project preview content.
  ValidationResults validation_results = 2;

  // The simulator URL to test the user preview.
  string simulator_url = 3;
}

// Streaming RPC request for CreateVersion.
message CreateVersionRequest {
  // Required. The parent resource name in the format `projects/{project}`. The
  // `{project}` is the cloud project ID associated with the project.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "actions.googleapis.com/Version"
    }
  ];

  // Required. List of files sent to the server at a time. This is a list of config files
  // or data files.
  // 1. The first request must be a ConfigFiles.
  // 2. The first request must have a ConfigFile with 'settings'.
  // 3. The first request must have a ConfigFile with 'manifest'.
  // 4. The webhook ConfigFile corresponding to inline cloud function must be
  //    streamed before the DataFile corresponding to its source code.
  Files files = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. The release channel to deploy the version, if specified. The supported
  // built in release channels are actions.channels.Production,
  // actions.channels.ClosedBeta, actions.channels.Alpha.
  // .
  string release_channel = 4 [(google.api.field_behavior) = OPTIONAL];
}

// RPC request for ReadDraft.
message ReadDraftRequest {
  // Required. The name of the resource in the format `projects/{project}/draft`. The
  // `{project}` is the cloud project ID associated with the project.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The version of the crypto key used to encrypt the account linking OAuth
  // client secret. If not specified, the primary key version is used for
  // encryption. Only relevant for projects with account linking with client
  // secret.
  string client_secret_encryption_key_version = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Streaming RPC response for ReadDraft.
message ReadDraftResponse {
  // List of files sent from the server at a time.
  Files files = 3;
}

// RPC request for ReadVersion.
message ReadVersionRequest {
  // Required. The name of the version resource in the format
  // `projects/{project}/versions/{version}`. `{project}` is the
  // cloud project ID associated with the project, `{version}` is the
  // identifier of the version being read.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The version of the crypto key used to encrypt the account linking OAuth
  // client secret. If not specified, the primary key version is used for
  // encryption. Only relevant for projects with account linking with client
  // secret.
  string client_secret_encryption_key_version = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Streaming RPC response for ReadVersion.
message ReadVersionResponse {
  // List of files sent from the server at a time.
  Files files = 1;
}

// RPC request for EncryptSecret.
message EncryptSecretRequest {
  // Required. The account linking client secret plaintext.
  string client_secret = 1 [(google.api.field_behavior) = REQUIRED];
}

// RPC response for EncryptSecret.
message EncryptSecretResponse {
  // Contains the encrypted account linking client secret and the key version
  // used to encrypt the secret.
  AccountLinkingSecret account_linking_secret = 1;
}

// RPC request for DecryptSecret.
message DecryptSecretRequest {
  // Required. The account linking client secret ciphertext.
  bytes encrypted_client_secret = 1 [(google.api.field_behavior) = REQUIRED];
}

// RPC response for DecryptSecret.
message DecryptSecretResponse {
  // The account linking client secret plaintext.
  string client_secret = 1;
}

// RPC request for ListSampleProjects.
message ListSampleProjectsRequest {
  // Optional. The maximum number of sample projects to return. The service may return
  // fewer than this value.
  // If unspecified, at most 1000 sample projects will be returned. Values above
  // 1000 will be coerced to 1000.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous 'ListSampleProjects' call.
  // Provide this to retrieve the subsequent page.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// RPC response for ListSampleProjects.
message ListSampleProjectsResponse {
  // The list of sample projects supported.
  repeated SampleProject sample_projects = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Definition of sample project resource.
message SampleProject {
  option (google.api.resource) = {
    type: "actions.googleapis.com/SampleProject"
    pattern: "sampleProjects/{sample_project}"
  };

  // The name of the sample project.
  // Format: `sampleProjects/{sample_project}`
  string name = 1;

  // The URL to the zip file where the sample is hosted.
  string hosted_url = 2;

  // The description of the sample project.
  string description = 3;
}

// RPC request for listing release channels
message ListReleaseChannelsRequest {
  // Required. The name of the resource in the format `projects/{project}`. The
  // `{project}` is the cloud project ID associated with the project.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "actions.googleapis.com/ReleaseChannel"
    }
  ];

  // The maximum number of release channels to return. The service may return
  // fewer than this value. If unspecified, at most 50 release channels will be
  // returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListReleaseChannels` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListReleaseChannels`
  // must match the call that provided the page token.
  string page_token = 3;
}

// RPC response for listing release channels
message ListReleaseChannelsResponse {
  // List of the release channels for the given project id.
  repeated ReleaseChannel release_channels = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// RPC request for listing versions
message ListVersionsRequest {
  // Required. The name of the resource in the format `projects/{project}`. The
  // `{project}` is the cloud project ID associated with the project.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "actions.googleapis.com/Version"
    }
  ];

  // The maximum number of versions to return. The service may return
  // fewer than this value. If unspecified, at most 50 versions will be
  // returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListVersions` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListVersions`
  // must match the call that provided the page token.
  string page_token = 3;
}

// RPC response for listing versions
message ListVersionsResponse {
  // List of the versions for the given project id.
  repeated Version versions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
