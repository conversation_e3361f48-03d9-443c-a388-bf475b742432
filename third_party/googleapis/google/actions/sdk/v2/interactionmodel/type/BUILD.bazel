# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "type_proto",
    srcs = [
        "class_reference.proto",
        "entity_display.proto",
        "free_text_type.proto",
        "regular_expression_type.proto",
        "synonym_type.proto",
        "type.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
