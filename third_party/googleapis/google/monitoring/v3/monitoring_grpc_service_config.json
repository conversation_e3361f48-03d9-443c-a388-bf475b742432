{"methodConfig": [{"name": [{"service": "google.monitoring.v3.AlertPolicyService", "method": "CreateAlertPolicy"}, {"service": "google.monitoring.v3.AlertPolicyService", "method": "UpdateAlertPolicy"}], "timeout": "30s"}, {"name": [{"service": "google.monitoring.v3.GroupService", "method": "ListGroups"}, {"service": "google.monitoring.v3.GroupService", "method": "GetGroup"}, {"service": "google.monitoring.v3.GroupService", "method": "DeleteGroup"}, {"service": "google.monitoring.v3.GroupService", "method": "ListGroupMembers"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.GroupService", "method": "UpdateGroup"}], "timeout": "180s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.GroupService", "method": "CreateGroup"}], "timeout": "30s"}, {"name": [{"service": "google.monitoring.v3.MetricService", "method": "CreateMetricDescriptor"}, {"service": "google.monitoring.v3.MetricService", "method": "CreateTimeSeries"}], "timeout": "12s"}, {"name": [{"service": "google.monitoring.v3.NotificationChannelService", "method": "ListNotificationChannelDescriptors"}, {"service": "google.monitoring.v3.NotificationChannelService", "method": "GetNotificationChannelDescriptor"}, {"service": "google.monitoring.v3.NotificationChannelService", "method": "ListNotificationChannels"}, {"service": "google.monitoring.v3.NotificationChannelService", "method": "GetNotificationChannel"}, {"service": "google.monitoring.v3.NotificationChannelService", "method": "DeleteNotificationChannel"}, {"service": "google.monitoring.v3.NotificationChannelService", "method": "GetNotificationChannelVerificationCode"}, {"service": "google.monitoring.v3.NotificationChannelService", "method": "VerifyNotificationChannel"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.ServiceMonitoringService", "method": "CreateService"}, {"service": "google.monitoring.v3.ServiceMonitoringService", "method": "UpdateService"}, {"service": "google.monitoring.v3.ServiceMonitoringService", "method": "CreateServiceLevelObjective"}, {"service": "google.monitoring.v3.ServiceMonitoringService", "method": "UpdateServiceLevelObjective"}], "timeout": "30s"}, {"name": [{"service": "google.monitoring.v3.ServiceMonitoringService", "method": "GetService"}, {"service": "google.monitoring.v3.ServiceMonitoringService", "method": "ListServices"}, {"service": "google.monitoring.v3.ServiceMonitoringService", "method": "DeleteService"}, {"service": "google.monitoring.v3.ServiceMonitoringService", "method": "GetServiceLevelObjective"}, {"service": "google.monitoring.v3.ServiceMonitoringService", "method": "ListServiceLevelObjectives"}, {"service": "google.monitoring.v3.ServiceMonitoringService", "method": "DeleteServiceLevelObjective"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.UptimeCheckService", "method": "ListUptimeCheckConfigs"}, {"service": "google.monitoring.v3.UptimeCheckService", "method": "GetUptimeCheckConfig"}, {"service": "google.monitoring.v3.UptimeCheckService", "method": "DeleteUptimeCheckConfig"}, {"service": "google.monitoring.v3.UptimeCheckService", "method": "ListUptimeCheckIps"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.AlertPolicyService", "method": "ListAlertPolicies"}, {"service": "google.monitoring.v3.AlertPolicyService", "method": "GetAlertPolicy"}, {"service": "google.monitoring.v3.AlertPolicyService", "method": "DeleteAlertPolicy"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.MetricService", "method": "ListMonitoredResourceDescriptors"}, {"service": "google.monitoring.v3.MetricService", "method": "GetMonitoredResourceDescriptor"}, {"service": "google.monitoring.v3.MetricService", "method": "ListMetricDescriptors"}, {"service": "google.monitoring.v3.MetricService", "method": "GetMetricDescriptor"}, {"service": "google.monitoring.v3.MetricService", "method": "DeleteMetricDescriptor"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.MetricService", "method": "ListTimeSeries"}], "timeout": "90s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.NotificationChannelService", "method": "CreateNotificationChannel"}, {"service": "google.monitoring.v3.NotificationChannelService", "method": "UpdateNotificationChannel"}, {"service": "google.monitoring.v3.NotificationChannelService", "method": "SendNotificationChannelVerificationCode"}], "timeout": "30s"}, {"name": [{"service": "google.monitoring.v3.UptimeCheckService", "method": "CreateUptimeCheckConfig"}, {"service": "google.monitoring.v3.UptimeCheckService", "method": "UpdateUptimeCheckConfig"}], "timeout": "30s"}, {"name": [{"service": "google.monitoring.v3.SnoozeService", "method": "GetSnooze"}, {"service": "google.monitoring.v3.SnoozeService", "method": "ListSnoozes"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.monitoring.v3.SnoozeService", "method": "CreateSnooze"}, {"service": "google.monitoring.v3.SnoozeService", "method": "UpdateSnooze"}], "timeout": "30s"}]}