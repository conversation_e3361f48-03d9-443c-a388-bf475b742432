# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "monitoring_proto",
    srcs = [
        "alert.proto",
        "alert_service.proto",
        "common.proto",
        "dropped_labels.proto",
        "group.proto",
        "group_service.proto",
        "metric.proto",
        "metric_service.proto",
        "mutation_record.proto",
        "notification.proto",
        "notification_service.proto",
        "query_service.proto",
        "service.proto",
        "service_service.proto",
        "snooze.proto",
        "snooze_service.proto",
        "span_context.proto",
        "uptime.proto",
        "uptime_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:distribution_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:label_proto",
        "//google/api:launch_stage_proto",
        "//google/api:metric_proto",
        "//google/api:monitored_resource_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:calendar_period_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "monitoring_proto_with_info",
    deps = [
        ":monitoring_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "monitoring_java_proto",
    deps = [":monitoring_proto"],
)

java_grpc_library(
    name = "monitoring_java_grpc",
    srcs = [":monitoring_proto"],
    deps = [":monitoring_java_proto"],
)

java_gapic_library(
    name = "monitoring_java_gapic",
    srcs = [":monitoring_proto_with_info"],
    gapic_yaml = "monitoring_gapic.yaml",
    grpc_service_config = "monitoring_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    test_deps = [
        ":monitoring_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":monitoring_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "monitoring_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.monitoring.v3.AlertPolicyServiceClientTest",
        "com.google.cloud.monitoring.v3.GroupServiceClientTest",
        "com.google.cloud.monitoring.v3.MetricServiceClientTest",
        "com.google.cloud.monitoring.v3.NotificationChannelServiceClientTest",
        "com.google.cloud.monitoring.v3.QueryServiceClientTest",
        "com.google.cloud.monitoring.v3.ServiceMonitoringServiceClientTest",
        "com.google.cloud.monitoring.v3.SnoozeServiceClientTest",
        "com.google.cloud.monitoring.v3.UptimeCheckServiceClientTest",
    ],
    runtime_deps = [":monitoring_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-monitoring-v3-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":monitoring_java_gapic",
        ":monitoring_java_grpc",
        ":monitoring_java_proto",
        ":monitoring_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "monitoring_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/monitoring/apiv3/v2/monitoringpb",
    protos = [":monitoring_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:api_go_proto",
        "//google/api:distribution_go_proto",
        "//google/api:label_go_proto",
        "//google/api:metric_go_proto",
        "//google/api:monitoredres_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:calendar_period_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "monitoring_go_gapic",
    srcs = [":monitoring_proto_with_info"],
    grpc_service_config = "monitoring_grpc_service_config.json",
    importpath = "cloud.google.com/go/monitoring/apiv3/v2;monitoring",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [
        ":monitoring_go_proto",
        "//google/api:metric_go_proto",
        "//google/api:monitoredres_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-monitoring-v3-go",
    deps = [
        ":monitoring_go_gapic",
        ":monitoring_go_gapic_srcjar-metadata.srcjar",
        ":monitoring_go_gapic_srcjar-snippets.srcjar",
        ":monitoring_go_gapic_srcjar-test.srcjar",
        ":monitoring_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "monitoring_py_gapic",
    srcs = [":monitoring_proto"],
    grpc_service_config = "monitoring_grpc_service_config.json",
    opt_args = ["python-gapic-namespace=google.cloud"],
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "monitoring_py_gapic_test",
    srcs = [
        "monitoring_py_gapic_pytest.py",
        "monitoring_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":monitoring_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "monitoring-v3-py",
    deps = [
        ":monitoring_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "monitoring_php_proto",
    deps = [":monitoring_proto"],
)

php_gapic_library(
    name = "monitoring_php_gapic",
    srcs = [":monitoring_proto_with_info"],
    grpc_service_config = "monitoring_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc+rest",
    deps = [
        ":monitoring_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-monitoring-v3-php",
    deps = [
        ":monitoring_php_gapic",
        ":monitoring_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "monitoring_nodejs_gapic",
    package_name = "@google-cloud/monitoring",
    src = ":monitoring_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "monitoring_grpc_service_config.json",
    main_service = "monitoring",
    package = "google.monitoring.v3",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "monitoring-v3-nodejs",
    deps = [
        ":monitoring_nodejs_gapic",
        ":monitoring_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "monitoring_ruby_proto",
    deps = [":monitoring_proto"],
)

ruby_grpc_library(
    name = "monitoring_ruby_grpc",
    srcs = [":monitoring_proto"],
    deps = [":monitoring_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "monitoring_ruby_gapic",
    srcs = [":monitoring_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=monitoring.googleapis.com",
        "ruby-cloud-api-shortname=monitoring",
        "ruby-cloud-env-prefix=MONITORING",
        "ruby-cloud-gem-name=google-cloud-monitoring-v3",
        "ruby-cloud-product-url=https://cloud.google.com/monitoring",
    ],
    grpc_service_config = "monitoring_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Monitoring collects metrics, events, and metadata from Google Cloud, Amazon Web Services (AWS), hosted uptime probes, and application instrumentation.",
    ruby_cloud_title = "Cloud Monitoring V3",
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [
        ":monitoring_ruby_grpc",
        ":monitoring_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-monitoring-v3-ruby",
    deps = [
        ":monitoring_ruby_gapic",
        ":monitoring_ruby_grpc",
        ":monitoring_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "monitoring_csharp_proto",
    extra_opts = [],
    deps = [":monitoring_proto"],
)

csharp_grpc_library(
    name = "monitoring_csharp_grpc",
    srcs = [":monitoring_proto"],
    deps = [":monitoring_csharp_proto"],
)

csharp_gapic_library(
    name = "monitoring_csharp_gapic",
    srcs = [":monitoring_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "monitoring_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "monitoring.yaml",
    transport = "grpc",
    deps = [
        ":monitoring_csharp_grpc",
        ":monitoring_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-monitoring-v3-csharp",
    deps = [
        ":monitoring_csharp_gapic",
        ":monitoring_csharp_grpc",
        ":monitoring_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "monitoring_cc_proto",
    deps = [":monitoring_proto"],
)

cc_grpc_library(
    name = "monitoring_cc_grpc",
    srcs = [":monitoring_proto"],
    grpc_only = True,
    deps = [":monitoring_cc_proto"],
)
