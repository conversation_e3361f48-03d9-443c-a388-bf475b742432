type: google.api.Service
config_version: 3
name: monitoring.googleapis.com
title: Cloud Monitoring API

apis:
- name: google.longrunning.Operations
- name: google.monitoring.v3.AlertPolicyService
- name: google.monitoring.v3.GroupService
- name: google.monitoring.v3.MetricService
- name: google.monitoring.v3.NotificationChannelService
- name: google.monitoring.v3.QueryService
- name: google.monitoring.v3.ServiceMonitoringService
- name: google.monitoring.v3.SnoozeService
- name: google.monitoring.v3.UptimeCheckService

types:
- name: google.monitoring.v3.DroppedLabels
- name: google.monitoring.v3.SpanContext

documentation:
  summary: Manages your Cloud Monitoring data and configurations.

authentication:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: 'google.monitoring.v3.AlertPolicyService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.AlertPolicyService.GetAlertPolicy
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.AlertPolicyService.ListAlertPolicies
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: 'google.monitoring.v3.GroupService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.GroupService.CreateGroup
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.GroupService.DeleteGroup
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.GroupService.UpdateGroup
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: 'google.monitoring.v3.MetricService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read,
        https://www.googleapis.com/auth/monitoring.write
  - selector: google.monitoring.v3.MetricService.CreateMetricDescriptor
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.write
  - selector: google.monitoring.v3.MetricService.CreateServiceTimeSeries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.write
  - selector: google.monitoring.v3.MetricService.CreateTimeSeries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.write
  - selector: google.monitoring.v3.MetricService.DeleteMetricDescriptor
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.MetricService.ListTimeSeries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: 'google.monitoring.v3.NotificationChannelService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.NotificationChannelService.GetNotificationChannel
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.NotificationChannelService.GetNotificationChannelDescriptor
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.NotificationChannelService.ListNotificationChannelDescriptors
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.NotificationChannelService.ListNotificationChannels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.QueryService.QueryTimeSeries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: 'google.monitoring.v3.ServiceMonitoringService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.ServiceMonitoringService.GetService
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.ServiceMonitoringService.GetServiceLevelObjective
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.ServiceMonitoringService.ListServiceLevelObjectives
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.ServiceMonitoringService.ListServices
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.SnoozeService.CreateSnooze
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.SnoozeService.GetSnooze
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.SnoozeService.ListSnoozes
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.SnoozeService.UpdateSnooze
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: 'google.monitoring.v3.UptimeCheckService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring,
        https://www.googleapis.com/auth/monitoring.read
  - selector: google.monitoring.v3.UptimeCheckService.CreateUptimeCheckConfig
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.UptimeCheckService.DeleteUptimeCheckConfig
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
  - selector: google.monitoring.v3.UptimeCheckService.UpdateUptimeCheckConfig
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/monitoring
