type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
# The settings of generated code in a specific language.
language_settings:
  java:
    package_name: com.google.cloud.monitoring.v3
    release_level: GA
  python:
    package_name: google.cloud.monitoring_v3.gapic
  go:
    package_name: cloud.google.com/go/monitoring/apiv3
  csharp:
    package_name: Google.Cloud.Monitoring.V3
    release_level: GA
  ruby:
    package_name: Google::Cloud::Monitoring::V3
    release_level: BETA
  php:
    package_name: Google\Cloud\Monitoring\V3
  nodejs:
    package_name: monitoring.v3
    domain_layer_location: google-cloud
# A list of API interface configurations.
interfaces:
- name: google.monitoring.v3.MetricService
  smoke_test:
    method: ListMonitoredResourceDescriptors
    init_fields:
    - name%project=$PROJECT_ID
