# This build file includes a target for the Ruby wrapper library for
# google-cloud-monitoring.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for monitoring.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v3 in this case.
ruby_cloud_gapic_library(
    name = "monitoring_ruby_wrapper",
    srcs = ["//google/monitoring/v3:monitoring_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-monitoring",
        "ruby-cloud-env-prefix=MONITORING",
        "ruby-cloud-wrapper-of=v3:0.15",
        "ruby-cloud-product-url=https://cloud.google.com/monitoring",
        "ruby-cloud-api-id=monitoring.googleapis.com",
        "ruby-cloud-api-shortname=monitoring",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Cloud Monitoring collects metrics, events, and metadata from Google Cloud, Amazon Web Services (AWS), hosted uptime probes, and application instrumentation.",
    ruby_cloud_title = "Cloud Monitoring",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-monitoring-ruby",
    deps = [
        ":monitoring_ruby_wrapper",
    ],
)
