// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "DashboardFilterProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A filter to reduce the amount of data charted in relevant widgets.
message DashboardFilter {
  // The type for the dashboard filter
  enum FilterType {
    // Filter type is unspecified. This is not valid in a well-formed request.
    FILTER_TYPE_UNSPECIFIED = 0;

    // Filter on a resource label value
    RESOURCE_LABEL = 1;

    // Filter on a metrics label value
    METRIC_LABEL = 2;

    // Filter on a user metadata label value
    USER_METADATA_LABEL = 3;

    // Filter on a system metadata label value
    SYSTEM_METADATA_LABEL = 4;

    // Filter on a group id
    GROUP = 5;
  }

  // Required. The key for the label
  string label_key = 1 [(google.api.field_behavior) = REQUIRED];

  // The placeholder text that can be referenced in a filter string or MQL
  // query. If omitted, the dashboard filter will be applied to all relevant
  // widgets in the dashboard.
  string template_variable = 3;

  // The default value used in the filter comparison
  oneof default_value {
    // A variable-length string value.
    string string_value = 4;
  }

  // The specified filter type
  FilterType filter_type = 5;
}
