// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/monitoring/dashboard/v1/metrics.proto";
import "google/monitoring/dashboard/v1/table_display_options.proto";
import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "TableProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A table that displays time series data.
message TimeSeriesTable {
  // Groups a time series query definition with table options.
  message TableDataSet {
    // Required. Fields for querying time series data from the
    // Stackdriver metrics API.
    TimeSeriesQuery time_series_query = 1
        [(google.api.field_behavior) = REQUIRED];

    // Optional. A template string for naming `TimeSeries` in the resulting data
    // set. This should be a string with interpolations of the form
    // `${label_name}`, which will resolve to the label's value i.e.
    // "${resource.labels.project_id}."
    string table_template = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The lower bound on data point frequency for this data set,
    // implemented by specifying the minimum alignment period to use in a time
    // series query For example, if the data is published once every 10 minutes,
    // the `min_alignment_period` should be at least 10 minutes. It would not
    // make sense to fetch and align data at one minute intervals.
    google.protobuf.Duration min_alignment_period = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Table display options for configuring how the table is
    // rendered.
    TableDisplayOptions table_display_options = 4
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Enum for metric metric_visualization
  enum MetricVisualization {
    // Unspecified state
    METRIC_VISUALIZATION_UNSPECIFIED = 0;

    // Default text rendering
    NUMBER = 1;

    // Horizontal bar rendering
    BAR = 2;
  }

  // The persistent settings for a table's columns.
  message ColumnSettings {
    // Required. The id of the column.
    string column = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Whether the column should be visible on page load.
    bool visible = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. The data displayed in this table.
  repeated TableDataSet data_sets = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Store rendering strategy
  MetricVisualization metric_visualization = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The list of the persistent column settings for the table.
  repeated ColumnSettings column_settings = 4
      [(google.api.field_behavior) = OPTIONAL];
}
