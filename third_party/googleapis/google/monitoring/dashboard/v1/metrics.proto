// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/monitoring/dashboard/v1/common.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "MetricsProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// TimeSeriesQuery collects the set of supported methods for querying time
// series data from the Stackdriver metrics API.
message TimeSeriesQuery {
  // Parameters needed to obtain data for the chart.
  oneof source {
    // Filter parameters to fetch time series.
    TimeSeriesFilter time_series_filter = 1;

    // Parameters to fetch a ratio between two time series filters.
    TimeSeriesFilterRatio time_series_filter_ratio = 2;

    // A query used to fetch time series with MQL.
    string time_series_query_language = 3;

    // A query used to fetch time series with PromQL.
    string prometheus_query = 6;
  }

  // The unit of data contained in fetched time series. If non-empty, this
  // unit will override any unit that accompanies fetched data. The format is
  // the same as the
  // [`unit`](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.metricDescriptors)
  // field in `MetricDescriptor`.
  string unit_override = 5;

  // Optional. If set, Cloud Monitoring will treat the full query duration as
  // the alignment period so that there will be only 1 output value.
  //
  // *Note: This could override the configured alignment period except for
  // the cases where a series of data points are expected, like
  //   - XyChart
  //   - Scorecard's spark chart
  bool output_full_duration = 7 [(google.api.field_behavior) = OPTIONAL];
}

// A filter that defines a subset of time series data that is displayed in a
// widget. Time series data is fetched using the
// [`ListTimeSeries`](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/list)
// method.
message TimeSeriesFilter {
  // Required. The [monitoring
  // filter](https://cloud.google.com/monitoring/api/v3/filters) that identifies
  // the metric types, resources, and projects to query.
  string filter = 1 [(google.api.field_behavior) = REQUIRED];

  // By default, the raw time series data is returned.
  // Use this field to combine multiple time series for different views of the
  // data.
  Aggregation aggregation = 2;

  // Apply a second aggregation after `aggregation` is applied.
  Aggregation secondary_aggregation = 3;

  // Selects an optional time series filter.
  oneof output_filter {
    // Ranking based time series filter.
    PickTimeSeriesFilter pick_time_series_filter = 4;

    // Statistics based time series filter.
    // Note: This field is deprecated and completely ignored by the API.
    StatisticalTimeSeriesFilter statistical_time_series_filter = 5
        [deprecated = true];
  }
}

// A pair of time series filters that define a ratio computation. The output
// time series is the pair-wise division of each aligned element from the
// numerator and denominator time series.
message TimeSeriesFilterRatio {
  // Describes a query to build the numerator or denominator of a
  // TimeSeriesFilterRatio.
  message RatioPart {
    // Required. The [monitoring
    // filter](https://cloud.google.com/monitoring/api/v3/filters) that
    // identifies the metric types, resources, and projects to query.
    string filter = 1 [(google.api.field_behavior) = REQUIRED];

    // By default, the raw time series data is returned.
    // Use this field to combine multiple time series for different views of the
    // data.
    Aggregation aggregation = 2;
  }

  // The numerator of the ratio.
  RatioPart numerator = 1;

  // The denominator of the ratio.
  RatioPart denominator = 2;

  // Apply a second aggregation after the ratio is computed.
  Aggregation secondary_aggregation = 3;

  // Selects an optional filter that is applied to the time series after
  // computing the ratio.
  oneof output_filter {
    // Ranking based time series filter.
    PickTimeSeriesFilter pick_time_series_filter = 4;

    // Statistics based time series filter.
    // Note: This field is deprecated and completely ignored by the API.
    StatisticalTimeSeriesFilter statistical_time_series_filter = 5
        [deprecated = true];
  }
}

// Defines a threshold for categorizing time series values.
message Threshold {
  // The color suggests an interpretation to the viewer when actual values cross
  // the threshold. Comments on each color provide UX guidance on how users can
  // be expected to interpret a given state color.
  enum Color {
    // Color is unspecified. Not allowed in well-formed requests.
    COLOR_UNSPECIFIED = 0;

    // Crossing the threshold is "concerning" behavior.
    YELLOW = 4;

    // Crossing the threshold is "emergency" behavior.
    RED = 6;
  }

  // Whether the threshold is considered crossed by an actual value above or
  // below its threshold value.
  enum Direction {
    // Not allowed in well-formed requests.
    DIRECTION_UNSPECIFIED = 0;

    // The threshold will be considered crossed if the actual value is above
    // the threshold value.
    ABOVE = 1;

    // The threshold will be considered crossed if the actual value is below
    // the threshold value.
    BELOW = 2;
  }

  // An axis identifier.
  enum TargetAxis {
    // The target axis was not specified. Defaults to Y1.
    TARGET_AXIS_UNSPECIFIED = 0;

    // The y_axis (the right axis of chart).
    Y1 = 1;

    // The y2_axis (the left axis of chart).
    Y2 = 2;
  }

  // A label for the threshold.
  string label = 1;

  // The value of the threshold. The value should be defined in the native scale
  // of the metric.
  double value = 2;

  // The state color for this threshold. Color is not allowed in a XyChart.
  Color color = 3;

  // The direction for the current threshold. Direction is not allowed in a
  // XyChart.
  Direction direction = 4;

  // The target axis to use for plotting the threshold. Target axis is not
  // allowed in a Scorecard.
  TargetAxis target_axis = 5;
}

// Defines the possible types of spark chart supported by the `Scorecard`.
enum SparkChartType {
  // Not allowed in well-formed requests.
  SPARK_CHART_TYPE_UNSPECIFIED = 0;

  // The sparkline will be rendered as a small line chart.
  SPARK_LINE = 1;

  // The sparkbar will be rendered as a small bar chart.
  SPARK_BAR = 2;
}
