// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "LogsPanelProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A widget that displays a stream of log.
message LogsPanel {
  // A filter that chooses which log entries to return.  See [Advanced Logs
  // Queries](https://cloud.google.com/logging/docs/view/advanced-queries).
  // Only log entries that match the filter are returned.  An empty filter
  // matches all log entries.
  string filter = 1;

  // The names of logging resources to collect logs for. Currently only projects
  // are supported. If empty, the widget will default to the host project.
  repeated string resource_names = 2;
}
