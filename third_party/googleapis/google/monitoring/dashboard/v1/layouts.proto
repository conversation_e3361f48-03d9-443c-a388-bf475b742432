// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/monitoring/dashboard/v1/widget.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "LayoutsProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A basic layout divides the available space into vertical columns of equal
// width and arranges a list of widgets using a row-first strategy.
message GridLayout {
  // The number of columns into which the view's width is divided. If omitted
  // or set to zero, a system default will be used while rendering.
  int64 columns = 1;

  // The informational elements that are arranged into the columns row-first.
  repeated Widget widgets = 2;
}

// A mosaic layout divides the available space into a grid of blocks, and
// overlays the grid with tiles. Unlike `GridLayout`, tiles may span multiple
// grid blocks and can be placed at arbitrary locations in the grid.
message MosaicLayout {
  // A single tile in the mosaic. The placement and size of the tile are
  // configurable.
  message Tile {
    // The zero-indexed position of the tile in grid blocks relative to the
    // left edge of the grid. Tiles must be contained within the specified
    // number of columns. `x_pos` cannot be negative.
    int32 x_pos = 1;

    // The zero-indexed position of the tile in grid blocks relative to the
    // top edge of the grid. `y_pos` cannot be negative.
    int32 y_pos = 2;

    // The width of the tile, measured in grid blocks. Tiles must have a
    // minimum width of 1.
    int32 width = 3;

    // The height of the tile, measured in grid blocks. Tiles must have a
    // minimum height of 1.
    int32 height = 4;

    // The informational widget contained in the tile. For example an `XyChart`.
    Widget widget = 5;
  }

  // The number of columns in the mosaic grid. The number of columns must be
  // between 1 and 12, inclusive.
  int32 columns = 1;

  // The tiles to display.
  repeated Tile tiles = 3;
}

// A simplified layout that divides the available space into rows
// and arranges a set of widgets horizontally in each row.
message RowLayout {
  // Defines the layout properties and content for a row.
  message Row {
    // The relative weight of this row. The row weight is used to adjust the
    // height of rows on the screen (relative to peers). Greater the weight,
    // greater the height of the row on the screen. If omitted, a value
    // of 1 is used while rendering.
    int64 weight = 1;

    // The display widgets arranged horizontally in this row.
    repeated Widget widgets = 2;
  }

  // The rows of content to display.
  repeated Row rows = 1;
}

// A simplified layout that divides the available space into vertical columns
// and arranges a set of widgets vertically in each column.
message ColumnLayout {
  // Defines the layout properties and content for a column.
  message Column {
    // The relative weight of this column. The column weight is used to adjust
    // the width of columns on the screen (relative to peers).
    // Greater the weight, greater the width of the column on the screen.
    // If omitted, a value of 1 is used while rendering.
    int64 weight = 1;

    // The display widgets arranged vertically in this column.
    repeated Widget widgets = 2;
  }

  // The columns of content to display.
  repeated Column columns = 1;
}
