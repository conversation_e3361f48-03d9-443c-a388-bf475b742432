// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/field_behavior.proto";
import "google/monitoring/dashboard/v1/metrics.proto";
import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "XyChartProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// A chart that displays data on a 2D (X and Y axes) plane.
message XyChart {
  // Groups a time series query definition with charting options.
  message DataSet {
    // The types of plotting strategies for data sets.
    enum PlotType {
      // Plot type is unspecified. The view will default to `LINE`.
      PLOT_TYPE_UNSPECIFIED = 0;

      // The data is plotted as a set of lines (one line per series).
      LINE = 1;

      // The data is plotted as a set of filled areas (one area per series),
      // with the areas stacked vertically (the base of each area is the top of
      // its predecessor, and the base of the first area is the x-axis). Since
      // the areas do not overlap, each is filled with a different opaque color.
      STACKED_AREA = 2;

      // The data is plotted as a set of rectangular boxes (one box per series),
      // with the boxes stacked vertically (the base of each box is the top of
      // its predecessor, and the base of the first box is the x-axis). Since
      // the boxes do not overlap, each is filled with a different opaque color.
      STACKED_BAR = 3;

      // The data is plotted as a heatmap. The series being plotted must have a
      // `DISTRIBUTION` value type. The value of each bucket in the distribution
      // is displayed as a color. This type is not currently available in the
      // Stackdriver Monitoring application.
      HEATMAP = 4;
    }

    // An axis identifier.
    enum TargetAxis {
      // The target axis was not specified. Defaults to Y1.
      TARGET_AXIS_UNSPECIFIED = 0;

      // The y_axis (the right axis of chart).
      Y1 = 1;

      // The y2_axis (the left axis of chart).
      Y2 = 2;
    }

    // Required. Fields for querying time series data from the
    // Stackdriver metrics API.
    TimeSeriesQuery time_series_query = 1
        [(google.api.field_behavior) = REQUIRED];

    // How this data should be plotted on the chart.
    PlotType plot_type = 2;

    // A template string for naming `TimeSeries` in the resulting data set.
    // This should be a string with interpolations of the form `${label_name}`,
    // which will resolve to the label's value.
    string legend_template = 3;

    // Optional. The lower bound on data point frequency for this data set,
    // implemented by specifying the minimum alignment period to use in a time
    // series query For example, if the data is published once every 10 minutes,
    // the `min_alignment_period` should be at least 10 minutes. It would not
    // make sense to fetch and align data at one minute intervals.
    google.protobuf.Duration min_alignment_period = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The target axis to use for plotting the metric.
    TargetAxis target_axis = 5 [(google.api.field_behavior) = OPTIONAL];
  }

  // A chart axis.
  message Axis {
    // Types of scales used in axes.
    enum Scale {
      // Scale is unspecified. The view will default to `LINEAR`.
      SCALE_UNSPECIFIED = 0;

      // Linear scale.
      LINEAR = 1;

      // Logarithmic scale (base 10).
      LOG10 = 2;
    }

    // The label of the axis.
    string label = 1;

    // The axis scale. By default, a linear scale is used.
    Scale scale = 2;
  }

  // Required. The data displayed in this chart.
  repeated DataSet data_sets = 1 [(google.api.field_behavior) = REQUIRED];

  // The duration used to display a comparison chart. A comparison chart
  // simultaneously shows values from two similar-length time periods
  // (e.g., week-over-week metrics).
  // The duration must be positive, and it can only be applied to charts with
  // data sets of LINE plot type.
  google.protobuf.Duration timeshift_duration = 4;

  // Threshold lines drawn horizontally across the chart.
  repeated Threshold thresholds = 5;

  // The properties applied to the x-axis.
  Axis x_axis = 6;

  // The properties applied to the y-axis.
  Axis y_axis = 7;

  // The properties applied to the y2-axis.
  Axis y2_axis = 9;

  // Display options for the chart.
  ChartOptions chart_options = 8;
}

// Options to control visual rendering of a chart.
message ChartOptions {
  // Chart mode options.
  enum Mode {
    // Mode is unspecified. The view will default to `COLOR`.
    MODE_UNSPECIFIED = 0;

    // The chart distinguishes data series using different color. Line
    // colors may get reused when there are many lines in the chart.
    COLOR = 1;

    // The chart uses the Stackdriver x-ray mode, in which each
    // data set is plotted using the same semi-transparent color.
    X_RAY = 2;

    // The chart displays statistics such as average, median, 95th percentile,
    // and more.
    STATS = 3;
  }

  // The chart mode.
  Mode mode = 1;
}
