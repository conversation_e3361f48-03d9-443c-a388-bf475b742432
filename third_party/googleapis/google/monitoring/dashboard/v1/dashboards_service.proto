// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.dashboard.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/monitoring/dashboard/v1/dashboard.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.Monitoring.Dashboard.V1";
option go_package = "cloud.google.com/go/monitoring/dashboard/apiv1/dashboardpb;dashboardpb";
option java_multiple_files = true;
option java_outer_classname = "DashboardsServiceProto";
option java_package = "com.google.monitoring.dashboard.v1";
option php_namespace = "Google\\Cloud\\Monitoring\\Dashboard\\V1";
option ruby_package = "Google::Cloud::Monitoring::Dashboard::V1";

// Manages Stackdriver dashboards. A dashboard is an arrangement of data display
// widgets in a specific layout.
service DashboardsService {
  option (google.api.default_host) = "monitoring.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/monitoring,"
      "https://www.googleapis.com/auth/monitoring.read,"
      "https://www.googleapis.com/auth/monitoring.write";

  // Creates a new custom dashboard. For examples on how you can use this API to
  // create dashboards, see [Managing dashboards by
  // API](https://cloud.google.com/monitoring/dashboards/api-dashboard). This
  // method requires the `monitoring.dashboards.create` permission on the
  // specified project. For more information about permissions, see [Cloud
  // Identity and Access Management](https://cloud.google.com/iam).
  rpc CreateDashboard(CreateDashboardRequest) returns (Dashboard) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/dashboards"
      body: "dashboard"
    };
    option (google.api.method_signature) = "parent,dashboard";
  }

  // Lists the existing dashboards.
  //
  // This method requires the `monitoring.dashboards.list` permission
  // on the specified project. For more information, see
  // [Cloud Identity and Access Management](https://cloud.google.com/iam).
  rpc ListDashboards(ListDashboardsRequest) returns (ListDashboardsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/dashboards"
    };
    option (google.api.method_signature) = "parent";
  }

  // Fetches a specific dashboard.
  //
  // This method requires the `monitoring.dashboards.get` permission
  // on the specified dashboard. For more information, see
  // [Cloud Identity and Access Management](https://cloud.google.com/iam).
  rpc GetDashboard(GetDashboardRequest) returns (Dashboard) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/dashboards/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes an existing custom dashboard.
  //
  // This method requires the `monitoring.dashboards.delete` permission
  // on the specified dashboard. For more information, see
  // [Cloud Identity and Access Management](https://cloud.google.com/iam).
  rpc DeleteDashboard(DeleteDashboardRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/dashboards/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Replaces an existing custom dashboard with a new definition.
  //
  // This method requires the `monitoring.dashboards.update` permission
  // on the specified dashboard. For more information, see
  // [Cloud Identity and Access Management](https://cloud.google.com/iam).
  rpc UpdateDashboard(UpdateDashboardRequest) returns (Dashboard) {
    option (google.api.http) = {
      patch: "/v1/{dashboard.name=projects/*/dashboards/*}"
      body: "dashboard"
    };
  }
}

// The `CreateDashboard` request.
message CreateDashboardRequest {
  // Required. The project on which to execute the request. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]
  //
  // The `[PROJECT_ID_OR_NUMBER]` must match the dashboard resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The initial dashboard specification.
  Dashboard dashboard = 2 [(google.api.field_behavior) = REQUIRED];

  // If set, validate the request and preview the review, but do not actually
  // save it.
  bool validate_only = 3;
}

// The `ListDashboards` request.
message ListDashboardsRequest {
  // Required. The scope of the dashboards to list. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // A positive number that is the maximum number of results to return.
  // If unspecified, a default of 1000 is used.
  int32 page_size = 2;

  // Optional. If this field is not empty then it must contain the
  // `nextPageToken` value returned by a previous call to this method.  Using
  // this field causes the method to return additional results from the previous
  // method call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The `ListDashboards` request.
message ListDashboardsResponse {
  // The list of requested dashboards.
  repeated Dashboard dashboards = 1;

  // If there are more results than have been returned, then this field is set
  // to a non-empty value.  To see the additional results,
  // use that value as `page_token` in the next call to this method.
  string next_page_token = 2;
}

// The `GetDashboard` request.
message GetDashboardRequest {
  // Required. The resource name of the Dashboard. The format is one of:
  //
  //  -  `dashboards/[DASHBOARD_ID]` (for system dashboards)
  //  -  `projects/[PROJECT_ID_OR_NUMBER]/dashboards/[DASHBOARD_ID]`
  //       (for custom dashboards).
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "monitoring.googleapis.com/Dashboard"
    }
  ];
}

// The `DeleteDashboard` request.
message DeleteDashboardRequest {
  // Required. The resource name of the Dashboard. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]/dashboards/[DASHBOARD_ID]
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "monitoring.googleapis.com/Dashboard"
    }
  ];
}

// The `UpdateDashboard` request.
message UpdateDashboardRequest {
  // Required. The dashboard that will replace the existing dashboard.
  Dashboard dashboard = 1 [(google.api.field_behavior) = REQUIRED];

  // If set, validate the request and preview the review, but do not actually
  // save it.
  bool validate_only = 3;
}
