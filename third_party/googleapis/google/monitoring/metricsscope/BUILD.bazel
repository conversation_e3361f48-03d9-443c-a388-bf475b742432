# This build file includes a target for the Ruby wrapper library for
# google-cloud-monitoring-metrics_scope.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for monitoring.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "metricsscope_ruby_wrapper",
    srcs = ["//google/monitoring/metricsscope/v1:metricsscope_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-monitoring-metrics_scope",
        "ruby-cloud-env-prefix=MONITORING",
        "ruby-cloud-wrapper-of=v1:0.5",
        "ruby-cloud-product-url=https://cloud.google.com/monitoring",
        "ruby-cloud-api-id=monitoring.googleapis.com",
        "ruby-cloud-api-shortname=monitoring",
    ],
    ruby_cloud_description = "Cloud Monitoring collects metrics, events, and metadata from Google Cloud, Amazon Web Services (AWS), hosted uptime probes, and application instrumentation.",
    ruby_cloud_title = "Cloud Monitoring Metrics Scopes",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-monitoring-metricsscope-ruby",
    deps = [
        ":metricsscope_ruby_wrapper",
    ],
)
