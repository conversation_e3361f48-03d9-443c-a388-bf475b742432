type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
interfaces:
- name: google.monitoring.metricsscope.v1.MetricsScopes
  methods:
  - name: CreateMonitoredProject
    long_running:
      initial_poll_delay_millis: 1000
      poll_delay_multiplier: 2
      max_poll_delay_millis: 10000
      total_poll_timeout_millis: 900000
  - name: DeleteMonitoredProject
    long_running:
      initial_poll_delay_millis: 1000
      poll_delay_multiplier: 2
      max_poll_delay_millis: 10000
      total_poll_timeout_millis: 900000
