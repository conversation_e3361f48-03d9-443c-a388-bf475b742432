// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.metricsscope.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/monitoring/metricsscope/apiv1/metricsscopepb;metricsscopepb";
option java_multiple_files = true;
option java_outer_classname = "MetricsScopeProto";
option java_package = "com.google.monitoring.metricsscope.v1";
option csharp_namespace = "Google.Cloud.Monitoring.MetricsScope.V1";
option php_namespace = "Google\\Cloud\\Monitoring\\MetricsScope\\V1";
option ruby_package = "Google::Cloud::Monitoring::MetricsScope::V1";

// Represents a [Metrics
// Scope](https://cloud.google.com/monitoring/settings#concept-scope) in Cloud
// Monitoring, which specifies one or more Google projects and zero or more AWS
// accounts to monitor together.
message MetricsScope {
  option (google.api.resource) = {
    type: "monitoring.googleapis.com/MetricsScope"
    pattern: "locations/global/metricsScope/{metrics_scope}"
  };

  // Immutable. The resource name of the Monitoring Metrics Scope.
  // On input, the resource name can be specified with the
  // scoping project ID or number. On output, the resource name is
  // specified with the scoping project number.
  // Example:
  // `locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The time when this `Metrics Scope` was created.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when this `Metrics Scope` record was last updated.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of projects monitored by this `Metrics Scope`.
  repeated MonitoredProject monitored_projects = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A [project being
// monitored](https://cloud.google.com/monitoring/settings/multiple-projects#create-multi)
// by a `Metrics Scope`.
message MonitoredProject {
  option (google.api.resource) = {
    type: "monitoring.googleapis.com/MonitoredProject"
    pattern: "locations/global/metricsScopes/{metrics_scope}/projects/{project}"
  };

  // Immutable. The resource name of the `MonitoredProject`. On input, the resource name
  // includes the scoping project ID and monitored project ID. On output, it
  // contains the equivalent project numbers.
  // Example:
  // `locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}/projects/{MONITORED_PROJECT_ID_OR_NUMBER}`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The time when this `MonitoredProject` was created.
  google.protobuf.Timestamp create_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}
