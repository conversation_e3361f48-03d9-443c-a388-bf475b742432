// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.marketingplatform.admin.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.MarketingPlatform.Admin.V1Alpha";
option go_package = "google.golang.org/genproto/googleapis/marketingplatform/admin/v1alpha;admin";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.ads.marketingplatform.admin.v1alpha";
option php_namespace = "Google\\Ads\\MarketingPlatform\\Admin\\V1alpha";
option ruby_package = "Google::Ads::MarketingPlatform::Admin::V1alpha";
option (google.api.resource_definition) = {
  type: "analyticsadmin.googleapis.com/Account"
  pattern: "accounts/{account}"
};

// The verification state of the link between a product account and a GMP
// organization.
enum LinkVerificationState {
  // The link state is unknown.
  LINK_VERIFICATION_STATE_UNSPECIFIED = 0;

  // The link is established.
  LINK_VERIFICATION_STATE_VERIFIED = 1;

  // The link is requested, but hasn't been approved by the product account
  // admin.
  LINK_VERIFICATION_STATE_NOT_VERIFIED = 2;
}

// A resource message representing a Google Marketing Platform organization.
message Organization {
  option (google.api.resource) = {
    type: "marketingplatformadmin.googleapis.com/Organization"
    pattern: "organizations/{organization}"
    plural: "organizations"
    singular: "organization"
  };

  // Identifier. The resource name of the GMP organization.
  // Format: organizations/{org_id}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The human-readable name for the organization.
  string display_name = 2;
}

// A resource message representing the link between a Google Analytics account
// and a Google Marketing Platform organization.
message AnalyticsAccountLink {
  option (google.api.resource) = {
    type: "marketingplatformadmin.googleapis.com/AnalyticsAccountLink"
    pattern: "organizations/{organization}/analyticsAccountLinks/{analytics_account_link}"
    plural: "analyticsAccountLinks"
    singular: "analyticsAccountLink"
  };

  // Identifier. Resource name of this AnalyticsAccountLink. Note the resource
  // ID is the same as the ID of the Analtyics account.
  //
  // Format:
  // organizations/{org_id}/analyticsAccountLinks/{analytics_account_link_id}
  // Example: "organizations/xyz/analyticsAccountLinks/1234"
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Immutable. The resource name of the AnalyticsAdmin API account.
  // The account ID will be used as the ID of this AnalyticsAccountLink
  // resource, which will become the final component of the resource name.
  //
  // Format: analyticsadmin.googleapis.com/accounts/{account_id}
  string analytics_account = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Account"
    }
  ];

  // Output only. The human-readable name for the Analytics account.
  string display_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The verification state of the link between the Analytics
  // account and the parent organization.
  LinkVerificationState link_verification_state = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
