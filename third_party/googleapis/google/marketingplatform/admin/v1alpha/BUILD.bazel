# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "admin_proto",
    srcs = [
        "marketingplatform_admin.proto",
        "resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
    ],
)

proto_library_with_info(
    name = "admin_proto_with_info",
    deps = [
        ":admin_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "admin_java_proto",
    deps = [":admin_proto"],
)

java_grpc_library(
    name = "admin_java_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_java_proto"],
)

java_gapic_library(
    name = "admin_java_gapic",
    srcs = [":admin_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "marketingplatform_admin_v1alpha_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "marketingplatformadmin_v1alpha.yaml",
    test_deps = [
        ":admin_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":admin_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "admin_java_gapic_test_suite",
    test_classes = [
        "com.google.ads.marketingplatform.admin.v1alpha.MarketingplatformAdminServiceClientHttpJsonTest",
        "com.google.ads.marketingplatform.admin.v1alpha.MarketingplatformAdminServiceClientTest",
    ],
    runtime_deps = [":admin_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-marketingplatform-admin-v1alpha-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":admin_java_gapic",
        ":admin_java_grpc",
        ":admin_java_proto",
        ":admin_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "admin_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/marketingplatform/admin/v1alpha",
    protos = [":admin_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "admin_go_gapic",
    srcs = [":admin_proto_with_info"],
    grpc_service_config = "marketingplatform_admin_v1alpha_grpc_service_config.json",
    importpath = "google.golang.org/google/marketingplatform/admin/v1alpha;admin",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "marketingplatformadmin_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":admin_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-marketingplatform-admin-v1alpha-go",
    deps = [
        ":admin_go_gapic",
        ":admin_go_gapic_srcjar-metadata.srcjar",
        ":admin_go_gapic_srcjar-snippets.srcjar",
        ":admin_go_gapic_srcjar-test.srcjar",
        ":admin_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "admin_py_gapic",
    srcs = [":admin_proto"],
    grpc_service_config = "marketingplatform_admin_v1alpha_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.ads",
        "python-gapic-name=marketingplatform_admin",
        "warehouse-package-name=google-ads-marketingplatform-admin",
    ],
    rest_numeric_enums = True,
    service_yaml = "marketingplatformadmin_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "admin_py_gapic_test",
    srcs = [
        "admin_py_gapic_pytest.py",
        "admin_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":admin_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "marketingplatform-admin-v1alpha-py",
    deps = [
        ":admin_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "admin_php_proto",
    deps = [":admin_proto"],
)

php_gapic_library(
    name = "admin_php_gapic",
    srcs = [":admin_proto_with_info"],
    grpc_service_config = "marketingplatform_admin_v1alpha_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "marketingplatformadmin_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":admin_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-marketingplatform-admin-v1alpha-php",
    deps = [
        ":admin_php_gapic",
        ":admin_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "admin_nodejs_gapic",
    package_name = "@google-ads/marketing-platform-admin",
    src = ":admin_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "marketingplatform_admin_v1alpha_grpc_service_config.json",
    package = "google.marketingplatform.admin.v1alpha",
    rest_numeric_enums = True,
    service_yaml = "marketingplatformadmin_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "marketingplatform-admin-v1alpha-nodejs",
    deps = [
        ":admin_nodejs_gapic",
        ":admin_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "admin_ruby_proto",
    deps = [":admin_proto"],
)

ruby_grpc_library(
    name = "admin_ruby_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "admin_ruby_gapic",
    srcs = [":admin_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-marketingplatform-admin-v1alpha"],
    grpc_service_config = "marketingplatform_admin_v1alpha_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "marketingplatformadmin_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":admin_ruby_grpc",
        ":admin_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-marketingplatform-admin-v1alpha-ruby",
    deps = [
        ":admin_ruby_gapic",
        ":admin_ruby_grpc",
        ":admin_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "admin_csharp_proto",
    extra_opts = [],
    deps = [":admin_proto"],
)

csharp_grpc_library(
    name = "admin_csharp_grpc",
    srcs = [":admin_proto"],
    deps = [":admin_csharp_proto"],
)

csharp_gapic_library(
    name = "admin_csharp_gapic",
    srcs = [":admin_proto_with_info"],
    grpc_service_config = "marketingplatform_admin_v1alpha_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "marketingplatformadmin_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":admin_csharp_grpc",
        ":admin_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-marketingplatform-admin-v1alpha-csharp",
    deps = [
        ":admin_csharp_gapic",
        ":admin_csharp_grpc",
        ":admin_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "admin_cc_proto",
    deps = [":admin_proto"],
)

cc_grpc_library(
    name = "admin_cc_grpc",
    srcs = [":admin_proto"],
    grpc_only = True,
    deps = [":admin_cc_proto"],
)
