#注意，ATLS_*的变量atlantis构建过程中专用的变量，修改这些变量务必确认是否正确，以及新增自定义变量避开ATLS_前缀
#运行前需要引入通用ci文件，请勿修改此部分
include:
  - remote: 'https://git.domob-inc.cn/bluefocus/atlantis/-/raw/master/.gitlab/ci_template/golang_ci.yml'
stages:
  - build
  - notify
variables:
  ATLS_NS: ""

  #GOROOT版本配置
  ATLS_GOROOT: "/usr/local/go-1.20/"

  #---------------------------------------------docker 构建相关----------------------------------
  #运行基础镜像，默认centos:7
  #ATLS_RUNNING_BASE_IMAGE: "alpine:3.17" alpine需要配合编译时禁用C链接库使用，体积会比centOS小(压缩后参考值: centos 80M, alpine 16M)
  #ATLS_RUNNING_BASE_IMAGE: "centos:7"
  #Dockerfile的位置,默认为根目录下(./Dockerfile)通过ci从atlantis拉取，如果修改此值，则认为使用自定义的dockerfile而不是atlantis构建模板中的Dockerfile
  #ATLS_DOCKER_FILE_PATH: "Dockerfile"



#编译生产环境，默认跟踪master分支的提交，如有特殊需求，可指定分支
compile_prod:
  extends: .compile_prod
  #如果是master分支对应生产环境，则默认无需做以下配置。如果有定制化分支，请反注释以下代码，并添加分支名称
  #only:
  #  - master

#编译预发布环境，默认跟踪develop分支的提交，如有特殊需求，可指定分支。
compile_pre:
  extends: .compile_pre
  #如果是develop分支对应预发布环境，则默认无需做以下配置。如果有定制化分支，请反注释以下代码，并添加分支名称
  #only:
  #  - develop

#新增tag时，自动触发编译。默认跟踪打tag事件
compile_tag:
  extends: .compile_tag

#跟踪feature分支代码的提交，自动编译。feature分支定义：除了master,develop,dev-offline和打tag外，其余分支提交都算feature分支。
compile_test:
  extends: .compile_test
  #默认非以下分支的提交，都属于feature分支
  #except:
  #  - tags
  #  - master
  #  - develop
  #  - dev-offline


#自动编译+自动部署，默认只跟踪dev-offline分支的提交，用于自动发布测试环境
compile_and_deploy:
  extends: .compile_and_deploy
  #默认dev-offline分支的提交，会自动发布到测试环境。自动发布的分支一定要再compile_test中排除掉，不然会造成重复打包
  #only:
  #  - dev-offline



#指定是否需要失败时发送企业微信通知，如果需要，则需要填入企业微信告警群的key
#notifyFailWeChat:
#    extends: .notifyFailWeChat
#    variables:
#        GIT_STRATEGY: "clone"
#        WWX_KEY: "1c9ab238-4d55-414a-8eb8-3aa888a87177"
