// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"git.domob-inc.cn/bluevision/bv_commons/app/demo/service"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxProducer"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxTask"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxconfig"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxgrpc"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxregistry/polaris"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxserver"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxstorage"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxtracing"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	biz2 "vision_hub/internal/biz/auth"
	"vision_hub/internal/biz/authorization"
	"vision_hub/internal/biz/common"
	biz6 "vision_hub/internal/biz/customer_project"
	"vision_hub/internal/biz/customer_project_space/account"
	"vision_hub/internal/biz/customer_project_space/config"
	"vision_hub/internal/biz/customer_project_space/material_manage"
	"vision_hub/internal/biz/customer_project_space/resource"
	biz3 "vision_hub/internal/biz/dict_manage"
	"vision_hub/internal/biz/internal_service"
	"vision_hub/internal/biz/internal_service/client_portal"
	"vision_hub/internal/biz/resource_pool"
	"vision_hub/internal/biz/resource_pool/ip"
	"vision_hub/internal/biz/resource_pool/outdoor_screen"
	"vision_hub/internal/biz/resource_pool/pr"
	"vision_hub/internal/biz/resource_pool/publisher"
	"vision_hub/internal/biz/resource_pool/reporter"
	"vision_hub/internal/biz/resource_pool/supplier"
	biz5 "vision_hub/internal/biz/task"
	biz7 "vision_hub/internal/biz/user_activity"
	biz4 "vision_hub/internal/biz/video_aigc"
	"vision_hub/internal/conf"
	"vision_hub/internal/data/auth/repo"
	repo7 "vision_hub/internal/data/authorization/repo"
	"vision_hub/internal/data/client"
	common2 "vision_hub/internal/data/common"
	repo3 "vision_hub/internal/data/common/repo"
	repo6 "vision_hub/internal/data/customer_project/repo"
	"vision_hub/internal/data/customer_project_space"
	repo9 "vision_hub/internal/data/customer_project_space/ads_account/repo"
	repo10 "vision_hub/internal/data/customer_project_space/config/repo"
	repo8 "vision_hub/internal/data/customer_project_space/material_manage/repo"
	repo4 "vision_hub/internal/data/dict_manage/repo"
	repo2 "vision_hub/internal/data/resource_pool/repo"
	repo5 "vision_hub/internal/data/task/repo"
	repo11 "vision_hub/internal/data/user_activity/repo"
	"vision_hub/internal/infrastructure/oss"
	"vision_hub/internal/infrastructure/redis"
	"vision_hub/internal/server"
	"vision_hub/internal/service/common"
	service3 "vision_hub/internal/service/consumer"
	"vision_hub/internal/service/cron"
	service2 "vision_hub/internal/service/http"
	customer_project_space2 "vision_hub/internal/service/http/customer_project_space"
	internal_service2 "vision_hub/internal/service/http/internal_service"
	service4 "vision_hub/internal/service/task"
)

// Injectors from wire.go:

// initApp init kratos application.
func initApp(bootstrap *kxconfig.Bootstrap, bizConf *conf.BizConf, logger log.Logger) (*kratos.App, func(), error) {
	gkratos_xApp := newApp()
	tracerProvider := kxtracing.NewTracerProvider(bootstrap)
	kxHttpServer := kxserver.NewHTTPServer(bootstrap, logger, tracerProvider)
	kxGrpcServer := kxserver.NewGRPCServer(bootstrap, logger, tracerProvider)
	mqLogger := gkratos_x.NewMqLogger(gkratos_xApp, bootstrap)
	kxRocketMQServer := kxserver.NewRocketMQServer(bootstrap, mqLogger, tracerProvider)
	kxCronServer := kxserver.NewCronServer(bootstrap)
	registrar := polaris.NewRegistrar(bootstrap)
	discovery := polaris.NewDiscovery(bootstrap)
	kxComponents := &gkratos_x.KxComponents{
		HttpSrv:     kxHttpServer,
		GrpcSrv:     kxGrpcServer,
		RocketmqSrv: kxRocketMQServer,
		CronSrv:     kxCronServer,
		Registrar:   registrar,
		Discovery:   discovery,
		Tracer:      tracerProvider,
	}
	kxCore := gkratos_x.NewKxCore(kxComponents)
	greeterService := service.NewGreeterService(logger)
	serviceLogHelper := common.NewServiceLogHelper(logger)
	bizLogHelper := biz.NewBizLogHelper(logger)
	mysqlDB := kxstorage.NewMysqlDB(bootstrap)
	dbClient, cleanup, err := kxstorage.NewDbClient(logger, mysqlDB)
	if err != nil {
		return nil, nil, err
	}
	backupDbClient, cleanup2, err := kxstorage.NewBackupSourceClient(logger, mysqlDB)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	cmdable, err := kxstorage.NewRedisCmd(bootstrap, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	redisClient, err := kxstorage.NewRedisClient(cmdable)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	dbSourceClient, cleanup3, err := kxstorage.NewDbSourceClient(logger, mysqlDB)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tsManagers := kxstorage.NewTransactionManager(dbClient, backupDbClient, dbSourceClient)
	data := &common2.Data{
		DbClient:       dbClient,
		BackupDbClient: backupDbClient,
		RedisClient:    redisClient,
		TsManagers:     tsManagers,
	}
	dataLogHelper := common2.NewDataLogHelper(logger)
	iAuthRepo := repo.NewAuthRepo(bizConf, dataLogHelper, data)
	iAuthThirdPartyRepo := repo.NewAuthThirdPartyRepo(bizConf, dataLogHelper)
	authCase := biz2.NewAuthCase(bizLogHelper, data, iAuthRepo, bizConf, iAuthThirdPartyRepo)
	baseService := common.NewBaseService(serviceLogHelper)
	authService := service2.NewAuthService(serviceLogHelper, authCase, baseService)
	iPrResourceRepo := repo2.NewPrResourceRepo(bizConf, dataLogHelper, data)
	iCommonRepo := repo3.NewCommonRepo(bizConf, dataLogHelper, data)
	prResourceCase := pr.NewPrResourceCase(bizLogHelper, data, iPrResourceRepo, iCommonRepo, bizConf)
	iIpResourceRepo := repo2.NewIpResourceRepo(bizConf, dataLogHelper, data)
	ipResourceCase := ip.NewIpResourceCase(bizLogHelper, data, iIpResourceRepo, iCommonRepo, bizConf)
	iPublisherResourceRepo := repo2.NewPublisherResourceRepo(bizConf, dataLogHelper, data)
	publisherResourceCase := publisher.NewPublisherResourceCase(bizLogHelper, data, iPublisherResourceRepo, iCommonRepo, bizConf)
	iSupplierResourceRepo := repo2.NewSupplierResourceRepo(bizConf, dataLogHelper, data)
	supplierResourceCase := supplier.NewSupplierResourceCase(bizLogHelper, data, iSupplierResourceRepo, iCommonRepo, bizConf)
	iOutdoorScreenResourceRepo := repo2.NewOutdoorScreenResourceRepo(bizConf, dataLogHelper, data)
	outdoorScreenResourceCase := outdoor_screen.NewOutdoorScreenResourceCase(bizLogHelper, data, iOutdoorScreenResourceRepo, iCommonRepo, bizConf)
	iReporterResourceRepo := repo2.NewReporterResourceRepo(bizConf, dataLogHelper, data)
	reporterResourceCase := reporter.NewReporterResourceCase(bizLogHelper, data, iReporterResourceRepo, iCommonRepo, bizConf)
	iResourceRepo := repo2.NewResourceRepo(bizConf, dataLogHelper, data)
	resourceCase := resource_pool.NewResourceCase(bizLogHelper, data, iResourceRepo, iCommonRepo, bizConf)
	resourceService := service2.NewResourceService(serviceLogHelper, prResourceCase, ipResourceCase, publisherResourceCase, supplierResourceCase, outdoorScreenResourceCase, reporterResourceCase, baseService, resourceCase)
	iDictManageRepo := repo4.NewDictManageRepo(bizConf, dataLogHelper, data)
	dictManageCase := biz3.NewDictManageCase(bizLogHelper, data, iDictManageRepo, iCommonRepo)
	dictManageService := service2.NewDictManageService(serviceLogHelper, baseService, dictManageCase)
	uploadOssClient := oss.NewUploadOssClient(bizConf, dataLogHelper)
	clientRedis := redis.NewRedisService(bizConf, dataLogHelper, redisClient)
	thirdPartyRepo := repo3.NewThirdPartyRepo(bizConf, dataLogHelper, uploadOssClient, clientRedis)
	commonCase := biz.NewCommonCase(bizLogHelper, data, bizConf, iCommonRepo, thirdPartyRepo)
	commonService := service2.NewCommonService(serviceLogHelper, commonCase, baseService, bizConf)
	videoAIGCCase := biz4.NewVideoAIGCCase(bizLogHelper, data, thirdPartyRepo, clientRedis, bizConf)
	videoAIGCService := service2.NewVideoAIGCService(serviceLogHelper, videoAIGCCase, baseService)
	iTaskRepo := repo5.NewTaskRepo(bizConf, dataLogHelper, data)
	rocketMqClient, cleanup4, err := kxProducer.NewRocketMqClient(bootstrap, logger, tracerProvider)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	taskCase := biz5.NewTaskCase(bizLogHelper, data, iTaskRepo, rocketMqClient, bizConf, iCommonRepo, thirdPartyRepo, publisherResourceCase, reporterResourceCase)
	taskApiService := service2.NewTaskApiService(serviceLogHelper, taskCase, baseService)
	iCustomerProjectRepo := repo6.NewCustomerProjectRepo(bizConf, dataLogHelper, data)
	clientFactory := kxgrpc.NewClientFactory(logger, discovery, tracerProvider)
	settlementServiceClient, err := internal_service.NewClientPortalSettlementClient(bizConf, clientFactory)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	signServiceClient, err := client.NewClientPortalSignClient(bizConf, clientFactory)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	clientPortalCase := client_portal.NewClientPortalCase(bizLogHelper, data, settlementServiceClient, signServiceClient)
	customerProjectCase := biz6.NewCustomerProjectCase(bizLogHelper, data, iCustomerProjectRepo, iCommonRepo, clientPortalCase)
	customerProjectService := service2.NewCustomerProjectService(bizLogHelper, customerProjectCase, baseService)
	clientPortalService := internal_service2.NewClientPortalService(bizLogHelper, clientPortalCase, baseService)
	iAuthGroupRepo := repo.NewAuthGroupRepo(dataLogHelper, data)
	authGroupCase := biz2.NewAuthGroupCase(bizLogHelper, iAuthGroupRepo)
	authGroupService := service2.NewAuthGroupService(bizLogHelper, authGroupCase, baseService)
	iAuthorizationRepo := repo7.NewAuthorizationRepo(dataLogHelper, data)
	authorizationCase := authorization.NewAuthorizationCase(bizLogHelper, data, iAuthorizationRepo, bizConf)
	authorizationService := service2.NewAuthorizationService(bizLogHelper, authorizationCase, baseService)
	iMaterialManageRepo := repo8.NewMaterialManageRepo(bizConf, dataLogHelper, data)
	iCustomerProjectCommRepo := customer_project_space.NewCustomerProjectCommRepo(bizConf, dataLogHelper, data)
	materialManageCase := material_manage.NewMaterialManageCase(bizConf, bizLogHelper, data, iMaterialManageRepo, thirdPartyRepo, iCustomerProjectRepo, iCommonRepo, iCustomerProjectCommRepo, clientRedis)
	materialManageService := customer_project_space2.NewMaterialManageService(bizLogHelper, materialManageCase, baseService)
	iAdsAccountRepo := repo9.NewAdsAccountRepo(bizConf, dataLogHelper, data)
	adsAccountCase := account.NewAdsAccountCase(bizLogHelper, iAdsAccountRepo, iCustomerProjectCommRepo)
	adsAccountService := customer_project_space2.NewAdsAccountService(bizLogHelper, adsAccountCase, baseService)
	iCustomerProjectConfigRepo := repo10.NewCustomerProjectConfigRepo(dataLogHelper, data)
	customerProjectConfigCase := config.NewCustomerProjectConfigCase(bizLogHelper, data, iCustomerProjectConfigRepo)
	customerProConfigService := customer_project_space2.NewCustomerProConfigService(bizLogHelper, customerProjectConfigCase, baseService)
	userActivityRepo := repo11.NewUserActivityRepo(bizConf, dataLogHelper, data)
	userActivityCase := biz7.NewUserActivityCase(bizLogHelper, data, userActivityRepo, iAuthRepo)
	userActivityService := service2.NewUserActivityService(bizLogHelper, baseService, userActivityCase)
	callbackService := service2.NewCallbackService(bizLogHelper, baseService)
	customerProResourceCase := resource.NewCustomerProResourceCase(bizLogHelper, iCustomerProjectCommRepo, prResourceCase, ipResourceCase, publisherResourceCase, supplierResourceCase, outdoorScreenResourceCase, reporterResourceCase, taskCase)
	customerProResourceService := customer_project_space2.NewCustomerProResourceService(bizLogHelper, customerProResourceCase, baseService, resourceService)
	httpServerOptions := server.HTTPServerOptions{
		Greeter:                    greeterService,
		AuthService:                authService,
		ResourceService:            resourceService,
		DictManageService:          dictManageService,
		CommonService:              commonService,
		VideoAIGCService:           videoAIGCService,
		TaskApiService:             taskApiService,
		CustomerProjectService:     customerProjectService,
		ClientPortalService:        clientPortalService,
		AuthGroupService:           authGroupService,
		AuthorizationService:       authorizationService,
		MaterialManageService:      materialManageService,
		AdsAccountManageService:    adsAccountService,
		CustomerProConfigService:   customerProConfigService,
		UserActivityService:        userActivityService,
		CallbackService:            callbackService,
		CustomerProResourceService: customerProResourceService,
	}
	httpServer := server.NewHTTPServer(kxCore, httpServerOptions)
	grpcServerOptions := server.GRPCServerOptions{
		Greeter: greeterService,
	}
	grpcServer := server.NewGRPCServer(kxCore, grpcServerOptions)
	taskService := service3.NewTaskService(serviceLogHelper, taskCase, baseService, bizConf)
	rocketMQServerOptions := server.RocketMQServerOptions{
		TaskService: taskService,
	}
	rocketmqServer, err := server.NewRocketMQServer(kxCore, rocketMQServerOptions, bizConf)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	cronServiceLogHelper := cron.NewCronServiceLogHelper(logger)
	materialMediumUploadService := cron.NewMaterialMediumUploadService(cronServiceLogHelper, materialManageCase)
	tiktokCheckAIGCTaskService := cron.NewTiktokCheckAIGCTaskService(cronServiceLogHelper, videoAIGCCase, clientRedis)
	materialMediumUploadOssService := cron.NewMaterialMediumUploadOssService(cronServiceLogHelper, materialManageCase)
	cronServerOptions := server.CronServerOptions{
		MaterialManageService:          materialMediumUploadService,
		TtCheckAIGCTaskService:         tiktokCheckAIGCTaskService,
		MaterialMediumUploadOssService: materialMediumUploadOssService,
	}
	server2 := server.NewCronServer(kxCronServer, cronServerOptions)
	kratosApp := gkratos_x.NewKratosApp(gkratos_xApp, bootstrap, logger, kxCore, httpServer, grpcServer, rocketmqServer, server2)
	return kratosApp, func() {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}

func initTaskServer(bootstrap *kxconfig.Bootstrap, bizConf *conf.BizConf, logger log.Logger) (*kxTask.Server, func(), error) {
	kxTaskServer := kxserver.NewTaskServer(bootstrap)
	taskTestService := service4.NewTaskTestService(logger)
	taskServerOptions := server.TaskServerOptions{
		TestTask: taskTestService,
	}
	server2 := server.NewTaskServer(kxTaskServer, taskServerOptions)
	return server2, func() {
	}, nil
}
