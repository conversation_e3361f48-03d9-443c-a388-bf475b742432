package ads_account

import "vision_hub/internal/biz/customer_project_space"

// GetAdsAccountListParams 获取广告账号列表参数
type GetAdsAccountListParams struct {
	CustomerProjectID int32
	AdsAccountID      string
	Medium            []int32
	Status            string
	PageNum           int32
	PageSize          int32
	Platform          string
}

// AdsAccountListItem 广告账号列表项
type AdsAccountListItem struct {
	AdsAccountID string `json:"ads_account_id"`
	Status       string `json:"status"`
}

// AdsAccountListItem 广告账号列表项
type AdsAccountListResp struct {
	AdsAccountID string `json:"ads_account_id"`
	Status       string `json:"status"`
	customer_project_space.ProjectAccountSign
}

type OpenAPIAccount struct {
	AdsAccountID string `gorm:"column:ads_account_id" json:"ads_account_id"`
	Medium       string `gorm:"column:medium" json:"medium"`
}
