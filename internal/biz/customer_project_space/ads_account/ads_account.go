package ads_account

import (
	"context"
	"encoding/json"
	"git.domob-inc.cn/bluevision/bv_interface_go/client_portal_server/enums"
	bizcommon "vision_hub/internal/biz/common"
	customerProjectSpaceBiz "vision_hub/internal/biz/customer_project_space"
	constants "vision_hub/internal/constants/customer_project_space"
	"vision_hub/internal/data/customer_project_space/model"
	"vision_hub/internal/errors/customer_project_space/ads_account"
)

// AdsAccountCase 广告账号业务实现
type AdsAccountCase struct {
	log             *bizcommon.BizLogHelper
	repo            IAdsAccountRepo
	customerProRepo customerProjectSpaceBiz.ICustomerProjectCommRepo
}

// NewAdsAccountCase 创建广告账号业务实例
func NewAdsAccountCase(log *bizcommon.BizLogHelper, repo IAdsAccountRepo, customerProRepo customerProjectSpaceBiz.ICustomerProjectCommRepo) *AdsAccountCase {
	return &AdsAccountCase{
		log:             log,
		repo:            repo,
		customerProRepo: customerProRepo,
	}
}

// GetAdsAccountList 获取广告账号列表
func (c *AdsAccountCase) GetAdsAccountList(ctx context.Context, params *GetAdsAccountListParams) ([]*AdsAccountListResp, *customerProjectSpaceBiz.Pagination, error) {
	// 获取列表记录
	data, page, err := c.repo.GetAdsAccountList(ctx, params)
	if err != nil {
		return nil, nil, err
	}
	// 获取签约信息
	adsAccountIds := make([]string, 0)
	for _, item := range data {
		adsAccountIds = append(adsAccountIds, item.AdsAccountID)
	}
	// 获取中台签约信息
	accountSign, _, err := c.customerProRepo.GetAccountSignList(ctx, adsAccountIds, &customerProjectSpaceBiz.ProjectAccountListParams{Medium: params.Medium}, false)
	if err != nil {
		return nil, nil, err
	}
	accountSignMap := make(map[string]customerProjectSpaceBiz.ProjectAccountSign)
	for _, item := range accountSign {
		accountSignMap[item.AccountID] = *item
	}
	res := make([]*AdsAccountListResp, 0)
	// 过滤媒体,重新组装数据
	for _, v := range data {
		// 过滤数据，如果Medium不为空，说明需要过滤非该条件的媒体数据，不在accountSignMap中直接跳过
		if _, ok := accountSignMap[v.AdsAccountID]; !ok && len(params.Medium) != 0 {
			continue
		}
		res = append(res, &AdsAccountListResp{
			AdsAccountID:       v.AdsAccountID,
			Status:             v.Status,
			ProjectAccountSign: accountSignMap[v.AdsAccountID],
		})
	}
	// total
	if len(params.Medium) != 0 {
		page.Total = int32(len(accountSign))
	}
	return res, page, nil
}

// AddAdsAccount 添加广告账号
func (c *AdsAccountCase) AddAdsAccount(ctx context.Context, customerProjectID int32, accountList []string) error {
	// 检查当前操作用户是否有该项目权限
	err := c.checkProjectPermission(ctx, customerProjectID)
	if err != nil {
		return err
	}
	// 检查账号是否存在
	existAccounts, err := c.customerProRepo.GetCurrentProjectAdsAccountIds(ctx, customerProjectID, accountList)
	if err != nil {
		return err
	}
	if len(existAccounts) > 0 {
		return ads_account.NewAdsAccountExistsErr(existAccounts)
	}
	// 获取签约信息
	accountSign, _, err := c.customerProRepo.GetAccountSignList(ctx, accountList, &customerProjectSpaceBiz.ProjectAccountListParams{}, false)
	if err != nil {
		return err
	}
	accountMediumMap := make(map[string]string)
	for _, item := range accountSign {
		accountMediumMap[item.AccountID] = enums.MediumTypeEnum_MediumType_name[item.MediumId]
	}
	insertItems := make([]*model.CustomerProjectResource, 0)
	for _, account := range accountList {
		extraInfo, err := json.Marshal(map[string]string{
			"medium": accountMediumMap[account],
		})
		if err != nil {
			return err
		}
		insertItems = append(insertItems, &model.CustomerProjectResource{
			CustomerProjectID: customerProjectID,
			ResourceID:        account,
			ResourceType:      constants.CustomerResourceAdsAccount,
			ResourceExtraInfo: extraInfo,
		})
	}
	return c.repo.AddAdsAccount(ctx, insertItems)
}

// EnOrDisableAdsAccount 启用/禁用广告账号
func (c *AdsAccountCase) EnOrDisableAdsAccount(ctx context.Context, customerProjectID int32, accountList []string, status string) error {
	// 检查当前操作用户是否有该项目权限
	err := c.checkProjectPermission(ctx, customerProjectID)
	if err != nil {
		return err
	}
	return c.repo.EnOrDisableAdsAccount(ctx, customerProjectID, accountList, status)
}

// RemoveAdsAccount 移除广告账号
func (c *AdsAccountCase) RemoveAdsAccount(ctx context.Context, customerProjectID int32, accountList []string) error {
	// 检查当前操作用户是否有该项目权限
	err := c.checkProjectPermission(ctx, customerProjectID)
	if err != nil {
		return err
	}
	return c.repo.RemoveAdsAccount(ctx, customerProjectID, accountList)
}

func (c *AdsAccountCase) checkProjectPermission(ctx context.Context, customerProjectID int32) error {
	if err := c.customerProRepo.CheckCurrentUserProjectPermission(ctx, customerProjectID); err != nil {
		return err
	}
	return nil
}

func (c *AdsAccountCase) PreviewAdsAccount(ctx context.Context, accountList []string) ([]*customerProjectSpaceBiz.ProjectAccountSign, error) {
	// 获取签约信息
	accountSign, _, err := c.customerProRepo.GetAccountSignList(ctx, accountList, &customerProjectSpaceBiz.ProjectAccountListParams{}, false)
	if err != nil {
		return nil, err
	}
	return accountSign, nil
}

func (c *AdsAccountCase) OpenAPIGetEnableAdsAccountList(ctx context.Context, customerProjectNumber string) ([]*OpenAPIAccount, error) {
	return c.repo.OpenAPIGetEnableAdsAccountList(ctx, customerProjectNumber)
}
