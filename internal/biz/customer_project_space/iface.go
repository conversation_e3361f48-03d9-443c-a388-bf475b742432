package customer_project_space

import (
	"context"
	"vision_hub/internal/data/customer_project_space/model"
)

type ICustomerProjectCommRepo interface {
	GetCurrentUserProject(ctx context.Context, userId int32) (map[int32]bool, error)
	CheckCurrentUserProjectPermission(ctx context.Context, projectId int32) error
	GetCurrentProjectAdsAccountIds(ctx context.Context, projectId int32, accountIds []string, resourceType string) ([]string, error)
	GetAccountSignList(ctx context.Context, accountIds []string, params *ProjectAccountListParams, isPaging bool) ([]*ProjectAccountSign, *Pagination, error)
	AddCustomerProResource(ctx context.Context, resource []*model.CustomerProjectResource) error
	RemoveCustomerProResource(ctx context.Context, resourceType string, resourceIdList []string, customerProId int32) error
	GetCustomerProResourceIds(ctx context.Context, customerProId int32, resourceType string, resourceStatus string) ([]string, error)
	UpdateCustomerProResourceStatus(ctx context.Context, resourceType string, resourceIdList []string, customerProId int32, resourceStatus string) error
}
