package resource

import (
	"context"
	"encoding/json"
	"fmt"
	bizcommon "vision_hub/internal/biz/common"
	customerProjectSpaceBiz "vision_hub/internal/biz/customer_project_space"
	bizIp "vision_hub/internal/biz/resource_pool/ip"
	bizOutdoorScreen "vision_hub/internal/biz/resource_pool/outdoor_screen"
	bizPr "vision_hub/internal/biz/resource_pool/pr"
	bizPublisher "vision_hub/internal/biz/resource_pool/publisher"
	bizReporter "vision_hub/internal/biz/resource_pool/reporter"
	bizSupplier "vision_hub/internal/biz/resource_pool/supplier"
	bizTask "vision_hub/internal/biz/task"
	constants "vision_hub/internal/constants/customer_project_space"
	resourceConstants "vision_hub/internal/constants/resource_pool"
	"vision_hub/internal/data/customer_project_space/model"
	authError "vision_hub/internal/errors/auth"
	resourcePoolErr "vision_hub/internal/errors/resource_pool"
	"vision_hub/internal/middleware"
)

type CustomerProResourceCase struct {
	log                       *bizcommon.BizLogHelper
	customerProRepo           customerProjectSpaceBiz.ICustomerProjectCommRepo
	resourcePrCase            *bizPr.PrResourceCase
	resourceIpCase            *bizIp.IpResourceCase
	resourcePublisherCase     *bizPublisher.PublisherResourceCase
	resourceSupplierCase      *bizSupplier.SupplierResourceCase
	resourceOutdoorScreenCase *bizOutdoorScreen.OutdoorScreenResourceCase
	resourceReporterCase      *bizReporter.ReporterResourceCase
	taskCase                  *bizTask.TaskCase
}

func NewCustomerProResourceCase(
	log *bizcommon.BizLogHelper,
	customerProRepo customerProjectSpaceBiz.ICustomerProjectCommRepo,
	resourcePrCase *bizPr.PrResourceCase,
	resourceIpCase *bizIp.IpResourceCase,
	resourcePublisherCase *bizPublisher.PublisherResourceCase,
	resourceSupplierCase *bizSupplier.SupplierResourceCase,
	resourceOutdoorScreenCase *bizOutdoorScreen.OutdoorScreenResourceCase,
	resourceReporterCase *bizReporter.ReporterResourceCase,
	taskCase *bizTask.TaskCase) *CustomerProResourceCase {
	return &CustomerProResourceCase{
		log:                       log,
		customerProRepo:           customerProRepo,
		resourcePrCase:            resourcePrCase,
		resourceIpCase:            resourceIpCase,
		resourcePublisherCase:     resourcePublisherCase,
		resourceSupplierCase:      resourceSupplierCase,
		resourceOutdoorScreenCase: resourceOutdoorScreenCase,
		resourceReporterCase:      resourceReporterCase,
		taskCase:                  taskCase,
	}
}

// AddCustomerProResource 新增项目资源
func (uc *CustomerProResourceCase) AddCustomerProResource(ctx context.Context, params *AddOrRemoveCustomerProResourceParams) error {
	// 验证当前用户是否有该项目权限
	if err := uc.customerProRepo.CheckCurrentUserProjectPermission(ctx, params.CustomerProjectID); err != nil {
		return err
	}
	// 获取用户基础信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}
	var resourceList []*model.CustomerProjectResource
	for _, id := range params.ResourceIDList {
		resourceList = append(resourceList, &model.CustomerProjectResource{
			CustomerProjectID: params.CustomerProjectID,
			ResourceType:      params.ResourceType,
			ResourceID:        id,
			ResourceStatus:    constants.CustomerResourceEnable,
			CreateUserID:      int32(userInfo.UserID),
		})
	}
	return uc.customerProRepo.AddCustomerProResource(ctx, resourceList)
}

// RemoveCustomerProResource 移除项目资源
func (uc *CustomerProResourceCase) RemoveCustomerProResource(ctx context.Context, params *AddOrRemoveCustomerProResourceParams) error {
	// 验证当前用户是否有该项目权限
	if err := uc.customerProRepo.CheckCurrentUserProjectPermission(ctx, params.CustomerProjectID); err != nil {
		return err
	}
	return uc.customerProRepo.RemoveCustomerProResource(ctx, params.ResourceType, params.ResourceIDList, params.CustomerProjectID)
}

// CheckCurrentUserProjectPermission 验证当前用户是否有该项目权限
func (uc *CustomerProResourceCase) CheckCurrentUserProjectPermission(ctx context.Context, projectID int32) error {
	return uc.customerProRepo.CheckCurrentUserProjectPermission(ctx, projectID)
}

// ExportCustomerProResource 导出项目资源
func (uc *CustomerProResourceCase) ExportCustomerProResource(ctx context.Context, projectID int32, resourceType, taskName, resourceName string) error {
	// 验证当前用户是否有该项目权限
	if err := uc.customerProRepo.CheckCurrentUserProjectPermission(ctx, projectID); err != nil {
		return err
	}
	var req interface{}
	var taskType string
	switch resourceType {
	/*
		case resourceConstants.ResourcePR:
			//req = bizPr.ListPrResourceReq{CustomerProjectId: projectID, BaseParam: bizPr.BaseParam{DisplayName: resourceName}}
			//taskType = bizTask.PRExport
		case resourceConstants.ResourceIP:
			//req = bizIp.ListIpResourceReq{CustomerProjectId: projectID, BaseParam: bizIp.BaseParam{DisplayName: resourceName}}
			//taskType = bizTask.IPExport
		case resourceConstants.ResourceSupplier:
			//req = bizSupplier.ListSupplierResourceReq{CustomerProjectId: projectID, BaseParam: bizSupplier.BaseParam{DisplayName: resourceName}}
			//taskType = bizTask.SupplierExport
		case resourceConstants.ResourceOutdoorScreen:
			//req = bizOutdoorScreen.ListOutdoorScreenResourceReq{CustomerProjectId: projectID, BaseParam: bizOutdoorScreen.BaseParam{DisplayName: resourceName}}
			//taskType = bizTask.OutdoorExport
	*/
	case resourceConstants.ResourcePR, resourceConstants.ResourceIP, resourceConstants.ResourceSupplier, resourceConstants.ResourceOutdoorScreen:
		return resourcePoolErr.ResourceExportSupportNotExist
	case resourceConstants.ResourceReporter:
		req = &bizReporter.GetReporterResourceListReq{CustomerProjectId: projectID, Name: resourceName}
		taskType = bizTask.ReporterExport
	case resourceConstants.ResourcePublisher:
		req = bizPublisher.ListPublisherResourceReq{CustomerProjectId: projectID, BaseParam: bizPublisher.BaseParam{Name: resourceName}}
		taskType = bizTask.PublisherExport
	default:
		return fmt.Errorf("资源类型错误")
	}
	reqBytes, err := json.Marshal(req)
	if err != nil {
		return err
	}
	if _, err = uc.taskCase.CreateTask(ctx, taskType, string(reqBytes), taskName); err != nil {
		return err
	}
	return nil
}
