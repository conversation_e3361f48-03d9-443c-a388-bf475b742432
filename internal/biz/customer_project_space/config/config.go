package config

import (
	"context"
	"encoding/json"
	"time"
	bizcommon "vision_hub/internal/biz/common"
	datacommon "vision_hub/internal/data/common"
	"vision_hub/internal/data/customer_project_space/model"
	authError "vision_hub/internal/errors/auth"
	"vision_hub/internal/middleware"
)

// CustomerProjectConfigCase 项目配置业务逻辑
type CustomerProjectConfigCase struct {
	log  *bizcommon.BizLogHelper
	data *datacommon.Data
	repo ICustomerProjectConfigRepo
}

// NewCustomerProjectConfigCase 创建项目配置业务逻辑实例
func NewCustomerProjectConfigCase(log *bizcommon.BizLogHelper, data *datacommon.Data, repo ICustomerProjectConfigRepo) *CustomerProjectConfigCase {
	return &CustomerProjectConfigCase{
		log:  log,
		data: data,
		repo: repo,
	}
}

// GetCustomerProConfig 获取项目配置
func (c *CustomerProjectConfigCase) GetCustomerProConfig(ctx context.Context, req *GetCustomerProConfigReq) (*CustomerProConfigListData, error) {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return nil, authError.QueryUserFail
	}

	// TODO: 检查用户是否有该项目的权限
	_ = userInfo

	// 获取配置列表
	configs, err := c.repo.GetCustomerProjectConfigs(ctx, req)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取项目配置失败: %v", err)
		return nil, err
	}

	// 转换为业务对象
	configList := make([]*CustomerProjectConfig, 0, len(configs))
	for _, config := range configs {
		bizConfig := &CustomerProjectConfig{
			ID:                config.ID,
			CustomerProjectID: config.CustomerProjectID,
			ConfigKey:         config.ConfigKey,
			//ConfigDisplayName: config.ConfigDisplayName,
			ConfigValue:  config.ConfigValue,
			ConfigStatus: config.ConfigStatus,
			CreateUserID: config.CreateUserID,
			UpdateUserID: config.UpdateUserID,
		}

		configList = append(configList, bizConfig)
	}

	return &CustomerProConfigListData{
		List: configList,
	}, nil
}

// SaveCustomerProConfig 保存项目配置
func (c *CustomerProjectConfigCase) SaveCustomerProConfig(ctx context.Context, req *AddCustomerProConfigReq) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}

	// TODO: 检查用户是否有该项目的权限
	_ = userInfo

	// 检查配置是否已存在
	existingConfig, err := c.repo.GetCustomerProjectConfigByKey(ctx, req.CustomerProjectID, req.ConfigKey)
	if err != nil && err.Error() != "record not found" {
		c.log.WithContext(ctx).Errorf("检查配置是否存在失败: %v", err)
		return err
	}

	if existingConfig != nil {
		// 更新现有配置
		updateParams := map[string]interface{}{
			"config_value":   json.RawMessage(req.ConfigValue),
			"update_time":    time.Now(),
			"update_user_id": userInfo.UserID,
		}

		err = c.repo.UpdateCustomerProjectConfig(ctx, existingConfig.ID, updateParams)
		if err != nil {
			c.log.WithContext(ctx).Errorf("更新项目配置失败: %v", err)
			return err
		}
	} else {
		// 创建新配置
		config := &model.CustomerProjectConfig{
			CustomerProjectID: req.CustomerProjectID,
			ConfigKey:         req.ConfigKey,
			ConfigValue:       req.ConfigValue,
			ConfigStatus:      ConfigStatusActive,
			CreateUserID:      int32(userInfo.UserID),
			UpdateUserID:      int32(userInfo.UserID),
		}

		err = c.repo.SaveCustomerProjectConfig(ctx, config)
		if err != nil {
			c.log.WithContext(ctx).Errorf("保存项目配置失败: %v", err)
			return err
		}
	}

	return nil
}
