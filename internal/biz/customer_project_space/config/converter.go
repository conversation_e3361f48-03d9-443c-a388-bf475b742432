package config

import (
	"encoding/json"
	"google.golang.org/protobuf/types/known/structpb"
	pb "vision_hub/api/customer_project_space/v1"
	"vision_hub/internal/util"
)

// ConvertGetRequestToBiz 转换获取请求为业务对象
func ConvertGetRequestToBiz(req *pb.GetCustomerProConfigRequest) *GetCustomerProConfigReq {
	return &GetCustomerProConfigReq{
		CustomerProjectID: req.CustomerProjectId,
		ConfigKey:         req.ConfigKey,
		ConfigStatus:      req.ConfigStatus,
	}
}

// ConvertAddRequestToBiz 转换添加请求为业务对象
func ConvertAddRequestToBiz(req *pb.AddCustomerProConfigRequest) *AddCustomerProConfigReq {
	return &AddCustomerProConfigReq{
		CustomerProjectID: req.CustomerProjectId,
		ConfigKey:         req.Config<PERSON>ey,
		ConfigValue:       req.ConfigValue,
	}
}

// ConvertConfigToPb 转换配置业务对象为Proto对象
func ConvertConfigToPb(config *CustomerProjectConfig) (*pb.CustomerProConfigData, error) {
	if config == nil {
		return nil, nil
	}

	pbConfig := &pb.CustomerProConfigData{
		Id:                int32(config.ID),
		ConfigKey:         config.ConfigKey,
		ConfigStatus:      config.ConfigStatus,
		ConfigDisplayName: config.ConfigDisplayName,
	}

	// 处理JSON配置值 - 如果是JSON格式则转换为字符串，否则直接使用
	if config.ConfigValue != "" {
		// 尝试解析为JSON，如果成功则说明是有效的JSON
		var jsonData interface{}
		if err := json.Unmarshal([]byte(config.ConfigValue), &jsonData); err == nil {
			// 是有效的JSON，直接使用字符串形式
			pbConfig.ConfigValue = config.ConfigValue
		} else {
			// 不是有效的JSON，作为普通字符串处理
			pbConfig.ConfigValue = config.ConfigValue
		}
	}

	return pbConfig, nil
}

// ConvertConfigListToPb 转换配置列表为Proto对象
func ConvertConfigListToPb(data *CustomerProConfigListData) ([]*pb.CustomerProConfigData, error) {
	if data == nil || len(data.List) == 0 {
		return []*pb.CustomerProConfigData{}, nil
	}

	pbList := make([]*pb.CustomerProConfigData, 0, len(data.List))
	for _, config := range data.List {
		pbConfig, err := ConvertConfigToPb(config)
		if err != nil {
			return nil, err
		}
		pbList = append(pbList, pbConfig)
	}

	return pbList, nil
}

// JSONToStruct 将JSON字符串转换为protobuf Struct（通用方法）
func JSONToStruct(jsonStr string) (*structpb.Struct, error) {
	return util.JSONToStruct(jsonStr)
}

// StructToJSON 将protobuf Struct转换为JSON字符串（通用方法）
func StructToJSON(s *structpb.Struct) (string, error) {
	return util.StructToJSON(s)
}
