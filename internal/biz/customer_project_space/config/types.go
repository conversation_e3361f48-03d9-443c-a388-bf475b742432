package config

import (
	"time"
)

// CustomerProjectConfig 项目配置业务对象
type CustomerProjectConfig struct {
	ID                int32     `json:"id"`
	CustomerProjectID int32     `json:"customer_project_id"`
	ConfigKey         string    `json:"config_key"`
	ConfigDisplayName string    `json:"config_display_name"`
	ConfigValue       string    `json:"config_value"` // JSON字符串
	ConfigStatus      string    `json:"config_status"`
	CreateUserID      int32     `json:"create_user_id"`
	UpdateUserID      int32     `json:"update_user_id"`
	CreateTime        time.Time `json:"create_time"`
	UpdateTime        time.Time `json:"update_time"`
}

// GetCustomerProConfigReq 获取项目配置请求
type GetCustomerProConfigReq struct {
	CustomerProjectID int32  `json:"customer_project_id"`
	ConfigKey         string `json:"config_key"`
	ConfigStatus      string `json:"config_status"`
}

// AddCustomerProConfigReq 添加项目配置请求
type AddCustomerProConfigReq struct {
	CustomerProjectID int32  `json:"customer_project_id"`
	ConfigKey         string `json:"config_key"`
	ConfigValue       string `json:"config_value"`
}

// CustomerProConfigListData 项目配置列表数据
type CustomerProConfigListData struct {
	List []*CustomerProjectConfig `json:"list"`
}

// ConfigStatus 配置状态常量
const (
	ConfigStatusActive   = "active"
	ConfigStatusInactive = "inactive"
)

// ConfigKey 配置键常量
const (
	ConfigKeyMaterialUpload = "material_upload"
	ConfigKeyAccountManage  = "account_manage"
	ConfigKeyReportSetting  = "report_setting"
)
