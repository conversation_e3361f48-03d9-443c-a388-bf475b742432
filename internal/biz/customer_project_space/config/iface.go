package config

import (
	"context"
	"vision_hub/internal/data/customer_project_space/model"
)

// ICustomerProjectConfigRepo 项目配置数据访问接口
type ICustomerProjectConfigRepo interface {
	// GetCustomerProjectConfigs 获取项目配置列表
	GetCustomerProjectConfigs(ctx context.Context, req *GetCustomerProConfigReq) ([]*model.CustomerProjectConfig, error)

	// SaveCustomerProjectConfig 保存项目配置
	SaveCustomerProjectConfig(ctx context.Context, config *model.CustomerProjectConfig) error

	// GetCustomerProjectConfigByKey 根据项目ID和配置键获取配置
	GetCustomerProjectConfigByKey(ctx context.Context, customerProjectID int32, configKey string) ([]*model.CustomerProjectConfig, error)

	// UpdateCustomerProjectConfig 更新项目配置
	UpdateCustomerProjectConfig(ctx context.Context, id int32, updateParams map[string]interface{}) error

	// DeleteCustomerProjectConfig 删除项目配置
	DeleteCustomerProjectConfig(ctx context.Context, id int32) error
}
