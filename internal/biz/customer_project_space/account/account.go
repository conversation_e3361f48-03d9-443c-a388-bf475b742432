package account

import (
	"context"
	"encoding/json"
	"git.domob-inc.cn/bluevision/bv_interface_go/client_portal_server/enums"
	authorizationBiz "vision_hub/internal/biz/authorization"
	bizcommon "vision_hub/internal/biz/common"
	customerProjectSpaceBiz "vision_hub/internal/biz/customer_project_space"
	configBiz "vision_hub/internal/biz/customer_project_space/config"
	"vision_hub/internal/constants"
	customerProjectSpaceData "vision_hub/internal/data/customer_project_space"
	"vision_hub/internal/data/customer_project_space/model"
	authError "vision_hub/internal/errors/auth"
	"vision_hub/internal/errors/customer_project_space/ads_account"
	"vision_hub/internal/infrastructure/third_party"
	"vision_hub/internal/middleware"
)

// AdsAccountCase 广告账号业务实现
type AdsAccountCase struct {
	log               *bizcommon.BizLogHelper
	repo              IAdsAccountRepo
	customerProRepo   customerProjectSpaceBiz.ICustomerProjectCommRepo
	configRepo        configBiz.ICustomerProjectConfigRepo
	authorizationRepo authorizationBiz.IAuthorizationRepo
}

// NewAdsAccountCase 创建广告账号业务实例
func NewAdsAccountCase(log *bizcommon.BizLogHelper, repo IAdsAccountRepo, customerProRepo customerProjectSpaceBiz.ICustomerProjectCommRepo, configRepo configBiz.ICustomerProjectConfigRepo, authorizationRepo authorizationBiz.IAuthorizationRepo) *AdsAccountCase {
	return &AdsAccountCase{
		log:               log,
		repo:              repo,
		customerProRepo:   customerProRepo,
		configRepo:        configRepo,
		authorizationRepo: authorizationRepo,
	}
}

// GetAccountList 获取广告账号列表
func (c *AdsAccountCase) GetAccountList(ctx context.Context, params *GetAdsAccountListParams) ([]*AdsAccountListResp, *customerProjectSpaceBiz.Pagination, error) {
	// 获取列表记录
	data, page, err := c.repo.GetAccountList(ctx, params)
	if err != nil {
		return nil, nil, err
	}
	switch params.Platform {
	case constants.AuthPlatformImpact:
		return c.GetImpactAccountList(ctx, data, params, page)
	default:
		// 默认走获取签约
		return c.GetAdsAccountList(ctx, data, params, page)
	}
}

// GetAdsAccountList 获取关联账号(中台签约信息)
func (c *AdsAccountCase) GetAdsAccountList(
	ctx context.Context,
	data []*AccountListItem,
	params *GetAdsAccountListParams,
	page *customerProjectSpaceBiz.Pagination) ([]*AdsAccountListResp, *customerProjectSpaceBiz.Pagination, error) {
	// 获取签约信息
	adsAccountIds := make([]string, 0)
	for _, item := range data {
		adsAccountIds = append(adsAccountIds, item.AccountID)
	}
	// 获取中台签约信息
	accountSign, _, err := c.customerProRepo.GetAccountSignList(ctx, adsAccountIds, &customerProjectSpaceBiz.ProjectAccountListParams{Medium: params.Medium}, false)
	if err != nil {
		return nil, nil, err
	}
	accountSignMap := make(map[string]customerProjectSpaceBiz.ProjectAccountSign)
	for _, item := range accountSign {
		accountSignMap[item.AccountID] = *item
	}
	res := make([]*AdsAccountListResp, 0)
	// 过滤媒体,重新组装数据
	for _, v := range data {
		// 过滤数据，如果Medium不为空，说明需要过滤非该条件的媒体数据，不在accountSignMap中直接跳过
		if _, ok := accountSignMap[v.AccountID]; !ok && len(params.Medium) != 0 {
			continue
		}
		res = append(res, &AdsAccountListResp{
			AccountID:          v.AccountID,
			Status:             v.Status,
			ProjectAccountSign: accountSignMap[v.AccountID],
		})
	}
	// total
	if len(params.Medium) != 0 {
		page.Total = int32(len(accountSign))
	}
	return res, page, nil
}

// GetImpactAccountList 获取关联账号(impact账号)
func (c *AdsAccountCase) GetImpactAccountList(
	ctx context.Context,
	data []*AccountListItem,
	params *GetAdsAccountListParams,
	page *customerProjectSpaceBiz.Pagination) ([]*AdsAccountListResp, *customerProjectSpaceBiz.Pagination, error) {
	res := make([]*AdsAccountListResp, 0)
	// TODO 获取campaign_name

	for _, v := range data {
		res = append(res, &AdsAccountListResp{
			AccountID:        v.AccountID,
			AccountName:      string(v.AccountExtraInfo),
			Medium:           constants.AuthPlatformImpact,
			Status:           v.Status,
			AccountExtraInfo: v.AccountExtraInfo,
		})
	}
	return res, page, nil
}

// AddAdsAccount 添加广告账号
func (c *AdsAccountCase) AddAdsAccount(ctx context.Context, customerProjectID int32, accountList []string, platform string) error {
	// 检查当前操作用户是否有该项目权限
	err := c.checkProjectPermission(ctx, customerProjectID)
	if err != nil {
		return err
	}

	// 根据platform参数获取对应的资源类型
	resourceType := customerProjectSpaceData.GetResourceTypeByPlatform(platform)

	// 检查账号是否存在
	existAccounts, err := c.customerProRepo.GetCurrentProjectAdsAccountIds(ctx, customerProjectID, accountList, resourceType)
	if err != nil {
		return err
	}
	if len(existAccounts) > 0 {
		return ads_account.NewAdsAccountExistsErr(existAccounts)
	}
	// 根据平台获取账号对应的medium信息
	accountMediumMap := make(map[string]string)
	switch platform {
	case constants.AuthPlatformImpact:
		// Impact平台直接设置medium为"impact"
		for _, account := range accountList {
			accountMediumMap[account] = constants.AuthPlatformImpact
		}
	default:
		// 媒介账号需要获取签约信息
		accountSign, _, err := c.customerProRepo.GetAccountSignList(ctx, accountList, &customerProjectSpaceBiz.ProjectAccountListParams{}, false)
		if err != nil {
			return err
		}
		for _, item := range accountSign {
			accountMediumMap[item.AccountID] = enums.MediumTypeEnum_MediumType_name[item.MediumId]
		}
	}

	// 构建插入数据
	insertItems := make([]*model.CustomerProjectResource, 0)
	for _, account := range accountList {
		extraInfo, err := json.Marshal(map[string]string{
			"medium": accountMediumMap[account],
		})
		if err != nil {
			return err
		}
		insertItems = append(insertItems, &model.CustomerProjectResource{
			CustomerProjectID: customerProjectID,
			ResourceID:        account,
			ResourceType:      resourceType,
			ResourceExtraInfo: extraInfo,
		})
	}
	return c.repo.AddAdsAccount(ctx, insertItems)
}

// EnOrDisableAdsAccount 启用/禁用广告账号
func (c *AdsAccountCase) EnOrDisableAdsAccount(ctx context.Context, customerProjectID int32, accountList []string, status string) error {
	// 检查当前操作用户是否有该项目权限
	err := c.checkProjectPermission(ctx, customerProjectID)
	if err != nil {
		return err
	}
	return c.repo.EnOrDisableAdsAccount(ctx, customerProjectID, accountList, status)
}

// RemoveAdsAccount 移除广告账号
func (c *AdsAccountCase) RemoveAdsAccount(ctx context.Context, customerProjectID int32, accountList []string, platform string) error {
	// 检查当前操作用户是否有该项目权限
	err := c.checkProjectPermission(ctx, customerProjectID)
	if err != nil {
		return err
	}

	// 根据platform参数获取对应的资源类型
	resourceType := customerProjectSpaceData.GetResourceTypeByPlatform(platform)

	// 检查账号是否存在于当前项目中
	existAccounts, err := c.customerProRepo.GetCurrentProjectAdsAccountIds(ctx, customerProjectID, accountList, resourceType)
	if err != nil {
		return err
	}
	if len(existAccounts) == 0 {
		return ads_account.NewAdsAccountNotExistsErr(accountList)
	}

	return c.repo.RemoveAdsAccount(ctx, customerProjectID, accountList, resourceType)
}

func (c *AdsAccountCase) checkProjectPermission(ctx context.Context, customerProjectID int32) error {
	if err := c.customerProRepo.CheckCurrentUserProjectPermission(ctx, customerProjectID); err != nil {
		return err
	}
	return nil
}

func (c *AdsAccountCase) PreviewAdsAccount(ctx context.Context, accountList []string) ([]*customerProjectSpaceBiz.ProjectAccountSign, error) {
	// 获取签约信息
	accountSign, _, err := c.customerProRepo.GetAccountSignList(ctx, accountList, &customerProjectSpaceBiz.ProjectAccountListParams{}, false)
	if err != nil {
		return nil, err
	}
	return accountSign, nil
}

func (c *AdsAccountCase) OpenAPIGetEnableAdsAccountList(ctx context.Context, customerProjectNumber string) ([]*OpenAPIAccount, error) {
	return c.repo.OpenAPIGetEnableAdsAccountList(ctx, customerProjectNumber)
}

// GetAccountByAuth 根据授权获取账号
func (c *AdsAccountCase) GetAccountByAuth(ctx context.Context, params *GetAccountByAuthParams) ([]*AccountByAuthData, error) {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return nil, authError.QueryUserFail
	}

	// 根据项目配置获取授权信息
	configReq := &configBiz.GetCustomerProConfigReq{
		CustomerProjectID: params.CustomerProjectID,
		ConfigKey:         params.ConfigKey,
	}

	// 根据config_key判断如何获取授权ID
	switch params.ConfigKey {
	case "auth_impact":
		// 对于auth_impact，config_value直接就是授权表的主键ID
		if params.ConfigValue == "" {
			c.log.WithContext(ctx).Errorf("auth_impact类型的config_value不能为空")
			return []*AccountByAuthData{}, nil
		}

		// 将字符串转换为int32
		var err error
		authID, err := json.Number(params.ConfigValue).Int64()
		if err != nil {
			c.log.WithContext(ctx).Errorf("无效的授权ID: %s, err: %v", params.ConfigValue, err)
			return []*AccountByAuthData{}, nil
		}
		authorizationID = int32(authID)

	default:

	}
	// 根据平台调用对应的API获取账号信息
	switch authorization.AuthorizationPlatform {
	case constants.AuthPlatformImpact:
		return c.getImpactAccounts(ctx, authorization.AuthorizationConfig)
	case constants.AuthPlatformTikTok:
		return c.getTikTokAccounts(ctx, authorization.AuthorizationConfig)
	default:
		c.log.WithContext(ctx).Errorf("不支持的授权平台: %s", authorization.AuthorizationPlatform)
		return []*AccountByAuthData{}, nil
	}
}

// getImpactAccounts 获取Impact平台账号
func (c *AdsAccountCase) getImpactAccounts(ctx context.Context, authConfig string) ([]*AccountByAuthData, error) {
	var config map[string]interface{}
	if err := json.Unmarshal([]byte(authConfig), &config); err != nil {
		c.log.WithContext(ctx).Errorf("解析Impact授权配置失败: %v", err)
		return nil, err
	}

	accountSID, ok := config["AccountSID"].(string)
	if !ok || accountSID == "" {
		c.log.WithContext(ctx).Errorf("Impact配置中缺少AccountSID")
		return []*AccountByAuthData{}, nil
	}

	authToken, ok := config["AuthToken"].(string)
	if !ok || authToken == "" {
		c.log.WithContext(ctx).Errorf("Impact配置中缺少AuthToken")
		return []*AccountByAuthData{}, nil
	}

	// 调用Impact API获取账号信息
	impactAPI := third_party.NewImpactAPI("", "")
	response, err := impactAPI.ValidateAuth(ctx, accountSID, authToken)
	if err != nil {
		c.log.WithContext(ctx).Errorf("调用Impact API失败: %v", err)
		return nil, err
	}

	// 转换为统一格式
	accounts := []*AccountByAuthData{
		{
			AccountID:   response.Data.AccountID,
			AccountName: response.Data.AccountName,
		},
	}

	return accounts, nil
}

// getTikTokAccounts 获取TikTok平台账号
func (c *AdsAccountCase) getTikTokAccounts(ctx context.Context, authConfig string) ([]*AccountByAuthData, error) {
	var config map[string]interface{}
	if err := json.Unmarshal([]byte(authConfig), &config); err != nil {
		c.log.WithContext(ctx).Errorf("解析TikTok授权配置失败: %v", err)
		return nil, err
	}

	advertiserIDs, ok := config["advertiser_ids"].([]interface{})
	if !ok || len(advertiserIDs) == 0 {
		c.log.WithContext(ctx).Errorf("TikTok配置中缺少advertiser_ids")
		return []*AccountByAuthData{}, nil
	}

	// 转换为统一格式
	accounts := make([]*AccountByAuthData, 0, len(advertiserIDs))
	for _, id := range advertiserIDs {
		if advertiserID, ok := id.(string); ok {
			accounts = append(accounts, &AccountByAuthData{
				AccountID:   advertiserID,
				AccountName: advertiserID, // TikTok可能需要额外API调用获取名称
			})
		}
	}

	return accounts, nil
}
