package account

import (
	"encoding/json"
	"vision_hub/internal/biz/customer_project_space"
)

// GetAdsAccountListParams 获取广告账号列表参数
type GetAdsAccountListParams struct {
	CustomerProjectID int32
	AccountID         string
	Medium            []int32
	Status            string
	PageNum           int32
	PageSize          int32
	Platform          string
}

// AccountListItem 广告账号列表项
type AccountListItem struct {
	AccountID        string          `gorm:"column:account_id" json:"account_id"`
	AccountExtraInfo json.RawMessage `gorm:"column:account_extra_info" json:"account_extra_info"`
	Status           string          `json:"status"`
}

// AccountListItem 广告账号列表项
type AdsAccountListResp struct {
	AccountID   string `json:"ads_account_id"`
	AccountName string `json:"account_name"`
	Status      string `json:"status"`
	customer_project_space.ProjectAccountSign
}

type OpenAPIAccount struct {
	AdsAccountID string `gorm:"column:ads_account_id" json:"ads_account_id"`
	Medium       string `gorm:"column:medium" json:"medium"`
}
