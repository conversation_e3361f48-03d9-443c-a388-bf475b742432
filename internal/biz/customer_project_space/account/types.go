package account

import (
	"encoding/json"
	"vision_hub/internal/biz/customer_project_space"
)

// GetAdsAccountListParams 获取广告账号列表参数
type GetAdsAccountListParams struct {
	CustomerProjectID int32
	AccountID         string
	Medium            []int32
	Status            string
	PageNum           int32
	PageSize          int32
	Platform          string
}

// AccountListItem 广告账号列表项
type AccountListItem struct {
	AccountID        string          `gorm:"column:account_id" json:"account_id"`
	AccountExtraInfo json.RawMessage `gorm:"column:account_extra_info" json:"account_extra_info"`
	Status           string          `json:"status"`
}

// AccountListItem 广告账号列表项
type AdsAccountListResp struct {
	AccountID        string          `json:"ads_account_id"`
	AccountName      string          `json:"account_name"`
	Medium           string          `json:"medium"`
	Status           string          `json:"status"`
	AccountExtraInfo json.RawMessage `gorm:"column:account_extra_info" json:"account_extra_info"`
	customer_project_space.ProjectAccountSign
}

type OpenAPIAccount struct {
	AdsAccountID string `gorm:"column:ads_account_id" json:"ads_account_id"`
	Medium       string `gorm:"column:medium" json:"medium"`
}

// GetAccountByAuthParams 根据授权获取账号参数
type GetAccountByAuthParams struct {
	CustomerProjectID int32  `json:"customer_project_id"`
	ConfigKey         string `json:"config_key"`
	// ConfigValue 不需要从请求传入，会根据项目ID和ConfigKey查询得到
}

// AccountItem 账号项
type AccountItem struct {
	AccountID   string `json:"account_id"`
	AccountName string `json:"account_name"`
}

// AccountByAuthData 按配置分组的账号数据
type AccountByAuthData struct {
	ConfigKey    string         `json:"config_key"`
	AccountItems []*AccountItem `json:"account_item"`
}
