package account

import (
	"testing"
	pb "vision_hub/api/customer_project_space/v1"
)

func TestGetAccountByAuthParams(t *testing.T) {
	// 测试指定config_key的参数
	params1 := &GetAccountByAuthParams{
		CustomerProjectID: 1,
		ConfigKey:         "auth_impact",
	}

	if params1.CustomerProjectID != 1 {
		t.<PERSON><PERSON><PERSON>("Expected CustomerProjectID to be 1, got %d", params1.CustomerProjectID)
	}

	if params1.ConfigKey != "auth_impact" {
		t.<PERSON><PERSON><PERSON>("Expected ConfigKey to be 'auth_impact', got %s", params1.ConfigKey)
	}

	// 测试不指定config_key的参数（查询所有配置）
	params2 := &GetAccountByAuthParams{
		CustomerProjectID: 1,
		ConfigKey:         "", // 空字符串表示查询所有配置
	}

	if params2.CustomerProjectID != 1 {
		t.<PERSON><PERSON><PERSON>("Expected CustomerProjectID to be 1, got %d", params2.CustomerProjectID)
	}

	if params2.ConfigKey != "" {
		t.<PERSON><PERSON><PERSON>("Expected ConfigKey to be empty, got %s", params2.ConfigKey)
	}
}

func TestAccountByAuthData(t *testing.T) {
	// 测试AccountItem
	accountItem := &AccountItem{
		AccountID:   "test_account_123",
		AccountName: "Test Account",
	}

	if accountItem.AccountID != "test_account_123" {
		t.Errorf("Expected AccountID to be 'test_account_123', got %s", accountItem.AccountID)
	}

	if accountItem.AccountName != "Test Account" {
		t.Errorf("Expected AccountName to be 'Test Account', got %s", accountItem.AccountName)
	}

	// 测试AccountByAuthData
	accountByAuth := &AccountByAuthData{
		ConfigKey:    "auth_impact",
		AccountItems: []*AccountItem{accountItem},
	}

	if accountByAuth.ConfigKey != "auth_impact" {
		t.Errorf("Expected ConfigKey to be 'auth_impact', got %s", accountByAuth.ConfigKey)
	}

	if len(accountByAuth.AccountItems) != 1 {
		t.Errorf("Expected AccountItems length to be 1, got %d", len(accountByAuth.AccountItems))
	}

	if accountByAuth.AccountItems[0].AccountID != "test_account_123" {
		t.Errorf("Expected first AccountItem AccountID to be 'test_account_123', got %s", accountByAuth.AccountItems[0].AccountID)
	}
}

func TestGetAccountByAuthRequest(t *testing.T) {
	// 测试指定config_key的proto请求结构
	req1 := &pb.GetAccountByAuthRequest{
		CustomerProjectId: 1,
		ConfigKey:         "auth_impact",
	}

	if req1.CustomerProjectId != 1 {
		t.Errorf("Expected CustomerProjectId to be 1, got %d", req1.CustomerProjectId)
	}

	if req1.ConfigKey != "auth_impact" {
		t.Errorf("Expected ConfigKey to be 'auth_impact', got %s", req1.ConfigKey)
	}

	// 测试不指定config_key的proto请求结构（查询所有配置）
	req2 := &pb.GetAccountByAuthRequest{
		CustomerProjectId: 1,
		ConfigKey:         "", // 空字符串表示查询所有配置
	}

	if req2.CustomerProjectId != 1 {
		t.Errorf("Expected CustomerProjectId to be 1, got %d", req2.CustomerProjectId)
	}

	if req2.ConfigKey != "" {
		t.Errorf("Expected ConfigKey to be empty, got %s", req2.ConfigKey)
	}
}

func TestGetAccountByAuthResponse(t *testing.T) {
	// 测试proto响应结构
	resp := &pb.GetAccountByAuthResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "成功",
		Data: []*pb.AccountByAuthData{
			{
				ConfigKey: "auth_impact",
				AccountItem: []*pb.AccountItem{
					{
						AccountId:   "test_account_123",
						AccountName: "Test Account",
					},
				},
			},
		},
	}

	if resp.ErrId != 0 {
		t.Errorf("Expected ErrId to be 0, got %d", resp.ErrId)
	}

	if len(resp.Data) != 1 {
		t.Errorf("Expected Data length to be 1, got %d", len(resp.Data))
	}

	if resp.Data[0].ConfigKey != "auth_impact" {
		t.Errorf("Expected ConfigKey to be 'auth_impact', got %s", resp.Data[0].ConfigKey)
	}

	if len(resp.Data[0].AccountItem) != 1 {
		t.Errorf("Expected AccountItem length to be 1, got %d", len(resp.Data[0].AccountItem))
	}

	if resp.Data[0].AccountItem[0].AccountId != "test_account_123" {
		t.Errorf("Expected AccountId to be 'test_account_123', got %s", resp.Data[0].AccountItem[0].AccountId)
	}
}
