package account

import (
	"testing"
	pb "vision_hub/api/customer_project_space/v1"
)

func TestGetAccountByAuthParams(t *testing.T) {
	// 测试auth_impact类型的参数
	params1 := &GetAccountByAuthParams{
		CustomerProjectID: 1,
		Config<PERSON>ey:         "auth_impact",
		ConfigValue:       "123", // 直接是授权ID
	}

	if params1.CustomerProjectID != 1 {
		t.<PERSON>rrorf("Expected CustomerProjectID to be 1, got %d", params1.CustomerProjectID)
	}

	if params1.ConfigKey != "auth_impact" {
		t.Errorf("Expected ConfigKey to be 'auth_impact', got %s", params1.ConfigKey)
	}

	if params1.ConfigValue != "123" {
		t.<PERSON>rrorf("Expected ConfigValue to be '123', got %s", params1.ConfigValue)
	}

	// 测试其他类型的参数
	params2 := &GetAccountByAuthParams{
		CustomerProjectID: 1,
		ConfigKey:         "other_config",
		ConfigValue:       `{"authorization_id": 456}`,
	}

	if params2.ConfigKey != "other_config" {
		t.Errorf("Expected ConfigKey to be 'other_config', got %s", params2.ConfigKey)
	}
}

func TestAccountByAuthData(t *testing.T) {
	account := &AccountByAuthData{
		AccountID:   "test_account_123",
		AccountName: "Test Account",
	}

	if account.AccountID != "test_account_123" {
		t.Errorf("Expected AccountID to be 'test_account_123', got %s", account.AccountID)
	}

	if account.AccountName != "Test Account" {
		t.Errorf("Expected AccountName to be 'Test Account', got %s", account.AccountName)
	}
}

func TestGetAccountByAuthRequest(t *testing.T) {
	// 测试proto请求结构
	req := &pb.GetAccountByAuthRequest{
		CustomerProjectId: 1,
		ConfigKey:         "impact_authorization",
		ConfigValue:       `{"authorization_id": 123}`,
	}

	if req.CustomerProjectId != 1 {
		t.Errorf("Expected CustomerProjectId to be 1, got %d", req.CustomerProjectId)
	}

	if req.ConfigKey != "impact_authorization" {
		t.Errorf("Expected ConfigKey to be 'impact_authorization', got %s", req.ConfigKey)
	}
}

func TestGetAccountByAuthResponse(t *testing.T) {
	// 测试proto响应结构
	resp := &pb.GetAccountByAuthResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "成功",
		Data: []*pb.AccountByAuthData{
			{
				AccountId:   "test_account_123",
				AccountName: "Test Account",
			},
		},
	}

	if resp.ErrId != 0 {
		t.Errorf("Expected ErrId to be 0, got %d", resp.ErrId)
	}

	if len(resp.Data) != 1 {
		t.Errorf("Expected Data length to be 1, got %d", len(resp.Data))
	}

	if resp.Data[0].AccountId != "test_account_123" {
		t.Errorf("Expected AccountId to be 'test_account_123', got %s", resp.Data[0].AccountId)
	}
}
