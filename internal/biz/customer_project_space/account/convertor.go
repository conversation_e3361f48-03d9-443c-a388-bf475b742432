package account

import (
	"git.domob-inc.cn/bluevision/bv_interface_go/client_portal_server/enums"
	"strconv"
	pb "vision_hub/api/customer_project_space/v1"
	customerProjectSpaceBiz "vision_hub/internal/biz/customer_project_space"
	"vision_hub/internal/biz/internal_service/client_portal"
)

func ConvertToPbAdsAccountSignInfo(detail []*customerProjectSpaceBiz.ProjectAccountSign) []*pb.AdsAccountListItem {
	res := make([]*pb.AdsAccountListItem, 0)
	for _, v := range detail {
		ourSideEntityId, _ := strconv.Atoi(v.OurSideEntity)
		res = append(res, &pb.AdsAccountListItem{
			AdsAccountId: v.AccountID,
			Medium:       enums.MediumTypeEnum_MediumType_name[v.MediumId],
			SignInfo: &pb.SignInfo{
				AeEmail:           v.AEEmail,
				AeName:            v.AEName,
				AeId:              v.AEID,
				CompanyId:         v.CompanyID,
				OurSideEntity:     v.OurSideEntity,
				SaleEmail:         v.SaleEmail,
				SaleId:            v.SaleID,
				SaleName:          v.SaleName,
				Settlement:        v.Settlement,
				SettlementId:      v.SettlementID,
				SignId:            v.SignID,
				SignSaleEmail:     v.SignSaleEmail,
				SignSaleId:        v.SignSaleID,
				SignSaleName:      v.SignSaleName,
				CompanyName:       client_portal.CompanyNameMap[v.CompanyID],
				OurSideEntityName: enums.OurSideEntityEnum_OurSideEntity_name[int32(ourSideEntityId)],
			},
		})
	}
	return res
}

func ConvertToPbAdsAccountList(data []*AdsAccountListResp) []*pb.AdsAccountListItem {
	res := make([]*pb.AdsAccountListItem, 0)
	for _, v := range data {
		ourSideEntityId, _ := strconv.Atoi(v.OurSideEntity)
		res = append(res, &pb.AdsAccountListItem{
			AdsAccountId: v.AccountID,
			Medium:       enums.MediumTypeEnum_MediumType_name[v.MediumId],
			Status:       v.Status,
			SignInfo: &pb.SignInfo{
				AeEmail:           v.AEEmail,
				AeName:            v.AEName,
				AeId:              v.AEID,
				CompanyId:         v.CompanyID,
				OurSideEntity:     v.OurSideEntity,
				SaleEmail:         v.SaleEmail,
				SaleId:            v.SaleID,
				SaleName:          v.SaleName,
				Settlement:        v.Settlement,
				SettlementId:      v.SettlementID,
				SignId:            v.SignID,
				SignSaleEmail:     v.SignSaleEmail,
				SignSaleId:        v.SignSaleID,
				SignSaleName:      v.SignSaleName,
				CompanyName:       client_portal.CompanyNameMap[v.CompanyID],
				OurSideEntityName: enums.OurSideEntityEnum_OurSideEntity_name[int32(ourSideEntityId)],
			},
		})
	}
	return res
}

func ConvertToPbAccountList(data []*AdsAccountListResp) []*pb.AccountListItem {
	res := make([]*pb.AccountListItem, 0)
	for _, v := range data {
		res = append(res, &pb.AccountListItem{
			AccountId:        v.AccountID,
			AccountName:      v.AccountName,
			Medium:           v.Medium,
			Status:           v.Status,
			AccountExtraInfo: v.AccountExtraInfo,
		})
	}
	return res
}

func ConvertAdsAccountToOpenAPIPb(data []*OpenAPIAccount) []*pb.OpenApiGetAdsAccountListData {
	res := make([]*pb.OpenApiGetAdsAccountListData, 0)
	adsAccountMap := make(map[string][]string)
	for _, v := range data {
		if adsAccountIds, ok := adsAccountMap[v.Medium]; ok {
			adsAccountMap[v.Medium] = append(adsAccountIds, v.AdsAccountID)
		} else {
			adsAccountMap[v.Medium] = []string{v.AdsAccountID}
		}
	}

	for k, v := range adsAccountMap {
		res = append(res, &pb.OpenApiGetAdsAccountListData{
			Medium:        k,
			AdsAccountIds: v,
		})
	}

	return res
}
