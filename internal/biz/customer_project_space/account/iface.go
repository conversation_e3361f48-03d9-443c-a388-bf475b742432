package ads_account

import (
	"context"
	customerProjectSpaceBiz "vision_hub/internal/biz/customer_project_space"
	"vision_hub/internal/data/customer_project_space/model"
)

// IAdsAccountRepo 广告账号仓储接口
type IAdsAccountRepo interface {
	// GetAdsAccountList 获取广告账号列表
	GetAdsAccountList(ctx context.Context, params *GetAdsAccountListParams) ([]*AccountListItem, *customerProjectSpaceBiz.Pagination, error)

	// AddAdsAccount 添加广告账号
	AddAdsAccount(ctx context.Context, accountList []*model.CustomerProjectResource) error

	// EnOrDisableAdsAccount 启用/禁用广告账号
	EnOrDisableAdsAccount(ctx context.Context, customerProjectID int32, accountList []string, status string) error

	// RemoveAdsAccount 移除广告账号
	RemoveAdsAccount(ctx context.Context, customerProjectID int32, accountList []string, resourceType string) error

	// OpenAPIGetEnableAdsAccountList 获取启用的广告账号列表
	OpenAPIGetEnableAdsAccountList(ctx context.Context, customerProjectNumber string) ([]*OpenAPIAccount, error)
}
