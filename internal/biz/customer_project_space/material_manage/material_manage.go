package material_manage

import (
	"bytes"
	"context"
	"database/sql"
	"fmt"
	"github.com/xuri/excelize/v2"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	pb "vision_hub/api/customer_project_space/v1"
	commonBiz "vision_hub/internal/biz/common"
	customerProjectBiz "vision_hub/internal/biz/customer_project"
	customerProjectSpaceBiz "vision_hub/internal/biz/customer_project_space"
	"vision_hub/internal/conf"
	"vision_hub/internal/constants"
	customerProSpaceCons "vision_hub/internal/constants/customer_project_space"
	datacommot "vision_hub/internal/data/common"
	"vision_hub/internal/data/customer_project_space/model"
	authError "vision_hub/internal/errors/auth"
	"vision_hub/internal/errors/cos"
	customerProErr "vision_hub/internal/errors/customer_project"
	materManageErr "vision_hub/internal/errors/customer_project_space/material_manage"
	redisClient "vision_hub/internal/infrastructure/redis"
	"vision_hub/internal/infrastructure/third_party"
	"vision_hub/internal/middleware"
	"vision_hub/internal/util"
)

var TIKTOK_TOKEN_REDIS_KEY = "tiktok_ads_token"

type MaterialManageCase struct {
	bizConf         *conf.BizConf
	log             *commonBiz.BizLogHelper
	Data            *datacommot.Data
	repo            IMaterialManageRepo
	commonRepo      commonBiz.ICommonRepo
	thirdPartyRepo  commonBiz.ThirdPartyRepo
	customerRepo    customerProjectBiz.ICustomerProjectRepo
	customerProRepo customerProjectSpaceBiz.ICustomerProjectCommRepo
	redisClient     *redisClient.ClientRedis
}

func NewMaterialManageCase(
	bizConf *conf.BizConf,
	log *commonBiz.BizLogHelper,
	Data *datacommot.Data, repo IMaterialManageRepo,
	thirdPartyRepo commonBiz.ThirdPartyRepo,
	customerRepo customerProjectBiz.ICustomerProjectRepo,
	commonRepo commonBiz.ICommonRepo,
	customerProRepo customerProjectSpaceBiz.ICustomerProjectCommRepo,
	redisClient *redisClient.ClientRedis) *MaterialManageCase {
	return &MaterialManageCase{
		bizConf:         bizConf,
		log:             log,
		Data:            Data,
		repo:            repo,
		thirdPartyRepo:  thirdPartyRepo,
		customerRepo:    customerRepo,
		commonRepo:      commonRepo,
		customerProRepo: customerProRepo,
		redisClient:     redisClient,
	}
}

func (c *MaterialManageCase) GetMaterialList(ctx context.Context, params *GetMaterialListParams) ([]*MaterialListItem, *Pagination, error) {
	items, page, err := c.repo.GetMaterialList(ctx, params)
	if err != nil {
		return nil, nil, err
	}
	return items, page, nil
}

func (c *MaterialManageCase) GetMaterialDetail(ctx context.Context, id int32) (*Material, error) {
	return c.repo.GetMaterialDetail(ctx, id)
}

func (c *MaterialManageCase) DelMaterial(ctx context.Context, ids []int32, customerProId int32) error {
	currentUser, err := middleware.GetUserInfo(ctx)
	if err != nil {
		return err
	}
	// 权限校验
	if err := c.customerProRepo.CheckCurrentUserProjectPermission(ctx, customerProId); err != nil {
		return err
	}
	// todo 删除素材权限校验
	return c.Data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		for _, id := range ids {
			material, err := c.repo.GetMaterialDetail(ctx, id)
			if err != nil {
				return err
			}
			// 删除后对oss文件重命名并且留存，文件名：deleted_xxx(user_name)_原文件名
			fileUrl := GenerateDeletedFilePath(material.MaterialURL, currentUser.Email)
			if err := c.repo.DelMaterial(ctx, id, fileUrl); err != nil {
				return err
			}
			if err = c.thirdPartyRepo.MoveOssFile(ctx, material.MaterialURL, fileUrl); err != nil {
				c.log.Errorf("oss异常: %v", err)
				return cos.CosErr
			}

		}
		return nil
	})

}

func (c *MaterialManageCase) UploadMaterialBatch(ctx context.Context, request *pb.UploadMaterialBatchRequest) error {
	// 获取项目信息
	customerProject, err := c.customerRepo.GetCustomerProjectBase(ctx, request.CustomerProjectId)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取客户项目信息失败: %v", err)
		return customerProErr.ProjectGetFail
	}
	if customerProject == nil || customerProject.IsDeleted == 1 {
		return customerProErr.ProjectEmpty
	}
	materialTagRelMap := make(map[string]*pb.UploadMaterialBatchCheckItem)
	// 获取项目素材信息
	customerProjectMaterials, err := c.customerRepo.GetCustomerProjectMaterial(ctx, request.CustomerProjectId)
	if err != nil {
		return err
	}
	var materialMd5Map map[string]bool
	if len(customerProjectMaterials) > 0 {
		materialMd5Map = make(map[string]bool)
		for _, material := range customerProjectMaterials {
			materialMd5Map[material.MaterialFilenameMd5] = true
		}
	}
	if request.MaterialTagsUri != "" {
		// 1. 检查素材
		materialTagRel, err := c.HasFileCorr(ctx, materialMd5Map, request.MaterialTagsUri, request.MaterialUri)
		if err != nil {
			return err
		}
		for _, item := range materialTagRel {
			if !item.Status {
				c.log.WithContext(ctx).Errorf("文件校验失败: %+v", item)
				return materManageErr.MaterialManageValidFileFail
			}
			materialTagRelMap[item.FileName] = item
		}
	}

	// 2. 获取用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}
	materials := make([]*model.Material, 0)
	//  插入素材表
	for _, url := range request.MaterialUri {
		var tags []byte
		var language string
		filename := filepath.Base(url)
		if item, ok := materialTagRelMap[filepath.Base(url)]; ok {
			tags, _ = util.MarshalNonEmptySlice(item.Tags)
			filename = item.FileName
			language = item.Language
		}
		// 检查是否在数据库中存在
		if materialMd5Map[util.MD5Hash(filename)] {
			c.log.Warnf("文件名重复，上传时跳过: %s", filename)
			continue
		}
		materials = append(materials, &model.Material{
			CreateUserID: int32(userInfo.UserID),
			MaterialName: filename,
			MaterialURL:  url,
			MaterialTags: tags,
			Type:         GetMaterialType(filename),
			Language:     language,
			Status:       customerProSpaceCons.MATERIAL_UPLOAD_COS_PENDING,
		})
	}
	if len(materials) == 0 {
		return materManageErr.MaterialManageRepeatFail
	}

	// 4. 批量保存到数据库
	return c.repo.UploadMaterialBatchTx(ctx, request.CustomerProjectId, materials)
}

// ProcessSingleMaterial 处理单个素材
func (c *MaterialManageCase) ProcessSingleMaterial(ctx context.Context, userInfo *middleware.UserBaseInfo, url string, materialTagRel []*pb.UploadMaterialBatchCheckItem) (*model.Material, error) {
	// 查找匹配的文件名
	var item *pb.UploadMaterialBatchCheckItem
	for _, rel := range materialTagRel {
		if rel.FileName == filepath.Base(url) {
			item = rel
			break
		}
	}
	if item == nil {
		return nil, fmt.Errorf("未找到匹配的文件名")
	}

	materialTags, _ := util.MarshalNonEmptySlice(item.Tags)

	// 处理视频文件
	if util.IsVideoFile(url) {
		return c.ProcessVideoMaterial(ctx, userInfo, url, item, materialTags)
	}

	// 处理图片文件
	if util.IsImageFile(url) {
		return c.ProcessImageMaterial(ctx, userInfo, url, item, materialTags)
	}

	return nil, fmt.Errorf("不支持的文件类型")
}

// ProcessSingleOssMaterial 处理单个素材
func (c *MaterialManageCase) ProcessSingleOssMaterial(ctx context.Context, material model.Material) (*model.Material, error) {
	// 查找匹配的文件名
	item := &pb.UploadMaterialBatchCheckItem{
		FileName: material.MaterialName,
		// Tags: material.MaterialTags,
		Language: material.Language,
	}

	userInfo := &middleware.UserBaseInfo{
		UserID: int64(material.CreateUserID),
	}
	// 处理视频文件
	if util.IsVideoFile(material.MaterialURL) {
		return c.ProcessVideoMaterial(ctx, userInfo, material.MaterialURL, item, material.MaterialTags)
	}

	// 处理图片文件
	if util.IsImageFile(material.MaterialURL) {
		return c.ProcessImageMaterial(ctx, userInfo, material.MaterialURL, item, material.MaterialTags)
	}

	return nil, fmt.Errorf("不支持的文件类型")
}

// ProcessVideoMaterial 处理视频素材
func (c *MaterialManageCase) ProcessVideoMaterial(ctx context.Context, userInfo *middleware.UserBaseInfo, url string, item *pb.UploadMaterialBatchCheckItem, materialTags []byte) (*model.Material, error) {
	// 获取视频信息
	mediaInfo, err := c.thirdPartyRepo.GetCosMediaInfo(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("获取视频信息失败: %v", err)
	}

	// 生成首帧并上传
	firstFrameCoSPath, err := c.thirdPartyRepo.GetCosVideoFirstFrameAndUpload(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("生成首帧失败: %v", err)
	}

	// 获取Header信息
	metadata, err := c.thirdPartyRepo.GetObjectHeader(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("获取Header信息失败: %v", err)
	}

	materialExtraInfoByte, _ := util.MarshalNonEmptyForObj(mediaInfo)
	videoWidth, _ := strconv.ParseInt(mediaInfo.Stream.Video[0].Width, 10, 64)
	videoHeight, _ := strconv.ParseInt(mediaInfo.Stream.Video[0].Height, 10, 64)
	filenameMd5 := util.MD5Hash(item.FileName)
	return &model.Material{
		CreateUserID:      int32(userInfo.UserID),
		MaterialName:      item.FileName,
		MaterialURL:       url,
		MaterialExtraInfo: materialExtraInfoByte,
		MaterialTags:      materialTags,
		MaterialTime:      mediaInfo.Stream.Video[0].Duration,
		MaterialWidth:     int32(videoWidth),
		MaterialHigh:      int32(videoHeight),
		MaterialCoverURL:  firstFrameCoSPath,
		Ratio:             CalculateRatio(videoWidth, videoHeight),
		Language:          item.Language,
		Size:              mediaInfo.Format.Size,
		Type:              customerProSpaceCons.MATERIAL_TYPE_VIDEO,
		// Status:              customerProSpaceCons.MATERIAL_UPLOAD_COS,
		MaterialMd5:         strings.Trim(metadata.Get("Etag"), `"`),
		MaterialFilenameMd5: filenameMd5,
		IsDeleted:           0,
	}, nil
}

// ProcessImageMaterial 处理图片素材
func (c *MaterialManageCase) ProcessImageMaterial(ctx context.Context, userInfo *middleware.UserBaseInfo, url string, item *pb.UploadMaterialBatchCheckItem, materialTags []byte) (*model.Material, error) {
	// 获取图片信息
	imageInfo, err := c.thirdPartyRepo.GetCosImageInfo(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("获取图片信息失败: %v", err)
	}

	metadata, err := c.thirdPartyRepo.GetObjectHeader(ctx, url)
	if err != nil {
		return nil, fmt.Errorf("获取Header信息失败: %v", err)
	}

	materialExtraInfoByte, _ := util.MarshalNonEmptyForObj(imageInfo)
	filenameMd5 := util.MD5Hash(item.FileName)
	return &model.Material{
		CreateUserID:      int32(userInfo.UserID),
		MaterialName:      item.FileName,
		MaterialURL:       url,
		MaterialExtraInfo: materialExtraInfoByte,
		MaterialTags:      materialTags,
		MaterialTime:      "",
		MaterialWidth:     int32(imageInfo.OriginalInfo.ImageInfo.Width),
		MaterialHigh:      int32(imageInfo.OriginalInfo.ImageInfo.Height),
		Ratio:             CalculateRatio(int64(imageInfo.OriginalInfo.ImageInfo.Width), int64(imageInfo.OriginalInfo.ImageInfo.Height)),
		MaterialCoverURL:  "",
		Language:          item.Language,
		Size:              strings.Trim(metadata.Get("Content-Length"), `"`),
		Type:              customerProSpaceCons.MATERIAL_TYPE_IMAGE,
		//Status:              customerProSpaceCons.MATERIAL_UPLOAD_COS,
		MaterialMd5:         strings.Trim(imageInfo.OriginalInfo.ETag, `"`),
		MaterialFilenameMd5: filenameMd5,
		IsDeleted:           0,
	}, nil
}

func (c *MaterialManageCase) GetMaterialTags(ctx context.Context, customerProId int32) ([]string, error) {
	return c.repo.GetMaterialTags(ctx, customerProId)
}

func GenerateDeletedFilePath(sourceFilePath string, userEmail string) string {
	filename := strings.Split(sourceFilePath, "/")
	filename[len(filename)-1] = fmt.Sprintf("deleted_%s_%s", userEmail, filename[len(filename)-1])
	return strings.Join(filename, "/")
}

func (c *MaterialManageCase) UploadMaterialBatchCheck(ctx context.Context, request *pb.UploadMaterialBatchRequest) ([]*pb.UploadMaterialBatchCheckItem, error) {
	// 获取项目信息
	customerProject, err := c.customerRepo.GetCustomerProjectBase(ctx, request.CustomerProjectId)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取客户项目信息失败: %v", err)
		return nil, customerProErr.ProjectGetFail
	}

	// 获取项目素材信息
	customerProjectMaterials, err := c.customerRepo.GetCustomerProjectMaterial(ctx, request.CustomerProjectId)
	if err != nil {
		return nil, err
	}
	var materialMd5Map map[string]bool
	if len(customerProjectMaterials) > 0 {
		materialMd5Map = make(map[string]bool)
		for _, material := range customerProjectMaterials {
			materialMd5Map[material.MaterialFilenameMd5] = true
		}
	}

	if customerProject != nil && customerProject.IsDeleted == 0 {
		return c.HasFileCorr(ctx, materialMd5Map, request.MaterialTagsUri, request.MaterialUri)
	}

	return nil, customerProErr.ProjectEmpty
}

// HasFileCorr 校验文件标签对应关系是否存在
func (c *MaterialManageCase) HasFileCorr(ctx context.Context, materialMd5Map map[string]bool, materialTagUrl string, materialsUrl []string) ([]*pb.UploadMaterialBatchCheckItem, error) {
	// 获取打标文件内容
	fileReader, err := c.thirdPartyRepo.GetFileByCos(ctx, materialTagUrl)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取打标文件内容失败: %v", err)
		return nil, materManageErr.MaterialManageGetResourceFileFail
	}
	languageMapping, err := c.commonRepo.GetReverseSysDictMapping(ctx, "language")
	if err != nil {
		return nil, err
	}
	// 解析 Excel 文件
	materials, err := ParseExcel(fileReader, languageMapping["language"])
	if err != nil {
		c.log.WithContext(ctx).Errorf("解析 Excel 文件失败: %v", err)
		return nil, materManageErr.MaterialManageParseFileFail
	}

	// 验证素材
	return ValidateMaterials(materialMd5Map, materials, materialsUrl), nil
}

func ValidateMaterials(materialMd5Map map[string]bool, materials map[string]map[string]interface{}, fileUrls []string) []*pb.UploadMaterialBatchCheckItem {
	results := make([]*pb.UploadMaterialBatchCheckItem, 0)
	processed := make(map[string]bool)

	var fileNames []string
	for _, url := range fileUrls {
		fileName := strings.Split(url, "/")[len(strings.Split(url, "/"))-1]
		fileNames = append(fileNames, fileName)
	}
	for _, fileName := range fileNames {
		result := &pb.UploadMaterialBatchCheckItem{
			FileName: fileName,
			Status:   true,
		}

		// 检查是否在数据库中存在
		if materialMd5Map[util.MD5Hash(fileName)] {
			result.Status = false
			result.ErrMsg = "项目中已存在同名素材，请修改素材名称"
			results = append(results, result)
			continue
		}
		// 检查是否已处理过（重复）
		if _, exists := processed[fileName]; exists {
			result.Status = false
			result.ErrMsg = "Excel中素材名重复，请修改"
			results = append(results, result)
			continue
		}
		processed[fileName] = true

		// 检查素材是否存在
		material, exists := materials[fileName]
		if !exists {
			result.Status = false
			result.ErrMsg = "Excel中缺少该素材记录，请补充或移除该素材文件"
			results = append(results, result)
			continue
		}

		// 检查语言和标签
		language, _ := material["language"].(string)
		tags, _ := material["tags"].([]string)

		if language == "" || len(tags) == 0 {
			result.Status = false
			result.ErrMsg = "缺少标签或语言信息，请在Excel中补充"
			results = append(results, result)
			continue
		}

		// 成功的情况
		result.Language = language
		result.Tags = tags
		results = append(results, result)
	}

	//	校验结果排序，校验失败的数据往前放
	sort.Slice(results, func(i, j int) bool {
		return !results[i].Status && results[j].Status
	})

	return results
}

func ParseExcel(fileReader *bytes.Reader, languageMapping map[string]string) (map[string]map[string]interface{}, error) {
	f, err := excelize.OpenReader(fileReader)
	if err != nil {
		return nil, fmt.Errorf("failed to open excel file: %v", err)
	}
	defer f.Close()

	// 获取第一个 sheet 的所有行
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, fmt.Errorf("failed to get rows from sheet: %v", err)
	}

	// 检查是否有数据行
	if len(rows) < 2 {
		return nil, fmt.Errorf("excel file has no data rows")
	}

	// 解析数据
	materials := make(map[string]map[string]interface{})
	header := rows[0]
	duplicateCheck := make(map[string]bool)

	for i, row := range rows[1:] {
		// 跳过空行
		if len(row) == 0 {
			continue
		}

		// 检查素材名是否存在
		if len(row) < 1 || strings.TrimSpace(row[0]) == "" {
			continue
		}
		materialName := strings.TrimSpace(row[0])

		// 检查是否重复
		if _, exists := duplicateCheck[materialName]; exists {
			continue // 跳过重复项
		}
		duplicateCheck[materialName] = true

		// 解析语言和标签
		language := ""
		if len(row) >= 2 {
			language = languageMapping[strings.TrimSpace(row[1])]
		}

		tags := make([]string, 0)
		for j := 2; j < len(row) && j < len(header); j++ {
			if strings.TrimSpace(row[j]) != "" {
				tags = append(tags, strings.TrimSpace(row[j]))
			}
		}

		// 存储素材信息
		materials[materialName] = map[string]interface{}{
			"row":      i + 2, // Excel 行号从1开始，且第一行是标题
			"language": language,
			"tags":     tags,
		}
	}

	return materials, nil
}

// UploadMaterialToMedium 上传素材到媒体库
func (c *MaterialManageCase) UploadMaterialToMedium(ctx context.Context, request *UploadMaterialToMediumReq) error {
	// TODO 项目权限校验、素材权限校验
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		return err
	}
	taskId := util.GenerateUuidNumber()
	materialUploadTasks := make([]*model.MaterialUploadLog, 0)
	for _, adsAccount := range request.AdsAccountList {
		for _, materialId := range request.MaterialIds {
			materialUploadTasks = append(materialUploadTasks, &model.MaterialUploadLog{
				AccountID:         adsAccount,
				Medium:            request.Medium,
				CustomerProjectID: request.CustomerProjectId,
				TaskID:            taskId,
				TaskStatus:        customerProSpaceCons.MATERIAL_MEDIUM_UPLOAD_SUBMIT,
				MaterialID:        materialId,
				CreateUserID:      int32(userInfo.UserID),
			})
		}
	}
	err = c.repo.AddUploadMaterialToMediumTask(ctx, materialUploadTasks)
	if err != nil {
		return err
	}
	return nil
}

// DealUploadMaterialToMediumTask 处理素材媒体上传Cron任务
func (c *MaterialManageCase) DealUploadMaterialToMediumTask(ctx context.Context) error {
	// 1.获取所有待处理的任务
	tasks, err := c.repo.GetMaterialUploadTask(ctx, customerProSpaceCons.MATERIAL_MEDIUM_UPLOAD_SUBMIT)
	if err != nil {
		c.log.Errorf("获取素材媒体上传任务失败: %v", err)
		return err
	}
	c.log.Infof("待处理素材媒体上传任务数量: %v", len(tasks))

	// 2.1 更新任务状态
	taskIds := make([]int32, 0)
	for _, task := range tasks {
		taskIds = append(taskIds, task.MaterialUploadLog.ID)
	}
	if len(taskIds) > 0 {
		// 2.2 更新任务状态
		err = c.repo.UpdateMaterialUploadTaskStatus(ctx, taskIds, customerProSpaceCons.MATERIAL_MEDIUM_UPLOAD_PROCESSING)
		if err != nil {
			c.log.Errorf(" 更新素材媒体上传任务Processing状态失败: %v", err)
			return err
		}
	}

	// 2.并行处理
	var wg sync.WaitGroup
	for _, task := range tasks {
		// 2.处理任务
		wg.Add(1)
		go func(task *UploadMaterialToMediumTask) {
			defer wg.Done()
			c.processUploadToMediumTask(ctx, task)
		}(task)
	}
	wg.Wait()
	return nil
}

// DealUploadMaterialOSSToMediumTask 处理素材媒体上传oss任务
func (c *MaterialManageCase) DealUploadMaterialOSSToMediumTask(ctx context.Context) error {
	// 1.获取所有待处理的任务
	taskInfos, err := c.repo.GetMaterialUploadOssTask(ctx, customerProSpaceCons.MATERIAL_UPLOAD_COS_PENDING)
	if err != nil {
		c.log.Errorf("获取素材媒体上传OSS任务失败: %v", err)
		return err
	}
	c.log.Infof("待处理素材媒体上传OSS任务数量: %v", len(taskInfos))

	// 3. 并发处理每个素材
	var (
		wg  sync.WaitGroup
		sem = make(chan struct{}, 3) // 信号量，最多允许3个goroutine同时执行
	)
	for _, task := range taskInfos {
		wg.Add(1)
		go func(task model.Material) {
			defer wg.Done()
			sem <- struct{}{}        // 获取信号量
			defer func() { <-sem }() // 释放信号量
			task.Status = customerProSpaceCons.MATERIAL_UPLOAD_COS_PROCESSING
			// 更新任务状态
			err = c.repo.UpdateMaterialUploadOssTask(ctx, &task)
			// 重试三次次数
			var material *model.Material
			for i := 0; i < 3; i++ {
				material, err = c.ProcessSingleOssMaterial(ctx, task)
				if err != nil {
					task.Status = customerProSpaceCons.MATERIAL_UPLOAD_COS_Fail
				} else {
					break
				}
			}
			if err == nil {
				material.ID = task.ID
				material.Status = customerProSpaceCons.MATERIAL_UPLOAD_COS_SUCCESS
			} else {
				material.Remark = fmt.Sprintf("处理素材 %s 失败: %v", task.MaterialURL, err)
			}
			// 更新任务状态
			err = c.repo.UpdateMaterialUploadOssTask(ctx, material)
			if err != nil {
				c.log.Errorf("任务ID: %v, 更新素材OSS上传任务状态失败: %v", task.ID, err)
				return
			}
		}(*task)
	}

	// 等待所有任务完成
	go func() {
		wg.Wait()
	}()

	return nil
}

func (c *MaterialManageCase) processUploadToMediumTask(ctx context.Context, task *UploadMaterialToMediumTask) {
	taskStatus := customerProSpaceCons.MATERIAL_MEDIUM_UPLOAD_FAIL
	var mediumMaterialId string
	var err error
	defer func() {
		if v := recover(); v != nil {
			c.log.Errorf("PANIC=%v", v)
		}
		var errMsg string
		if err != nil {
			errMsg = err.Error()
		}
		uploadTime := sql.NullTime{}
		_ = uploadTime.Scan(time.Now())
		// 2.1 更新任务状态
		c.log.Infof("任务ID: %v, task_id: %v, 素材媒体上传任务%v, errMsg: %v", task.MaterialUploadLog.ID, task.TaskID, taskStatus, errMsg)
		err = c.repo.UpdateMaterialUploadTask(ctx, task.MaterialUploadLog.ID, &model.MaterialUploadLog{
			TaskStatus:       taskStatus,
			MediumMaterialID: mediumMaterialId,
			MediumUploadTime: uploadTime,
			UploadError:      errMsg,
		})
		if err != nil {
			c.log.Errorf("任务ID: %v, task_id: %v, 更新素材媒体上传任务Finish状态失败: %v", task.MaterialUploadLog.ID, task.TaskID, err)
		}
	}()

	// 3.上传
	c.log.Infof("任务ID: %v, task_id: %v, medium: %v, ads_account: %v, material: %v, 开始上传素材到媒体", task.MaterialUploadLog.ID, task.TaskID, task.Medium, task.MaterialUploadLog.AccountID, task.MaterialName)
	mediumMaterialId, err = c.uploadMaterialToMedium(ctx, task)
	if err != nil {
		c.log.Errorf("任务ID: %v, task_id: %v, 素材上传失败: %v", task.MaterialUploadLog.ID, task.TaskID, err)
		return
	}
	taskStatus = customerProSpaceCons.MATERIAL_MEDIUM_UPLOAD_SUCCESS
}

// uploadMaterialToMedium 上传素材到媒体
func (c *MaterialManageCase) uploadMaterialToMedium(ctx context.Context, task *UploadMaterialToMediumTask) (string, error) {
	var err error
	var mediumMaterialId string
	fileUrl, err := c.thirdPartyRepo.GenerateSignedURL(ctx, task.MaterialURL, 1*time.Hour)
	if err != nil {
		return "", err
	}
	// 分媒体
	switch task.Medium {
	// 目前只支持tiktok
	case constants.MaterialMediumTikTok:
		mediumMaterialId, err = c.TiktokMaterialUpload(ctx, task, fileUrl)
		if err != nil {
			return "", err
		}
	default:
		return "", materManageErr.NewNotSupportUploadMedium(task.Medium)
	}
	return mediumMaterialId, nil
}

func (c *MaterialManageCase) TiktokMaterialUpload(ctx context.Context, task *UploadMaterialToMediumTask, fileUrl string) (string, error) {
	var err error
	var uploadStatus bool
	var mediumMaterialId string
	var comResp third_party.TikTokCommonResponse
	var videoResp third_party.VideoFixUploadResponse
	var imageResp third_party.TiktokImageUploadResponse
	tiktokAPI := third_party.NewTikTokAPI(c.bizConf.TiktokVideo.BaseUrl, c.bizConf.TiktokVideo.Token, c.bizConf.SysConf.Proxy)
	for _, token := range constants.TIKTOK_TOKEN_PRIORITY {
		// 根据优先级获取token
		bizToken := c.getTikTokTokenByPriority(ctx, token, task.AccountID)
		// 开始上传，最多重试3次
		maxRetries := 5
		for i := 0; i < maxRetries; i++ {
			switch task.Material.Type {
			// video类型上传
			case customerProSpaceCons.MATERIAL_TYPE_VIDEO:
				videoResp, err = tiktokAPI.VideoFixUpload(ctx, third_party.UploadParam{
					FileName:     task.MaterialName,
					FileURL:      fileUrl,
					AdvertiserID: task.AccountID,
					TiktokToken:  bizToken,
				})
				comResp = videoResp.TikTokCommonResponse
				if videoResp.VideoFixUploadResponseData != nil && len(videoResp.VideoFixUploadResponseData) > 0 {
					mediumMaterialId = videoResp.VideoFixUploadResponseData[0].VideoId
				}
			// image类型上传
			case customerProSpaceCons.MATERIAL_TYPE_IMAGE:
				imageResp, err = tiktokAPI.UploadImage(ctx, third_party.UploadParam{
					FileName:     task.MaterialName,
					FileURL:      fileUrl,
					AdvertiserID: task.AccountID,
					TiktokToken:  bizToken,
				})
				comResp = imageResp.TikTokCommonResponse
				mediumMaterialId = imageResp.Data.ImageId
			default:
				return "", fmt.Errorf("不支持上传的素材类型：%v", task.Material.Type)
			}
			c.log.Infof("resp: %v ,err: %v", comResp, err)
			// 如果碰到40001错误，说明token和广告账户授权有问题，直接跳过
			if comResp.Code == 40001 {
				break
			}
			// 成功或不可重试的错误则退出循环
			if err == nil && comResp.Code == 0 {
				uploadStatus = true
				// 设置redis缓存,出现错误不认为是上传失败
				err = c.redisClient.Set(ctx, fmt.Sprintf("%s_%s", TIKTOK_TOKEN_REDIS_KEY, task.AccountID), bizToken)
				if err != nil {
					c.log.Errorf("设置redis缓存失败: %v", err)
				}
				break
			}
			// 最后一次重试仍然失败，返回错误
			if i == maxRetries-1 {
				return "", fmt.Errorf("上传失败, msg: %v, err: %v", comResp.Message, err)
			}
			// 等待一段时间后重试
			time.Sleep(time.Second * time.Duration((i+1)*5))
		}
		// 成功之后无需继续重试
		if uploadStatus {
			break
		}
	}
	// 所有token授权都被跳过，则说明所有token都无该广告账号权限
	if !uploadStatus {
		return "", fmt.Errorf("请检查该账号授权状态")
	}
	return mediumMaterialId, nil
}

func (c *MaterialManageCase) getTikTokTokenByPriority(ctx context.Context, tokenKey string, adsAccountId string) string {
	// 先从redis中获取
	c.log.Infof("从redis中获取token: %v", c.redisClient)
	result, err := c.redisClient.Get(ctx, fmt.Sprintf("%s_%s", TIKTOK_TOKEN_REDIS_KEY, adsAccountId))
	if err != nil {
		c.log.Errorf("从redis中获取token失败: %v", err)
	}
	if result != "" {
		return result
	}
	// 根据优先级获取token
	switch tokenKey {
	case constants.TIKTOK_TOKEN_BIZ:
		return c.bizConf.TiktokToken.Biz
	case constants.TIKTOK_TOKEN_BIZ1:
		return c.bizConf.TiktokToken.Biz1
	case constants.TIKTOK_TOKEN_BIZ2:
		return c.bizConf.TiktokToken.Biz2
	case constants.TIKTOK_TOKEN_BIZ3:
		return c.bizConf.TiktokToken.Biz3
	case constants.TIKTOK_TOKEN_BIZ4:
		return c.bizConf.TiktokToken.Biz4
	case constants.TIKTOK_TOKEN_BIZ5:
		return c.bizConf.TiktokToken.Biz5
	case constants.TIKTOK_TOKEN_BIZ6:
		return c.bizConf.TiktokToken.Biz6
	case constants.TIKTOK_TOKEN_BIZ7:
		return c.bizConf.TiktokToken.Biz7
	default:
		return ""
	}
}

func (c *MaterialManageCase) UploadMaterialToMediumLog(ctx context.Context, request *GetMaterialUploadLog) (*MaterialUploadLogList, error) {
	// 获取用户信息
	currentUser, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return nil, authError.QueryUserFail
	}

	return c.repo.GetMaterialUploadLog(ctx, request, currentUser.UserID, currentUser.IsStaff)
}
