package publisher

import (
	"encoding/json"
	"vision_hub/internal/data/resource_pool/model"
)

// 用于BO定义

type PublisherResourceData struct {
	PublisherResource         model.ResourcePublisherBase
	PublisherResourceLinkList []model.ResourcePublisherLink
	AffiliatePublisher
	ResourceContactList []model.ResourceContact
}

type AffiliatePublisher struct {
	BusinessContact   []BusinessContact
	PublisherBusiness []model.ResourcePublisherBusiness
	Platform          []model.ResourcePublisherPlatform
	Channel           model.ResourcePublisherChannel
}

type BusinessContact struct {
	Contact           model.ResourceContact           `gorm:"embedded"`
	PublisherBusiness model.ResourcePublisherBusiness `gorm:"embedded"`
}

type Publisher struct {
	Pagination Pagination
	List       []*model.ResourcePublisherBase
}

type Pagination struct {
	PageIndex int32
	PageSize  int32
	Total     int32
}

type ListPublisherResourceReq struct {
	BaseParam         `json:"base_param,omitempty" `
	AffiliateParam    `json:"affiliate_param,omitempty"`
	OrderParam        `json:"order_param,omitempty"`
	MinFans           int32       `json:"min_fans"`
	MaxFans           int32       `json:"max_fans"`
	MinMonthlyVisits  int32       `json:"min_monthly_visits"`
	MaxMonthlyVisits  int32       `json:"max_monthly_visits"`
	CountryTop        []StringArr `json:"country_top,omitempty"`
	PublisherLink     string      `json:"publisher_link"`
	TypeList          []string    `json:"type_list"`
	CustomerProjectId int32       `json:"customer_project_id,omitempty"`
}

type Indicator struct {
	MozSpamScore           int32 `json:"mozSpamScore,omitempty"`
	MozDomainAuthority     int32 `json:"mozDomainAuthority,omitempty"`
	SimilarWebGlobalRank   int32 `json:"similarWebGlobalRank,omitempty"`
	SimilarWebVerified     bool  `json:"similarWebVerified,omitempty"`
	AverageViews           int32 `json:"averageViews,omitempty"`
	AverageShare           int32 `json:"averageShare,omitempty"`
	AverageLikes           int32 `json:"averageLikes,omitempty"`
	AverageReviews         int32 `json:"averageReviews,omitempty"`
	AverageInteractionRate int32 `json:"averageInteractionRate,omitempty"`
	AverageFavorites       int32 `json:"averageFavorites,omitempty"`
}

type BaseParam struct {
	Name          string      `json:"name"`
	RegionList    []string    `json:"region_list"`
	CountryList   []string    `json:"country_list"`
	GenderRatio   GenderRatio `json:"gender_ratio"`
	AgeList       []string    `json:"age_list"`
	Price         Price       `json:"price"`
	PageNum       int32       `json:"page_num"`
	PageSize      int32       `json:"page_size"`
	RegionCountry []StringArr `json:"region_country,omitempty"`
	SizeList      []string    `json:"size_list"`
}
type StringArr struct {
	Value []string `json:"value"`
}
type GenderRatio struct {
	Male   float32 `json:"male"`
	Female float32 `json:"female"`
}

type Price struct {
	Min      float32 `json:"min"`
	Max      float32 `json:"max"`
	Currency string  `json:"currency,omitempty"`
}

type AffiliateParam struct {
	AffPlatformNameList      []string `json:"aff_platform_name_list"`
	ChannelTypeList          []string `json:"channel_type_list"`
	PromotionCategoryList    []string `json:"promotion_category_list"`
	PromotionTypeList        []string `json:"promotion_type_list"`
	CustomerJourneyStageList []string `json:"customer_journey_stage_list"`
	MarketingObjectivesList  []string `json:"marketing_objectives_list"`
	MagnitudeList            []string `json:"magnitude_list"`
	TagList                  []string `json:"tag_list"`
	AffPlatformPublisherId   string   `json:"aff_platform_publisher_id"`
	AffPlatformPublisherName string   `json:"aff_platform_publisher_name"`
}

type OrderParam struct {
	Field string `json:"field"`
	Order string `json:"order"`
}

// ExportPublisherResponse
type ExportPublisherResponse struct {
	Data      []FlatPublisherResource
	ExcelHead map[string]string
}

// FlatPublisherResource 扁平化的联盟客资源结构
type FlatPublisherResource struct {
	// 基础信息 (ResourcePublisherBase)
	ID                       int32  `json:"id"`
	Name                     string `json:"name"`
	RegionCountry            string `json:"region_country"`
	WebsiteURL               string `json:"website_url"`
	Introduction             string `json:"introduction"`
	Traffic                  int64  `json:"traffic"`
	GenderRatioMale          string `json:"gender_ratio_male"`
	GenderRatioFemale        string `json:"gender_ratio_female"`
	AgeTop                   string `json:"age_top"`
	CountryTop               string `json:"country_top"`
	Size                     string `json:"size"`
	PlatformName             string `json:"platform_name"`
	AffPlatformPublisherID   string `json:"aff_platform_publisher_id"`
	AffPlatformPublisherName string `json:"aff_platform_publisher_name"`
	MediaViscosity           string `json:"media_viscosity"`

	// 商务联系人信息 (BusinessContact)
	ContactName       string `json:"contact_name"`
	ContactInfo       string `json:"contact_info"`
	ResponsiblePerson string `json:"responsible_person"`
	ContactRemarks    string `json:"contact_remarks"`

	//MinPrice                float32 `json:"min_price"`
	//MaxPrice                float32 `json:"max_price"`
	//Unit                    string  `json:"unit"`
	//BusinessRemarks         string  `json:"business_remarks"`
	//CooperationRequirements string  `json:"cooperation_requirements"`

	// 渠道信息 (ResourcePublisherChannel)
	ChannelType          string `json:"channel_type"`
	PromotionCategory    string `json:"promotion_category"`
	PromotionType        string `json:"promotion_type"`
	CustomerJourneyStage string `json:"customer_journey_stage"`
	MarketingObjectives  string `json:"marketing_objectives"`
	Tag                  string `json:"tag"`
}

var ExportHead = map[string]string{
	// 基础信息
	"ID":                "资源ID",
	"DisplayName":       "名称",
	"RegionCountry":     "所在国家/地区",
	"WebsiteURL":        "网站链接",
	"Introduction":      "简介",
	"Traffic":           "流量(K)",
	"GenderRatioMale":   "受众性别比例(男)",
	"GenderRatioFemale": "受众性别比例(女)",
	"AgeTop":            "受众年龄",
	"CountryTop":        "受众国家",
	// 商务联系人信息
	"ContactName":       "联系人",
	"ContactInfo":       "联系方式(email/phone/wechat/whatsapp)",
	"ResponsiblePerson": "跟进人联系方式(email/phone/wechat/whatsapp)",
	"ContactRemarks":    "联系人备注",
	//"MinPrice":                "最低价格",
	//"MaxPrice":                "最高价格",
	//"Unit":                    "单位",
	//"BusinessRemarks":         "商务备注",
	//"CooperationRequirements": "合作要求&报价细节",
	// 平台信息
	"PlatformName":             "平台名称",
	"AffPlatformPublisherID":   "联盟客ID",
	"AffPlatformPublisherName": "联盟客名称",
	// 渠道信息
	"ChannelType":          "渠道类型",
	"PromotionCategory":    "推广类目",
	"PromotionType":        "推广方式",
	"CustomerJourneyStage": "客户旅程阶段",
	"MarketingObjectives":  "营销目标",
	"Tag":                  "Tag",
	"Size":                 "量级Size",
	"MediaViscosity":       "媒体粘性",
}

type PublisherListItem struct {
	ID                    int32           `gorm:"column:id" json:"id"`
	Name                  string          `gorm:"column:name" json:"name"`
	LogoUrl               string          `gorm:"column:logo_url" json:"logo_url"`
	RegionCountry         json.RawMessage `gorm:"column:region_country" json:"region_country"`
	Introduction          string          `gorm:"column:introduction" json:"introduction"`
	Type                  string          `gorm:"column:type" json:"type"`
	Size                  string          `gorm:"column:size" json:"size"`
	PublisherPlatform     string          `gorm:"column:publisher_platform" json:"publisher_platform"`
	PublisherPlatformName string          `gorm:"column:publisher_platform_name" json:"publisher_platform_name"`
	LinkStr               string          `gorm:"column:link_str" json:"link_str"`
}

type ExportPublisherListItem struct {
	model.ResourcePublisherBase    `gorm:"embedded" json:"-"`
	model.ResourcePublisherChannel `gorm:"embedded" json:"-"`
	LinkStr                        string                             `gorm:"column:link_str" json:"-"`
	Links                          []string                           `gorm:"-" json:"links"`
	Contacts                       []*model.ResourceContact           `gorm:"-" json:"-"`
	Business                       []*model.ResourcePublisherBusiness `gorm:"-" json:"-"`
}

type ContactDetails struct {
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	Wechat   string `json:"wechat"`
	Whatsapp string `json:"whatsapp"`
}
