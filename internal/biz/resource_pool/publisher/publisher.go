package publisher

import (
	"context"
	"strings"
	pb "vision_hub/api/resource_manager/v1"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/biz/resource_pool"
	"vision_hub/internal/conf"
	datacommot "vision_hub/internal/data/common"
	authError "vision_hub/internal/errors/auth"
)

type PublisherResourceCase struct {
	log                   *bizcommon.BizLogHelper
	Data                  *datacommot.Data
	publisherResourceRepo IPublisherResourceRepo
	commonRepo            bizcommon.ICommonRepo
	bizConf               *conf.BizConf
}

func NewPublisherResourceCase(log *bizcommon.BizLogHelper, data *datacommot.Data, publisherResourceRepo IPublisherResourceRepo, commonRepo bizcommon.ICommonRepo, bizConf *conf.BizConf) *PublisherResourceCase {
	return &PublisherResourceCase{
		log:                   log,
		Data:                  data,
		publisherResourceRepo: publisherResourceRepo,
		commonRepo:            commonRepo,
		bizConf:               bizConf,
	}
}

func (a *PublisherResourceCase) GetPublisherResourceDetail(ctx context.Context, resourceId int32) (*PublisherResourceData, error) {
	// 判断资源是否存在
	resourceExist, err := a.publisherResourceRepo.CheckPublisherResourceExist(ctx, resourceId)
	if !resourceExist {
		return nil, err
	}
	// 获取token中的用户id
	userInfo, err := a.commonRepo.GetCurrentUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return nil, authError.QueryUserFail
	}
	groups := resource_pool.GetGroupsFromUserInfo(userInfo)
	return a.publisherResourceRepo.GetPublisherResourceDetail(ctx, resourceId, groups)
}

// AddPublisherResource 新增Publisher资源
func (a *PublisherResourceCase) AddPublisherResource(ctx context.Context, req PublisherResourceData) error {
	// 检查必要参数
	if err := a.CheckParams(req); err != nil {
		return err
	}
	// 获取token中的用户id
	userInfo, err := a.commonRepo.GetCurrentUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return authError.QueryUserFail
	}
	groups := resource_pool.GetGroupsFromUserInfo(userInfo)
	return a.publisherResourceRepo.AddPublisherBaseTx(ctx, req, userInfo.Email, groups)
}

// UpdatePublisherResource 更新Publisher资源
func (a *PublisherResourceCase) UpdatePublisherResource(ctx context.Context, req PublisherResourceData) error {
	// 检查必要参数
	if err := a.CheckParams(req); err != nil {
		return err
	}
	// 获取token中的用户id
	userInfo, err := a.commonRepo.GetCurrentUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return authError.QueryUserFail
	}
	groups := resource_pool.GetGroupsFromUserInfo(userInfo)

	return a.publisherResourceRepo.UpdatePublisherBaseTx(ctx, req, userInfo.Email, groups)
}

// DelPublisherResource 删除Publisher资源
func (a *PublisherResourceCase) DelPublisherResource(ctx context.Context, resourceId int32) error {
	// 判断资源是否存在
	resourceExist, err := a.publisherResourceRepo.CheckPublisherResourceExist(ctx, resourceId)
	if !resourceExist {
		return err
	}
	return a.publisherResourceRepo.DelPublisherResource(ctx, resourceId)
}

// ListPublisherResource 获取Publisher资源列表
func (a *PublisherResourceCase) ListPublisherResource(ctx context.Context, req ListPublisherResourceReq) (*pb.PublisherList, error) {
	// 获取token中的用户id
	userInfo, err := a.commonRepo.GetCurrentUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return nil, authError.QueryUserFail
	}
	groups := resource_pool.GetGroupsFromUserInfo(userInfo)
	publisher, count, err := a.publisherResourceRepo.ListPublisherResource(ctx, req, groups)
	if err != nil {
		return nil, err
	}
	return a.ListPublisherToPb(publisher, count, req.PageNum, req.PageSize), nil
}

func (a *PublisherResourceCase) ListPublisherToPb(publisher []*PublisherListItem, count int64, pageIndex, pageSize int32) *pb.PublisherList {
	list := make([]*pb.PublisherListItem, 0)
	for _, v := range publisher {
		links := strings.Split(v.LinkStr, ",")
		regionCountry := resource_pool.GetRegionCountryToPb(v.RegionCountry)

		list = append(list, &pb.PublisherListItem{
			Id:                    v.ID,
			Name:                  v.Name,
			LogoUrl:               v.LogoUrl,
			RegionCountry:         regionCountry,
			Introduction:          v.Introduction,
			Type:                  v.Type,
			Size:                  v.Size,
			PublisherPlatform:     v.PublisherPlatform,
			PublisherPlatformName: v.PublisherPlatformName,
			Links:                 links,
		})
	}
	ret := &pb.PublisherList{
		Pagination: resource_pool.BuildPager(int32(count), pageIndex, pageSize),
		List:       list,
	}
	return ret
}

func (a *PublisherResourceCase) CheckParams(req PublisherResourceData) error {

	//if req.PublisherResource.WebsiteURL == "" {
	//	return resourceError.PublisherResourceUrlEmpty
	//}
	//if len(req.Platform) == 0 {
	//	return resourceError.PublisherResourcePlatformEmpty
	//}
	//for _, v := range req.Platform {
	//	if v.AffPlatformName == "" {
	//		return resourceError.PublisherResourcePlatformNameEmpty
	//	}
	//}
	//if req.PublisherResource.DisplayName == "" {
	//	return resourceError.PublisherResourceNameEmpty
	//}

	return nil
}

func (a *PublisherResourceCase) ExportPublisherResource(ctx context.Context, req ListPublisherResourceReq) (*ExportPublisherResponse, error) {
	data, err := a.publisherResourceRepo.ExportPublisherResource(ctx, req)
	if err != nil {
		return nil, err
	}
	sysDictMapping, err := a.commonRepo.GetSysDictMapping(ctx, "")
	if err != nil {
		return nil, err
	}
	return ConvertToFlatPublisherResources(data, sysDictMapping), nil
}
