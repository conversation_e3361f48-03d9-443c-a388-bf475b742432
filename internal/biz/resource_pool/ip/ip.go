package ip

import (
	"context"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/biz/resource_pool"
	"vision_hub/internal/conf"
	datacommot "vision_hub/internal/data/common"
	authError "vision_hub/internal/errors/auth"
)

type IpResourceCase struct {
	log            *bizcommon.BizLogHelper
	Data           *datacommot.Data
	ipResourceRepo IIpResourceRepo
	commonRepo     bizcommon.ICommonRepo
	bizConf        *conf.BizConf
}

func NewIpResourceCase(log *bizcommon.BizLogHelper, data *datacommot.Data, ipResourceRepo IIpResourceRepo, commonRepo bizcommon.ICommonRepo, bizConf *conf.BizConf) *IpResourceCase {
	return &IpResourceCase{
		log:            log,
		Data:           data,
		ipResourceRepo: ipResourceRepo,
		commonRepo:     commonRepo,
		bizConf:        bizConf,
	}
}

// AddIpResource 新增IP资源
func (a *IpResourceCase) AddIpResource(ctx context.Context, req IpResourceData) error {
	// 判断参数
	//exist, err := a.ipResourceRepo.CheckIpNameExist(ctx, req.IpResource.DisplayName, 0)
	//if err != nil {
	//	a.log.WithContext(ctx).Errorf("校验IP资源名称失败: %v", err)
	//	return err
	//}
	//if exist {
	//	return resourceError.ResourceNameExist
	//}
	// 获取token中的用户id
	userInfo, err := a.commonRepo.GetCurrentUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return authError.QueryUserFail
	}
	groups := resource_pool.GetGroupsFromUserInfo(userInfo)

	return a.ipResourceRepo.AddIpBaseTx(ctx, req, userInfo.Email, groups)
}

// UpdateIpResource 更新IP资源
func (a *IpResourceCase) UpdateIpResource(ctx context.Context, req IpResourceData) error {
	// 判断资源是否存在
	resourceExist, err := a.ipResourceRepo.CheckIpResourceExist(ctx, req.IpResource.ID)
	if !resourceExist {
		return err
	}
	// 判断参数
	//exist, err := a.ipResourceRepo.CheckIpNameExist(ctx, req.IpResource.DisplayName, req.IpResource.ID)
	//if err != nil {
	//	a.log.WithContext(ctx).Errorf("校验IP资源名称失败: %v", err)
	//	return err
	//}
	//if exist {
	//	return resourceError.ResourceNameExist
	//}
	// 获取token中的用户id
	userInfo, err := a.commonRepo.GetCurrentUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return authError.QueryUserFail
	}
	groups := resource_pool.GetGroupsFromUserInfo(userInfo)

	return a.ipResourceRepo.UpdateIpBaseTx(ctx, req, userInfo.Email, groups)
}

// GetIpResourceDetail 获取Ip资源
func (a *IpResourceCase) GetIpResourceDetail(ctx context.Context, resourceId int32) (*IpResourceData, error) {
	// 判断资源是否存在
	resourceExist, err := a.ipResourceRepo.CheckIpResourceExist(ctx, resourceId)
	if !resourceExist {
		return nil, err
	}
	// 获取token中的用户id
	userInfo, err := a.commonRepo.GetCurrentUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return nil, authError.QueryUserFail
	}
	groupCode := resource_pool.GetGroupsFromUserInfo(userInfo)

	return a.ipResourceRepo.GetIpResourceDetail(ctx, resourceId, groupCode)
}

// DelIpResource 删除Ip资源
func (a *IpResourceCase) DelIpResource(ctx context.Context, resourceId int32) error {
	// 判断资源是否存在
	resourceExist, err := a.ipResourceRepo.CheckIpResourceExist(ctx, resourceId)
	if !resourceExist {
		return err
	}
	return a.ipResourceRepo.DelIpResource(ctx, resourceId)
}

// ListIpResource 获取Ip资源
func (a *IpResourceCase) ListIpResource(ctx context.Context, req ListIpResourceReq) (*ListIpResponse, error) {
	// 获取token中的用户id
	userInfo, err := a.commonRepo.GetCurrentUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return nil, authError.QueryUserFail
	}
	groups := resource_pool.GetGroupsFromUserInfo(userInfo)
	return a.ipResourceRepo.ListIpResource(ctx, req, groups)
}
