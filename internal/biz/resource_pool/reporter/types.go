package reporter

import (
	"encoding/json"
	"strings"
	"time"
	"vision_hub/internal/data/resource_pool/model"
)

// 用于BO定义

type ReporterResourceData struct {
	Base          *model.ResourceReporterBase      `json:"reporter_base"`
	ReporterMedia []*model.ResourceReporterMedium  `json:"reporter_media"`
	Contact       *model.ResourceContact           `json:"resource_contact"`
	Articles      []*model.ResourceReporterArticle `json:"reporter_article"`
}

// GetReporterResourceListReq 记者库列表请求参数
type GetReporterResourceListReq struct {
	Name              string      `json:"name"`
	MediaName         string      `json:"media_name"`
	Tag               string      `json:"tag"`
	RegionCountry     []StringArr `json:"region_country,omitempty"`
	Beats             []string    `json:"beats"`
	IsEstablishConn   int32       `json:"is_establish_conn"`
	City              string      `json:"city"`
	PageSize          int32       `json:"page_size"`
	PageNum           int32       `json:"page_num"`
	CustomerProjectId int32       `json:"customer_project_id,omitempty"`
}

type StringArr struct {
	Value []string `json:"value"`
}

// ReporterResource 记者库列表返回数据
type ReporterResource struct {
	*model.ResourceReporterBase
	MediaIds    string                          `json:"media_ids"`
	ReportMedia []*model.ResourceReporterMedium `gorm:"serializer:json"`
}

type GetReporterArticleListReq struct {
	PageSize   int32 `json:"page_size"`
	PageNum    int32 `json:"page_num"`
	ReporterId int32 `json:"reporter_id"`
}
type GetReporterArticleItem struct {
	Id                 int       `json:"id"`
	ArticleUrl         string    `json:"article_url"`
	ArticleTitle       string    `json:"article_title"`
	ArticlePublishTime time.Time `json:"article_publish_time"`
	ArticleDescription string    `json:"article_description"`
	MediaName          string    `json:"media_name"`
}
type GetReporterArticleResponse struct {
	List     []*GetReporterArticleItem `json:"list"`
	Total    int32                     `json:"total"`
	PageSize int32                     `json:"page_size"`
	PageNum  int32                     `json:"page_num"`
}

type GetReporterDetailResponse struct {
	*model.ResourceReporterBase
	ReportMedia []*model.ResourceReporterMedium `gorm:"-" json:"report_media"`
	ContactInfo string                          `json:"contact_info"`
}

type ExportReporterData struct {
	Name              string          `gorm:"column:name" json:"name"`
	RegionCountry     json.RawMessage `gorm:"column:region_country" json:"region_country"`
	Title             string          `gorm:"column:title" json:"title"`
	Beats             json.RawMessage `gorm:"column:beats" json:"beats"`
	IsEstablishConn   string          `gorm:"column:is_establish_conn" json:"is_establish_conn"`
	EstablishConnUser string          `gorm:"column:establish_conn_user" json:"establish_conn_user"`
	ContactInfo       json.RawMessage `gorm:"column:contact_info" json:"contact_info"`
	SocialMedias      json.RawMessage `gorm:"column:social_medias" json:"social_medias"`
	MediaInfo         json.RawMessage `gorm:"column:media_info" json:"media_info"`
}

// ExportReporterResponse
type ExportReporterResponse struct {
	Data      []FlatReporterResource
	ExcelHead map[string]string
}
type ExportMediaData struct {
	MediaName     string `json:"media_name"`
	SimilarwebUvm int64  `json:"similarweb_uvm"`
}

// FlatReporterResource 扁平化的记者资源
type FlatReporterResource struct {
	// 基础信息 (ResourceReporterBase)
	Name              string `json:"name"`
	RegionCountry     string `json:"region_country"`
	Title             string `json:"title"`
	Beats             string `json:"beats"`
	MediaName         string `json:"media_name"`
	SimilarwebUvm     int64  `json:"similarweb_uvm"`
	ContactEmail      string `json:"contact_email"`
	ContactPhone      string `json:"contact_phone"`
	ContactWechat     string `json:"contact_wechat"`
	ContactWhatsapp   string `json:"contact_whatsapp"`
	SocialMedias      string `json:"social_medias"`
	IsEstablishConn   string `json:"is_establish_conn"`
	EstablishConnUser string `json:"establish_conn_user"`
}

var ExportHead = map[string]string{
	// 基础信息
	"DisplayName":       "姓名",
	"RegionCountry":     "所在国家/地区",
	"Title":             "职称",
	"Beats":             "领域",
	"MediaName":         "媒体名称",
	"SimilarwebUvm":     "Similarweb UV",
	"IsEstablishConn":   "是否已合作",
	"EstablishConnUser": "建联联系人",
	"ContactEmail":      "email",
	"ContactPhone":      "phone",
	"ContactWechat":     "wechat",
	"ContactWhatsapp":   "whatsapp",
	"SocialMedias":      "社交媒体账号",
}

type SocialMedias []struct {
	Media string `json:"media"`
	Url   string `json:"url"`
}

func (c SocialMedias) ToString() string {
	var mediaMap = make(map[string][]string)
	for _, media := range c {
		if media.Url != "" { // 过滤掉空字符串
			mediaMap[media.Media] = append(mediaMap[media.Media], media.Url)
		}
	}
	var ret []string
	for media, urls := range mediaMap {
		ret = append(ret, media+": "+strings.Join(urls, ", "))
	}
	return strings.Join(ret, "\n")
}
