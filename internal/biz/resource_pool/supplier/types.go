package supplier

import "vision_hub/internal/data/resource_pool/model"

// 用于BO定义

type SupplierResourceData struct {
	SupplierResource model.ResourceSupplierBase `json:"base_info"`
	BrandSupplier    `json:"brand_info"`
}

type BrandSupplier struct {
	BrandBusinessContact []BrandBusinessContact
}

type BrandBusinessContact struct {
	Contact               model.ResourceContact               `gorm:"embedded"`
	SupplierBrandBusiness model.ResourceSupplierBrandBusiness `gorm:"embedded"`
}

type ListSupplierResourceReq struct {
	BaseParam         `json:"base_param,omitempty" `
	BrandParam        `json:"brand_param,omitempty"`
	AffiliateParam    `json:"affiliate_param,omitempty"`
	CustomerProjectId int32 `json:"customer_project_id,omitempty"`
}

type BaseParam struct {
	Name                  string      `json:"name"`
	RegionList            []string    `json:"region_list"`
	CountryList           []string    `json:"country_list"`
	Price                 Price       `json:"price"`
	MainBusinessList      []string    `json:"main_business_list"`
	AdvantageIndustryList []string    `json:"advantage_industry_list"`
	SupplierAttributes    string      `json:"supplier_attributes"`
	PageNum               int32       `json:"page_num"`
	PageSize              int32       `json:"page_size"`
	RegionCountry         []StringArr `json:"region_country,omitempty"`
}
type StringArr struct {
	Value []string `json:"value"`
}

type Price struct {
	Min      float32 `json:"min"`
	Max      float32 `json:"max"`
	Currency float32 `json:"currency,omitempty"`
}
type BrandParam struct {
	ContactType      string   `json:"contact_type"`
	PaymentTermsList []string `json:"payment_terms_list"`
	TypeList         []string `json:"type_list"`
	MagnitudeList    []string `json:"magnitude_list"`
	OperateMethod    string   `json:"operate_method"`
}

type AffiliateParam struct {
	AffPlatformNameList      []string `json:"aff_platform_name_list"`
	ChannelTypeList          []string `json:"channel_type_list"`
	PromotionCategoryList    []string `json:"promotion_category_list"`
	PromotionTypeList        []string `json:"promotion_type_list"`
	CustomerJourneyStageList []string `json:"customer_journey_stage_list"`
	MarketingObjectivesList  []string `json:"marketing_objectives_list"`
	MagnitudeList            []string `json:"magnitude_list"`
	TagList                  []string `json:"tag_list"`
	AffPlatformPublisherId   string   `json:"aff_platform_publisher_id"`
	AffPlatformPublisherName string   `json:"aff_platform_publisher_name"`
}

// ExportSupplierResponse
type ExportSupplierResponse struct {
	Data      []FlatSupplierResource
	ExcelHead map[string]string
}

// FlatSupplierResource 扁平化的供应商资源结构
type FlatSupplierResource struct {
	// 基础信息 (ResourceSupplierBase)
	ID                  int32  `json:"id"`
	Name                string `json:"name"`
	Type                string `json:"type"`
	MainBusiness        string `json:"main_business"`
	AdvantageIndustry   string `json:"advantage_industry"`
	SupplierAttributes  int64  `json:"supplier_attributes"`
	CompanyIntroduction string `json:"company_introduction"`
	Remarks             string `json:"remarks"`
	IntroductionDate    string `json:"introduction_date"`
	RegionCountry       string `json:"region_country"`

	// 商务联系人信息 (BusinessContact)
	ContactName       string `json:"contact_name"`
	ContactInfo       string `json:"contact_info"`
	ResponsiblePerson string `json:"responsible_person"`
	ContactRemarks    string `json:"contact_remarks"`

	MinPrice        float32 `json:"min_price"`
	MaxPrice        float32 `json:"max_price"`
	Unit            string  `json:"unit"`
	BusinessRemarks string  `json:"business_remarks"`
	OperateMethod   string  `json:"operate_method"`
}

var ExportHead = map[string]string{
	// 基础信息
	"ID":                  "资源ID",
	"DisplayName":         "供应商名称",
	"RegionCountry":       "所在国家/地区",
	"Type":                "类型",
	"MainBusiness":        "主营业务",
	"AdvantageIndustry":   "优势行业",
	"SupplierAttributes":  "供应商属性",
	"CompanyIntroduction": "公司简介",
	"Remarks":             "备注",
	"IntroductionDate":    "引入日期",
	// 商务联系人信息
	"ContactName":       "联系人",
	"ContactInfo":       "联系方式",
	"ResponsiblePerson": "跟进人联系方式",
	"ContactRemarks":    "联系人备注",
	"MinPrice":          "最低价格",
	"MaxPrice":          "最高价格",
	"Unit":              "单位",
	"BusinessRemarks":   "商务备注",
	"OperateMethod":     "合作方式",
}
