package biz

// 用于VO-BO数据对象之间的转换
// VO 视图层对象
// BO 业务对象
// DO 数据访问对象
import (
	"encoding/json"
	pb "vision_hub/api/auth/v1"
	"vision_hub/internal/data/auth/model"
)

// bizOrgToPbOrg 将 biz 结构转换为 pb 结构
func bizOrgToPbOrg(org *OrgTree) *pb.OrgData {
	if org == nil {
		return nil
	}
	children := make([]*pb.OrgData, len(org.Children))
	for i, child := range org.Children {
		children[i] = bizOrgToPbOrg(child)
	}
	return &pb.OrgData{
		OrgId:    int32(org.ID),
		OrgName:  org.OrgName,
		Children: children,
	}
}

func UserPageListParamsToBo(params *pb.UserPageListRequest) *UserPageListParams {
	if params == nil {
		return nil
	}
	return &UserPageListParams{
		PageNum:  params.PageNum,
		PageSize: params.PageSize,
		Email:    params.Email,
		UserName: params.UserName,
		OrgID:    params.OrgId,
		UserID:   params.UserId,
	}
}

func ConvertOrgUserToPb(orgUser []*model.SysUser) []*pb.OrgUser {
	if orgUser == nil {
		return nil
	}
	var ret []*pb.OrgUser
	for _, user := range orgUser {
		ret = append(ret, &pb.OrgUser{
			UserId:   user.ID,
			Email:    user.Email,
			UserName: user.UserName,
		})

	}
	return ret
}

func QueryOrgListParamsToBo(params *pb.QueryOrgListRequest) *OrgListParams {
	return &OrgListParams{
		OrgId:      params.OrgId,
		Search:     params.Search,
		IsShowUser: params.IsShowUser,
	}
}

// CreateOrUpdateGroupRequestToBo 将Pb结构转换为biz结构
func CreateOrUpdateGroupRequestToBo(req *pb.AddOrUpdateGroupRequest) (*CreateOrUpdateGroupReq, error) {
	return &CreateOrUpdateGroupReq{
		SysGroup: &model.SysGroup{
			ID:          req.Id,
			Name:        req.Name,
			Code:        req.Code,
			Description: req.Description,
		},
		UserIds: req.UserIds,
	}, nil
}

// ConvertGroupListReqToBo 将Pb结构转换为biz结构
func ConvertGroupListReqToBo(req *pb.QueryGroupListRequest) *QueryGroupListReq {
	return &QueryGroupListReq{
		PageSize:  req.PageSize,
		PageNum:   req.PageNum,
		GroupName: req.GroupName,
	}
}

// ConvertGroupDetailToPb 将biz结构转换为Pb结构
func ConvertGroupDetailToPb(data *GroupDetail) *pb.Group {
	users := make([]*pb.CommonUserInfo, 0)
	for _, v := range data.Users {
		users = append(users, &pb.CommonUserInfo{
			UserId:    v.UserID,
			UserName:  v.UserName,
			UserEmail: v.UserEmail,
		})
	}
	return &pb.Group{
		Id:          data.ID,
		Name:        data.Name,
		Code:        data.Code,
		Description: data.Description,
		GroupUsers:  users,
	}
}

// ConvertGroupListToPb 将biz结构转换为Pb结构
func ConvertGroupListToPb(data *GroupList) *pb.GroupListItem {
	var groups []*pb.Group
	for _, v := range data.List {
		groups = append(groups, ConvertGroupDetailToPb(&v))
	}
	return &pb.GroupListItem{
		Pagination: &pb.Pagination{
			Total:    data.Total,
			PageSize: data.PageSize,
			PageNum:  data.PageNum,
		},
		List: groups,
	}
}

// ConvertMenuTreeToReply 将菜单树转换为响应
func ConvertMenuTreeToReply(data []*MenuTree) (*pb.QueryMenuTreeResponse, error) {
	menuTree := make([]*pb.MenuTree, 0)
	dataByte, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(dataByte, &menuTree)
	if err != nil {
		return nil, err
	}
	return &pb.QueryMenuTreeResponse{Data: menuTree}, nil
}
