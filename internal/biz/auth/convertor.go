package biz

// 用于VO-BO数据对象之间的转换
// VO 视图层对象
// BO 业务对象
// DO 数据访问对象
import (
	"encoding/json"
	pb "vision_hub/api/auth/v1"
	"vision_hub/internal/data/auth/model"
)

// bizOrgToPbOrg 将 biz 结构转换为 pb 结构
func bizOrgToPbOrg(org *OrgTree) *pb.OrgData {
	if org == nil {
		return nil
	}
	children := make([]*pb.OrgData, len(org.Children))
	for i, child := range org.Children {
		children[i] = bizOrgToPbOrg(child)
	}
	return &pb.OrgData{
		OrgId:    int32(org.ID),
		OrgName:  org.OrgName,
		Children: children,
	}
}

func UserPageListParamsToBo(params *pb.UserPageListRequest) *UserPageListParams {
	if params == nil {
		return nil
	}
	return &UserPageListParams{
		PageNum:  params.PageNum,
		PageSize: params.PageSize,
		Email:    params.Email,
		UserName: params.UserName,
		OrgID:    params.OrgId,
		UserID:   params.UserId,
	}
}

func ConvertOrgUserToPb(orgUser []*model.SysUser) []*pb.OrgUser {
	if orgUser == nil {
		return nil
	}
	var ret []*pb.OrgUser
	for _, user := range orgUser {
		ret = append(ret, &pb.OrgUser{
			UserId:   user.ID,
			Email:    user.Email,
			UserName: user.UserName,
		})

	}
	return ret
}

func QueryOrgListParamsToBo(params *pb.QueryOrgListRequest) *OrgListParams {
	return &OrgListParams{
		OrgId:      params.OrgId,
		Search:     params.Search,
		IsShowUser: params.IsShowUser,
	}
}

// CreateOrUpdateRoleRequestToBo 将Pb结构转换为biz结构
func CreateOrUpdateRoleRequestToBo(req *pb.AddOrUpdateRoleRequest) (*CreateOrUpdateRoleReq, error) {
	return &CreateOrUpdateRoleReq{
		SysRole: &model.SysRole{
			ID:          req.Id,
			Name:        req.Name,
			Code:        req.Code,
			Description: req.Description,
		},
		MenuKeys: req.MenuKeys,
	}, nil
}

// ConvertRoleListReqToBo 将Pb结构转换为biz结构
func ConvertRoleListReqToBo(req *pb.ListAuthRoleRequest) *QueryRoleListReq {
	return &QueryRoleListReq{
		PageSize: req.PageSize,
		PageNum:  req.PageNum,
		RoleName: req.RoleName,
	}
}

// ConvertRoleDetailToPb 将biz结构转换为Pb结构
func ConvertRoleDetailToPb(data *RoleDetail) *pb.Role {
	users := make([]*pb.CommonUserInfo, 0)
	for _, v := range data.Users {
		users = append(users, &pb.CommonUserInfo{
			UserId:    v.UserID,
			UserName:  v.UserName,
			UserEmail: v.UserEmail,
		})
	}
	menuTree, menuKeys := buildMenuTree(data.Menus)
	return &pb.Role{
		RoleId:      data.ID,
		RoleName:    data.Name,
		Code:        data.Code,
		Description: data.Description,
		RoleUsers:   users,
		Menus:       menuTree,
		MenuKeys:    menuKeys,
	}
}

// 2. 构建树形结构
func buildMenuTree(sysMenus []*model.SysMenu) ([]*pb.MenuTree, []string) {
	menuKeys := make([]string, 0)
	menus := make([]*pb.MenuTree, 0)
	for _, sysMenu := range sysMenus {
		menuKeys = append(menuKeys, sysMenu.MenuKey)
		menus = append(menus, &pb.MenuTree{
			MenuId:   sysMenu.ID,
			Icon:     sysMenu.Icon,
			MenuType: int32(sysMenu.MenuType),
			MenuName: sysMenu.MenuName,
			Path:     sysMenu.Path,
			Url:      sysMenu.URL,
			Target:   sysMenu.Target,
			MenuKey:  sysMenu.MenuKey,
			ParentId: sysMenu.ParentID,
		})
	}

	menuMap := make(map[int32]*pb.MenuTree)
	for i := range menus {
		menuMap[menus[i].MenuId] = menus[i]
	}

	// 构建树
	var tree []*pb.MenuTree
	for i := range menus {
		menu := menus[i]
		if menu.ParentId == 0 {
			tree = append(tree, menu)
		} else {
			if parent, ok := menuMap[menu.ParentId]; ok {
				parent.Children = append(parent.Children, menu)
			}
		}
	}
	return tree, menuKeys
}

// ConvertRoleListToPb 将biz结构转换为Pb结构
func ConvertRoleListToPb(data *RoleList) *pb.RoleListItem {
	var roles []*pb.Role
	for _, v := range data.List {
		roles = append(roles, ConvertRoleDetailToPb(&v))
	}
	return &pb.RoleListItem{
		Pagination: &pb.Pagination{
			Total:    data.Total,
			PageSize: data.PageSize,
			PageNum:  data.PageNum,
		},
		List: roles,
	}
}

// ConvertMenuTreeToReply 将菜单树转换为响应
func ConvertMenuTreeToReply(data []*MenuTree) (*pb.QueryMenuTreeResponse, error) {
	menuTree := make([]*pb.MenuTree, 0)
	dataByte, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(dataByte, &menuTree)
	if err != nil {
		return nil, err
	}
	return &pb.QueryMenuTreeResponse{Data: menuTree}, nil
}
