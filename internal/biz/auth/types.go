package biz

import "vision_hub/internal/data/auth/model"

// 用于BO定义

type UserInfo struct {
	ID        int32
	Name      string
	Email     string
	Password  string
	FirstName string
	LastName  string
	Phone     string
	IsStaff   int8
	Avatar    string
	Unionid   string
	Openid    string
	Userid    string
}

type User struct {
	Username string
	Email    string
	Password string
}

type Group struct {
	ID      int32
	MUserID int32
	GroupID int32
	Code    string `json:"code"`
}

type Org struct {
	ID       int32
	OrgName  string
	OrgCode  string
	ParentId int64
	Level    int32
}

type OrgTree struct {
	ID       int64
	OrgName  string
	Children []*OrgTree
}

type UserPageListParams struct {
	PageNum  int32
	PageSize int32
	Email    string
	UserName string
	OrgID    int32
	UserID   int32
}

type UserListItem struct {
	ID         int32
	Email      string
	UserName   string
	OrgID      int32
	OrgName    string
	Phone      string
	FirstName  string
	LastName   string
	CreateTime string
	UpdateTime string
}

type FeishuRequest struct {
	AppID     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
}

type FeishuResponse struct {
	Code       int    `json:"code"`
	Msg        string `json:"msg"`
	GrantToken string `json:"app_access_token"`
}

// FeishuUserAccessTokenRequest 请求 Feishu 获取用户访问令牌
type FeishuUserAccessTokenRequest struct {
	GrantType    string `json:"grant_type"`
	Code         string `json:"code"`
	ClientId     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	RedirectUri  string `json:"redirect_uri"`
	CodeVerifier string `json:"code_verifier"`
}

// FeishuUserAccessTokenResponse 响应结构体
type FeishuUserAccessTokenResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		AccessToken string `json:"access_token"`
	} `json:"data"`
}

type FeishuNewUserAccessTokenResponse struct {
	Code                  int    `json:"code"`
	Error                 string `json:"error"`
	ErrorDescription      string `json:"error_description"`
	AccessToken           string `json:"access_token"`
	ExpiresIn             int    `json:"expires_in"`
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	Scope                 string `json:"scope"`
	TokenType             string `json:"token_type"`
}

// FeishuUserInfoResponse 用户信息的响应结构
type FeishuUserInfoResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Name            string `json:"name"`
		EnName          string `json:"en_name"`
		AvatarUrl       string `json:"avatar_url"`
		AvatarThumb     string `json:"avatar_thumb"`  // 用户头像 72x72
		AvatarMiddle    string `json:"avatar_middle"` // 用户头像 240x240
		AvatarBig       string `json:"avatar_big"`    // 用户头像 640x640
		OpenId          string `json:"open_id"`       // 用户在应用内的唯一标识
		UnionId         string `json:"union_id"`      // 用户对ISV的唯一标识，对于同一个ISV，用户在其名下所有应用的union_id相同
		Email           string `json:"email"`
		EnterpriseEmail string `json:"enterprise_email"`
		UserId          string `json:"user_id"`
		Mobile          string `json:"mobile"`
		TenantKey       string `json:"tenant_key"`  // 当前企业标识
		EmployeeNo      string `json:"employee_no"` // 用户工号
	} `json:"data"`
}

type OrgListParams struct {
	OrgId      int32  `json:"org_id"`
	Search     string `json:"search"`
	IsShowUser bool   `json:"is_show_user"`
}

type OrgTreeData struct {
	Id       int32  `json:"id"`
	OrgName  string `json:"org_name"`
	ParentId int32  `json:"parent_id"`
	OrgLevel int32  `json:"org_level"`
}

// CreateOrUpdateGroupReq 角色相关定义
type CreateOrUpdateGroupReq struct {
	*model.SysGroup
	UserIds []int32 `json:"user_ids"`
}

type QueryGroupListReq struct {
	PageSize  int32  `json:"page_size"`
	PageNum   int32  `json:"page_num"`
	GroupName string `json:"group_name"`
}

type GroupDetail struct {
	*model.SysGroup
	Users      []CommonUsers `gorm:"-" json:"-"`
	UsersBytes []byte        `gorm:"column:users" json:"-"`
}
type GroupList struct {
	Total    int32         `json:"total"`
	PageSize int32         `json:"page_size"`
	PageNum  int32         `json:"page_num"`
	List     []GroupDetail `json:"list"`
}

type CommonUsers struct {
	UserID    int32  `gorm:"column:user_id" json:"user_id"`
	UserName  string `gorm:"column:user_name" json:"user_name"`
	UserEmail string `gorm:"column:user_email" json:"user_email"`
}

type MenuTree struct {
	Id            int32      `gorm:"column:id" json:"id"`
	MenuName      string     `gorm:"column:menu_name" json:"menu_name"`
	ParentId      int32      `gorm:"column:parent_id" json:"parent_id"`
	ApiName       string     `gorm:"column:api_name" json:"api_name"`
	FrontApiName  string     `gorm:"column:front_api_name" json:"front_api_name"`
	Children      []MenuTree `gorm:"-" json:"children"`
	ChildrenBytes []byte     `gorm:"column:children" json:"-"`
}
