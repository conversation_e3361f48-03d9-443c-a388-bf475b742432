package biz

import (
	"context"
	"fmt"
	"git.domob-inc.cn/bluevision/bv_commons/gtoken"
	"golang.org/x/crypto/bcrypt"
	"time"
	pb "vision_hub/api/auth/v1"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/conf"
	"vision_hub/internal/data/auth/model"
	datacommot "vision_hub/internal/data/common"
	authError "vision_hub/internal/errors/auth"
	"vision_hub/internal/infrastructure/auth"
	"vision_hub/internal/middleware"
)

type AuthCase struct {
	log           *bizcommon.BizLogHelper
	Data          *datacommot.Data
	authRepo      IAuthRepo
	authThirdRepo IAuthThirdPartyRepo
	bizConf       *conf.BizConf
}

func NewAuthCase(log *bizcommon.BizLogHelper, data *datacommot.Data, authRepo IAuthRepo, bizConf *conf.BizConf,
	authThirdRepo IAuthThirdPartyRepo) *AuthCase {
	return &AuthCase{
		log:           log,
		Data:          data,
		authRepo:      authRepo,
		bizConf:       bizConf,
		authThirdRepo: authThirdRepo,
	}
}

// GetUserInfo 获取用户信息
func (a *AuthCase) GetUserInfo(ctx context.Context) (*pb.UserInfo, error) {
	// 获取token中的用户id
	uid, err := gtoken.GetUidFromToken(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("获取用户id失败: %v", err)
		return nil, authError.GetUidFromTokenFail
	}
	// 获取用户信息
	userInfo, err := a.authRepo.GetUserInfoByID(ctx, int32(uid))
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return nil, authError.QueryUserFail
	}

	// 获取用户
	groupList, err := a.authRepo.GetGroupsByUserID(ctx, userInfo.ID)
	group := make([]*pb.GroupInfo, 0)
	if len(*groupList) > 0 {
		for _, v := range *groupList {
			group = append(group, &pb.GroupInfo{
				Id:   v.GroupID,
				Code: v.Code,
			})
		}
	}
	return &pb.UserInfo{
		UserId:    userInfo.ID,
		UserName:  userInfo.Name,
		Email:     userInfo.Email,
		FirstName: userInfo.FirstName,
		LastName:  userInfo.LastName,
		Phone:     userInfo.Phone,
		IsStaff:   int32(userInfo.IsStaff),
		Group:     group,
	}, nil
}

// AddUser a new user.
func (a *AuthCase) AddUser(ctx context.Context, req *pb.AddUserRequest) error {
	// 检验参数
	validateErr := ValidateAddParam(req)
	if validateErr != nil {
		return validateErr
	}

	// 检查邮箱是否已注册
	existingUser, err := a.authRepo.GetUserInfoByEmail(ctx, req.Email, 0)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return authError.QueryEmailFail
	}
	if existingUser != nil {
		a.log.WithContext(ctx).Errorf("邮箱已注册: %s", req.Email)
		return authError.ExistEmailFail
	}
	// 加密密码
	hashedPassword, err := HashPassword(req.Password)
	if err != nil {
		a.log.WithContext(ctx).Errorf("密码加密失败: %v", err)
		return authError.PasswdEncryptFail
	}
	encryptPassword := hashedPassword

	// 检查部门是否存在
	parentOrg, err := a.authRepo.GetOrgByID(ctx, req.OrgId)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门失败: %v", err)
		return authError.OrgQueryFail
	}
	if parentOrg == nil {
		a.log.WithContext(ctx).Errorf("部门不存在: %d", req.OrgId)
		return authError.OryNotExist
	}

	user := &model.SysUser{
		Password:  encryptPassword,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Email:     req.Email,
		UserName:  fmt.Sprintf("%s%s", req.LastName, req.FirstName),
		IsActive:  1,
		Phone:     req.Phone,
		OrgID:     req.OrgId,
	}
	if err = a.authRepo.CreateUserTx(ctx, user); err != nil {
		return authError.DataCreateUserFail
	}

	return nil
}

func ValidateAddParam(req *pb.AddUserRequest) error {
	if req.FirstName == "" {
		return authError.RegisterFirstNameRequired
	}
	if req.LastName == "" {
		return authError.RegisterLastNameRequired
	}
	if req.Email == "" {
		return authError.LoginUserEmailRequired
	}
	if req.Password == "" {
		return authError.LoginPasswdRequired
	}
	if req.Phone == "" {
		return authError.RegisterPhoneRequired
	}
	if req.OrgId == 0 {
		return authError.OrgIDRequired
	}
	return nil
}

func (a *AuthCase) UpdateUser(ctx context.Context, req *pb.UpdateUserRequest) error {
	// 检验参数
	validateErr := ValidateUpdateParam(req)
	if validateErr != nil {
		return validateErr
	}

	// 检查邮箱是否已注册
	existingUser, err := a.authRepo.GetUserInfoByEmail(ctx, req.Email, req.Id)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return authError.QueryEmailFail
	}
	if existingUser != nil {
		a.log.WithContext(ctx).Errorf("邮箱已注册: %s", req.Email)
		return authError.ExistEmailFail
	}

	// 检查部门是否存在
	parentOrg, err := a.authRepo.GetOrgByID(ctx, req.OrgId)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门失败: %v", err)
		return authError.OrgQueryFail
	}
	if parentOrg == nil {
		a.log.WithContext(ctx).Errorf("部门不存在: %d", req.OrgId)
		return authError.OryNotExist
	}

	user := &map[string]interface{}{
		"first_name": req.FirstName,
		"last_name":  req.LastName,
		"user_name":  fmt.Sprintf("%s%s", req.LastName, req.FirstName),
		"email":      req.Email,
		"phone":      req.Phone,
		"org_id":     req.OrgId,
	}
	if err = a.authRepo.UpdateUser(ctx, int32(req.Id), user); err != nil {
		a.log.WithContext(ctx).Errorf("更新用户失败: %v", err)
		return authError.DataUpdateUserFail
	}

	userBaseInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}
	// 用户部门关系
	sysUserOrg := &map[string]interface{}{
		"update_user": userBaseInfo.Email,
		"org_id":      req.OrgId,
	}
	if err := a.authRepo.UpdateUserOrg(ctx, int32(req.Id), sysUserOrg); err != nil {
		a.log.WithContext(ctx).Errorf("更新用户部门关联关系失败: %v", err)
		return authError.DataUpdateUserFail
	}

	return nil
}

func (a *AuthCase) DeleteUser(ctx context.Context, request *pb.DeleteUserRequest) error {
	err := a.authRepo.DeleteUserTx(ctx, request.UserIds)
	if err != nil {
		return authError.DataDeleteUserFail
	}
	return nil
}

func ValidateUpdateParam(req *pb.UpdateUserRequest) error {
	if req.FirstName == "" {
		return authError.RegisterFirstNameRequired
	}
	if req.LastName == "" {
		return authError.RegisterLastNameRequired
	}
	if req.Email == "" {
		return authError.LoginUserEmailRequired
	}
	if req.Phone == "" {
		return authError.RegisterPhoneRequired
	}
	if req.OrgId == 0 {
		return authError.OrgIDRequired
	}
	return nil
}

// Login verifies the user credentials and generates a JWT token.
func (a *AuthCase) Login(ctx context.Context, email, password string) (string, *UserInfo, error) {
	// 检验参数
	if email == "" {
		return "", nil, authError.LoginUserEmailRequired
	}
	if password == "" {
		return "", nil, authError.LoginPasswdRequired
	}

	user, err := a.authRepo.GetUserInfoByEmail(ctx, email, 0)
	if err != nil {
		a.log.WithContext(ctx).Errorf(fmt.Sprintf("查询用户失败, email: %s, err: %v", email, err))
		return "", nil, authError.QueryUserFail
	}
	if user == nil {
		a.log.WithContext(ctx).Errorf(fmt.Sprintf("用户不存在, email: %s", email))
		return "", nil, authError.LoginUserNotExist
	}
	// 验证密码
	if err := CheckPassword(user.Password, password); err != nil {
		a.log.WithContext(ctx).Errorf(fmt.Sprintf("密码错误, email: %s, err: %v", email, err))
		return "", nil, authError.LoginPasswdFail
	}

	groups, err := a.authRepo.GetGroupsByUserID(ctx, user.ID)
	if err != nil {
		a.log.WithContext(ctx).Errorf(fmt.Sprintf("获取用户角色失败, email: %s, err: %v", email, err))
		return "", nil, authError.LoginGetRoleFail
	}

	// Convert roles to role names
	groupIDs := make([]int32, len(*groups))
	for i, role := range *groups {
		groupIDs[i] = role.GroupID
	}

	// Generate JWT token
	token, err := auth.GenerateToken(uint(user.ID), user.Userid, user.Name, email, groupIDs, user.IsStaff, a.bizConf.LoginConf.Secret, time.Hour*24)
	if err != nil {
		return "", nil, authError.LoginGenerateTokenFail
	}
	_ = a.authRepo.UpdateUser(ctx, user.ID, &map[string]interface{}{"last_login": time.Now()})
	return token, user, nil
}

// HashPassword 加密密码
func HashPassword(password string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), 10)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

// CheckPassword 验证密码
func CheckPassword(hashedPassword, plainPassword string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(plainPassword))
}

func (a *AuthCase) UserPageList(ctx context.Context, req *UserPageListParams) (*pb.UserPageListData, error) {
	data, pagination, err := a.authRepo.GetUserPageList(ctx, req)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户列表失败: %v", err)
		return nil, authError.UserPageListFail
	}
	ret := make([]*pb.UserPageListDataItem, 0)
	if len(*data) > 0 {
		for _, item := range *data {
			ret = append(ret, &pb.UserPageListDataItem{
				Id:         item.ID,
				Email:      item.Email,
				UserName:   item.UserName,
				OrgId:      int32(item.OrgID),
				OrgName:    item.OrgName,
				Phone:      item.Phone,
				FirstName:  item.FirstName,
				LastName:   item.LastName,
				CreateTime: bizcommon.ConvertDbCreateTimeToTimeStr(item.CreateTime),
				UpdateTime: bizcommon.ConvertDbCreateTimeToTimeStr(item.UpdateTime),
			})
		}
	}
	return &pb.UserPageListData{
		List:       ret,
		Pagination: pagination,
	}, nil
}

func (a *AuthCase) GetUserSelect(ctx context.Context, search string, ids []int32, pageSize int32) ([]*pb.CommonUserInfo, error) {
	ret := make([]*pb.CommonUserInfo, 0)
	data, err := a.authRepo.GetUserSelect(ctx, search, ids, pageSize)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户列表失败: %v", err)
		return nil, authError.UserSelectFail
	}
	if len(*data) > 0 {
		for _, item := range *data {
			ret = append(ret, &pb.CommonUserInfo{
				UserId:    item.ID,
				UserName:  item.UserName,
				UserEmail: item.Email,
			})
		}
	}
	return ret, nil
}

// ResetPassword 重置密码
func (a *AuthCase) ResetPassword(ctx context.Context, UserId int32, Password, ConfirmPassword string) error {
	if Password == "" {
		return authError.LoginPasswdRequired
	}
	if ConfirmPassword == "" {
		return authError.ResetPasswordConfirmPasswdRequired
	}
	if Password != ConfirmPassword {
		return authError.ResetPasswordPasswdNotEqual
	}
	// 判断用户是否存在
	user, err := a.authRepo.GetUserInfoByID(ctx, UserId)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return authError.QueryUserFail
	}
	if user == nil {
		a.log.WithContext(ctx).Errorf("用户不存在: %d", UserId)
		return authError.LoginUserNotExist
	}

	// 加密密码
	hashedPassword, err := HashPassword(Password)
	if err != nil {
		a.log.WithContext(ctx).Errorf("密码加密失败: %v", err)
		return authError.PasswdEncryptFail
	}
	encryptPassword := hashedPassword

	err = a.authRepo.UpdateUser(ctx, UserId, &map[string]interface{}{"password": encryptPassword})
	if err != nil {
		a.log.WithContext(ctx).Errorf("重置密码失败: %v", err)
		return authError.ResetPasswordFail
	}
	return nil
}

// AddOrg 添加部门
func (a *AuthCase) AddOrg(ctx context.Context, req *pb.AddOrgRequest) error {
	// 检查必要参数
	if err := a.CheckAddParams(req); err != nil {
		return err
	}
	// 检验部门是否已存在
	existingOrg, err := a.authRepo.GetOrgByOrgName(ctx, req.OrgName)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门失败: %v", err)
		return authError.OrgNameQueryFail
	}
	if existingOrg != nil {
		a.log.WithContext(ctx).Errorf("部门已存在: %s", req.OrgName)
		return authError.OrgNameExistFail
	}

	// 父级部门信息
	parentOrg, err := a.authRepo.GetOrgByID(ctx, req.ParentId)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门失败: %v", err)
		return authError.OrgQueryFail
	}
	if parentOrg == nil {
		a.log.WithContext(ctx).Errorf("部门不存在: %d", req.ParentId)
		return authError.OryNotExist
	}

	err = a.authRepo.AddOrg(ctx, &model.SysOrg{
		OrgName:  req.OrgName,
		ParentID: int64(req.ParentId),
		OrgCode:  "",
		OrgLevel: parentOrg.Level + 1,
	})
	if err != nil {
		a.log.WithContext(ctx).Errorf("添加部门失败: %v", err)
		return authError.OrgAddFail
	}
	return nil
}

// UpdateOrg 更新部门
func (a *AuthCase) UpdateOrg(ctx context.Context, req *pb.UpdateOrgRequest) error {
	// 检查必要参数
	if err := a.CheckUpdateParams(req); err != nil {
		return err
	}
	err := a.authRepo.UpdateOrg(ctx, req.OrgId, &map[string]interface{}{"org_name": req.OrgName})
	if err != nil {
		a.log.WithContext(ctx).Errorf("更新部门失败: %v", err)
		return authError.OrgUpdateFail
	}
	return nil
}

func (a *AuthCase) DeleteOrg(ctx context.Context, params *OrgListParams) error {
	orgList, err := a.authRepo.GetOrgList(ctx, params)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门失败: %v", err)
		return authError.OrgQueryFail
	}
	if orgList == nil || len(*orgList) == 0 {
		a.log.WithContext(ctx).Warnf("部门及子部门不存在: %d", params.OrgId)
		return authError.OryNotExist
	}
	orgIds := make([]int32, 0)
	for _, v := range *orgList {
		orgIds = append(orgIds, v.Id)
	}
	var orgUser []*model.SysUser
	orgUser, err = a.authRepo.GetOrgUserByOrgId(ctx, orgIds, &pb.QueryOrgByUserRequest{})
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门用户失败: %v", err)
		return authError.OrgDeleteFail
	}
	if len(orgUser) > 0 {
		a.log.WithContext(ctx).Errorf("部门下存在用户，无法删除")
		return authError.OrgHasUserDeleteFail
	}

	err = a.authRepo.DeleteOrg(ctx, orgIds)
	if err != nil {
		a.log.WithContext(ctx).Errorf("删除部门失败: %v", err)
		return authError.OrgDeleteFail
	}

	return nil
}

// QueryOrgList 部门列表
func (a *AuthCase) QueryOrgList(ctx context.Context, params *OrgListParams) ([]*pb.OrgData, error) {
	orgList, err := a.authRepo.GetOrgList(ctx, params)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门失败: %v", err)
		return nil, authError.OrgQueryFail
	}
	ret := make([]*pb.OrgData, 0)
	if len(*orgList) > 0 {
		nodeMap := make(map[int32]*pb.OrgData)
		// 存储所有节点，用于后续查找
		allNodes := make(map[int32]bool)
		for _, v := range *orgList {
			orgUsers := make([]*model.SysUser, 0)
			if params.IsShowUser {
				orgUsers, err = a.authRepo.GetOrgUserByOrgId(ctx, []int32{v.Id}, &pb.QueryOrgByUserRequest{})
				if err != nil {
					a.log.WithContext(ctx).Errorf("查询部门成员信息失败: %v", err)
					return nil, err
				}
			}
			node := &pb.OrgData{
				OrgId:   int32(v.Id),
				OrgName: v.OrgName,
				OrgUser: ConvertOrgUserToPb(orgUsers),
			}
			nodeMap[v.Id] = node
			allNodes[v.Id] = true
		}

		for _, v := range *orgList {
			currentNode := nodeMap[v.Id]
			parentId := v.ParentId

			if parentId != 0 {
				// 查找父节点
				if parent, exists := nodeMap[parentId]; exists {
					// 将当前节点添加到父节点的Children中
					parent.Children = append(parent.Children, currentNode)
					nodeMap[parentId] = parent
				}
			}
		}

		// 取首条数据作为开始节点
		firstNodeId := (*orgList)[0].Id
		if firstNodeId == 0 {
			// 找出所有没有父节点的节点作为根节点
			for id := range allNodes {
				hasParent := false
				for _, v := range *orgList {
					if v.Id == id && v.ParentId != 0 {
						hasParent = true
						break
					}
				}
				if !hasParent {
					ret = append(ret, nodeMap[id])
				}
			}
		} else {
			// 返回指定ID的节点及其子树
			if node, exists := nodeMap[firstNodeId]; exists {
				ret = append(ret, node)
			}
		}
	}

	return ret, nil
}

func (a *AuthCase) FeishuLogin(ctx context.Context, req *pb.FeishuLoginRequest) (string, *UserInfo, error) {
	if err := a.CheckFeishuLoginParams(req); err != nil {
		return "", nil, err
	}

	// 获取用户token用于获取用户信息
	userAccessToken, err := a.authThirdRepo.GetNewUserAccessToken(ctx, a.bizConf.Feishu.LoginAppId, a.bizConf.Feishu.LoginAppSecret, req)
	if err != nil {
		a.log.WithContext(ctx).Errorf("获取飞书用户凭证失败: %v", err)
		return "", nil, authError.FeishuLoginGetUserAccessTokenFail
	}

	// 获取用户基础信息
	userInfo, err := a.authThirdRepo.GetUserInfo(ctx, userAccessToken)
	if err != nil {
		a.log.WithContext(ctx).Errorf("获取飞书用户信息失败: %v", err)
		return "", nil, authError.FeishuLoginGetUserInfoFail
	}
	email := userInfo.Data.Email
	if email == "" {
		a.log.WithContext(ctx).Errorf("获取飞书用户邮箱失败，邮箱为空")
		return "", nil, authError.FeishuLoginGetUserEmailFail
	}

	// 获取用户详细信息
	userDetail, err := a.authThirdRepo.GetUserDetail(ctx, a.bizConf.Feishu.LoginAppId, a.bizConf.Feishu.LoginAppSecret, userInfo.Data.UnionId, userAccessToken)
	if err != nil {
		a.log.WithContext(ctx).Errorf("获取飞书用户详细信息失败: %v", err)
		return "", nil, authError.FeishuLoginGetUserInfoFail
	}
	// 用户会存在多个部门的情况，目前只取第一个部门以及对应的部门路径
	var department, departmentPath *string
	if len(userDetail.DepartmentPath) > 0 {
		department = userDetail.DepartmentPath[0].DepartmentName.Name
		departmentPath = userDetail.DepartmentPath[0].DepartmentPath.DepartmentPathName.Name
	}

	// 判断用户是否存在
	user, err := a.authRepo.GetUserInfoByEmail(ctx, email, 0)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return "", nil, authError.QueryUserFail
	}
	if user == nil {
		a.log.WithContext(ctx).Errorf("用户不存在: %s", email)
		return "", nil, authError.LoginUserNotExist
	}

	groups, err := a.authRepo.GetGroupsByUserID(ctx, user.ID)
	if err != nil {
		a.log.WithContext(ctx).Errorf(fmt.Sprintf("获取用户角色失败, email: %s, err: %v", email, err))
		return "", nil, authError.LoginGetRoleFail
	}

	// Convert roles to role names
	groupIDs := make([]int32, len(*groups))
	for i, role := range *groups {
		groupIDs[i] = role.GroupID
	}
	// Generate JWT token
	token, err := auth.GenerateToken(uint(user.ID), user.Userid, user.Name, email, groupIDs, user.IsStaff, a.bizConf.LoginConf.Secret, time.Hour*24)
	if err != nil {
		return "", nil, authError.LoginGenerateTokenFail
	}
	_ = a.authRepo.UpdateUser(ctx, user.ID, &map[string]interface{}{
		"avatar":          userInfo.Data.AvatarUrl,
		"union_id":        userInfo.Data.UnionId,
		"open_id":         userInfo.Data.OpenId,
		"user_id":         userInfo.Data.UserId,
		"job_title":       userDetail.JobTitle,
		"department":      department,
		"department_path": departmentPath,
		"last_login":      time.Now(),
	})
	return token, user, nil
}

func (a *AuthCase) QueryOrgByUser(ctx context.Context, request *pb.QueryOrgByUserRequest) ([]*pb.OrgUser, error) {
	ret := make([]*pb.OrgUser, 0)
	// 用户基础信息
	orgUser, err := a.authRepo.GetOrgUserByUserName(ctx, request.Search)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return nil, authError.UserSelectFail
	}
	if orgUser == nil {
		a.log.WithContext(ctx).Warnf("用户不存在")
		return nil, nil
	}
	for _, user := range orgUser {
		ret = append(ret, &pb.OrgUser{
			UserId:   user.ID,
			UserName: user.UserName,
			Email:    user.Email,
		})
	}
	return ret, nil
}

// QueryMenuTree 查询菜单树
func (a *AuthCase) QueryMenuTree(ctx context.Context) ([]*MenuTree, error) {
	// 用户基础信息
	userBaseInfo, err := a.GetUserInfo(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return nil, authError.QueryUserFail
	}
	// 非超级管理员无法请求
	if userBaseInfo.IsStaff != 1 {
		return nil, authError.UserNotPermission
	}
	// 查询菜单树
	menuList, err := a.authRepo.GetMenuTree(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询菜单失败: %v", err)
		return nil, authError.QueryMenuTreeFail
	}

	return menuList, nil
}

func (a *AuthCase) CheckAddParams(req *pb.AddOrgRequest) error {
	if req.OrgName == "" {
		return authError.OrgNameRequired
	}
	if req.ParentId == 0 {
		return authError.OrgParentIDRequired
	}

	return nil
}

func (a *AuthCase) CheckUpdateParams(req *pb.UpdateOrgRequest) error {
	if req.OrgName == "" {
		return authError.OrgNameRequired
	}
	if req.OrgId == 0 {
		return authError.OrgIDRequired
	}

	return nil
}

func (a *AuthCase) CheckFeishuLoginParams(req *pb.FeishuLoginRequest) error {

	if req.Code == "" {
		return authError.LoginCodeRequired
	}
	if req.SourceUrl == "" {
		return authError.LoginSourceUrlRequired
	}

	return nil
}
