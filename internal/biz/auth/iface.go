package biz

import (
	"context"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	pb "vision_hub/api/auth/v1"
	commonBiz "vision_hub/internal/biz/common"
	"vision_hub/internal/data/auth/model"
)

// 用于repo层接口定义

// IAuthRepo 用户相关接口
type IAuthRepo interface {
	// 用户相关
	CreateUserTx(ctx context.Context, user *model.SysUser) error
	GetUserInfoByID(ctx context.Context, id int32) (*UserInfo, error)
	GetUserInfoByEmail(ctx context.Context, email string, id int64) (*UserInfo, error)
	GetGroupsByUserID(ctx context.Context, userID int32) (*[]Group, error)
	UpdateUser(ctx context.Context, userID int32, updateParams *map[string]interface{}) error
	GetUserPageList(ctx context.Context, params *UserPageListParams) (*[]UserListItem, *pb.Pagination, error)
	GetUserSelect(ctx context.Context, search string, ids []int32, pageSize int32) (*[]commonBiz.UserInfoStruct, error)
	// 部门相关
	AddOrg(ctx context.Context, org *model.SysOrg) error
	UpdateOrg(ctx context.Context, orgId int32, updateParams *map[string]interface{}) error
	GetOrgList(ctx context.Context, params *OrgListParams) (*[]OrgTreeData, error)
	GetOrgListByParentID(ctx context.Context, parentID int64) (*[]Org, error)
	GetOrgByID(ctx context.Context, Id int32) (*Org, error)
	GetOrgByOrgName(ctx context.Context, orgName string) (*Org, error)
	GetOrgUserByUserName(ctx context.Context, userName string) ([]*model.SysUser, error)
	GetOrgUserByOrgId(ctx context.Context, orgIds []int32, req *pb.QueryOrgByUserRequest) ([]*model.SysUser, error)
	DeleteOrg(ctx context.Context, orgIds []int32) error
	GetUserOrgByUserId(ctx context.Context, userId int64) (*model.SysUserOrg, error)
	UpdateUserOrg(ctx context.Context, userId int32, orgUser *map[string]interface{}) error

	DeleteUserTx(ctx context.Context, userIds []int32) error

	GetMenuTree(ctx context.Context) ([]*MenuTree, error)
	GetOrgNamesByUserID(ctx context.Context, userID int64) (orgName string, err error)
}

type IAuthThirdPartyRepo interface {
	GetAppAccessToken(ctx context.Context, loginAppId, LoginAppSecret string) (string, error)
	GetUserAccessToken(ctx context.Context, appAccessToken, code string) (string, error)
	GetUserInfo(ctx context.Context, userAccessToken string) (FeishuUserInfoResponse, error)
	GetNewUserAccessToken(ctx context.Context, loginAppId, LoginAppSecret string, params *pb.FeishuLoginRequest) (string, error)
	GetUserDetail(ctx context.Context, loginAppId, LoginAppSecret, unionId, userAccessToken string) (*larkcontact.User, error)
}

// IAuthGroupRepo 角色相关接口
type IAuthGroupRepo interface {
	CreateGroup(ctx context.Context, req *CreateOrUpdateGroupReq) error
	UpdateGroup(ctx context.Context, req *CreateOrUpdateGroupReq) error
	DeleteGroup(ctx context.Context, groupIds int32) error
	GetGroupDetail(ctx context.Context, groupId int32) (*GroupDetail, error)
	QueryGroupList(ctx context.Context, params *QueryGroupListReq) (*GroupList, error)
	CheckGroupExists(ctx context.Context, groupName, groupCode string, id int32) (bool, bool, error)
}
