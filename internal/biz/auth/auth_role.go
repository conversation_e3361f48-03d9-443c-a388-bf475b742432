package biz

import (
	"context"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/errors/auth"
	"vision_hub/internal/middleware"
)

// AuthRoleCase handles the business logic for auth roles.
type AuthRoleCase struct {
	log  *bizcommon.BizLogHelper
	repo IAuthRoleRepo
}

// NewAuthRoleCase creates a new AuthRoleCase.
func NewAuthRoleCase(log *bizcommon.BizLogHelper, repo IAuthRoleRepo) *AuthRoleCase {
	return &AuthRoleCase{
		log:  log,
		repo: repo,
	}
}

// CreateRole creates a new auth Role.
func (c *AuthRoleCase) CreateRole(ctx context.Context, req *CreateOrUpdateRoleReq) error {
	// Get user info
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return auth.QueryUserFail
	}
	if userInfo.IsStaff != 1 {
		return auth.UserNotPermissionUpdateRole
	}
	// Check if Role name exists
	nameExist, codeExist, err := c.repo.CheckRoleExists(ctx, req.Name, req.Code)
	if err != nil {
		c.log.WithContext(ctx).Errorf("检查角色名称是否已存在失败: %v", err)
		return err
	}
	if nameExist {
		return auth.RoleNameExists
	}
	if codeExist {
		return auth.RoleCodeExists
	}
	req.CreateUser = userInfo.Email
	// req.UpdateUser = userInfo.Email
	// Create role
	return c.repo.CreateRole(ctx, req)
}

// UpdateRole updates an existing auth role.
func (c *AuthRoleCase) UpdateRole(ctx context.Context, req *CreateOrUpdateRoleReq) error {
	// Get user info
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return auth.QueryUserFail
	}
	if userInfo.IsStaff != 1 {
		return auth.UserNotPermissionUpdateRole
	}

	req.UpdateUser = userInfo.Email
	// req.CreateUser = userInfo.Email
	// Update role
	return c.repo.UpdateRole(ctx, req)
}

// DeleteRole deletes an auth role.
func (c *AuthRoleCase) DeleteRole(ctx context.Context, id int32) error {

	// Get user info
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return auth.QueryUserFail
	}
	c.log.Infof("删除角色，用户信息：%v，id: %d", userInfo, id)
	if userInfo.IsStaff != 1 {
		return auth.UserNotPermissionUpdateRole
	}
	// Delete role
	// 先判断是否存在关联关系
	roleUser, err := c.repo.GetRoleUser(ctx, id)
	if err != nil {
		return auth.DelRoleUser
	}
	if len(roleUser) > 0 {
		return auth.DelRoleUserExists
	}
	return c.repo.DeleteRole(ctx, id)
}

// GetRoleDetail retrieves the details of an auth role.
func (c *AuthRoleCase) GetRoleDetail(ctx context.Context, id int32) (*RoleDetail, error) {
	// Get role detail
	return c.repo.GetRoleDetail(ctx, id)
}

// QueryRoleList retrieves a list of auth role.
func (c *AuthRoleCase) QueryRoleList(ctx context.Context, req *QueryRoleListReq) (*RoleList, error) {
	// Query role list
	return c.repo.QueryRoleList(ctx, req)
}
