package biz

import (
	"github.com/google/wire"
	biz "vision_hub/internal/biz/auth"
	authorizationBiz "vision_hub/internal/biz/authorization"
	common "vision_hub/internal/biz/common"
	customerProjectBiz "vision_hub/internal/biz/customer_project"
	adsAccountBiz "vision_hub/internal/biz/customer_project_space/account"
	materialManageBiz "vision_hub/internal/biz/customer_project_space/material_manage"
	customerResourceBiz "vision_hub/internal/biz/customer_project_space/resource"
	dictManageBiz "vision_hub/internal/biz/dict_manage"
	internalService "vision_hub/internal/biz/internal_service"
	clientPortalBiz "vision_hub/internal/biz/internal_service/client_portal"
	resourceBiz "vision_hub/internal/biz/resource_pool"
	resourceIpBiz "vision_hub/internal/biz/resource_pool/ip"
	resourceOutdoorScreenBiz "vision_hub/internal/biz/resource_pool/outdoor_screen"
	resourceBrBiz "vision_hub/internal/biz/resource_pool/pr"
	resourcePublisherBiz "vision_hub/internal/biz/resource_pool/publisher"
	resourceReporterBiz "vision_hub/internal/biz/resource_pool/reporter"
	resourceSupplierBiz "vision_hub/internal/biz/resource_pool/supplier"
	taskBiz "vision_hub/internal/biz/task"
	userActivity "vision_hub/internal/biz/user_activity"
	videoAigcBiz "vision_hub/internal/biz/video_aigc"
)

// BizProviderSet is biz providers.
var BizProviderSet = wire.NewSet(
	common.NewBizLogHelper,
	biz.NewAuthCase,
	biz.NewAuthGroupCase,
	authorizationBiz.NewAuthorizationCase,
	common.NewCommonCase,
	resourceBrBiz.NewPrResourceCase,
	resourceIpBiz.NewIpResourceCase,
	dictManageBiz.NewDictManageCase,
	resourcePublisherBiz.NewPublisherResourceCase,
	resourceSupplierBiz.NewSupplierResourceCase,
	resourceOutdoorScreenBiz.NewOutdoorScreenResourceCase,
	resourceReporterBiz.NewReporterResourceCase,
	videoAigcBiz.NewVideoAIGCCase,
	taskBiz.NewTaskCase,
	customerProjectBiz.NewCustomerProjectCase,
	clientPortalBiz.NewClientPortalCase,
	internalService.NewClientPortalSettlementClient,
	materialManageBiz.NewMaterialManageCase,
	adsAccountBiz.NewAdsAccountCase,
	userActivity.NewUserActivityCase,
	resourceBiz.NewResourceCase,
	customerResourceBiz.NewCustomerProResourceCase,
)
