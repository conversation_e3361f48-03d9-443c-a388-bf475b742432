package authorization

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/conf"
	"vision_hub/internal/constants"
	"vision_hub/internal/data/authorization/model"
	datacommon "vision_hub/internal/data/common"
	authError "vision_hub/internal/errors/auth"
	authorizationError "vision_hub/internal/errors/authorization"
	"vision_hub/internal/infrastructure/third_party"
	"vision_hub/internal/middleware"
)

// AuthorizationCase 授权业务逻辑
type AuthorizationCase struct {
	log  *bizcommon.BizLogHelper
	data *datacommon.Data
	repo IAuthorizationRepo
	bc   *conf.BizConf
}

// NewAuthorizationCase 创建授权业务逻辑实例
func NewAuthorizationCase(log *bizcommon.BizLogHelper, data *datacommon.Data, repo IAuthorizationRepo, bc *conf.BizConf) *AuthorizationCase {
	return &AuthorizationCase{
		log:  log,
		data: data,
		repo: repo,
		bc:   bc,
	}
}

// CreateAuthorization 创建授权
func (c *AuthorizationCase) CreateAuthorization(ctx context.Context, req *CreateAuthorizationReq) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}

	// 验证授权配置并获取更新后的配置
	var finalConfigJSON string
	if req.AuthorizationConfig != "" {
		// 解析原始配置
		var configMap map[string]interface{}
		if err := json.Unmarshal([]byte(req.AuthorizationConfig), &configMap); err != nil {
			c.log.WithContext(ctx).Errorf("配置JSON格式错误: %v", err)
			return fmt.Errorf("配置JSON格式错误: %v", err)
		}

		// 验证配置（可能会修改configMap，如TikTok添加token信息）
		if err := c.validateAuthConfigWithUpdate(ctx, req.AuthorizationType, req.AuthorizationPlatform, configMap); err != nil {
			c.log.WithContext(ctx).Errorf("授权配置验证失败: %v", err)
			return fmt.Errorf("授权配置验证失败: %v", err)
		}

		// 将更新后的配置转换为JSON
		updatedConfigBytes, err := json.Marshal(configMap)
		if err != nil {
			c.log.WithContext(ctx).Errorf("序列化更新后的配置失败: %v", err)
			return fmt.Errorf("序列化更新后的配置失败: %v", err)
		}
		finalConfigJSON = string(updatedConfigBytes)
	}

	// 检查授权名称在当前用户下是否已存在
	exists, err := c.repo.CheckAuthorizationExists(ctx, req.Name, int32(userInfo.UserID), 0)
	if err != nil {
		c.log.WithContext(ctx).Errorf("检查授权名称是否存在失败: %v", err)
		return err
	}
	if exists {
		c.log.WithContext(ctx).Errorf("用户 %d 的授权名称已存在: %s", userInfo.UserID, req.Name)
		return authError.GroupNameExists // 复用现有错误类型
	}

	// 使用验证后的配置
	var configJSON json.RawMessage
	if finalConfigJSON != "" {
		configJSON = json.RawMessage(finalConfigJSON)
	}

	// 创建授权模型
	auth := &model.SysUserAuthorization{
		Name:                  req.Name,
		AuthorizationType:     req.AuthorizationType,
		AuthorizationPlatform: req.AuthorizationPlatform,
		AuthorizationConfig:   configJSON,
		Status:                req.Status,
		CreateUserID:          int32(userInfo.UserID),
	}

	// 设置过期时间
	if req.ExpiresAt != nil {
		auth.ExpiresAt.Time = *req.ExpiresAt
		auth.ExpiresAt.Valid = true
	}

	// 使用事务保证原子性
	return c.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 创建授权
		err = c.repo.CreateAuthorization(ctx, auth)
		if err != nil {
			c.log.WithContext(ctx).Errorf("创建授权失败: %v", err)
			return err
		}

		// 记录操作日志
		if logErr := c.createOptLog(ctx, "CREATE", auth.ID, nil, auth); logErr != nil {
			c.log.WithContext(ctx).Errorf("记录创建操作日志失败: %v", logErr)
			return logErr // 事务中操作日志失败也要回滚
		}

		return nil
	})
}

// UpdateAuthorization 更新授权
func (c *AuthorizationCase) UpdateAuthorization(ctx context.Context, req *UpdateAuthorizationReq) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}

	// 检查授权是否存在，并获取原始数据
	originalAuth, err := c.repo.GetAuthorizationByID(ctx, req.ID)
	if err != nil {
		c.log.WithContext(ctx).Errorf("授权不存在: %d", req.ID)
		return err
	}

	// 权限检查：非管理员只能更新自己创建的授权
	if userInfo.IsStaff != 1 && originalAuth.CreateUserID != int32(userInfo.UserID) {
		c.log.WithContext(ctx).Errorf("用户 %d 无权限更新授权 %d", userInfo.UserID, req.ID)
		return authError.UserNotPermission
	}

	// 检查授权名称在当前用户下是否已被其他记录使用
	exists, err := c.repo.CheckAuthorizationExists(ctx, req.Name, originalAuth.CreateUserID, req.ID)
	if err != nil {
		c.log.WithContext(ctx).Errorf("检查授权名称是否存在失败: %v", err)
		return err
	}
	if exists {
		c.log.WithContext(ctx).Errorf("用户 %d 的授权名称已存在: %s", originalAuth.CreateUserID, req.Name)
		return authError.GroupNameExists // 复用现有错误类型
	}

	// 准备更新参数
	updateParams := map[string]interface{}{
		"name":                   req.Name,
		"authorization_type":     req.AuthorizationType,
		"authorization_platform": req.AuthorizationPlatform,
		"status":                 req.Status,
		"update_time":            time.Now(),
	}

	// 处理授权配置
	if req.AuthorizationConfig != "" {
		updateParams["authorization_config"] = json.RawMessage(req.AuthorizationConfig)
	}

	// 处理过期时间
	if req.ExpiresAt != nil {
		updateParams["expires_at"] = req.ExpiresAt
	}

	// 使用事务保证原子性
	return c.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 更新授权
		err = c.repo.UpdateAuthorization(ctx, req.ID, updateParams)
		if err != nil {
			c.log.WithContext(ctx).Errorf("更新授权失败: %v", err)
			return err
		}

		// 记录操作日志
		if logErr := c.createOptLog(ctx, "UPDATE", req.ID, originalAuth, updateParams); logErr != nil {
			c.log.WithContext(ctx).Errorf("记录更新操作日志失败: %v", logErr)
			return logErr // 事务中操作日志失败也要回滚
		}

		return nil
	})
}

// ListAuthorization 获取授权列表
func (c *AuthorizationCase) ListAuthorization(ctx context.Context, req *ListAuthorizationReq) (*AuthorizationListData, error) {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return nil, authError.QueryUserFail
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 非管理员只能查看自己的授权
	if userInfo.IsStaff != 1 {
		req.CreateUserID = int32(userInfo.UserID)
	}

	auths, pagination, err := c.repo.ListAuthorization(ctx, req)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取授权列表失败: %v", err)
		return nil, err
	}

	// 转换为业务对象
	authList := make([]*Authorization, 0, len(auths))
	for _, auth := range auths {
		bizAuth := &Authorization{
			ID:                    auth.ID,
			Name:                  auth.Name,
			AuthorizationType:     auth.AuthorizationType,
			AuthorizationPlatform: auth.AuthorizationPlatform,
			Status:                auth.Status,
			CreateUserID:          auth.CreateUserID,
		}

		// 处理授权配置
		if auth.AuthorizationConfig != nil {
			bizAuth.AuthorizationConfig = string(auth.AuthorizationConfig)
		}

		// 处理过期时间
		if auth.ExpiresAt.Valid {
			bizAuth.ExpiresAt = &auth.ExpiresAt.Time
		}

		authList = append(authList, bizAuth)
	}

	return &AuthorizationListData{
		List:       authList,
		Pagination: pagination,
	}, nil
}

// RemoveAuthorization 删除授权
func (c *AuthorizationCase) RemoveAuthorization(ctx context.Context, id int32) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}

	// 检查授权是否存在，并获取原始数据
	originalAuth, err := c.repo.GetAuthorizationByID(ctx, id)
	if err != nil {
		c.log.WithContext(ctx).Errorf("授权不存在: %d", id)
		return err
	}

	// 权限检查：非管理员只能删除自己创建的授权
	if userInfo.IsStaff != 1 && originalAuth.CreateUserID != int32(userInfo.UserID) {
		c.log.WithContext(ctx).Errorf("用户 %d 无权限删除授权 %d", userInfo.UserID, id)
		return authError.UserNotPermission
	}

	// 使用事务保证原子性
	return c.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 删除授权
		err = c.repo.DeleteAuthorization(ctx, id)
		if err != nil {
			c.log.WithContext(ctx).Errorf("删除授权失败: %v", err)
			return err
		}

		// 记录操作日志
		if logErr := c.createOptLog(ctx, "DELETE", id, originalAuth, nil); logErr != nil {
			c.log.WithContext(ctx).Errorf("记录删除操作日志失败: %v", logErr)
			return logErr // 事务中操作日志失败也要回滚
		}

		return nil
	})
}

// createOptLog 创建操作日志
func (c *AuthorizationCase) createOptLog(ctx context.Context, optType string, authorizationID int32, originalData, updateData interface{}) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return err
	}

	// 转换原始数据为JSON
	var originalNote json.RawMessage
	if originalData != nil {
		originalBytes, err := json.Marshal(originalData)
		if err != nil {
			c.log.WithContext(ctx).Errorf("序列化原始数据失败: %v", err)
			return err
		}
		originalNote = originalBytes
	}

	// 转换更新数据为JSON
	var updateNote json.RawMessage
	if updateData != nil {
		updateBytes, err := json.Marshal(updateData)
		if err != nil {
			c.log.WithContext(ctx).Errorf("序列化更新数据失败: %v", err)
			return err
		}
		updateNote = updateBytes
	}

	// 创建操作日志
	optLog := &model.SysUserAuthorizationOptLog{
		OptUser:         userInfo.Email,
		OptType:         optType,
		AuthorizationID: authorizationID,
		OriginalNote:    originalNote,
		UpdateNote:      updateNote,
	}

	return c.repo.CreateAuthorizationOptLog(ctx, optLog)
}

// validateAuthConfig 验证授权配置（通用方法）
func (c *AuthorizationCase) validateAuthConfig(ctx context.Context, authType, platform, configJSON string) error {
	// 只支持 Impact 和 TikTok
	if platform != "impact" && platform != "tiktok" {
		return fmt.Errorf("不支持的授权平台: %s", platform)
	}

	// 解析配置JSON
	var configMap map[string]interface{}
	if err := json.Unmarshal([]byte(configJSON), &configMap); err != nil {
		return fmt.Errorf("配置JSON格式错误: %v", err)
	}

	// 根据平台进行验证
	switch platform {
	case "impact":
		return c.validateImpactAuth(ctx, configMap)
	case "tiktok":
		return c.validateTikTokAuth(ctx, configMap)
	default:
		return fmt.Errorf("不支持的授权平台: %s", platform)
	}
}

// validateImpactAuth 验证Impact授权
func (c *AuthorizationCase) validateImpactAuth(ctx context.Context, config map[string]interface{}) error {
	accountSID, ok := config["account_sid"].(string)
	if !ok || accountSID == "" {
		return authorizationError.ImpactMissingAccountSID
	}

	authToken, ok := config["auth_token"].(string)
	if !ok || authToken == "" {
		return authorizationError.ImpactMissingAuthToken
	}

	// 调用third_party验证Impact授权
	impactAPI := third_party.NewImpactAPI("", c.bc.SysConf.Proxy)
	response, err := impactAPI.ValidateAuth(ctx, accountSID, authToken)
	if err != nil {
		c.log.WithContext(ctx).Errorf("Impact API连接失败: %v", err)
		return authorizationError.ImpactAPIConnectionFail
	}

	if response.Status != "SUCCESS" && response.Status != "" {
		c.log.WithContext(ctx).Errorf("Impact授权无效: %s", response.Message)
		return authorizationError.ImpactInvalidCredentials
	}

	return nil
}

// validateTikTokAuth 验证TikTok授权
func (c *AuthorizationCase) validateTikTokAuth(ctx context.Context, config map[string]interface{}) error {
	authCode, ok := config["auth_code"].(string)
	if !ok || authCode == "" {
		return authorizationError.TikTokMissingAuthCode
	}

	// 从配置文件获取app_id和secret
	if c.bc.TiktokOauth == nil {
		c.log.WithContext(ctx).Errorf("TikTok OAuth配置未设置")
		return authorizationError.TikTokConfigMissingAppID
	}

	appID := c.bc.TiktokOauth.AppId
	secret := c.bc.TiktokOauth.Secret
	baseURL := c.bc.TiktokOauth.BaseUrl

	if appID == "" {
		return authorizationError.TikTokConfigMissingAppID
	}
	if secret == "" {
		return authorizationError.TikTokConfigMissingSecret
	}
	if baseURL == "" {
		baseURL = "https://business-api.tiktok.com"
	}

	// 调用third_party通过auth_code换取token
	tiktokAPI := third_party.NewTikTokAPI(baseURL, "", c.bc.SysConf.Proxy)
	tokenResponse, err := tiktokAPI.GetAccessTokenByAuthCode(ctx, authCode, appID, secret)
	if err != nil {
		c.log.WithContext(ctx).Errorf("TikTok token交换失败: %v", err)
		return authorizationError.TikTokTokenExchangeFail
	}

	// 将token信息保存到config中
	config["access_token"] = tokenResponse.Data.AccessToken
	config["advertiser_ids"] = tokenResponse.Data.AdvertiserIDs
	config["scope"] = tokenResponse.Data.Scope
	config["expires_in"] = tokenResponse.Data.ExpiresIn
	config["refresh_token"] = tokenResponse.Data.RefreshToken
	config["refresh_token_expires_in"] = tokenResponse.Data.RefreshTokenExpiresIn

	// 移除敏感的auth_code
	delete(config, "auth_code")

	return nil
}

// validateAuthConfigWithUpdate 验证授权配置并更新配置内容
func (c *AuthorizationCase) validateAuthConfigWithUpdate(ctx context.Context, authType, platform string, config map[string]interface{}) error {
	// 检查是否为支持的平台
	if !constants.IsValidAuthPlatform(platform) {
		return authorizationError.UnsupportedAuthPlatform
	}

	// 根据平台进行验证
	switch platform {
	case constants.AuthPlatformImpact:
		return c.validateImpactAuth(ctx, config)
	case constants.AuthPlatformTikTok:
		return c.validateTikTokAuth(ctx, config)
	default:
		return authorizationError.UnsupportedAuthPlatform
	}
}
