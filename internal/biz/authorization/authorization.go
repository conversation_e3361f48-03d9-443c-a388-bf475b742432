package authorization

import (
	"context"
	"encoding/json"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/conf"
	"vision_hub/internal/constants"
	"vision_hub/internal/data/authorization/model"
	datacommon "vision_hub/internal/data/common"
	authError "vision_hub/internal/errors/auth"
	authorizationError "vision_hub/internal/errors/authorization"
	"vision_hub/internal/infrastructure/third_party"
	"vision_hub/internal/middleware"
)

// AuthorizationCase 授权业务逻辑
type AuthorizationCase struct {
	log  *bizcommon.BizLogHelper
	data *datacommon.Data
	repo IAuthorizationRepo
	bc   *conf.BizConf
}

// NewAuthorizationCase 创建授权业务逻辑实例
func NewAuthorizationCase(log *bizcommon.BizLogHelper, data *datacommon.Data, repo IAuthorizationRepo, bc *conf.BizConf) *AuthorizationCase {
	return &AuthorizationCase{
		log:  log,
		data: data,
		repo: repo,
		bc:   bc,
	}
}

// CreateAuthorization 创建授权
func (c *AuthorizationCase) CreateAuthorization(ctx context.Context, req *CreateAuthorizationReq) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}
	// 创建授权模型
	auth := &model.SysUserAuthorization{
		AuthorizationType:     req.AuthorizationType,
		AuthorizationPlatform: req.AuthorizationPlatform,
		AuthorizationConfig:   req.AuthorizationConfig,
		CreateUserID:          int32(userInfo.UserID),
	}
	// 验证授权配置
	switch req.AuthorizationPlatform {
	case constants.AuthPlatformImpact:
		auth.Name, err = c.validateImpactAuth(ctx, req.AuthorizationConfig)
		break
	case constants.AuthPlatformTikTok:
		err = c.validateTikTokAuth(ctx, req.AuthorizationConfig)
		break
	default:
		return authorizationError.UnsupportedAuthPlatform
	}
	if err != nil {
		c.log.WithContext(ctx).Errorf("验证授权配置失败: %v", err)
		return err
	}
	// 使用事务保证原子性
	return c.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 创建授权
		err = c.repo.CreateAuthorization(ctx, auth)
		if err != nil {
			c.log.WithContext(ctx).Errorf("创建授权失败: %v", err)
			return err
		}

		// 记录操作日志
		if logErr := c.createOptLog(ctx, "CREATE", auth.ID, nil, auth); logErr != nil {
			c.log.WithContext(ctx).Errorf("记录创建操作日志失败: %v", logErr)
			return logErr // 事务中操作日志失败也要回滚
		}

		return nil
	})
}

// UpdateAuthorization 更新授权
func (c *AuthorizationCase) UpdateAuthorization(ctx context.Context, req *UpdateAuthorizationReq) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}

	// 检查授权是否存在，并获取原始数据
	originalAuth, err := c.repo.GetAuthorizationByID(ctx, req.ID)
	if err != nil {
		c.log.WithContext(ctx).Errorf("授权不存在: %d", req.ID)
		return err
	}

	// 权限检查：非管理员只能更新自己创建的授权
	if userInfo.IsStaff != 1 && originalAuth.CreateUserID != int32(userInfo.UserID) {
		c.log.WithContext(ctx).Errorf("用户 %d 无权限更新该授权", userInfo.UserID)
		return authError.UserNotPermission
	}

	// 准备更新参数
	updateParams := map[string]interface{}{
		"authorization_type":     req.AuthorizationType,
		"authorization_platform": req.AuthorizationPlatform,
		"authorization_config":   req.AuthorizationConfig,
	}

	// 验证授权配置
	switch req.AuthorizationPlatform {
	case constants.AuthPlatformImpact:
		updateParams["name"], err = c.validateImpactAuth(ctx, req.AuthorizationConfig)
		break
	case constants.AuthPlatformTikTok:
		err = c.validateTikTokAuth(ctx, req.AuthorizationConfig)
		break
	default:
		return authorizationError.UnsupportedAuthPlatform
	}
	if err != nil {
		c.log.WithContext(ctx).Errorf("验证授权配置失败: %v", err)
		return err
	}

	// 使用事务保证原子性
	return c.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 更新授权
		err = c.repo.UpdateAuthorization(ctx, req.ID, updateParams)
		if err != nil {
			c.log.WithContext(ctx).Errorf("更新授权失败: %v", err)
			return err
		}

		// 记录操作日志
		if logErr := c.createOptLog(ctx, "UPDATE", req.ID, originalAuth, updateParams); logErr != nil {
			c.log.WithContext(ctx).Errorf("记录更新操作日志失败: %v", logErr)
			return logErr // 事务中操作日志失败也要回滚
		}

		return nil
	})
}

// ListAuthorization 获取授权列表
func (c *AuthorizationCase) ListAuthorization(ctx context.Context, req *ListAuthorizationReq) (*AuthorizationListData, error) {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return nil, authError.QueryUserFail
	}

	// 设置默认分页参数
	if req.PageNum <= 0 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 非管理员只能查看自己的授权
	if userInfo.IsStaff != 1 {
		req.CreateUserID = int32(userInfo.UserID)
	}

	auths, pagination, err := c.repo.ListAuthorization(ctx, req)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取授权列表失败: %v", err)
		return nil, err
	}

	// 转换为业务对象
	authList := make([]*Authorization, 0, len(auths))
	for _, auth := range auths {
		bizAuth := &Authorization{
			ID:                    auth.ID,
			Name:                  auth.Name,
			AuthorizationType:     auth.AuthorizationType,
			AuthorizationPlatform: auth.AuthorizationPlatform,
			Status:                auth.Status,
			CreateUserID:          auth.CreateUserID,
		}

		// 处理授权配置
		if auth.AuthorizationConfig != nil {
			bizAuth.AuthorizationConfig = string(auth.AuthorizationConfig)
		}

		// 处理过期时间
		if auth.ExpiresAt.Valid {
			bizAuth.ExpiresAt = &auth.ExpiresAt.Time
		}

		authList = append(authList, bizAuth)
	}

	return &AuthorizationListData{
		List:       authList,
		Pagination: pagination,
	}, nil
}

// GetAuthorizationDetail 获取授权详情
func (c *AuthorizationCase) GetAuthorizationDetail(ctx context.Context, id int32) (*Authorization, error) {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return nil, authError.QueryUserFail
	}

	// 根据ID获取授权信息
	auth, err := c.repo.GetAuthorizationByID(ctx, id)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取授权详情失败: %v", err)
		return nil, authorizationError.AuthorizationNotFound
	}

	// 非管理员只能查看自己的授权
	if userInfo.IsStaff != 1 && auth.CreateUserID != int32(userInfo.UserID) {
		c.log.WithContext(ctx).Errorf("用户 %d 无权限查看授权 %d", userInfo.UserID, id)
		return nil, authorizationError.AuthorizationNoPermission
	}

	// 转换为业务对象
	bizAuth := &Authorization{
		ID:                    auth.ID,
		Name:                  auth.Name,
		AuthorizationType:     auth.AuthorizationType,
		AuthorizationPlatform: auth.AuthorizationPlatform,
		Status:                auth.Status,
		CreateUserID:          auth.CreateUserID,
	}

	// 处理授权配置
	if auth.AuthorizationConfig != nil {
		bizAuth.AuthorizationConfig = string(auth.AuthorizationConfig)
	}

	// 处理过期时间
	if auth.ExpiresAt.Valid {
		bizAuth.ExpiresAt = &auth.ExpiresAt.Time
	}

	return bizAuth, nil
}

// RemoveAuthorization 删除授权
func (c *AuthorizationCase) RemoveAuthorization(ctx context.Context, id int32) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return authError.QueryUserFail
	}

	// 检查授权是否存在，并获取原始数据
	originalAuth, err := c.repo.GetAuthorizationByID(ctx, id)
	if err != nil {
		c.log.WithContext(ctx).Errorf("授权不存在: %d", id)
		return err
	}

	// 权限检查：非管理员只能删除自己创建的授权
	if userInfo.IsStaff != 1 && originalAuth.CreateUserID != int32(userInfo.UserID) {
		c.log.WithContext(ctx).Errorf("用户 %d 无权限删除授权 %d", userInfo.UserID, id)
		return authError.UserNotPermission
	}

	// 使用事务保证原子性
	return c.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 删除授权
		err = c.repo.DeleteAuthorization(ctx, id)
		if err != nil {
			c.log.WithContext(ctx).Errorf("删除授权失败: %v", err)
			return err
		}

		// 记录操作日志
		if logErr := c.createOptLog(ctx, "DELETE", id, originalAuth, nil); logErr != nil {
			c.log.WithContext(ctx).Errorf("记录删除操作日志失败: %v", logErr)
			return logErr // 事务中操作日志失败也要回滚
		}

		return nil
	})
}

// createOptLog 创建操作日志
func (c *AuthorizationCase) createOptLog(ctx context.Context, optType string, authorizationID int32, originalData, updateData interface{}) error {
	// 获取当前用户信息
	userInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		c.log.WithContext(ctx).Errorf("获取用户信息失败: %v", err)
		return err
	}

	// 转换原始数据为JSON
	var originalNote json.RawMessage
	if originalData != nil {
		originalBytes, err := json.Marshal(originalData)
		if err != nil {
			c.log.WithContext(ctx).Errorf("序列化原始数据失败: %v", err)
			return err
		}
		originalNote = originalBytes
	}

	// 转换更新数据为JSON
	var updateNote json.RawMessage
	if updateData != nil {
		updateBytes, err := json.Marshal(updateData)
		if err != nil {
			c.log.WithContext(ctx).Errorf("序列化更新数据失败: %v", err)
			return err
		}
		updateNote = updateBytes
	}

	// 创建操作日志
	optLog := &model.SysUserAuthorizationOptLog{
		OptUser:         userInfo.Email,
		OptType:         optType,
		AuthorizationID: authorizationID,
		OriginalNote:    originalNote,
		UpdateNote:      updateNote,
	}

	return c.repo.CreateAuthorizationOptLog(ctx, optLog)
}

// validateImpactAuth 验证Impact授权
func (c *AuthorizationCase) validateImpactAuth(ctx context.Context, config json.RawMessage) (string, error) {
	var impactAuthConfig third_party.ImpactAuthConfig
	err := json.Unmarshal(config, &impactAuthConfig)
	if err != nil {
		return "", err
	}

	// 调用third_party验证Impact授权
	impactAPI := third_party.NewImpactAPI("", c.bc.SysConf.Proxy)
	response, err := impactAPI.GetCompanyName(ctx, impactAuthConfig.AccountSID, impactAuthConfig.AuthToken)
	if err != nil {
		c.log.WithContext(ctx).Errorf("Impact授权验证失败: %v", err)
		return "", authorizationError.ImpactInvalidCredentials
	}

	// ValidateAuth方法已经处理了状态逻辑，如果返回成功说明状态是OK
	c.log.WithContext(ctx).Infof("Impact授权验证成功，AccountSID: %s, CompanyName: %s", impactAuthConfig.AccountSID, response.CompanyName)
	return response.CompanyName, nil
}

// validateTikTokAuth 验证TikTok授权
func (c *AuthorizationCase) validateTikTokAuth(ctx context.Context, config json.RawMessage) error {
	return nil
}
