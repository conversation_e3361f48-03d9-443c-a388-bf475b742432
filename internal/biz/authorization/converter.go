package authorization

import (
	"encoding/json"
	"google.golang.org/protobuf/types/known/structpb"
	pb "vision_hub/api/authorization/v1"
)

// ConvertCreateRequestToBiz 转换创建请求为业务对象
func ConvertCreateRequestToBiz(req *pb.CreateAuthorizationRequest) (*CreateAuthorizationReq, error) {
	bizReq := &CreateAuthorizationReq{
		Name:                  req.Name,
		AuthorizationType:     req.AuthorizationType,
		AuthorizationPlatform: req.AuthorizationPlatform,
	}

	// 转换授权配置
	if req.AuthorizationConfig != nil {
		configBytes, err := req.AuthorizationConfig.MarshalJSON()
		if err != nil {
			return nil, err
		}
		bizReq.AuthorizationConfig = configBytes
	}

	return bizReq, nil
}

// ConvertUpdateRequestToBiz 转换更新请求为业务对象
func ConvertUpdateRequestToBiz(req *pb.UpdateAuthorizationRequest) (*UpdateAuthorizationReq, error) {
	bizReq := &UpdateAuthorizationReq{
		ID:                    req.Id,
		AuthorizationType:     req.AuthorizationType,
		AuthorizationPlatform: req.AuthorizationPlatform,
	}

	// 转换授权配置
	if req.AuthorizationConfig != nil {
		configBytes, err := req.AuthorizationConfig.MarshalJSON()
		if err != nil {
			return nil, err
		}
		bizReq.AuthorizationConfig = configBytes
	}

	return bizReq, nil
}

// ConvertListRequestToBiz 转换列表请求为业务对象
func ConvertListRequestToBiz(req *pb.ListAuthorizationRequest) *ListAuthorizationReq {
	return &ListAuthorizationReq{
		Name:                  req.Name,
		AuthorizationPlatform: req.AuthorizationPlatform,
		PageNum:               req.PageNum,
		PageSize:              req.PageSize,
	}
}

// ConvertAuthorizationToPb 转换授权业务对象为Proto对象
func ConvertAuthorizationToPb(auth *Authorization) (*pb.Authorization, error) {
	pbAuth := &pb.Authorization{
		Id:                    auth.ID,
		Name:                  auth.Name,
		AuthorizationType:     auth.AuthorizationType,
		AuthorizationPlatform: auth.AuthorizationPlatform,
		Status:                auth.Status,
		CreateUserId:          auth.CreateUserID,
		CreateTime:            auth.CreateTime.Format("2006-01-02 15:04:05"),
		UpdateTime:            auth.UpdateTime.Format("2006-01-02 15:04:05"),
		ExpiresAt:             auth.ExpiresAt.Format("2006-01-02 15:04:05"),
	}

	// 转换授权配置
	if auth.AuthorizationConfig != nil {
		var configMap map[string]interface{}
		if err := json.Unmarshal(auth.AuthorizationConfig, &configMap); err == nil {
			if configStruct, err := structpb.NewStruct(configMap); err == nil {
				pbAuth.AuthorizationConfig = configStruct
			}
		}
	}

	return pbAuth, nil
}

// ConvertAuthorizationListToPb 转换授权列表为Proto对象
func ConvertAuthorizationListToPb(data *AuthorizationListData) (*pb.AuthorizationListData, error) {
	pbList := make([]*pb.Authorization, 0, len(data.List))

	for _, auth := range data.List {
		pbAuth, err := ConvertAuthorizationToPb(auth)
		if err != nil {
			return nil, err
		}
		pbList = append(pbList, pbAuth)
	}

	return &pb.AuthorizationListData{
		List:       pbList,
		Pagination: data.Pagination,
	}, nil
}
