package authorization

import (
	"context"
	pb "vision_hub/api/authorization/v1"
	"vision_hub/internal/data/authorization/model"
)

// IAuthorizationRepo 授权数据访问接口
type IAuthorizationRepo interface {
	// UpsertAuthorization Save授权
	UpsertAuthorization(ctx context.Context, auth *model.SysUserAuthorization) error

	// CreateAuthorization 创建授权
	CreateAuthorization(ctx context.Context, auth *model.SysUserAuthorization) error

	// UpdateAuthorization 更新授权
	UpdateAuthorization(ctx context.Context, id int32, updateParams map[string]interface{}) error

	// GetAuthorizationByID 根据ID获取授权
	GetAuthorizationByID(ctx context.Context, id int32) (*model.SysUserAuthorization, error)

	// ListAuthorization 获取授权列表
	ListAuthorization(ctx context.Context, req *ListAuthorizationReq) ([]*model.SysUserAuthorization, *pb.Pagination, error)

	// DeleteAuthorization 删除授权
	DeleteAuthorization(ctx context.Context, id int32) error

	// CheckAuthorizationExists 检查授权是否存在（在指定用户范围内）
	CheckAuthorizationExists(ctx context.Context, name string, createUserID int32, excludeID int32) (bool, error)

	// CreateAuthorizationOptLog 创建操作日志
	CreateAuthorizationOptLog(ctx context.Context, log *model.SysUserAuthorizationOptLog) error
}
