package authorization

import (
	"time"
	pb "vision_hub/api/authorization/v1"
)

// Authorization 业务对象
type Authorization struct {
	ID                    int32     `json:"id"`
	Name                  string    `json:"name"`
	AuthorizationType     string    `json:"authorization_type"`
	AuthorizationPlatform string    `json:"authorization_platform"`
	AuthorizationConfig   []byte    `json:"authorization_config"`
	Status                string    `json:"status"`
	ExpiresAt             time.Time `json:"expires_at"`
	CreateUserID          int32     `json:"create_user_id"`
	CreateTime            time.Time `json:"create_time"`
	UpdateTime            time.Time `json:"update_time"`
}

// CreateAuthorizationReq 创建授权请求
type CreateAuthorizationReq struct {
	Name                  string `json:"name"`
	AuthorizationType     string `json:"authorization_type"`
	AuthorizationPlatform string `json:"authorization_platform"`
	AuthorizationConfig   []byte `json:"authorization_config"`
}

// UpdateAuthorizationReq 更新授权请求
type UpdateAuthorizationReq struct {
	ID                    int32  `json:"id"`
	AuthorizationType     string `json:"authorization_type"`
	AuthorizationPlatform string `json:"authorization_platform"`
	AuthorizationConfig   []byte `json:"authorization_config"`
}

// ListAuthorizationReq 列表查询请求
type ListAuthorizationReq struct {
	Name                  string   `json:"name"`
	AuthorizationPlatform []string `json:"authorization_platform"`
	PageNum               int32    `json:"page_num"`
	PageSize              int32    `json:"page_size"`
	CreateUserID          int32    `json:"create_user_id"` // 创建用户ID，用于权限控制
}

// AuthorizationListData 授权列表数据
type AuthorizationListData struct {
	List       []*Authorization `json:"list"`
	Pagination *pb.Pagination   `json:"pagination"`
}
