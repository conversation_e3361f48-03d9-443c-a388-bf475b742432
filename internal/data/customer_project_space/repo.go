package customer_project_space

import (
	"context"
	"vision_hub/internal/biz/customer_project_space"
	biz "vision_hub/internal/biz/customer_project_space"
	"vision_hub/internal/conf"
	constants "vision_hub/internal/constants/customer_project_space"
	"vision_hub/internal/data/common"
	"vision_hub/internal/data/customer_project_space/model"
	"vision_hub/internal/data/customer_project_space/query"
	customerProjectSpaceErr "vision_hub/internal/errors/customer_project_space"
	"vision_hub/internal/middleware"

	"gorm.io/gorm/clause"
)

// CustomerProjectCommRepo 项目空间
type CustomerProjectCommRepo struct {
	conf *conf.BizConf
	log  *common.DataLogHelper
	data *common.Data
}

// NewCustomerProjectCommRepo 项目空间-公共资源方法
func NewCustomerProjectCommRepo(conf *conf.BizConf, logger *common.DataLogHelper, data *common.Data) customer_project_space.ICustomerProjectCommRepo {
	return &CustomerProjectCommRepo{
		conf: conf,
		log:  logger,
		data: data,
	}
}
func (c CustomerProjectCommRepo) CheckCurrentUserProjectPermission(ctx context.Context, projectId int32) error {
	currentUser, err := middleware.GetUserInfo(ctx)
	if err != nil {
		return err
	}
	currentUserId := currentUser.UserID
	if currentUser.IsStaff == 1 {
		currentUserId = 0
	}
	customerProMapping, err := c.GetCurrentUserProject(ctx, int32(currentUserId))
	if err != nil {
		return err
	}
	if !customerProMapping[projectId] {
		return customerProjectSpaceErr.CustomerProNoPermission
	}
	return nil
}

// GetCurrentUserProject 获取当前用户已有的客户项目
func (c CustomerProjectCommRepo) GetCurrentUserProject(ctx context.Context, userId int32) (map[int32]bool, error) {
	var proIds []int32
	q := query.Use(c.data.DbClient.DB(ctx)).CustomerProjectAuth
	tmpQuery := q.WithContext(ctx).Select(q.CustomerProjectID)
	// 管理员不需要检验，获取所有项目
	if userId != 0 {
		tmpQuery = tmpQuery.Where(q.RelationID.Eq(userId), q.RelationType.Eq("user"))
	}
	if err := tmpQuery.Scan(&proIds); err != nil {
		return nil, err
	}
	res := make(map[int32]bool)
	for _, id := range proIds {
		res[id] = true
	}
	return res, nil
}

// GetCurrentProjectAdsAccountIds 获取当前项目已有的广告账号
func (c CustomerProjectCommRepo) GetCurrentProjectAdsAccountIds(ctx context.Context, projectId int32, accountIds []string, resourceType string) ([]string, error) {
	var res []string
	q := query.Use(c.data.DbClient.DB(ctx)).CustomerProjectResource
	tmpQuery := q.WithContext(ctx).Select(q.ResourceID).
		Where(q.CustomerProjectID.Eq(projectId), q.ResourceType.Eq(resourceType))
	if len(accountIds) > 0 {
		tmpQuery = tmpQuery.Where(q.ResourceID.In(accountIds...))
	}
	if err := tmpQuery.Scan(&res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *CustomerProjectCommRepo) GetAccountSignList(ctx context.Context, accountIds []string, params *biz.ProjectAccountListParams, isPaging bool) ([]*biz.ProjectAccountSign, *biz.Pagination, error) {
	var projectAccountSign []*biz.ProjectAccountSign
	tmpQuery := c.data.BackupDbClient.DB(ctx).Table("account AS a")
	tmpQuery.Select("a.account_id, c.contract_id, s.id AS sign_id, st.id AS settlement_id, st.title AS settlement,a.medium as medium_id,"+
		"s.company_id, s.our_side_entity, MAX(CASE WHEN u.id = s.sale_id THEN u.id END) AS sale_id,"+
		"MAX(CASE WHEN u.id = s.sale_id THEN u.user_name END) AS sale_name,"+
		"MAX(CASE WHEN u.id = s.sale_id THEN u.email END) AS sale_email,"+
		"MAX(CASE WHEN u.id = s.ae_id THEN u.id END) AS ae_id,"+
		"MAX(CASE WHEN u.id = s.ae_id THEN u.user_name END) AS ae_name,"+
		"MAX(CASE WHEN u.id = s.ae_id THEN u.email END) AS ae_email,"+
		"MAX(CASE WHEN u.id = s.sign_sale_id THEN u.id END) AS sign_sale_id,"+
		"MAX(CASE WHEN u.id = s.sign_sale_id THEN u.user_name END) AS sign_sale_name,"+
		"MAX(CASE WHEN u.id = s.sign_sale_id THEN u.email END) AS sign_sale_email").
		Joins("LEFT JOIN sign s ON a.sign_id = s.id").
		Joins("LEFT JOIN settlement st ON s.settlement_id = st.id").
		Joins("LEFT JOIN contract c ON c.sign_id = s.id").
		Joins("LEFT JOIN User u ON u.id IN (s.sale_id, s.ae_id, s.sign_sale_id)").
		Where("a.account_id IN ? AND a.active = 1", accountIds).
		Group("a.id, s.id, st.id, st.title, s.company_id, s.our_side_entity,a.medium")
	if len(params.Medium) > 0 {
		tmpQuery = tmpQuery.Where("a.medium in ?", params.Medium)
	}

	var count int64
	if err := tmpQuery.Count(&count).Error; err != nil {
		c.log.WithContext(ctx).Errorf("获取签约列表总数异常: %v", err)
		return nil, nil, err
	}

	var pagination *biz.Pagination
	if isPaging {
		if params.PageNum > 0 && params.PageSize > 0 {
			offset := (params.PageNum - 1) * params.PageSize
			tmpQuery = tmpQuery.Offset(int(offset)).Limit(int(params.PageSize))
		}
		pagination = &biz.Pagination{
			Total:    int32(count),
			PageNum:  params.PageNum,
			PageSize: params.PageSize,
		}
	}

	err := tmpQuery.Find(&projectAccountSign).Error
	if err != nil {
		c.log.WithContext(ctx).Errorf("查询项目账号关联信息失败: %+v", err)
		return nil, nil, err
	}

	return projectAccountSign, pagination, nil
}

func (c CustomerProjectCommRepo) AddCustomerProResource(ctx context.Context, resource []*model.CustomerProjectResource) error {
	q := query.Use(c.data.DbClient.DB(ctx)).CustomerProjectResource
	//TODO 操作日志记录
	return q.WithContext(ctx).Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "resource_id"},
			{Name: "resource_type"},
			{Name: "customer_project_id"},
		},
		DoUpdates: clause.AssignmentColumns([]string{"update_time"}),
	}).CreateInBatches(resource, 500)
}

func (c CustomerProjectCommRepo) RemoveCustomerProResource(ctx context.Context, resourceType string, resourceIdList []string, customerProId int32) error {
	q := query.Use(c.data.DbClient.DB(ctx)).CustomerProjectResource
	tmpQuery := q.WithContext(ctx).Where(q.CustomerProjectID.Eq(customerProId), q.ResourceType.Eq(resourceType))
	//TODO 操作日志记录
	if len(resourceIdList) > 0 {
		tmpQuery = tmpQuery.Where(q.ResourceID.In(resourceIdList...))
	}
	if _, err := tmpQuery.Delete(); err != nil {
		return err
	}
	return nil
}

func (c CustomerProjectCommRepo) UpdateCustomerProResourceStatus(ctx context.Context, resourceType string, resourceIdList []string, customerProId int32, resourceStatus string) error {
	q := query.Use(c.data.DbClient.DB(ctx)).CustomerProjectResource
	tmpQuery := q.WithContext(ctx).Where(q.CustomerProjectID.Eq(customerProId), q.ResourceType.Eq(resourceType))
	//TODO 操作日志记录
	if len(resourceIdList) > 0 {
		tmpQuery = tmpQuery.Where(q.ResourceID.In(resourceIdList...))
	}
	if _, err := tmpQuery.UpdateColumn(q.ResourceStatus, resourceStatus); err != nil {
		return err
	}
	return nil
}

func (c CustomerProjectCommRepo) GetCustomerProResourceIds(ctx context.Context, customerProId int32, resourceType string, resourceStatus string) ([]string, error) {
	q := query.Use(c.data.DbClient.DB(ctx)).CustomerProjectResource
	tmpQuery := q.WithContext(ctx).Select(q.ResourceID).
		Where(q.CustomerProjectID.Eq(customerProId), q.ResourceType.Eq(resourceType))
	if resourceStatus != "" {
		tmpQuery = tmpQuery.Where(q.ResourceStatus.Eq(resourceStatus))
	}
	var res []string
	if err := tmpQuery.Scan(&res); err != nil {
		return nil, err
	}
	return res, nil
}
