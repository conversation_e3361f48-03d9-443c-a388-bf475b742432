// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameCustomerProjectConfig = "customer_project_config"

// CustomerProjectConfig mapped from table <customer_project_config>
type CustomerProjectConfig struct {
	ID                int32        `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:自增主键" json:"id"`                                                                                               // 自增主键
	CreateTime        sql.NullTime `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                                                   // 创建时间
	UpdateTime        sql.NullTime `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                                                   // 更新时间
	CreateUserID      int32        `gorm:"column:create_user_id;type:int;not null;comment:创建人" json:"create_user_id"`                                                                                             // 创建人
	UpdateUserID      int32        `gorm:"column:update_user_id;type:int;not null;comment:更新人" json:"update_user_id"`                                                                                             // 更新人
	CustomerProjectID int32        `gorm:"column:customer_project_id;type:int;not null;index:idx_customer_project_id_config_status_config_key,priority:1;comment:项目id" json:"customer_project_id"`                // 项目id
	ConfigKey         string       `gorm:"column:config_key;type:varchar(64);not null;index:idx_customer_project_id_config_status_config_key,priority:3;comment:配置key:auth_impact|auth_tiktok" json:"config_key"` // 配置key:auth_impact|auth_tiktok
	ConfigValue       string       `gorm:"column:config_value;type:varchar(256);not null;comment:配置值:授权表主键" json:"config_value"`                                                                                  // 配置值:授权表主键
	ConfigStatus      string       `gorm:"column:config_status;type:varchar(64);not null;index:idx_customer_project_id_config_status_config_key,priority:2;default:enable;comment:配置状态" json:"config_status"`     // 配置状态
}

// TableName CustomerProjectConfig's table name
func (*CustomerProjectConfig) TableName() string {
	return TableNameCustomerProjectConfig
}
