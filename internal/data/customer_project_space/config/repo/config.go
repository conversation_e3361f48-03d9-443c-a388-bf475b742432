package repo

import (
	"context"
	"gorm.io/gorm"
	configBiz "vision_hub/internal/biz/customer_project_space/config"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/data/common"
	"vision_hub/internal/data/customer_project_space/model"
)

// CustomerProjectConfigRepo 项目配置数据访问层
type CustomerProjectConfigRepo struct {
	log  *common.DataLogHelper
	data *common.Data
}

// NewCustomerProjectConfigRepo 创建项目配置数据访问层实例
func NewCustomerProjectConfigRepo(logger *common.DataLogHelper, data *common.Data) configBiz.ICustomerProjectConfigRepo {
	return &CustomerProjectConfigRepo{
		log:  logger,
		data: data,
	}
}

// GetCustomerProjectConfigs 获取项目配置列表
func (r *CustomerProjectConfigRepo) GetCustomerProjectConfigs(ctx context.Context, req *configBiz.GetCustomerProConfigReq) ([]*model.CustomerProjectConfig, error) {
	db := r.data.DbClient.DB(ctx)
	
	query := db.Model(&model.CustomerProjectConfig{}).
		Where("customer_project_id = ?", req.CustomerProjectID)

	// 根据配置键过滤
	if req.ConfigKey > 0 {
		// 这里假设ConfigKey是枚举值，需要根据实际业务逻辑转换
		// 暂时跳过这个过滤条件，或者可以根据具体需求实现
	}

	// 根据状态过滤
	if req.ConfigStatus != "" {
		query = query.Where("config_status = ?", req.ConfigStatus)
	}

	var configs []*model.CustomerProjectConfig
	err := query.Order("create_time DESC").Find(&configs).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("获取项目配置列表失败: %v", err)
		return nil, err
	}

	return configs, nil
}

// SaveCustomerProjectConfig 保存项目配置
func (r *CustomerProjectConfigRepo) SaveCustomerProjectConfig(ctx context.Context, config *model.CustomerProjectConfig) error {
	db := r.data.DbClient.DB(ctx)
	
	err := db.Create(config).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("保存项目配置失败: %v", err)
		return err
	}

	return nil
}

// GetCustomerProjectConfigByKey 根据项目ID和配置键获取配置
func (r *CustomerProjectConfigRepo) GetCustomerProjectConfigByKey(ctx context.Context, customerProjectID int32, configKey string) (*model.CustomerProjectConfig, error) {
	db := r.data.DbClient.DB(ctx)
	
	var config model.CustomerProjectConfig
	err := db.Where("customer_project_id = ? AND config_key = ?", customerProjectID, configKey).
		First(&config).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.log.WithContext(ctx).Errorf("根据键获取项目配置失败: %v", err)
		return nil, err
	}

	return &config, nil
}

// UpdateCustomerProjectConfig 更新项目配置
func (r *CustomerProjectConfigRepo) UpdateCustomerProjectConfig(ctx context.Context, id int32, updateParams map[string]interface{}) error {
	db := r.data.DbClient.DB(ctx)
	
	err := db.Model(&model.CustomerProjectConfig{}).
		Where("id = ?", id).
		Updates(updateParams).Error
	
	if err != nil {
		r.log.WithContext(ctx).Errorf("更新项目配置失败: %v", err)
		return err
	}

	return nil
}

// DeleteCustomerProjectConfig 删除项目配置
func (r *CustomerProjectConfigRepo) DeleteCustomerProjectConfig(ctx context.Context, id int32) error {
	db := r.data.DbClient.DB(ctx)
	
	err := db.Delete(&model.CustomerProjectConfig{}, id).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("删除项目配置失败: %v", err)
		return err
	}

	return nil
}
