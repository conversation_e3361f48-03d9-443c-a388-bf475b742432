package repo

import (
	"testing"
	"vision_hub/internal/biz/customer_project_space/ads_account"
	"vision_hub/internal/constants"
	customerProjectSpaceConstants "vision_hub/internal/constants/customer_project_space"
	customerProjectSpaceData "vision_hub/internal/data/customer_project_space"
)

func TestGetResourceTypeByPlatform(t *testing.T) {
	tests := []struct {
		name     string
		platform string
		expected string
	}{
		{
			name:     "Impact platform",
			platform: constants.AuthPlatformImpact,
			expected: customerProjectSpaceConstants.CustomerResourceImpactCampaign,
		},
		{
			name:     "Empty platform",
			platform: "",
			expected: customerProjectSpaceConstants.CustomerResourceAdsAccount,
		},
		{
			name:     "TikTok platform",
			platform: constants.AuthPlatformTikTok,
			expected: customerProjectSpaceConstants.CustomerResourceAdsAccount,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := customerProjectSpaceData.GetResourceTypeByPlatform(tt.platform)
			if result != tt.expected {
				t.<PERSON>rrorf("GetResourceTypeByPlatform(%s) = %s, expected %s", tt.platform, result, tt.expected)
			}
		})
	}
}

func TestGetAdsAccountListParams_Platform(t *testing.T) {
	// 测试GetAdsAccountListParams结构体是否包含Platform字段
	params := &ads_account.GetAdsAccountListParams{
		CustomerProjectID: 1,
		AdsAccountID:      "test",
		Platform:          constants.AuthPlatformImpact,
	}

	if params.Platform != constants.AuthPlatformImpact {
		t.Errorf("Expected Platform to be %s, got %s", constants.AuthPlatformImpact, params.Platform)
	}
}
