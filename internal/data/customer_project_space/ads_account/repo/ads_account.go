package repo

import (
	"context"
	customerProjectSpaceBiz "vision_hub/internal/biz/customer_project_space"
	"vision_hub/internal/biz/customer_project_space/ads_account"
	"vision_hub/internal/conf"
	constants "vision_hub/internal/constants/customer_project_space"
	"vision_hub/internal/data/common"
	"vision_hub/internal/data/customer_project_space/model"
	"vision_hub/internal/data/customer_project_space/query"
	"vision_hub/internal/util"
)

// AdsAccountRepo 广告账号仓储实现
type AdsAccountRepo struct {
	conf *conf.BizConf
	log  *common.DataLogHelper
	data *common.Data
}

// NewAdsAccountRepo 创建广告账号仓储实例
func NewAdsAccountRepo(conf *conf.BizConf, logger *common.DataLogHelper, data *common.Data) ads_account.IAdsAccountRepo {
	return &AdsAccountRepo{
		conf: conf,
		log:  logger,
		data: data,
	}
}
func (a *AdsAccountRepo) GetAdsAccountList(ctx context.Context, params *ads_account.GetAdsAccountListParams) ([]*ads_account.AdsAccountListItem, *customerProjectSpaceBiz.Pagination, error) {
	// 根据platform参数获取对应的资源类型
	var resourceType string
	if params.Platform != "" {
		resourceType = customerProjectSpaceData.GetResourceTypeByPlatform(params.Platform)
	} else {
		// 如果没有指定platform，默认查询ads_account类型
		resourceType = constants.CustomerResourceAdsAccount
	}

	tmpQuery := a.data.DbClient.DB(ctx).Model(model.CustomerProjectResource{}).
		Select("resource_id AS ads_account_id,resource_status AS status").
		Where("customer_project_id = ? and resource_type = ?", params.CustomerProjectID, resourceType).
		Order("customer_project_resource.create_time DESC")
	// 广告账号模糊查询
	if params.AdsAccountID != "" {
		tmpQuery = tmpQuery.Where("resource_id like ?", util.FormatLikeParam(params.AdsAccountID, true, true))
	}
	// 广告账号状态查询
	if params.Status != "" {
		tmpQuery = tmpQuery.Where("resource_status = ?", params.Status)
	}
	// 获取数据总数
	var count int64
	if err := tmpQuery.Count(&count).Error; err != nil {
		a.log.WithContext(ctx).Errorf("获取总数异常: %v", err)
		return nil, nil, err
	}
	var res []*ads_account.AdsAccountListItem
	offset := (params.PageNum - 1) * params.PageSize
	// 获取记录
	if err := tmpQuery.Limit(int(params.PageSize)).Offset(int(offset)).Find(&res).Error; err != nil {
		return nil, nil, err
	}
	return res, &customerProjectSpaceBiz.Pagination{
		Total:    int32(count),
		PageNum:  params.PageNum,
		PageSize: params.PageSize,
	}, nil

}

func (a *AdsAccountRepo) AddAdsAccount(ctx context.Context, accountList []*model.CustomerProjectResource) error {
	q := query.Use(a.data.DbClient.DB(ctx)).CustomerProjectResource
	//TODO 操作日志记录
	return q.WithContext(ctx).CreateInBatches(accountList, 500)
}

func (a *AdsAccountRepo) EnOrDisableAdsAccount(ctx context.Context, customerProjectID int32, accountList []string, status string) error {
	q := query.Use(a.data.DbClient.DB(ctx)).CustomerProjectResource
	//TODO 操作日志记录
	_, err := q.WithContext(ctx).
		Where(q.CustomerProjectID.Eq(customerProjectID), q.ResourceID.In(accountList...)).
		Update(q.ResourceStatus, status)
	return err
}

func (a *AdsAccountRepo) RemoveAdsAccount(ctx context.Context, customerProjectID int32, accountList []string, resourceType string) error {
	q := query.Use(a.data.DbClient.DB(ctx)).CustomerProjectResource

	//TODO 操作日志记录
	_, err := q.WithContext(ctx).
		Where(q.CustomerProjectID.Eq(customerProjectID), q.ResourceID.In(accountList...), q.ResourceType.Eq(resourceType)).
		Delete()
	return err
}

func (a *AdsAccountRepo) OpenAPIGetEnableAdsAccountList(ctx context.Context, customerProjectNumber string) ([]*ads_account.OpenAPIAccount, error) {
	// 查询所有账号相关的资源类型（ads_account 和 impact_campaign）
	resourceTypes := []string{constants.CustomerResourceAdsAccount, constants.CustomerResourceImpactCampaign}
	res := make([]*ads_account.OpenAPIAccount, 0)
	err := a.data.DbClient.DB(ctx).Model(model.CustomerProjectResource{}).
		Select("resource_id AS ads_account_id,JSON_UNQUOTE(resource_extra_info->'$.medium') AS medium").
		Joins("INNER JOIN customer_project ON (customer_project.id = customer_project_resource.customer_project_id AND customer_project_resource.resource_type IN ?)", resourceTypes).
		Where("customer_project.project_id = ? AND customer_project_resource.resource_status = ?", customerProjectNumber, "enable").
		Scan(&res).Error
	return res, err
}
