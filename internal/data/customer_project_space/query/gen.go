// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                      db,
		CustomerProject:         newCustomerProject(db, opts...),
		CustomerProjectAuth:     newCustomerProjectAuth(db, opts...),
		CustomerProjectConfig:   newCustomerProjectConfig(db, opts...),
		CustomerProjectResource: newCustomerProjectResource(db, opts...),
		Material:                newMaterial(db, opts...),
		MaterialUploadLog:       newMaterialUploadLog(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	CustomerProject         customerProject
	CustomerProjectAuth     customerProjectAuth
	CustomerProjectConfig   customerProjectConfig
	CustomerProjectResource customerProjectResource
	Material                material
	MaterialUploadLog       materialUploadLog
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                      db,
		CustomerProject:         q.CustomerProject.clone(db),
		CustomerProjectAuth:     q.CustomerProjectAuth.clone(db),
		CustomerProjectConfig:   q.CustomerProjectConfig.clone(db),
		CustomerProjectResource: q.CustomerProjectResource.clone(db),
		Material:                q.Material.clone(db),
		MaterialUploadLog:       q.MaterialUploadLog.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                      db,
		CustomerProject:         q.CustomerProject.replaceDB(db),
		CustomerProjectAuth:     q.CustomerProjectAuth.replaceDB(db),
		CustomerProjectConfig:   q.CustomerProjectConfig.replaceDB(db),
		CustomerProjectResource: q.CustomerProjectResource.replaceDB(db),
		Material:                q.Material.replaceDB(db),
		MaterialUploadLog:       q.MaterialUploadLog.replaceDB(db),
	}
}

type queryCtx struct {
	CustomerProject         *customerProjectDo
	CustomerProjectAuth     *customerProjectAuthDo
	CustomerProjectConfig   *customerProjectConfigDo
	CustomerProjectResource *customerProjectResourceDo
	Material                *materialDo
	MaterialUploadLog       *materialUploadLogDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		CustomerProject:         q.CustomerProject.WithContext(ctx),
		CustomerProjectAuth:     q.CustomerProjectAuth.WithContext(ctx),
		CustomerProjectConfig:   q.CustomerProjectConfig.WithContext(ctx),
		CustomerProjectResource: q.CustomerProjectResource.WithContext(ctx),
		Material:                q.Material.WithContext(ctx),
		MaterialUploadLog:       q.MaterialUploadLog.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
