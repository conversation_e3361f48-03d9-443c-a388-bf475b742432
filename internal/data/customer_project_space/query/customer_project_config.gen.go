// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/customer_project_space/model"
)

func newCustomerProjectConfig(db *gorm.DB, opts ...gen.DOOption) customerProjectConfig {
	_customerProjectConfig := customerProjectConfig{}

	_customerProjectConfig.customerProjectConfigDo.UseDB(db, opts...)
	_customerProjectConfig.customerProjectConfigDo.UseModel(&model.CustomerProjectConfig{})

	tableName := _customerProjectConfig.customerProjectConfigDo.TableName()
	_customerProjectConfig.ALL = field.NewAsterisk(tableName)
	_customerProjectConfig.ID = field.NewInt32(tableName, "id")
	_customerProjectConfig.CreateTime = field.NewField(tableName, "create_time")
	_customerProjectConfig.UpdateTime = field.NewField(tableName, "update_time")
	_customerProjectConfig.CreateUserID = field.NewInt32(tableName, "create_user_id")
	_customerProjectConfig.UpdateUserID = field.NewInt32(tableName, "update_user_id")
	_customerProjectConfig.CustomerProjectID = field.NewInt32(tableName, "customer_project_id")
	_customerProjectConfig.ConfigKey = field.NewString(tableName, "config_key")
	_customerProjectConfig.ConfigValue = field.NewString(tableName, "config_value")
	_customerProjectConfig.ConfigStatus = field.NewString(tableName, "config_status")

	_customerProjectConfig.fillFieldMap()

	return _customerProjectConfig
}

type customerProjectConfig struct {
	customerProjectConfigDo customerProjectConfigDo

	ALL               field.Asterisk
	ID                field.Int32  // 自增主键
	CreateTime        field.Field  // 创建时间
	UpdateTime        field.Field  // 更新时间
	CreateUserID      field.Int32  // 创建人
	UpdateUserID      field.Int32  // 更新人
	CustomerProjectID field.Int32  // 项目id
	ConfigKey         field.String // 配置key:auth_impact|auth_tiktok
	ConfigValue       field.String // 配置值:授权表主键
	ConfigStatus      field.String // 配置状态

	fieldMap map[string]field.Expr
}

func (c customerProjectConfig) Table(newTableName string) *customerProjectConfig {
	c.customerProjectConfigDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c customerProjectConfig) As(alias string) *customerProjectConfig {
	c.customerProjectConfigDo.DO = *(c.customerProjectConfigDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *customerProjectConfig) updateTableName(table string) *customerProjectConfig {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt32(table, "id")
	c.CreateTime = field.NewField(table, "create_time")
	c.UpdateTime = field.NewField(table, "update_time")
	c.CreateUserID = field.NewInt32(table, "create_user_id")
	c.UpdateUserID = field.NewInt32(table, "update_user_id")
	c.CustomerProjectID = field.NewInt32(table, "customer_project_id")
	c.ConfigKey = field.NewString(table, "config_key")
	c.ConfigValue = field.NewString(table, "config_value")
	c.ConfigStatus = field.NewString(table, "config_status")

	c.fillFieldMap()

	return c
}

func (c *customerProjectConfig) WithContext(ctx context.Context) *customerProjectConfigDo {
	return c.customerProjectConfigDo.WithContext(ctx)
}

func (c customerProjectConfig) TableName() string { return c.customerProjectConfigDo.TableName() }

func (c customerProjectConfig) Alias() string { return c.customerProjectConfigDo.Alias() }

func (c customerProjectConfig) Columns(cols ...field.Expr) gen.Columns {
	return c.customerProjectConfigDo.Columns(cols...)
}

func (c *customerProjectConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *customerProjectConfig) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 9)
	c.fieldMap["id"] = c.ID
	c.fieldMap["create_time"] = c.CreateTime
	c.fieldMap["update_time"] = c.UpdateTime
	c.fieldMap["create_user_id"] = c.CreateUserID
	c.fieldMap["update_user_id"] = c.UpdateUserID
	c.fieldMap["customer_project_id"] = c.CustomerProjectID
	c.fieldMap["config_key"] = c.ConfigKey
	c.fieldMap["config_value"] = c.ConfigValue
	c.fieldMap["config_status"] = c.ConfigStatus
}

func (c customerProjectConfig) clone(db *gorm.DB) customerProjectConfig {
	c.customerProjectConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c customerProjectConfig) replaceDB(db *gorm.DB) customerProjectConfig {
	c.customerProjectConfigDo.ReplaceDB(db)
	return c
}

type customerProjectConfigDo struct{ gen.DO }

func (c customerProjectConfigDo) Debug() *customerProjectConfigDo {
	return c.withDO(c.DO.Debug())
}

func (c customerProjectConfigDo) WithContext(ctx context.Context) *customerProjectConfigDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c customerProjectConfigDo) ReadDB() *customerProjectConfigDo {
	return c.Clauses(dbresolver.Read)
}

func (c customerProjectConfigDo) WriteDB() *customerProjectConfigDo {
	return c.Clauses(dbresolver.Write)
}

func (c customerProjectConfigDo) Session(config *gorm.Session) *customerProjectConfigDo {
	return c.withDO(c.DO.Session(config))
}

func (c customerProjectConfigDo) Clauses(conds ...clause.Expression) *customerProjectConfigDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c customerProjectConfigDo) Returning(value interface{}, columns ...string) *customerProjectConfigDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c customerProjectConfigDo) Not(conds ...gen.Condition) *customerProjectConfigDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c customerProjectConfigDo) Or(conds ...gen.Condition) *customerProjectConfigDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c customerProjectConfigDo) Select(conds ...field.Expr) *customerProjectConfigDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c customerProjectConfigDo) Where(conds ...gen.Condition) *customerProjectConfigDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c customerProjectConfigDo) Order(conds ...field.Expr) *customerProjectConfigDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c customerProjectConfigDo) Distinct(cols ...field.Expr) *customerProjectConfigDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c customerProjectConfigDo) Omit(cols ...field.Expr) *customerProjectConfigDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c customerProjectConfigDo) Join(table schema.Tabler, on ...field.Expr) *customerProjectConfigDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c customerProjectConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *customerProjectConfigDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c customerProjectConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *customerProjectConfigDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c customerProjectConfigDo) Group(cols ...field.Expr) *customerProjectConfigDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c customerProjectConfigDo) Having(conds ...gen.Condition) *customerProjectConfigDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c customerProjectConfigDo) Limit(limit int) *customerProjectConfigDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c customerProjectConfigDo) Offset(offset int) *customerProjectConfigDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c customerProjectConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *customerProjectConfigDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c customerProjectConfigDo) Unscoped() *customerProjectConfigDo {
	return c.withDO(c.DO.Unscoped())
}

func (c customerProjectConfigDo) Create(values ...*model.CustomerProjectConfig) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c customerProjectConfigDo) CreateInBatches(values []*model.CustomerProjectConfig, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c customerProjectConfigDo) Save(values ...*model.CustomerProjectConfig) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c customerProjectConfigDo) First() (*model.CustomerProjectConfig, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectConfig), nil
	}
}

func (c customerProjectConfigDo) Take() (*model.CustomerProjectConfig, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectConfig), nil
	}
}

func (c customerProjectConfigDo) Last() (*model.CustomerProjectConfig, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectConfig), nil
	}
}

func (c customerProjectConfigDo) Find() ([]*model.CustomerProjectConfig, error) {
	result, err := c.DO.Find()
	return result.([]*model.CustomerProjectConfig), err
}

func (c customerProjectConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CustomerProjectConfig, err error) {
	buf := make([]*model.CustomerProjectConfig, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c customerProjectConfigDo) FindInBatches(result *[]*model.CustomerProjectConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c customerProjectConfigDo) Attrs(attrs ...field.AssignExpr) *customerProjectConfigDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c customerProjectConfigDo) Assign(attrs ...field.AssignExpr) *customerProjectConfigDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c customerProjectConfigDo) Joins(fields ...field.RelationField) *customerProjectConfigDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c customerProjectConfigDo) Preload(fields ...field.RelationField) *customerProjectConfigDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c customerProjectConfigDo) FirstOrInit() (*model.CustomerProjectConfig, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectConfig), nil
	}
}

func (c customerProjectConfigDo) FirstOrCreate() (*model.CustomerProjectConfig, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectConfig), nil
	}
}

func (c customerProjectConfigDo) FindByPage(offset int, limit int) (result []*model.CustomerProjectConfig, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c customerProjectConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c customerProjectConfigDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c customerProjectConfigDo) Delete(models ...*model.CustomerProjectConfig) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *customerProjectConfigDo) withDO(do gen.Dao) *customerProjectConfigDo {
	c.DO = *do.(*gen.DO)
	return c
}
