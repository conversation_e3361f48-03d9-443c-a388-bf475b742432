// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/customer_project_space/model"
)

func newCustomerProjectResource(db *gorm.DB, opts ...gen.DOOption) customerProjectResource {
	_customerProjectResource := customerProjectResource{}

	_customerProjectResource.customerProjectResourceDo.UseDB(db, opts...)
	_customerProjectResource.customerProjectResourceDo.UseModel(&model.CustomerProjectResource{})

	tableName := _customerProjectResource.customerProjectResourceDo.TableName()
	_customerProjectResource.ALL = field.NewAsterisk(tableName)
	_customerProjectResource.ID = field.NewInt32(tableName, "id")
	_customerProjectResource.CreateTime = field.NewField(tableName, "create_time")
	_customerProjectResource.UpdateTime = field.NewField(tableName, "update_time")
	_customerProjectResource.CustomerProjectID = field.NewInt32(tableName, "customer_project_id")
	_customerProjectResource.ResourceID = field.NewString(tableName, "resource_id")
	_customerProjectResource.ResourceType = field.NewString(tableName, "resource_type")
	_customerProjectResource.ResourceStatus = field.NewString(tableName, "resource_status")
	_customerProjectResource.ResourceExtraInfo = field.NewBytes(tableName, "resource_extra_info")
	_customerProjectResource.CreateUserID = field.NewInt32(tableName, "create_user_id")

	_customerProjectResource.fillFieldMap()

	return _customerProjectResource
}

type customerProjectResource struct {
	customerProjectResourceDo customerProjectResourceDo

	ALL               field.Asterisk
	ID                field.Int32  // 关系表主键ID
	CreateTime        field.Field  // 创建时间
	UpdateTime        field.Field  // 更新时间
	CustomerProjectID field.Int32  // 客户项目的主键id
	ResourceID        field.String // 项目拥有资源ID
	ResourceType      field.String // 资源类型:material、account
	ResourceStatus    field.String
	ResourceExtraInfo field.Bytes
	CreateUserID      field.Int32

	fieldMap map[string]field.Expr
}

func (c customerProjectResource) Table(newTableName string) *customerProjectResource {
	c.customerProjectResourceDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c customerProjectResource) As(alias string) *customerProjectResource {
	c.customerProjectResourceDo.DO = *(c.customerProjectResourceDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *customerProjectResource) updateTableName(table string) *customerProjectResource {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt32(table, "id")
	c.CreateTime = field.NewField(table, "create_time")
	c.UpdateTime = field.NewField(table, "update_time")
	c.CustomerProjectID = field.NewInt32(table, "customer_project_id")
	c.ResourceID = field.NewString(table, "resource_id")
	c.ResourceType = field.NewString(table, "resource_type")
	c.ResourceStatus = field.NewString(table, "resource_status")
	c.ResourceExtraInfo = field.NewBytes(table, "resource_extra_info")
	c.CreateUserID = field.NewInt32(table, "create_user_id")

	c.fillFieldMap()

	return c
}

func (c *customerProjectResource) WithContext(ctx context.Context) *customerProjectResourceDo {
	return c.customerProjectResourceDo.WithContext(ctx)
}

func (c customerProjectResource) TableName() string { return c.customerProjectResourceDo.TableName() }

func (c customerProjectResource) Alias() string { return c.customerProjectResourceDo.Alias() }

func (c customerProjectResource) Columns(cols ...field.Expr) gen.Columns {
	return c.customerProjectResourceDo.Columns(cols...)
}

func (c *customerProjectResource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *customerProjectResource) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 9)
	c.fieldMap["id"] = c.ID
	c.fieldMap["create_time"] = c.CreateTime
	c.fieldMap["update_time"] = c.UpdateTime
	c.fieldMap["customer_project_id"] = c.CustomerProjectID
	c.fieldMap["resource_id"] = c.ResourceID
	c.fieldMap["resource_type"] = c.ResourceType
	c.fieldMap["resource_status"] = c.ResourceStatus
	c.fieldMap["resource_extra_info"] = c.ResourceExtraInfo
	c.fieldMap["create_user_id"] = c.CreateUserID
}

func (c customerProjectResource) clone(db *gorm.DB) customerProjectResource {
	c.customerProjectResourceDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c customerProjectResource) replaceDB(db *gorm.DB) customerProjectResource {
	c.customerProjectResourceDo.ReplaceDB(db)
	return c
}

type customerProjectResourceDo struct{ gen.DO }

func (c customerProjectResourceDo) Debug() *customerProjectResourceDo {
	return c.withDO(c.DO.Debug())
}

func (c customerProjectResourceDo) WithContext(ctx context.Context) *customerProjectResourceDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c customerProjectResourceDo) ReadDB() *customerProjectResourceDo {
	return c.Clauses(dbresolver.Read)
}

func (c customerProjectResourceDo) WriteDB() *customerProjectResourceDo {
	return c.Clauses(dbresolver.Write)
}

func (c customerProjectResourceDo) Session(config *gorm.Session) *customerProjectResourceDo {
	return c.withDO(c.DO.Session(config))
}

func (c customerProjectResourceDo) Clauses(conds ...clause.Expression) *customerProjectResourceDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c customerProjectResourceDo) Returning(value interface{}, columns ...string) *customerProjectResourceDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c customerProjectResourceDo) Not(conds ...gen.Condition) *customerProjectResourceDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c customerProjectResourceDo) Or(conds ...gen.Condition) *customerProjectResourceDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c customerProjectResourceDo) Select(conds ...field.Expr) *customerProjectResourceDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c customerProjectResourceDo) Where(conds ...gen.Condition) *customerProjectResourceDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c customerProjectResourceDo) Order(conds ...field.Expr) *customerProjectResourceDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c customerProjectResourceDo) Distinct(cols ...field.Expr) *customerProjectResourceDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c customerProjectResourceDo) Omit(cols ...field.Expr) *customerProjectResourceDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c customerProjectResourceDo) Join(table schema.Tabler, on ...field.Expr) *customerProjectResourceDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c customerProjectResourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *customerProjectResourceDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c customerProjectResourceDo) RightJoin(table schema.Tabler, on ...field.Expr) *customerProjectResourceDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c customerProjectResourceDo) Group(cols ...field.Expr) *customerProjectResourceDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c customerProjectResourceDo) Having(conds ...gen.Condition) *customerProjectResourceDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c customerProjectResourceDo) Limit(limit int) *customerProjectResourceDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c customerProjectResourceDo) Offset(offset int) *customerProjectResourceDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c customerProjectResourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *customerProjectResourceDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c customerProjectResourceDo) Unscoped() *customerProjectResourceDo {
	return c.withDO(c.DO.Unscoped())
}

func (c customerProjectResourceDo) Create(values ...*model.CustomerProjectResource) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c customerProjectResourceDo) CreateInBatches(values []*model.CustomerProjectResource, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c customerProjectResourceDo) Save(values ...*model.CustomerProjectResource) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c customerProjectResourceDo) First() (*model.CustomerProjectResource, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectResource), nil
	}
}

func (c customerProjectResourceDo) Take() (*model.CustomerProjectResource, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectResource), nil
	}
}

func (c customerProjectResourceDo) Last() (*model.CustomerProjectResource, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectResource), nil
	}
}

func (c customerProjectResourceDo) Find() ([]*model.CustomerProjectResource, error) {
	result, err := c.DO.Find()
	return result.([]*model.CustomerProjectResource), err
}

func (c customerProjectResourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CustomerProjectResource, err error) {
	buf := make([]*model.CustomerProjectResource, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c customerProjectResourceDo) FindInBatches(result *[]*model.CustomerProjectResource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c customerProjectResourceDo) Attrs(attrs ...field.AssignExpr) *customerProjectResourceDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c customerProjectResourceDo) Assign(attrs ...field.AssignExpr) *customerProjectResourceDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c customerProjectResourceDo) Joins(fields ...field.RelationField) *customerProjectResourceDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c customerProjectResourceDo) Preload(fields ...field.RelationField) *customerProjectResourceDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c customerProjectResourceDo) FirstOrInit() (*model.CustomerProjectResource, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectResource), nil
	}
}

func (c customerProjectResourceDo) FirstOrCreate() (*model.CustomerProjectResource, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CustomerProjectResource), nil
	}
}

func (c customerProjectResourceDo) FindByPage(offset int, limit int) (result []*model.CustomerProjectResource, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c customerProjectResourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c customerProjectResourceDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c customerProjectResourceDo) Delete(models ...*model.CustomerProjectResource) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *customerProjectResourceDo) withDO(do gen.Dao) *customerProjectResourceDo {
	c.DO = *do.(*gen.DO)
	return c
}
