package customer_project_space

import (
	"testing"
	"vision_hub/internal/constants"
	customerProjectSpaceConstants "vision_hub/internal/constants/customer_project_space"
)

func TestGetResourceTypeByPlatform(t *testing.T) {
	tests := []struct {
		name     string
		platform string
		expected string
	}{
		{
			name:     "Impact platform",
			platform: constants.AuthPlatformImpact,
			expected: customerProjectSpaceConstants.CustomerResourceImpactCampaign,
		},
		{
			name:     "Default platform (empty)",
			platform: "",
			expected: customerProjectSpaceConstants.CustomerResourceAdsAccount,
		},
		{
			name:     "Default platform (unknown)",
			platform: "unknown",
			expected: customerProjectSpaceConstants.CustomerResourceAdsAccount,
		},
		{
			name:     "TikTok platform (should use default)",
			platform: constants.AuthPlatformTikTok,
			expected: customerProjectSpaceConstants.CustomerResourceAdsAccount,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetResourceTypeByPlatform(tt.platform)
			if result != tt.expected {
				t.<PERSON><PERSON><PERSON>("GetResourceTypeByPlatform(%s) = %s, expected %s", tt.platform, result, tt.expected)
			}
		})
	}
}
