package customer_project_space

import (
	"testing"
	constants "vision_hub/internal/constants/customer_project_space"
)

func TestGetResourceTypeByPlatform(t *testing.T) {
	tests := []struct {
		name     string
		platform string
		expected string
	}{
		{
			name:     "Impact platform",
			platform: "impact",
			expected: constants.CustomerResourceImpactCampaign,
		},
		{
			name:     "Default platform (empty)",
			platform: "",
			expected: constants.CustomerResourceAdsAccount,
		},
		{
			name:     "Default platform (unknown)",
			platform: "unknown",
			expected: constants.CustomerResourceAdsAccount,
		},
		{
			name:     "TikTok platform (should use default)",
			platform: "tiktok",
			expected: constants.CustomerResourceAdsAccount,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetResourceTypeByPlatform(tt.platform)
			if result != tt.expected {
				t.Errorf("GetResourceTypeByPlatform(%s) = %s, expected %s", tt.platform, result, tt.expected)
			}
		})
	}
}
