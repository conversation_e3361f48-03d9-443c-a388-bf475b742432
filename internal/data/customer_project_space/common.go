package customer_project_space

import (
	"vision_hub/internal/constants"
	customerProjectSpaceConstants "vision_hub/internal/constants/customer_project_space"
)

// GetResourceTypeByPlatform 根据平台参数获取对应的资源类型
// 这是一个通用方法，添加新平台时只需要在这里修改
func GetResourceTypeByPlatform(platform string) string {
	switch platform {
	case "impact":
		return constants.CustomerResourceImpactCampaign
	default:
		return constants.CustomerResourceAdsAccount
	}
}
