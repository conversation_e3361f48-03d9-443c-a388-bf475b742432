package repo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strings"
	bizCommon "vision_hub/internal/biz/common"
	biz "vision_hub/internal/biz/resource_pool/ip"
	"vision_hub/internal/conf"
	constants "vision_hub/internal/constants/resource_pool"
	"vision_hub/internal/data/common"
	"vision_hub/internal/data/resource_pool/model"
	"vision_hub/internal/data/resource_pool/query"
	resourceError "vision_hub/internal/errors/resource_pool"
	"vision_hub/internal/util"
)

type IpResourceRepo struct {
	conf *conf.BizConf
	log  *common.DataLogHelper
	data *common.Data
}

func NewIpResourceRepo(conf *conf.BizConf, logger *common.DataLogHelper, data *common.Data) biz.IIpResourceRepo {
	return &IpResourceRepo{
		conf: conf,
		log:  logger,
		data: data,
	}
}
func (p IpResourceRepo) CheckIpNameExist(ctx context.Context, name string, resourceId int32) (bool, error) {
	ipQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPBase
	count, err := ipQuery.WithContext(ctx).Where(ipQuery.Name.Eq(name), ipQuery.ID.Neq(resourceId)).Count()
	return count > 0, err
}

func (p IpResourceRepo) AddIpBaseTx(ctx context.Context, ipResource biz.IpResourceData, userEmail string, groups []bizCommon.GroupInfo) error {
	err := p.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 添加基础信息
		resourceId, err := p.AddIpBase(ctx, ipResource.IpResource)
		if err != nil {
			return err
		}

		// 根据角色类型调用不同的方法
		for _, group := range groups {
			switch group.Code {
			case constants.BrandGroupCode:
				err = p.AddIpResourceBrand(ctx, ipResource, group.Code, resourceId)
			case constants.AffiliateGroupCode:
				err = p.AddIpResourceAffiliate(ctx, ipResource, group.Code, resourceId)
			default:
				p.log.WithContext(ctx).Warnf("未知的角色类型: %v", group.Code)
				continue
			}
			if err != nil {
				return err
			}
		}
		// 操作记录
		originNote := []byte(`{}`)
		updateNote, err := json.Marshal(ipResource)
		if err != nil {
			return err
		}
		return CreateResourceLog(ctx, p.data.DbClient.DB(ctx), model.ResourceOperationLog{
			ResourceID:   resourceId,
			ResourceType: constants.ResourceIP,
			OptType:      constants.OptTypeCreate,
			OptUser:      userEmail,
			OriginalNote: originNote,
			UpdateNote:   updateNote,
		})
	})
	return err
}

// AddIpBase 添加资源基础信息
func (p IpResourceRepo) AddIpBase(ctx context.Context, ipResource model.ResourceIPBase) (int32, error) {
	ipQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPBase

	//var baseInfo *model.ResourceIPBase
	var err error
	if ipResource.ID > 0 {
		//baseInfo, err = ipQuery.WithContext(ctx).
		//	Where(ipQuery.ID.Eq(baseInfo.ID)).
		//	First()
		//if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		//	p.log.WithContext(ctx).Errorf("查询IP资源基础信息失败: %v", err)
		//	return 0, resourceError.ResourceEmptyFail
		//}
		//if baseInfo == nil {
		//	p.log.WithContext(ctx).Warnf("更新IP资源资源时未找到对应数据: %d", ipResource.ID)
		//	return 0, resourceError.ResourceEmpty
		//}
		//if baseInfo.IsDeleted == 1 {
		//	p.log.WithContext(ctx).Warnf("当前IP资源基础信息已被删除: %d", baseInfo.ID)
		//	return 0, resourceError.ResourceEmpty
		//}
		// 更新记录
		//var resultInfo gen.ResultInfo
		if err = ipQuery.WithContext(ctx).Save(&ipResource); err != nil {
			p.log.WithContext(ctx).Errorf("更新IP资源基础信息失败: %v", err)
			return 0, resourceError.ResourceUpdateErr
		}
		//if resultInfo.RowsAffected > 0 {
		//	p.log.WithContext(ctx).Debugf("更新IP资源基础信息成功: %d", ipResource.ID)
		//}
	} else {
		//baseInfo, err = ipQuery.WithContext(ctx).
		//	Where(ipQuery.DisplayName.Eq(ipResource.DisplayName)).
		//	First()
		if err := ipQuery.WithContext(ctx).Create(&ipResource); err != nil {
			p.log.WithContext(ctx).Errorf("新增IP资源基础信息失败: %v", err)
			return 0, resourceError.ResourceAddErr
		}
		p.log.WithContext(ctx).Debugf("新增IP资源基础信息成功: %d", ipResource.ID)
		return ipResource.ID, nil
		//if baseInfo != nil {
		//	p.log.WithContext(ctx).Warnf("当前IP资源基础信息已存在: %d", baseInfo.ID)
		//	return 0, resourceError.ResourceNotEmpty
		//}
	}

	return ipResource.ID, nil
}

// AddIpResourceBrand 添加品牌商资源
func (p IpResourceRepo) AddIpResourceBrand(ctx context.Context, ipResource biz.IpResourceData, groupCode string, resourceId int32) error {
	var err error
	// 联系人信息
	for _, ipBrand := range ipResource.BrandBusinessContact {
		err = p.AddIpBrandBusinessContact(ctx, ipBrand.IpBrandBusiness, ipBrand.Contact, resourceId, groupCode)
		if err != nil {
			return err
		}
	}
	return nil
}

// AddIpResourceAffiliate 添加联盟资源
func (p IpResourceRepo) AddIpResourceAffiliate(ctx context.Context, ipResource biz.IpResourceData, groupCode string, resourceId int32) error {
	var err error
	for _, ipAffiliate := range ipResource.AffiliateBusinessContact {
		err = p.AddIpAffiliateBusinessContact(ctx, ipAffiliate.IpAffiliateBusiness, ipAffiliate.Contact, resourceId, groupCode)
		if err != nil {
			return err
		}
	}
	// 联盟平台信息
	for _, plat := range ipResource.AffiliatePlatform {
		plat.ResourceID = resourceId
		err = query.Use(p.data.DbClient.DB(ctx)).ResourceIPAffiliatePlatform.WithContext(ctx).Create(&plat)
		if err != nil {
			return err
		}
	}
	// 联盟渠道信息
	channel := ipResource.AffiliateChannel
	channel.ResourceID = resourceId
	err = query.Use(p.data.DbClient.DB(ctx)).ResourceIPAffiliateChannel.WithContext(ctx).Create(&channel)
	if err != nil {
		return err
	}
	return nil
}

// AddIpBrandBusinessContact 添加品牌商联系人
func (p IpResourceRepo) AddIpBrandBusinessContact(ctx context.Context, ipBrandBusiness model.ResourceIPBrandBusiness, ipBrandContact model.ResourceContact, resourceId int32, groupCode string) error {
	ipQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPBrandBusiness
	ipContactQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceContact
	ipBrandBusiness.ResourceID = resourceId
	err := ipQuery.WithContext(ctx).Create(&ipBrandBusiness)
	if err != nil {
		return err
	}
	ipBrandContact.ResourceID = resourceId
	ipBrandContact.BusinessID = ipBrandBusiness.ID
	ipBrandContact.ResourceRoleType = groupCode
	return ipContactQuery.WithContext(ctx).Create(&ipBrandContact)
}

// AddIpAffiliateBusinessContact 添加联盟商联系人
func (p IpResourceRepo) AddIpAffiliateBusinessContact(ctx context.Context, ipAffiliateBusiness model.ResourceIPAffiliateBusiness, ipBrandContact model.ResourceContact, resourceId int32, groupCode string) error {
	ipQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPAffiliateBusiness
	ipContactQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceContact
	ipAffiliateBusiness.ResourceID = resourceId
	err := ipQuery.WithContext(ctx).Create(&ipAffiliateBusiness)
	if err != nil {
		return err
	}
	ipBrandContact.ResourceID = resourceId
	ipBrandContact.BusinessID = ipAffiliateBusiness.ID
	ipBrandContact.ResourceRoleType = groupCode
	return ipContactQuery.WithContext(ctx).Create(&ipBrandContact)
}

func (p IpResourceRepo) UpdateIpBaseTx(ctx context.Context, ipResource biz.IpResourceData, userEmail string, groups []bizCommon.GroupInfo) error {
	var err error
	originNote, err := p.GetIpResourceDetail(ctx, ipResource.IpResource.ID, groups)
	if err != nil {
		return err
	}
	originNoteJson, err := json.Marshal(originNote)
	if err != nil {
		return err
	}
	err = p.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 添加基础信息
		_, err := p.AddIpBase(ctx, ipResource.IpResource)
		if err != nil {
			return err
		}

		// 根据角色类型调用不同的方法
		for _, group := range groups {
			switch group.Code {
			case constants.BrandGroupCode:
				err = p.UpdateIpResourceBrand(ctx, ipResource, group.Code)
				break
			case constants.AffiliateGroupCode:
				err = p.UpdateIpResourceAffiliate(ctx, ipResource, group.Code)
				break
			default:
				p.log.WithContext(ctx).Warnf("未知的角色类型: %v", group.Code)
				continue
			}
			if err != nil {
				return err
			}
		}
		// 操作记录
		updateNote, err := json.Marshal(ipResource)
		if err != nil {
			return err
		}
		return CreateResourceLog(ctx, p.data.DbClient.DB(ctx), model.ResourceOperationLog{
			ResourceID:   ipResource.IpResource.ID,
			ResourceType: constants.ResourceIP,
			OptType:      constants.OptTypeUpdate,
			OptUser:      userEmail,
			OriginalNote: originNoteJson,
			UpdateNote:   updateNote,
		})
	})
	return err
}

// DeleteIpResourceBrand 删除品牌商资源
func (p IpResourceRepo) DeleteIpResourceBrand(ctx context.Context, resourceId int32, groupCode string) error {
	ipQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPBrandBusiness
	ipContactQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceContact
	// 删除品牌
	err := p.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 删除品牌商联系人
		_, err := ipQuery.WithContext(ctx).Where(ipQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourceIPBrandBusiness{})
		if err != nil {
			return err
		}
		// 删除联系人
		_, err = ipContactQuery.WithContext(ctx).
			Where(ipContactQuery.ResourceID.Eq(resourceId), ipContactQuery.ResourceType.Eq(constants.ResourceIP), ipContactQuery.ResourceRoleType.Eq(groupCode)).
			Delete(&model.ResourceContact{})
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdateIpResourceBrand 更新品牌商资源
func (p IpResourceRepo) UpdateIpResourceBrand(ctx context.Context, ipResource biz.IpResourceData, groupCode string) error {
	err := p.DeleteIpResourceBrand(ctx, ipResource.IpResource.ID, groupCode)
	if err != nil {
		return err
	}
	err = p.AddIpResourceBrand(ctx, ipResource, groupCode, ipResource.IpResource.ID)
	if err != nil {
		return err
	}
	return nil
}

// DeleteIpResourceAffiliate 删除联盟资源
func (p IpResourceRepo) DeleteIpResourceAffiliate(ctx context.Context, resourceId int32, groupCode string) error {
	ipQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPAffiliateBusiness
	ipContactQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceContact
	ipPlatQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPAffiliatePlatform
	ipChannelQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPAffiliateChannel
	// 删除品牌
	err := p.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 删除品牌商联系人
		_, err := ipQuery.WithContext(ctx).Where(ipQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourceIPAffiliateBusiness{})
		if err != nil {
			return err
		}
		// 删除联系人
		_, err = ipContactQuery.WithContext(ctx).
			Where(ipContactQuery.ResourceID.Eq(resourceId), ipContactQuery.ResourceType.Eq(constants.ResourceIP), ipContactQuery.ResourceRoleType.Eq(groupCode)).
			Delete(&model.ResourceContact{})
		if err != nil {
			return err
		}
		// 删除平台
		_, err = ipPlatQuery.WithContext(ctx).Where(ipPlatQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourceIPAffiliatePlatform{})
		if err != nil {
			return err
		}
		// 删除渠道
		_, err = ipChannelQuery.WithContext(ctx).Where(ipChannelQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourceIPAffiliateChannel{})
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdateIpResourceAffiliate 更新联盟资源
func (p IpResourceRepo) UpdateIpResourceAffiliate(ctx context.Context, ipResource biz.IpResourceData, groupCode string) error {
	err := p.DeleteIpResourceAffiliate(ctx, ipResource.IpResource.ID, groupCode)
	if err != nil {
		return err
	}
	err = p.AddIpResourceAffiliate(ctx, ipResource, groupCode, ipResource.IpResource.ID)
	if err != nil {
		return err
	}

	return nil
}

func (p IpResourceRepo) getIpBrandDetail(ctx context.Context, resourceId int32) (biz.BrandIp, error) {
	q := query.Use(p.data.DbClient.DB(ctx))
	// 查询品牌信息
	var brandBusiness []biz.BrandBusinessContact
	err := q.ResourceIPBrandBusiness.WithContext(ctx).
		Select(q.ResourceIPBrandBusiness.ALL, q.ResourceContact.ALL).
		LeftJoin(q.ResourceContact, q.ResourceIPBrandBusiness.ID.EqCol(q.ResourceContact.BusinessID)).
		Where(
			q.ResourceIPBrandBusiness.ResourceID.Eq(resourceId),
			q.ResourceContact.ResourceType.Eq(constants.ResourceIP),
			q.ResourceContact.ResourceRoleType.Eq(constants.BrandGroupCode),
		).Scan(&brandBusiness)
	if err != nil {
		return biz.BrandIp{}, fmt.Errorf("查询品牌客商务信息失败: %v", err)
	}
	return biz.BrandIp{
		BrandBusinessContact: brandBusiness,
	}, nil
}

func (p IpResourceRepo) getIpAffiliateDetail(ctx context.Context, resourceId int32) (biz.AffiliateIp, error) {
	q := query.Use(p.data.DbClient.DB(ctx))
	// 查询联盟信息
	var affiliateBusiness []biz.AffiliateBusinessContact
	err := q.ResourceIPAffiliateBusiness.WithContext(ctx).
		Select(q.ResourceIPAffiliateBusiness.ALL, q.ResourceContact.ALL).
		LeftJoin(q.ResourceContact, q.ResourceIPAffiliateBusiness.ID.EqCol(q.ResourceContact.BusinessID)).
		Where(
			q.ResourceIPAffiliateBusiness.ResourceID.Eq(resourceId),
			q.ResourceContact.ResourceType.Eq(constants.ResourceIP),
			q.ResourceContact.ResourceRoleType.Eq(constants.AffiliateGroupCode),
		).Scan(&affiliateBusiness)
	if err != nil {
		return biz.AffiliateIp{}, fmt.Errorf("查询联盟客商务信息失败: %v", err)
	}

	// 查询联盟平台信息
	var affiliatePlatforms []model.ResourceIPAffiliatePlatform
	err = q.ResourceIPAffiliatePlatform.WithContext(ctx).
		Where(q.ResourceIPAffiliatePlatform.ResourceID.Eq(resourceId)).
		Scan(&affiliatePlatforms)

	// 查询联盟渠道信息
	var affiliateChannel model.ResourceIPAffiliateChannel
	err = q.ResourceIPAffiliateChannel.WithContext(ctx).
		Where(q.ResourceIPAffiliateChannel.ResourceID.Eq(resourceId)).
		Scan(&affiliateChannel)
	return biz.AffiliateIp{
		AffiliateBusinessContact: affiliateBusiness,
		AffiliatePlatform:        affiliatePlatforms,
		AffiliateChannel:         affiliateChannel,
	}, err
}

func (p IpResourceRepo) GetIpResourceDetail(ctx context.Context, resourceId int32, groups []bizCommon.GroupInfo) (*biz.IpResourceData, error) {
	var ret biz.IpResourceData
	// 查询基础信息
	q := query.Use(p.data.DbClient.DB(ctx))
	base, err := q.ResourceIPBase.WithContext(ctx).
		Where(q.ResourceIPBase.ID.Eq(resourceId), q.ResourceIPBase.IsDeleted.Eq(0)).
		First()
	if err != nil {
		return nil, fmt.Errorf("查询基础信息失败: %w", err)
	}
	ret.IpResource = *base
	// 根据角色类型调用不同的方法
	for _, group := range groups {
		switch group.Code {
		case constants.BrandGroupCode:
			ret.BrandIp, err = p.getIpBrandDetail(ctx, resourceId)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("查询品牌媒介基础信息失败: %w", err)
			}
		case constants.AffiliateGroupCode:
			ret.AffiliateIp, err = p.getIpAffiliateDetail(ctx, resourceId)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("查询联盟运营基础信息失败: %w", err)
			}
		default:
			p.log.WithContext(ctx).Warnf("未知的角色类型: %v", group.Code)
			continue
		}
	}
	return &ret, nil
}

func (p IpResourceRepo) DelIpResource(ctx context.Context, resourceId int32) error {
	ipQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPBase
	_, err := ipQuery.WithContext(ctx).Where(ipQuery.ID.Eq(resourceId)).Update(ipQuery.IsDeleted, 1)
	return err
}

func (p IpResourceRepo) ListIpResource(ctx context.Context, req biz.ListIpResourceReq, groups []bizCommon.GroupInfo) (*biz.ListIpResponse, error) {
	ipQuery := p.data.DbClient.DB(ctx).Model(&model.ResourceIPBase{}).
		Joins("LEFT JOIN resource_ip_affiliate_business ON resource_ip_base.id = resource_ip_affiliate_business.resource_id").
		Joins("LEFT JOIN resource_ip_affiliate_channel ON resource_ip_base.id = resource_ip_affiliate_channel.resource_id").
		Joins("LEFT JOIN resource_ip_affiliate_platform ON resource_ip_base.id = resource_ip_affiliate_platform.resource_id").
		Joins("LEFT JOIN resource_ip_brand_business ON resource_ip_base.id = resource_ip_brand_business.resource_id").
		Where("resource_ip_base.is_deleted = ?", 0)

	// 资源名称
	if req.Name != "" {
		ipQuery = ipQuery.Where("resource_ip_base.name LIKE ?", util.FormatLikeParam(req.Name, true, true))
	}
	// 类型
	if len(req.TypeList) > 0 {
		ipQuery = util.JSONQuery(ipQuery, "resource_ip_base.type", "", "", req.TypeList)
	}

	// --品牌
	if req.BrandParam.ContactType != "" {
		ipQuery = ipQuery.Where("resource_ip_brand_business.contact_type = ?", req.BrandParam.ContactType)
	}
	if len(req.BrandParam.ServiceContent) > 0 {
		ipQuery = ipQuery.Where("resource_ip_brand_business.service_content IN ?", req.BrandParam.ServiceContent)
	}
	// 项目资源筛选
	if req.CustomerProjectId > 0 {
		ipQuery = ipQuery.Joins("INNER JOIN customer_project_resource ON customer_project_resource.resource_id = resource_ip_base.id").
			Where("customer_project_resource.customer_project_id = ?", req.CustomerProjectId).
			Where("customer_project_resource.resource_type = ?", constants.ResourceIP)
	}
	// --联盟
	// 客户旅程阶段
	if len(req.CustomerJourneyStageList) > 0 {
		ipQuery = ipQuery.Where("resource_ip_affiliate_channel.customer_journey_stage in ?", req.CustomerJourneyStageList)
	}
	// 营销目标
	if len(req.MarketingObjectivesList) > 0 {
		ipQuery = util.JSONQuery(ipQuery, "resource_ip_affiliate_channel.marketing_objectives", "", "", req.MarketingObjectivesList)
	}
	// 联盟客所属平台名称
	if len(req.AffPlatformNameList) > 0 {
		ipQuery = ipQuery.Where("resource_ip_affiliate_platform.aff_platform_name in ?", req.AffPlatformNameList)
	}
	// 联盟客名称
	if req.AffPlatformPublisherName != "" {
		ipQuery = ipQuery.Where("resource_ip_affiliate_platform.aff_platform_publisher_name like ?", util.FormatLikeParam(req.AffPlatformPublisherName, true, true))
	}
	// 平台内该联盟客ID
	if req.AffPlatformPublisherId != "" {
		ipQuery = ipQuery.Where("resource_ip_affiliate_platform.aff_platform_publisher_id like ?", util.FormatLikeParam(req.AffPlatformPublisherId, true, true))
	}
	// 渠道类型
	if len(req.ChannelTypeList) > 0 {
		ipQuery = util.JSONQuery(ipQuery, "resource_ip_affiliate_channel.channel_type", "", "", req.ChannelTypeList)
	}
	// 推广类目
	if len(req.PromotionCategoryList) > 0 {
		ipQuery = util.JSONQuery(ipQuery, "resource_ip_affiliate_channel.promotion_category", "", "", req.PromotionCategoryList)
	}
	// 推广方式
	if len(req.PromotionTypeList) > 0 {
		ipQuery = util.JSONQuery(ipQuery, "resource_ip_affiliate_channel.promotion_type", "", "", req.PromotionTypeList)
	}
	// 标签
	if len(req.AffiliateParam.TagList) > 0 {
		ipQuery = util.JSONQuery(ipQuery, "resource_ip_affiliate_channel.tag", "", "", req.AffiliateParam.TagList)
	}
	// 国家/地区
	if len(req.RegionCountry) > 0 {
		req.RegionCountry = append(req.RegionCountry, biz.StringArr{Value: []string{"global"}})
		var countryArr []string
		for _, country := range req.RegionCountry {
			rcQuerySql := fmt.Sprintf("JSON_CONTAINS_PATH(region_country,'one','$.\"%s\"')", country.Value[0])
			if len(country.Value) > 1 {
				rcQuerySql = fmt.Sprintf("%s AND JSON_OVERLAPS(JSON_EXTRACT(region_country,'$.\"%s\"'), '[\"%s\"]')", rcQuerySql, country.Value[0], country.Value[1])
			}
			countryArr = append(countryArr, fmt.Sprintf("(%s)", rcQuerySql))
		}
		ipQuery = ipQuery.Where(fmt.Sprintf("(%s)", strings.Join(countryArr, " OR ")))
	}
	// 价格
	var priceQuerySqlArr []string
	for _, group := range groups {
		priceQuerySql := make([]string, 0)
		switch group.Code {
		case constants.BrandGroupCode:
			// 报价
			if req.Price.Min > 0 {
				priceQuerySql = append(priceQuerySql, fmt.Sprintf("%s%f", "JSON_EXTRACT(resource_ip_brand_business.price, '$.min_usd') >= ", req.Price.Min))
			}
			if req.Price.Max > 0 {
				priceQuerySql = append(priceQuerySql, fmt.Sprintf("%s%f", "JSON_EXTRACT(resource_ip_brand_business.price, '$.max_usd') >= ", req.Price.Max))
			}
		case constants.AffiliateGroupCode:
			// 报价
			if req.Price.Min > 0 {
				priceQuerySql = append(priceQuerySql, fmt.Sprintf("%s%f", "JSON_EXTRACT(resource_ip_affiliate_business.price, '$.min_usd') >= ", req.Price.Min))
			}
			if req.Price.Max > 0 {
				priceQuerySql = append(priceQuerySql, fmt.Sprintf("%s%f", "JSON_EXTRACT(resource_ip_affiliate_business.price, '$.max_usd') <= ", req.Price.Max))
			}
		default:
			p.log.WithContext(ctx).Errorf("未知的角色：%v", group.Code)
			continue
		}
		if len(priceQuerySql) > 0 {
			priceQuerySqlArr = append(priceQuerySqlArr, strings.Join(priceQuerySql, " AND "))
		}
	}
	// 不同角色用OR连接
	if len(priceQuerySqlArr) > 0 {
		ipQuery = ipQuery.Where(fmt.Sprintf("(%s)", strings.Join(priceQuerySqlArr, " OR ")))
	}

	ipQuery = ipQuery.Group("resource_ip_base.id").Order("resource_ip_base.create_time desc,resource_ip_base.id desc")

	var count int64
	if err := ipQuery.Count(&count).Error; err != nil {
		p.log.WithContext(ctx).Errorf("Failed to execute count query: %v", err)
		return nil, resourceError.ResourceEmptyFail
	}

	if req.PageNum > 0 && req.PageSize > 0 {
		offset := (req.PageNum - 1) * req.PageSize
		ipQuery = ipQuery.Offset(int(offset)).Limit(int(req.PageSize))
	}

	var ipList []*model.ResourceIPBase
	if err := ipQuery.Find(&ipList).Error; err != nil {
		p.log.WithContext(ctx).Errorf("Failed to execute query: %v", err)
		return nil, resourceError.ResourceEmptyFail
	}

	return &biz.ListIpResponse{Data: ipList, Total: int32(count), PageNum: req.PageNum, PageSize: req.PageSize}, nil
}

func (p IpResourceRepo) CheckIpResourceExist(ctx context.Context, resourceId int32) (bool, error) {
	ipQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceIPBase
	_, err := ipQuery.WithContext(ctx).Where(ipQuery.ID.Eq(resourceId)).First()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return false, resourceError.ResourceEmpty
	}
	if err != nil {
		return false, resourceError.ResourceEmptyFail
	}
	return true, nil
}
