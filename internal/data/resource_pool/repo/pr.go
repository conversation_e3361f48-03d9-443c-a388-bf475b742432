package repo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strings"
	bizCommon "vision_hub/internal/biz/common"
	biz "vision_hub/internal/biz/resource_pool/pr"
	"vision_hub/internal/conf"
	constants "vision_hub/internal/constants/resource_pool"
	"vision_hub/internal/data/common"
	"vision_hub/internal/data/resource_pool/model"
	"vision_hub/internal/data/resource_pool/query"
	resourceError "vision_hub/internal/errors/resource_pool"
	"vision_hub/internal/util"
)

type PrResourceRepo struct {
	conf *conf.BizConf
	log  *common.DataLogHelper
	data *common.Data
}

func NewPrResourceRepo(conf *conf.BizConf, logger *common.DataLogHelper, data *common.Data) biz.IPrResourceRepo {
	return &PrResourceRepo{
		conf: conf,
		log:  logger,
		data: data,
	}
}
func (p PrResourceRepo) CheckPrHomepageExist(ctx context.Context, homepage string, resourceId int32) (bool, error) {
	prQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrBase
	count, err := prQuery.WithContext(ctx).Where(prQuery.Homepage.Eq(homepage), prQuery.ID.Neq(resourceId)).Count()
	return count > 0, err
}

func (p PrResourceRepo) AddPrBaseTx(ctx context.Context, prResource biz.PrResourceData, userEmail string, groups []bizCommon.GroupInfo) error {
	err := p.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 添加基础信息
		resourceId, err := p.AddPrBase(ctx, prResource.PrResource)
		if err != nil {
			return err
		}

		// 根据角色类型调用不同的方法
		for _, group := range groups {
			switch group.Code {
			case constants.BrandGroupCode:
				err = p.AddPrResourceBrand(ctx, prResource, group.Code, resourceId)
			case constants.AffiliateGroupCode:
				err = p.AddPrResourceAffiliate(ctx, prResource, group.Code, resourceId)
			default:
				p.log.WithContext(ctx).Warnf("未知的角色类型: %v", group.Code)
				continue
			}
			if err != nil {
				return err
			}
		}
		originNote := []byte(`{}`)
		updateNote, err := json.Marshal(prResource)
		if err != nil {
			return err
		}
		// 操作记录
		return CreateResourceLog(ctx, p.data.DbClient.DB(ctx), model.ResourceOperationLog{
			ResourceID:   resourceId,
			ResourceType: constants.ResourcePR,
			OptType:      constants.OptTypeCreate,
			OptUser:      userEmail,
			OriginalNote: originNote,
			UpdateNote:   updateNote,
		})
	})
	return err
}

// AddPrBase 添加资源基础信息
func (p PrResourceRepo) AddPrBase(ctx context.Context, prResource model.ResourcePrBase) (int32, error) {
	prQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrBase
	//var baseInfo *model.ResourcePrBase
	var err error
	if prResource.ID > 0 {
		//baseInfo, err = prQuery.WithContext(ctx).
		//	Where(prQuery.ID.Eq(baseInfo.ID)).
		//	First()
		//if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		//	p.log.WithContext(ctx).Errorf("查询PR资源基础信息失败: %v", err)
		//	return 0, resourceError.ResourceEmptyFail
		//}
		//if baseInfo == nil {
		//	p.log.WithContext(ctx).Warnf("更新PR资源资源时未找到对应数据: %d", prResource.ID)
		//	return 0, resourceError.ResourceEmpty
		//}
		//if baseInfo.IsDeleted == 1 {
		//	p.log.WithContext(ctx).Warnf("当前PR资源基础信息已被删除: %d", baseInfo.ID)
		//	return 0, resourceError.ResourceEmpty
		//}
		// 更新记录
		//var resultInfo gen.ResultInfo
		if err = prQuery.WithContext(ctx).Save(&prResource); err != nil {
			p.log.WithContext(ctx).Errorf("更新PR资源基础信息失败: %v", err)
			return 0, resourceError.ResourceUpdateErr
		}
		//if resultInfo.RowsAffected > 0 {
		//	p.log.WithContext(ctx).Debugf("更新PR资源基础信息成功: %d", prResource.ID)
		//}
	} else {
		//baseInfo, err = prQuery.WithContext(ctx).
		//	Where(prQuery.DisplayName.Eq(prResource.DisplayName)).
		//	First()

		if err := prQuery.WithContext(ctx).Create(&prResource); err != nil {
			p.log.WithContext(ctx).Errorf("新增PR资源基础信息失败: %v", err)
			return 0, resourceError.ResourceAddErr
		}
		p.log.WithContext(ctx).Debugf("新增PR资源基础信息成功: %d", prResource.ID)
		return prResource.ID, nil

		//if baseInfo != nil {
		//	p.log.WithContext(ctx).Warnf("当前PR资源基础信息已存在: %d", baseInfo.ID)
		//	return 0, resourceError.ResourceNotEmpty
		//}
	}

	return prResource.ID, nil
}

// AddPrResourceBrand 添加品牌商资源
func (p PrResourceRepo) AddPrResourceBrand(ctx context.Context, prResource biz.PrResourceData, groupCode string, resourceId int32) error {
	var err error
	// 联系人信息
	for _, prBrand := range prResource.BrandBusinessContact {
		err = p.AddPrBrandBusinessContact(ctx, prBrand.PrBrandBusiness, prBrand.Contact, resourceId, groupCode)
		if err != nil {
			return err
		}
	}
	// 标签信息
	for _, tag := range prResource.BrandPrTag {
		tag.ResourceID = resourceId
		err = query.Use(p.data.DbClient.DB(ctx)).ResourcePrBrandTag.WithContext(ctx).Create(&tag)
		if err != nil {
			return err
		}
	}

	return nil
}

// AddPrResourceAffiliate 添加联盟资源
func (p PrResourceRepo) AddPrResourceAffiliate(ctx context.Context, prResource biz.PrResourceData, groupCode string, resourceId int32) error {
	var err error
	for _, prAffiliate := range prResource.AffiliateBusinessContact {
		err = p.AddPrAffiliateBusinessContact(ctx, prAffiliate.PrAffiliateBusiness, prAffiliate.Contact, resourceId, groupCode)
		if err != nil {
			return err
		}
	}
	// 联盟平台信息
	for _, plat := range prResource.AffiliatePlatform {
		plat.ResourceID = resourceId
		err = query.Use(p.data.DbClient.DB(ctx)).ResourcePrAffiliatePlatform.WithContext(ctx).Create(&plat)
		if err != nil {
			return err
		}
	}
	// 联盟渠道信息
	channel := prResource.AffiliateChannel
	channel.ResourceID = resourceId
	err = query.Use(p.data.DbClient.DB(ctx)).ResourcePrAffiliateChannel.WithContext(ctx).Create(&channel)
	if err != nil {
		return err
	}

	return nil
}

// AddPrBrandBusinessContact 添加品牌商联系人
func (p PrResourceRepo) AddPrBrandBusinessContact(ctx context.Context, prBrandBusiness model.ResourcePrBrandBusiness, prBrandContact model.ResourceContact, resourceId int32, groupCode string) error {
	prQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrBrandBusiness
	prContactQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceContact
	prBrandBusiness.ResourceID = resourceId
	err := prQuery.WithContext(ctx).Create(&prBrandBusiness)
	if err != nil {
		return err
	}
	prBrandContact.ResourceID = resourceId
	prBrandContact.BusinessID = prBrandBusiness.ID
	prBrandContact.ResourceRoleType = groupCode
	return prContactQuery.WithContext(ctx).Create(&prBrandContact)
}

// AddPrAffiliateBusinessContact 添加联盟商联系人
func (p PrResourceRepo) AddPrAffiliateBusinessContact(ctx context.Context, prAffiliateBusiness model.ResourcePrAffiliateBusiness, prBrandContact model.ResourceContact, resourceId int32, groupCode string) error {
	prQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrAffiliateBusiness
	prContactQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceContact
	prAffiliateBusiness.ResourceID = resourceId
	err := prQuery.WithContext(ctx).Create(&prAffiliateBusiness)
	if err != nil {
		return err
	}
	prBrandContact.ResourceID = resourceId
	prBrandContact.BusinessID = prAffiliateBusiness.ID
	prBrandContact.ResourceRoleType = groupCode
	return prContactQuery.WithContext(ctx).Create(&prBrandContact)
}

func (p PrResourceRepo) UpdatePrBaseTx(ctx context.Context, prResource biz.PrResourceData, userEmail string, groups []bizCommon.GroupInfo) error {
	var err error
	originNote, err := p.GetPrResourceDetail(ctx, prResource.PrResource.ID, groups)
	if err != nil {
		return err
	}
	originNoteJson, err := json.Marshal(originNote)
	if err != nil {
		return err
	}
	err = p.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 添加基础信息
		_, err := p.AddPrBase(ctx, prResource.PrResource)
		if err != nil {
			return err
		}

		// 根据角色类型调用不同的方法
		for _, group := range groups {
			switch group.Code {
			case constants.BrandGroupCode:
				err = p.UpdatePrResourceBrand(ctx, prResource, group.Code)
			case constants.AffiliateGroupCode:
				err = p.UpdatePrResourceAffiliate(ctx, prResource, group.Code)
			default:
				p.log.WithContext(ctx).Warnf("未知的角色类型: %v", group.Code)
				continue
			}
			if err != nil {
				return err
			}
		}

		updateNote, err := json.Marshal(prResource)
		if err != nil {
			return err
		}
		// 操作记录
		return CreateResourceLog(ctx, p.data.DbClient.DB(ctx), model.ResourceOperationLog{
			ResourceID:   prResource.PrResource.ID,
			ResourceType: constants.ResourcePR,
			OptType:      constants.OptTypeUpdate,
			OptUser:      userEmail,
			OriginalNote: originNoteJson,
			UpdateNote:   updateNote,
		})
	})
	return err
}

// DeletePrResourceBrand 删除品牌商资源
func (p PrResourceRepo) DeletePrResourceBrand(ctx context.Context, resourceId int32, groupCode string) error {
	prQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrBrandBusiness
	prContactQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceContact
	prTagQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrBrandTag
	// 删除品牌
	err := p.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 删除品牌商联系人
		_, err := prQuery.WithContext(ctx).Where(prQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourcePrBrandBusiness{})
		if err != nil {
			return err
		}
		// 删除联系人
		_, err = prContactQuery.WithContext(ctx).
			Where(prContactQuery.ResourceID.Eq(resourceId), prContactQuery.ResourceType.Eq(constants.ResourcePR), prContactQuery.ResourceRoleType.Eq(groupCode)).
			Delete(&model.ResourceContact{})
		if err != nil {
			return err
		}
		// 删除标签
		_, err = prTagQuery.WithContext(ctx).Where(prTagQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourcePrBrandTag{})
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdatePrResourceBrand 更新品牌商资源
func (p PrResourceRepo) UpdatePrResourceBrand(ctx context.Context, prResource biz.PrResourceData, groupCode string) error {
	err := p.DeletePrResourceBrand(ctx, prResource.PrResource.ID, groupCode)
	if err != nil {
		return err
	}
	err = p.AddPrResourceBrand(ctx, prResource, groupCode, prResource.PrResource.ID)
	if err != nil {
		return err
	}
	return nil
}

// DeletePrResourceAffiliate 删除联盟资源
func (p PrResourceRepo) DeletePrResourceAffiliate(ctx context.Context, resourceId int32, groupCode string) error {
	prQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrAffiliateBusiness
	prContactQuery := query.Use(p.data.DbClient.DB(ctx)).ResourceContact
	prPlatQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrAffiliatePlatform
	prChannelQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrAffiliateChannel
	// 删除品牌
	err := p.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 删除品牌商联系人
		_, err := prQuery.WithContext(ctx).Where(prQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourcePrAffiliateBusiness{})
		if err != nil {
			return err
		}
		// 删除联系人
		_, err = prContactQuery.WithContext(ctx).
			Where(prContactQuery.ResourceID.Eq(resourceId), prContactQuery.ResourceType.Eq(constants.ResourcePR), prContactQuery.ResourceRoleType.Eq(groupCode)).
			Delete(&model.ResourceContact{})
		if err != nil {
			return err
		}
		// 删除平台
		_, err = prPlatQuery.WithContext(ctx).Where(prPlatQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourcePrAffiliatePlatform{})
		if err != nil {
			return err
		}
		// 删除渠道
		_, err = prChannelQuery.WithContext(ctx).Where(prChannelQuery.ResourceID.Eq(resourceId)).Delete(&model.ResourcePrAffiliateChannel{})
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdatePrResourceAffiliate 更新联盟资源
func (p PrResourceRepo) UpdatePrResourceAffiliate(ctx context.Context, prResource biz.PrResourceData, groupCode string) error {
	err := p.DeletePrResourceAffiliate(ctx, prResource.PrResource.ID, groupCode)
	if err != nil {
		return err
	}
	err = p.AddPrResourceAffiliate(ctx, prResource, groupCode, prResource.PrResource.ID)
	if err != nil {
		return err
	}
	return nil
}

func (p PrResourceRepo) getPrBrandDetail(ctx context.Context, resourceId int32) (biz.BrandPr, error) {
	q := query.Use(p.data.DbClient.DB(ctx))
	// 查询品牌信息
	var brandBusiness []biz.BrandBusinessContact
	err := q.ResourcePrBrandBusiness.WithContext(ctx).
		Select(q.ResourcePrBrandBusiness.ALL, q.ResourceContact.ALL).
		LeftJoin(q.ResourceContact, q.ResourcePrBrandBusiness.ID.EqCol(q.ResourceContact.BusinessID)).
		Where(
			q.ResourcePrBrandBusiness.ResourceID.Eq(resourceId),
			q.ResourceContact.ResourceType.Eq(constants.ResourcePR),
			q.ResourceContact.ResourceRoleType.Eq(constants.BrandGroupCode),
		).Scan(&brandBusiness)
	if err != nil {
		return biz.BrandPr{}, fmt.Errorf("查询品牌客商务信息失败: %v", err)
	}
	// 查询品牌标签
	var brandTags []model.ResourcePrBrandTag
	err = q.ResourcePrBrandTag.WithContext(ctx).Select(q.ResourcePrBrandTag.Name).
		Where(q.ResourcePrBrandTag.ResourceID.Eq(resourceId)).Scan(&brandTags)

	return biz.BrandPr{
		BrandBusinessContact: brandBusiness,
		BrandPrTag:           brandTags,
	}, err
}

func (p PrResourceRepo) getPrAffiliateDetail(ctx context.Context, resourceId int32) (biz.AffiliatePr, error) {
	q := query.Use(p.data.DbClient.DB(ctx))
	// 查询联盟信息
	var affiliateBusiness []biz.AffiliateBusinessContact
	err := q.ResourcePrAffiliateBusiness.WithContext(ctx).
		Select(q.ResourcePrAffiliateBusiness.ALL, q.ResourceContact.ALL).
		LeftJoin(q.ResourceContact, q.ResourcePrAffiliateBusiness.ID.EqCol(q.ResourceContact.BusinessID)).
		Where(
			q.ResourcePrAffiliateBusiness.ResourceID.Eq(resourceId),
			q.ResourceContact.ResourceType.Eq(constants.ResourcePR),
			q.ResourceContact.ResourceRoleType.Eq(constants.AffiliateGroupCode),
		).Scan(&affiliateBusiness)
	if err != nil {
		return biz.AffiliatePr{}, fmt.Errorf("查询联盟客商务信息失败: %v", err)
	}

	// 查询联盟平台信息
	var affiliatePlatforms []model.ResourcePrAffiliatePlatform
	err = q.ResourcePrAffiliatePlatform.WithContext(ctx).
		Where(q.ResourcePrAffiliatePlatform.ResourceID.Eq(resourceId)).
		Scan(&affiliatePlatforms)

	// 查询联盟渠道信息
	var affiliateChannel model.ResourcePrAffiliateChannel
	err = q.ResourcePrAffiliateChannel.WithContext(ctx).
		Where(q.ResourcePrAffiliateChannel.ResourceID.Eq(resourceId)).
		Scan(&affiliateChannel)
	return biz.AffiliatePr{
		AffiliateBusinessContact: affiliateBusiness,
		AffiliatePlatform:        affiliatePlatforms,
		AffiliateChannel:         affiliateChannel,
	}, err
}

func (p PrResourceRepo) GetPrResourceDetail(ctx context.Context, resourceId int32, groups []bizCommon.GroupInfo) (*biz.PrResourceData, error) {
	var ret biz.PrResourceData
	// 1. 查询基础信息
	q := query.Use(p.data.DbClient.DB(ctx))
	base, err := q.ResourcePrBase.WithContext(ctx).
		Where(q.ResourcePrBase.ID.Eq(resourceId), q.ResourcePrBase.IsDeleted.Eq(0)).
		First()
	if err != nil {
		return nil, fmt.Errorf("查询基础信息失败: %w", err)
	}
	ret.PrResource = *base
	// 2. 查询角色可见详情信息
	// 根据角色类型调用不同的方法
	for _, group := range groups {
		switch group.Code {
		case constants.BrandGroupCode:
			ret.BrandPr, err = p.getPrBrandDetail(ctx, resourceId)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("查询品牌媒介基础信息失败: %w", err)
			}
		case constants.AffiliateGroupCode:
			ret.AffiliatePr, err = p.getPrAffiliateDetail(ctx, resourceId)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("查询联盟运营基础信息失败: %w", err)
			}
		default:
			p.log.WithContext(ctx).Warnf("未知的角色类型: %v", group.Code)
			continue
		}
	}
	return &ret, nil
}

func (p PrResourceRepo) DelPrResource(ctx context.Context, resourceId int32) error {
	prQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrBase
	_, err := prQuery.WithContext(ctx).Where(prQuery.ID.Eq(resourceId)).Update(prQuery.IsDeleted, 1)
	return err
}

func (p PrResourceRepo) ListPrResource(ctx context.Context, req biz.ListPrResourceReq, groups []bizCommon.GroupInfo) (*biz.ListPrResponse, error) {
	prQuery := p.data.DbClient.DB(ctx).Model(&model.ResourcePrBase{}).
		Joins("LEFT JOIN resource_pr_affiliate_business ON resource_pr_base.id = resource_pr_affiliate_business.resource_id").
		Joins("LEFT JOIN resource_pr_affiliate_channel ON resource_pr_base.id = resource_pr_affiliate_channel.resource_id").
		Joins("LEFT JOIN resource_pr_affiliate_platform ON resource_pr_base.id = resource_pr_affiliate_platform.resource_id").
		Joins("LEFT JOIN resource_pr_brand_business ON resource_pr_base.id = resource_pr_brand_business.resource_id").
		Joins("LEFT JOIN resource_pr_brand_tag ON resource_pr_base.id = resource_pr_brand_tag.resource_id").
		Where("resource_pr_base.is_deleted = ?", 0)

	// 资源名称
	if req.Name != "" {
		prQuery = prQuery.Where("resource_pr_base.name LIKE ?", util.FormatLikeParam(req.Name, true, true))
	}
	// 量级Size
	if len(req.SizeList) > 0 {
		prQuery = prQuery.Where("resource_pr_base.size IN ?", req.SizeList)
	}
	// 类型
	if len(req.TypeList) > 0 {
		prQuery = util.JSONQuery(prQuery, "resource_pr_base.type", "", "", req.TypeList)
	}
	// 受众地区
	if len(req.CountryTop) > 0 {
		prQuery = util.JSONQuery(prQuery, "resource_pr_base.country_top", "", "", req.CountryTop)
	}
	// 语言筛选
	if len(req.LanguageList) > 0 {
		// 不限语言分类加上
		req.LanguageList = append(req.LanguageList, "any")
		prQuery = util.JSONQuery(prQuery, "resource_pr_base.language_category", "", "", req.LanguageList)
	}
	// 产品类型
	if len(req.ProductTypeList) > 0 {
		prQuery = prQuery.Where(prQuery, "resource_pr_base.product_type in ?", req.ProductTypeList)
	}
	// 发布速度
	if req.ReleaseSpeed.Unit != "" {
		// 报价
		if req.ReleaseSpeed.Min > 0 {
			prQuery = prQuery.Where("JSON_EXTRACT(resource_pr_base.release_speed, '$.min') >= ?", req.ReleaseSpeed.Min)
		}
		if req.ReleaseSpeed.Max > 0 {
			prQuery = prQuery.Where("JSON_EXTRACT(resource_pr_base.release_speed, '$.max') <= ?", req.ReleaseSpeed.Max)
		}
		prQuery = prQuery.Where("JSON_EXTRACT(resource_pr_base.release_speed, '$.unit') = ?", req.ReleaseSpeed.Unit)
	}
	// 是否包翻译
	if req.HasTranslate != "" {
		prQuery = prQuery.Where("resource_pr_base.has_translate = ?", req.HasTranslate)
	}
	// 是否需要底部信息
	if req.HasBottomInfo != "" {
		prQuery = prQuery.Where("resource_pr_base.has_bottom_info = ?", req.HasBottomInfo)
	}
	// 项目资源筛选
	if req.CustomerProjectId > 0 {
		prQuery = prQuery.Joins("INNER JOIN customer_project_resource ON resource_pr_base.id = customer_project_resource.resource_id").
			Where("customer_project_resource.customer_project_id = ?", req.CustomerProjectId).
			Where("customer_project_resource.resource_type = ?", constants.ResourcePR)
	}
	// 供应商筛选
	if len(req.SupplierList) > 0 {
		prQuery = util.JSONQuery(prQuery, "resource_pr_base.supplier", "", "", req.SupplierList)
	}
	// --品牌
	if req.BrandParam.ContactType != "" {
		prQuery = prQuery.Where("resource_pr_brand_business.contact_type = ?", req.BrandParam.ContactType)
	}
	if len(req.BrandParam.PaymentTermsList) > 0 {
		prQuery = prQuery.Where("resource_pr_brand_business.payment_terms IN ?", req.BrandParam.PaymentTermsList)
	}
	if len(req.BrandParam.TagList) > 0 {
		prQuery = prQuery.Where("resource_pr_brand_tag.name IN ?", req.BrandParam.TagList)
	}

	// --联盟
	// 客户旅程阶段
	if len(req.CustomerJourneyStageList) > 0 {
		prQuery = prQuery.Where("resource_pr_affiliate_channel.customer_journey_stage in ?", req.CustomerJourneyStageList)
	}
	// 营销目标
	if len(req.MarketingObjectivesList) > 0 {
		prQuery = util.JSONQuery(prQuery, "resource_pr_affiliate_channel.marketing_objectives", "", "", req.MarketingObjectivesList)
	}
	// 联盟客所属平台名称
	if len(req.AffPlatformNameList) > 0 {
		prQuery = prQuery.Where("resource_pr_affiliate_platform.aff_platform_name in ?", req.AffPlatformNameList)
	}
	// 联盟客名称
	if req.AffPlatformPublisherName != "" {
		prQuery = prQuery.Where("resource_pr_affiliate_platform.aff_platform_publisher_name like ?", util.FormatLikeParam(req.AffPlatformPublisherName, true, true))
	}
	// 平台内该联盟客ID
	if req.AffPlatformPublisherId != "" {
		prQuery = prQuery.Where("resource_pr_affiliate_platform.aff_platform_publisher_id like ?", util.FormatLikeParam(req.AffPlatformPublisherId, true, true))
	}
	// 渠道类型
	if len(req.ChannelTypeList) > 0 {
		prQuery = util.JSONQuery(prQuery, "resource_pr_affiliate_channel.channel_type", "", "", req.ChannelTypeList)
	}
	// 推广类目
	if len(req.PromotionCategoryList) > 0 {
		prQuery = util.JSONQuery(prQuery, "resource_pr_affiliate_channel.promotion_category", "", "", req.PromotionCategoryList)
	}
	// 推广方式
	if len(req.PromotionTypeList) > 0 {
		prQuery = util.JSONQuery(prQuery, "resource_pr_affiliate_channel.promotion_type", "", "", req.PromotionTypeList)
	}
	// 标签
	if len(req.AffiliateParam.TagList) > 0 {
		prQuery = util.JSONQuery(prQuery, "resource_pr_affiliate_channel.tag", "", "", req.AffiliateParam.TagList)
	}
	// 国家/地区
	if len(req.RegionCountry) > 0 {
		req.RegionCountry = append(req.RegionCountry, biz.StringArr{Value: []string{"global"}})
		var countryArr []string
		for _, country := range req.RegionCountry {
			rcQuerySql := fmt.Sprintf("JSON_CONTAINS_PATH(region_country,'one','$.\"%s\"')", country.Value[0])
			if len(country.Value) > 1 {
				rcQuerySql = fmt.Sprintf("%s AND JSON_OVERLAPS(JSON_EXTRACT(region_country,'$.\"%s\"'), '[\"%s\"]')", rcQuerySql, country.Value[0], country.Value[1])
			}
			countryArr = append(countryArr, fmt.Sprintf("(%s)", rcQuerySql))
		}
		prQuery = prQuery.Where(fmt.Sprintf("(%s)", strings.Join(countryArr, " OR ")))
	}
	// 价格
	var priceQuerySqlArr []string
	for _, group := range groups {
		priceQuerySql := make([]string, 0)
		switch group.Code {
		case constants.BrandGroupCode:
			// 报价
			if req.Price.Min > 0 {
				priceQuerySql = append(priceQuerySql, fmt.Sprintf("%s%f", "JSON_EXTRACT(resource_pr_brand_business.price, '$.min_usd') >= ", req.Price.Min))
			}
			if req.Price.Max > 0 {
				priceQuerySql = append(priceQuerySql, fmt.Sprintf("%s%f", "JSON_EXTRACT(resource_pr_brand_business.price, '$.max_usd') <= ", req.Price.Max))
			}
		case constants.AffiliateGroupCode:
			// 报价
			if req.Price.Min > 0 {
				priceQuerySql = append(priceQuerySql, fmt.Sprintf("%s%f", "JSON_EXTRACT(resource_pr_affiliate_business.price, '$.min_usd') >= ", req.Price.Min))
			}
			if req.Price.Max > 0 {
				priceQuerySql = append(priceQuerySql, fmt.Sprintf("%s%f", "JSON_EXTRACT(resource_pr_affiliate_business.price, '$.max_usd') <= ", req.Price.Max))
			}
		default:
			p.log.WithContext(ctx).Errorf("未知的角色：%v", group.Code)
			continue
		}
		if len(priceQuerySql) > 0 {
			priceQuerySqlArr = append(priceQuerySqlArr, strings.Join(priceQuerySql, " AND "))
		}
	}
	// 不同角色用OR连接
	if len(priceQuerySqlArr) > 0 {
		prQuery = prQuery.Where(fmt.Sprintf("(%s)", strings.Join(priceQuerySqlArr, " OR ")))
	}

	prQuery = prQuery.Group("resource_pr_base.id").Order("resource_pr_base.create_time DESC , resource_pr_base.id DESC")

	var count int64
	if err := prQuery.Count(&count).Error; err != nil {
		p.log.WithContext(ctx).Errorf("Failed to execute count query: %v", err)
		return nil, resourceError.ResourceEmptyFail
	}

	if req.PageNum > 0 && req.PageSize > 0 {
		offset := (req.PageNum - 1) * req.PageSize
		prQuery = prQuery.Offset(int(offset)).Limit(int(req.PageSize))
	}
	var prList []*model.ResourcePrBase
	if err := prQuery.Find(&prList).Error; err != nil {
		p.log.WithContext(ctx).Errorf("Failed to execute query: %v", err)
		return nil, resourceError.ResourceEmptyFail
	}
	return &biz.ListPrResponse{Data: prList, Total: int32(count), PageNum: req.PageNum, PageSize: req.PageSize}, nil
}

func (p PrResourceRepo) CheckPrResourceExist(ctx context.Context, resourceId int32) (bool, error) {
	prQuery := query.Use(p.data.DbClient.DB(ctx)).ResourcePrBase
	_, err := prQuery.WithContext(ctx).Where(prQuery.ID.Eq(resourceId)).First()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return false, resourceError.ResourceEmpty
	}
	if err != nil {
		return false, resourceError.ResourceEmptyFail
	}
	return true, nil
}
