// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/resource_pool/model"
)

func newResourcePrAffiliateBusiness(db *gorm.DB, opts ...gen.DOOption) resourcePrAffiliateBusiness {
	_resourcePrAffiliateBusiness := resourcePrAffiliateBusiness{}

	_resourcePrAffiliateBusiness.resourcePrAffiliateBusinessDo.UseDB(db, opts...)
	_resourcePrAffiliateBusiness.resourcePrAffiliateBusinessDo.UseModel(&model.ResourcePrAffiliateBusiness{})

	tableName := _resourcePrAffiliateBusiness.resourcePrAffiliateBusinessDo.TableName()
	_resourcePrAffiliateBusiness.ALL = field.NewAsterisk(tableName)
	_resourcePrAffiliateBusiness.ID = field.NewInt32(tableName, "id")
	_resourcePrAffiliateBusiness.CreateTime = field.NewField(tableName, "create_time")
	_resourcePrAffiliateBusiness.UpdateTime = field.NewField(tableName, "update_time")
	_resourcePrAffiliateBusiness.CooperationRequirements = field.NewString(tableName, "cooperation_requirements")
	_resourcePrAffiliateBusiness.Price = field.NewBytes(tableName, "price")
	_resourcePrAffiliateBusiness.ResourceID = field.NewInt32(tableName, "resource_id")
	_resourcePrAffiliateBusiness.MediaKit = field.NewBytes(tableName, "media_kit")
	_resourcePrAffiliateBusiness.Remarks = field.NewString(tableName, "remarks")

	_resourcePrAffiliateBusiness.fillFieldMap()

	return _resourcePrAffiliateBusiness
}

type resourcePrAffiliateBusiness struct {
	resourcePrAffiliateBusinessDo resourcePrAffiliateBusinessDo

	ALL                     field.Asterisk
	ID                      field.Int32  // PR联盟商务信息ID
	CreateTime              field.Field  // 创建时间
	UpdateTime              field.Field  // 更新时间
	CooperationRequirements field.String // 合作要求&报价细节
	Price                   field.Bytes  // 价格区间:{}
	ResourceID              field.Int32  // 资源ID
	MediaKit                field.Bytes  // 媒体工具包
	Remarks                 field.String // 备注

	fieldMap map[string]field.Expr
}

func (r resourcePrAffiliateBusiness) Table(newTableName string) *resourcePrAffiliateBusiness {
	r.resourcePrAffiliateBusinessDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r resourcePrAffiliateBusiness) As(alias string) *resourcePrAffiliateBusiness {
	r.resourcePrAffiliateBusinessDo.DO = *(r.resourcePrAffiliateBusinessDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *resourcePrAffiliateBusiness) updateTableName(table string) *resourcePrAffiliateBusiness {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt32(table, "id")
	r.CreateTime = field.NewField(table, "create_time")
	r.UpdateTime = field.NewField(table, "update_time")
	r.CooperationRequirements = field.NewString(table, "cooperation_requirements")
	r.Price = field.NewBytes(table, "price")
	r.ResourceID = field.NewInt32(table, "resource_id")
	r.MediaKit = field.NewBytes(table, "media_kit")
	r.Remarks = field.NewString(table, "remarks")

	r.fillFieldMap()

	return r
}

func (r *resourcePrAffiliateBusiness) WithContext(ctx context.Context) *resourcePrAffiliateBusinessDo {
	return r.resourcePrAffiliateBusinessDo.WithContext(ctx)
}

func (r resourcePrAffiliateBusiness) TableName() string {
	return r.resourcePrAffiliateBusinessDo.TableName()
}

func (r resourcePrAffiliateBusiness) Alias() string { return r.resourcePrAffiliateBusinessDo.Alias() }

func (r resourcePrAffiliateBusiness) Columns(cols ...field.Expr) gen.Columns {
	return r.resourcePrAffiliateBusinessDo.Columns(cols...)
}

func (r *resourcePrAffiliateBusiness) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *resourcePrAffiliateBusiness) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 8)
	r.fieldMap["id"] = r.ID
	r.fieldMap["create_time"] = r.CreateTime
	r.fieldMap["update_time"] = r.UpdateTime
	r.fieldMap["cooperation_requirements"] = r.CooperationRequirements
	r.fieldMap["price"] = r.Price
	r.fieldMap["resource_id"] = r.ResourceID
	r.fieldMap["media_kit"] = r.MediaKit
	r.fieldMap["remarks"] = r.Remarks
}

func (r resourcePrAffiliateBusiness) clone(db *gorm.DB) resourcePrAffiliateBusiness {
	r.resourcePrAffiliateBusinessDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r resourcePrAffiliateBusiness) replaceDB(db *gorm.DB) resourcePrAffiliateBusiness {
	r.resourcePrAffiliateBusinessDo.ReplaceDB(db)
	return r
}

type resourcePrAffiliateBusinessDo struct{ gen.DO }

func (r resourcePrAffiliateBusinessDo) Debug() *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Debug())
}

func (r resourcePrAffiliateBusinessDo) WithContext(ctx context.Context) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r resourcePrAffiliateBusinessDo) ReadDB() *resourcePrAffiliateBusinessDo {
	return r.Clauses(dbresolver.Read)
}

func (r resourcePrAffiliateBusinessDo) WriteDB() *resourcePrAffiliateBusinessDo {
	return r.Clauses(dbresolver.Write)
}

func (r resourcePrAffiliateBusinessDo) Session(config *gorm.Session) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Session(config))
}

func (r resourcePrAffiliateBusinessDo) Clauses(conds ...clause.Expression) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r resourcePrAffiliateBusinessDo) Returning(value interface{}, columns ...string) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r resourcePrAffiliateBusinessDo) Not(conds ...gen.Condition) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r resourcePrAffiliateBusinessDo) Or(conds ...gen.Condition) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r resourcePrAffiliateBusinessDo) Select(conds ...field.Expr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r resourcePrAffiliateBusinessDo) Where(conds ...gen.Condition) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r resourcePrAffiliateBusinessDo) Order(conds ...field.Expr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r resourcePrAffiliateBusinessDo) Distinct(cols ...field.Expr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r resourcePrAffiliateBusinessDo) Omit(cols ...field.Expr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r resourcePrAffiliateBusinessDo) Join(table schema.Tabler, on ...field.Expr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r resourcePrAffiliateBusinessDo) LeftJoin(table schema.Tabler, on ...field.Expr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r resourcePrAffiliateBusinessDo) RightJoin(table schema.Tabler, on ...field.Expr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r resourcePrAffiliateBusinessDo) Group(cols ...field.Expr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r resourcePrAffiliateBusinessDo) Having(conds ...gen.Condition) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r resourcePrAffiliateBusinessDo) Limit(limit int) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r resourcePrAffiliateBusinessDo) Offset(offset int) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r resourcePrAffiliateBusinessDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r resourcePrAffiliateBusinessDo) Unscoped() *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Unscoped())
}

func (r resourcePrAffiliateBusinessDo) Create(values ...*model.ResourcePrAffiliateBusiness) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r resourcePrAffiliateBusinessDo) CreateInBatches(values []*model.ResourcePrAffiliateBusiness, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r resourcePrAffiliateBusinessDo) Save(values ...*model.ResourcePrAffiliateBusiness) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r resourcePrAffiliateBusinessDo) First() (*model.ResourcePrAffiliateBusiness, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrAffiliateBusiness), nil
	}
}

func (r resourcePrAffiliateBusinessDo) Take() (*model.ResourcePrAffiliateBusiness, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrAffiliateBusiness), nil
	}
}

func (r resourcePrAffiliateBusinessDo) Last() (*model.ResourcePrAffiliateBusiness, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrAffiliateBusiness), nil
	}
}

func (r resourcePrAffiliateBusinessDo) Find() ([]*model.ResourcePrAffiliateBusiness, error) {
	result, err := r.DO.Find()
	return result.([]*model.ResourcePrAffiliateBusiness), err
}

func (r resourcePrAffiliateBusinessDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResourcePrAffiliateBusiness, err error) {
	buf := make([]*model.ResourcePrAffiliateBusiness, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r resourcePrAffiliateBusinessDo) FindInBatches(result *[]*model.ResourcePrAffiliateBusiness, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r resourcePrAffiliateBusinessDo) Attrs(attrs ...field.AssignExpr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r resourcePrAffiliateBusinessDo) Assign(attrs ...field.AssignExpr) *resourcePrAffiliateBusinessDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r resourcePrAffiliateBusinessDo) Joins(fields ...field.RelationField) *resourcePrAffiliateBusinessDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r resourcePrAffiliateBusinessDo) Preload(fields ...field.RelationField) *resourcePrAffiliateBusinessDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r resourcePrAffiliateBusinessDo) FirstOrInit() (*model.ResourcePrAffiliateBusiness, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrAffiliateBusiness), nil
	}
}

func (r resourcePrAffiliateBusinessDo) FirstOrCreate() (*model.ResourcePrAffiliateBusiness, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrAffiliateBusiness), nil
	}
}

func (r resourcePrAffiliateBusinessDo) FindByPage(offset int, limit int) (result []*model.ResourcePrAffiliateBusiness, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r resourcePrAffiliateBusinessDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r resourcePrAffiliateBusinessDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r resourcePrAffiliateBusinessDo) Delete(models ...*model.ResourcePrAffiliateBusiness) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *resourcePrAffiliateBusinessDo) withDO(do gen.Dao) *resourcePrAffiliateBusinessDo {
	r.DO = *do.(*gen.DO)
	return r
}
