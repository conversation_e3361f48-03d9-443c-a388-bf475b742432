// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/resource_pool/model"
)

func newResourcePrBase(db *gorm.DB, opts ...gen.DOOption) resourcePrBase {
	_resourcePrBase := resourcePrBase{}

	_resourcePrBase.resourcePrBaseDo.UseDB(db, opts...)
	_resourcePrBase.resourcePrBaseDo.UseModel(&model.ResourcePrBase{})

	tableName := _resourcePrBase.resourcePrBaseDo.TableName()
	_resourcePrBase.ALL = field.NewAsterisk(tableName)
	_resourcePrBase.ID = field.NewInt32(tableName, "id")
	_resourcePrBase.CreateTime = field.NewField(tableName, "create_time")
	_resourcePrBase.UpdateTime = field.NewField(tableName, "update_time")
	_resourcePrBase.Name = field.NewString(tableName, "name")
	_resourcePrBase.Type = field.NewBytes(tableName, "type")
	_resourcePrBase.CountryTop = field.NewBytes(tableName, "country_top")
	_resourcePrBase.Supplier = field.NewBytes(tableName, "supplier")
	_resourcePrBase.Homepage = field.NewString(tableName, "homepage")
	_resourcePrBase.Introduction = field.NewString(tableName, "introduction")
	_resourcePrBase.Size = field.NewString(tableName, "size")
	_resourcePrBase.MonthlyVisit = field.NewInt32(tableName, "monthly_visit")
	_resourcePrBase.IntroductionDate = field.NewTime(tableName, "introduction_date")
	_resourcePrBase.GenderRatio = field.NewBytes(tableName, "gender_ratio")
	_resourcePrBase.AgeTop = field.NewBytes(tableName, "age_top")
	_resourcePrBase.IsDeleted = field.NewInt8(tableName, "is_deleted")
	_resourcePrBase.RegionCountry = field.NewBytes(tableName, "region_country")
	_resourcePrBase.LanguageCategory = field.NewBytes(tableName, "language_category")
	_resourcePrBase.ProductType = field.NewString(tableName, "product_type")
	_resourcePrBase.ReleaseSpeed = field.NewBytes(tableName, "release_speed")
	_resourcePrBase.IncludeStatus = field.NewString(tableName, "include_status")
	_resourcePrBase.HasTranslate = field.NewInt8(tableName, "has_translate")
	_resourcePrBase.HasBottomInfo = field.NewInt8(tableName, "has_bottom_info")
	_resourcePrBase.ImageCost = field.NewString(tableName, "image_cost")
	_resourcePrBase.ProductDetail = field.NewString(tableName, "product_detail")
	_resourcePrBase.ServiceProcess = field.NewString(tableName, "service_process")
	_resourcePrBase.SpecificRequirement = field.NewString(tableName, "specific_requirement")
	_resourcePrBase.PromotionReport = field.NewBytes(tableName, "promotion_report")

	_resourcePrBase.fillFieldMap()

	return _resourcePrBase
}

type resourcePrBase struct {
	resourcePrBaseDo resourcePrBaseDo

	ALL                 field.Asterisk
	ID                  field.Int32  // PRID
	CreateTime          field.Field  // 创建时间
	UpdateTime          field.Field  // 更新时间
	Name                field.String // PR资源名称
	Type                field.Bytes  // PR资源类型
	CountryTop          field.Bytes  // 受众国家Top5
	Supplier            field.Bytes  // 供应商ID
	Homepage            field.String // 主页链接
	Introduction        field.String // 简介
	Size                field.String // 量级
	MonthlyVisit        field.Int32  // 月访量
	IntroductionDate    field.Time   // 引入日期
	GenderRatio         field.Bytes  // 性别比例:{}
	AgeTop              field.Bytes  // 受众年龄:[]
	IsDeleted           field.Int8   // 是否已删除：1则表示删除，0则未删除
	RegionCountry       field.Bytes
	LanguageCategory    field.Bytes  // 语言分类
	ProductType         field.String // 产品类型
	ReleaseSpeed        field.Bytes  // 发布速度
	IncludeStatus       field.String // 收录情况
	HasTranslate        field.Int8   // 是否包翻译
	HasBottomInfo       field.Int8   // 是否需要底部信息
	ImageCost           field.String // 图片费用
	ProductDetail       field.String // 产品详情
	ServiceProcess      field.String // 服务流程
	SpecificRequirement field.String // 具体要求
	PromotionReport     field.Bytes  // 宣发报告

	fieldMap map[string]field.Expr
}

func (r resourcePrBase) Table(newTableName string) *resourcePrBase {
	r.resourcePrBaseDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r resourcePrBase) As(alias string) *resourcePrBase {
	r.resourcePrBaseDo.DO = *(r.resourcePrBaseDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *resourcePrBase) updateTableName(table string) *resourcePrBase {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt32(table, "id")
	r.CreateTime = field.NewField(table, "create_time")
	r.UpdateTime = field.NewField(table, "update_time")
	r.Name = field.NewString(table, "name")
	r.Type = field.NewBytes(table, "type")
	r.CountryTop = field.NewBytes(table, "country_top")
	r.Supplier = field.NewBytes(table, "supplier")
	r.Homepage = field.NewString(table, "homepage")
	r.Introduction = field.NewString(table, "introduction")
	r.Size = field.NewString(table, "size")
	r.MonthlyVisit = field.NewInt32(table, "monthly_visit")
	r.IntroductionDate = field.NewTime(table, "introduction_date")
	r.GenderRatio = field.NewBytes(table, "gender_ratio")
	r.AgeTop = field.NewBytes(table, "age_top")
	r.IsDeleted = field.NewInt8(table, "is_deleted")
	r.RegionCountry = field.NewBytes(table, "region_country")
	r.LanguageCategory = field.NewBytes(table, "language_category")
	r.ProductType = field.NewString(table, "product_type")
	r.ReleaseSpeed = field.NewBytes(table, "release_speed")
	r.IncludeStatus = field.NewString(table, "include_status")
	r.HasTranslate = field.NewInt8(table, "has_translate")
	r.HasBottomInfo = field.NewInt8(table, "has_bottom_info")
	r.ImageCost = field.NewString(table, "image_cost")
	r.ProductDetail = field.NewString(table, "product_detail")
	r.ServiceProcess = field.NewString(table, "service_process")
	r.SpecificRequirement = field.NewString(table, "specific_requirement")
	r.PromotionReport = field.NewBytes(table, "promotion_report")

	r.fillFieldMap()

	return r
}

func (r *resourcePrBase) WithContext(ctx context.Context) *resourcePrBaseDo {
	return r.resourcePrBaseDo.WithContext(ctx)
}

func (r resourcePrBase) TableName() string { return r.resourcePrBaseDo.TableName() }

func (r resourcePrBase) Alias() string { return r.resourcePrBaseDo.Alias() }

func (r resourcePrBase) Columns(cols ...field.Expr) gen.Columns {
	return r.resourcePrBaseDo.Columns(cols...)
}

func (r *resourcePrBase) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *resourcePrBase) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 27)
	r.fieldMap["id"] = r.ID
	r.fieldMap["create_time"] = r.CreateTime
	r.fieldMap["update_time"] = r.UpdateTime
	r.fieldMap["name"] = r.Name
	r.fieldMap["type"] = r.Type
	r.fieldMap["country_top"] = r.CountryTop
	r.fieldMap["supplier"] = r.Supplier
	r.fieldMap["homepage"] = r.Homepage
	r.fieldMap["introduction"] = r.Introduction
	r.fieldMap["size"] = r.Size
	r.fieldMap["monthly_visit"] = r.MonthlyVisit
	r.fieldMap["introduction_date"] = r.IntroductionDate
	r.fieldMap["gender_ratio"] = r.GenderRatio
	r.fieldMap["age_top"] = r.AgeTop
	r.fieldMap["is_deleted"] = r.IsDeleted
	r.fieldMap["region_country"] = r.RegionCountry
	r.fieldMap["language_category"] = r.LanguageCategory
	r.fieldMap["product_type"] = r.ProductType
	r.fieldMap["release_speed"] = r.ReleaseSpeed
	r.fieldMap["include_status"] = r.IncludeStatus
	r.fieldMap["has_translate"] = r.HasTranslate
	r.fieldMap["has_bottom_info"] = r.HasBottomInfo
	r.fieldMap["image_cost"] = r.ImageCost
	r.fieldMap["product_detail"] = r.ProductDetail
	r.fieldMap["service_process"] = r.ServiceProcess
	r.fieldMap["specific_requirement"] = r.SpecificRequirement
	r.fieldMap["promotion_report"] = r.PromotionReport
}

func (r resourcePrBase) clone(db *gorm.DB) resourcePrBase {
	r.resourcePrBaseDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r resourcePrBase) replaceDB(db *gorm.DB) resourcePrBase {
	r.resourcePrBaseDo.ReplaceDB(db)
	return r
}

type resourcePrBaseDo struct{ gen.DO }

func (r resourcePrBaseDo) Debug() *resourcePrBaseDo {
	return r.withDO(r.DO.Debug())
}

func (r resourcePrBaseDo) WithContext(ctx context.Context) *resourcePrBaseDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r resourcePrBaseDo) ReadDB() *resourcePrBaseDo {
	return r.Clauses(dbresolver.Read)
}

func (r resourcePrBaseDo) WriteDB() *resourcePrBaseDo {
	return r.Clauses(dbresolver.Write)
}

func (r resourcePrBaseDo) Session(config *gorm.Session) *resourcePrBaseDo {
	return r.withDO(r.DO.Session(config))
}

func (r resourcePrBaseDo) Clauses(conds ...clause.Expression) *resourcePrBaseDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r resourcePrBaseDo) Returning(value interface{}, columns ...string) *resourcePrBaseDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r resourcePrBaseDo) Not(conds ...gen.Condition) *resourcePrBaseDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r resourcePrBaseDo) Or(conds ...gen.Condition) *resourcePrBaseDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r resourcePrBaseDo) Select(conds ...field.Expr) *resourcePrBaseDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r resourcePrBaseDo) Where(conds ...gen.Condition) *resourcePrBaseDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r resourcePrBaseDo) Order(conds ...field.Expr) *resourcePrBaseDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r resourcePrBaseDo) Distinct(cols ...field.Expr) *resourcePrBaseDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r resourcePrBaseDo) Omit(cols ...field.Expr) *resourcePrBaseDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r resourcePrBaseDo) Join(table schema.Tabler, on ...field.Expr) *resourcePrBaseDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r resourcePrBaseDo) LeftJoin(table schema.Tabler, on ...field.Expr) *resourcePrBaseDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r resourcePrBaseDo) RightJoin(table schema.Tabler, on ...field.Expr) *resourcePrBaseDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r resourcePrBaseDo) Group(cols ...field.Expr) *resourcePrBaseDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r resourcePrBaseDo) Having(conds ...gen.Condition) *resourcePrBaseDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r resourcePrBaseDo) Limit(limit int) *resourcePrBaseDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r resourcePrBaseDo) Offset(offset int) *resourcePrBaseDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r resourcePrBaseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *resourcePrBaseDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r resourcePrBaseDo) Unscoped() *resourcePrBaseDo {
	return r.withDO(r.DO.Unscoped())
}

func (r resourcePrBaseDo) Create(values ...*model.ResourcePrBase) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r resourcePrBaseDo) CreateInBatches(values []*model.ResourcePrBase, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r resourcePrBaseDo) Save(values ...*model.ResourcePrBase) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r resourcePrBaseDo) First() (*model.ResourcePrBase, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrBase), nil
	}
}

func (r resourcePrBaseDo) Take() (*model.ResourcePrBase, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrBase), nil
	}
}

func (r resourcePrBaseDo) Last() (*model.ResourcePrBase, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrBase), nil
	}
}

func (r resourcePrBaseDo) Find() ([]*model.ResourcePrBase, error) {
	result, err := r.DO.Find()
	return result.([]*model.ResourcePrBase), err
}

func (r resourcePrBaseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResourcePrBase, err error) {
	buf := make([]*model.ResourcePrBase, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r resourcePrBaseDo) FindInBatches(result *[]*model.ResourcePrBase, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r resourcePrBaseDo) Attrs(attrs ...field.AssignExpr) *resourcePrBaseDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r resourcePrBaseDo) Assign(attrs ...field.AssignExpr) *resourcePrBaseDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r resourcePrBaseDo) Joins(fields ...field.RelationField) *resourcePrBaseDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r resourcePrBaseDo) Preload(fields ...field.RelationField) *resourcePrBaseDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r resourcePrBaseDo) FirstOrInit() (*model.ResourcePrBase, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrBase), nil
	}
}

func (r resourcePrBaseDo) FirstOrCreate() (*model.ResourcePrBase, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResourcePrBase), nil
	}
}

func (r resourcePrBaseDo) FindByPage(offset int, limit int) (result []*model.ResourcePrBase, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r resourcePrBaseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r resourcePrBaseDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r resourcePrBaseDo) Delete(models ...*model.ResourcePrBase) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *resourcePrBaseDo) withDO(do gen.Dao) *resourcePrBaseDo {
	r.DO = *do.(*gen.DO)
	return r
}
