// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/dict_manage/model"
)

func newSysDictManage(db *gorm.DB, opts ...gen.DOOption) sysDictManage {
	_sysDictManage := sysDictManage{}

	_sysDictManage.sysDictManageDo.UseDB(db, opts...)
	_sysDictManage.sysDictManageDo.UseModel(&model.SysDictManage{})

	tableName := _sysDictManage.sysDictManageDo.TableName()
	_sysDictManage.ALL = field.NewAsterisk(tableName)
	_sysDictManage.ID = field.NewInt32(tableName, "id")
	_sysDictManage.CreateTime = field.NewField(tableName, "create_time")
	_sysDictManage.UpdateTime = field.NewField(tableName, "update_time")
	_sysDictManage.Label = field.NewString(tableName, "label")
	_sysDictManage.LabelKey = field.NewString(tableName, "label_key")
	_sysDictManage.LabelValue = field.NewInt32(tableName, "label_value")
	_sysDictManage.ParentKey = field.NewString(tableName, "parent_key")
	_sysDictManage.Remark = field.NewString(tableName, "remark")
	_sysDictManage.CreateUser = field.NewString(tableName, "create_user")
	_sysDictManage.UpdateUser = field.NewString(tableName, "update_user")

	_sysDictManage.fillFieldMap()

	return _sysDictManage
}

type sysDictManage struct {
	sysDictManageDo sysDictManageDo

	ALL        field.Asterisk
	ID         field.Int32  // 自增主键
	CreateTime field.Field  // 创建时间
	UpdateTime field.Field  // 更新时间
	Label      field.String // 标签
	LabelKey   field.String // 标签key
	LabelValue field.Int32  // 标签值
	ParentKey  field.String // 父节点key
	Remark     field.String // 备注
	CreateUser field.String // 创建人
	UpdateUser field.String // 更新人

	fieldMap map[string]field.Expr
}

func (s sysDictManage) Table(newTableName string) *sysDictManage {
	s.sysDictManageDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysDictManage) As(alias string) *sysDictManage {
	s.sysDictManageDo.DO = *(s.sysDictManageDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysDictManage) updateTableName(table string) *sysDictManage {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.CreateTime = field.NewField(table, "create_time")
	s.UpdateTime = field.NewField(table, "update_time")
	s.Label = field.NewString(table, "label")
	s.LabelKey = field.NewString(table, "label_key")
	s.LabelValue = field.NewInt32(table, "label_value")
	s.ParentKey = field.NewString(table, "parent_key")
	s.Remark = field.NewString(table, "remark")
	s.CreateUser = field.NewString(table, "create_user")
	s.UpdateUser = field.NewString(table, "update_user")

	s.fillFieldMap()

	return s
}

func (s *sysDictManage) WithContext(ctx context.Context) *sysDictManageDo {
	return s.sysDictManageDo.WithContext(ctx)
}

func (s sysDictManage) TableName() string { return s.sysDictManageDo.TableName() }

func (s sysDictManage) Alias() string { return s.sysDictManageDo.Alias() }

func (s sysDictManage) Columns(cols ...field.Expr) gen.Columns {
	return s.sysDictManageDo.Columns(cols...)
}

func (s *sysDictManage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysDictManage) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["label"] = s.Label
	s.fieldMap["label_key"] = s.LabelKey
	s.fieldMap["label_value"] = s.LabelValue
	s.fieldMap["parent_key"] = s.ParentKey
	s.fieldMap["remark"] = s.Remark
	s.fieldMap["create_user"] = s.CreateUser
	s.fieldMap["update_user"] = s.UpdateUser
}

func (s sysDictManage) clone(db *gorm.DB) sysDictManage {
	s.sysDictManageDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysDictManage) replaceDB(db *gorm.DB) sysDictManage {
	s.sysDictManageDo.ReplaceDB(db)
	return s
}

type sysDictManageDo struct{ gen.DO }

func (s sysDictManageDo) Debug() *sysDictManageDo {
	return s.withDO(s.DO.Debug())
}

func (s sysDictManageDo) WithContext(ctx context.Context) *sysDictManageDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysDictManageDo) ReadDB() *sysDictManageDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysDictManageDo) WriteDB() *sysDictManageDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysDictManageDo) Session(config *gorm.Session) *sysDictManageDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysDictManageDo) Clauses(conds ...clause.Expression) *sysDictManageDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysDictManageDo) Returning(value interface{}, columns ...string) *sysDictManageDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysDictManageDo) Not(conds ...gen.Condition) *sysDictManageDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysDictManageDo) Or(conds ...gen.Condition) *sysDictManageDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysDictManageDo) Select(conds ...field.Expr) *sysDictManageDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysDictManageDo) Where(conds ...gen.Condition) *sysDictManageDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysDictManageDo) Order(conds ...field.Expr) *sysDictManageDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysDictManageDo) Distinct(cols ...field.Expr) *sysDictManageDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysDictManageDo) Omit(cols ...field.Expr) *sysDictManageDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysDictManageDo) Join(table schema.Tabler, on ...field.Expr) *sysDictManageDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysDictManageDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysDictManageDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysDictManageDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysDictManageDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysDictManageDo) Group(cols ...field.Expr) *sysDictManageDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysDictManageDo) Having(conds ...gen.Condition) *sysDictManageDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysDictManageDo) Limit(limit int) *sysDictManageDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysDictManageDo) Offset(offset int) *sysDictManageDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysDictManageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysDictManageDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysDictManageDo) Unscoped() *sysDictManageDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysDictManageDo) Create(values ...*model.SysDictManage) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysDictManageDo) CreateInBatches(values []*model.SysDictManage, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysDictManageDo) Save(values ...*model.SysDictManage) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysDictManageDo) First() (*model.SysDictManage, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysDictManage), nil
	}
}

func (s sysDictManageDo) Take() (*model.SysDictManage, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysDictManage), nil
	}
}

func (s sysDictManageDo) Last() (*model.SysDictManage, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysDictManage), nil
	}
}

func (s sysDictManageDo) Find() ([]*model.SysDictManage, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysDictManage), err
}

func (s sysDictManageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysDictManage, err error) {
	buf := make([]*model.SysDictManage, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysDictManageDo) FindInBatches(result *[]*model.SysDictManage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysDictManageDo) Attrs(attrs ...field.AssignExpr) *sysDictManageDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysDictManageDo) Assign(attrs ...field.AssignExpr) *sysDictManageDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysDictManageDo) Joins(fields ...field.RelationField) *sysDictManageDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysDictManageDo) Preload(fields ...field.RelationField) *sysDictManageDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysDictManageDo) FirstOrInit() (*model.SysDictManage, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysDictManage), nil
	}
}

func (s sysDictManageDo) FirstOrCreate() (*model.SysDictManage, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysDictManage), nil
	}
}

func (s sysDictManageDo) FindByPage(offset int, limit int) (result []*model.SysDictManage, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysDictManageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysDictManageDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysDictManageDo) Delete(models ...*model.SysDictManage) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysDictManageDo) withDO(do gen.Dao) *sysDictManageDo {
	s.DO = *do.(*gen.DO)
	return s
}
