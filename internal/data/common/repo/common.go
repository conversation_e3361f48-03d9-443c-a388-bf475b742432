package repo

import (
	"context"
	"errors"
	"git.domob-inc.cn/bluevision/bv_commons/gtoken"
	"gorm.io/gorm"
	pb "vision_hub/api/common/v1"
	biz "vision_hub/internal/biz/common"
	"vision_hub/internal/conf"
	"vision_hub/internal/data/common"
	commonModel "vision_hub/internal/data/common/model"
	"vision_hub/internal/data/common/query"
	dictModel "vision_hub/internal/data/dict_manage/model"
	dictQuery "vision_hub/internal/data/dict_manage/query"
	"vision_hub/internal/data/resource_pool/model"
	authError "vision_hub/internal/errors/auth"
	commonError "vision_hub/internal/errors/common"
	projectError "vision_hub/internal/errors/project_management"
)

type CommonRepo struct {
	conf *conf.BizConf
	log  *common.DataLogHelper
	data *common.Data
}

func NewCommonRepo(conf *conf.BizConf, logger *common.DataLogHelper, data *common.Data) biz.ICommonRepo {
	return &CommonRepo{
		conf: conf,
		log:  logger,
		data: data,
	}
}

// GetSysUserInfo 获取用户信息数据模型
func (a *CommonRepo) GetSysUserInfo(ctx context.Context, userId int32) (*biz.UserInfoStruct, error) {
	userInfo := &biz.UserInfoStruct{}
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	queryCondition, err := userQuery.WithContext(ctx).
		Where(userQuery.ID.Eq(userId)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return userInfo, err
		}
		return nil, err
	}
	return &biz.UserInfoStruct{
		ID:       queryCondition.ID,
		UserName: queryCondition.UserName,
		Email:    queryCondition.Email,
		IsStaff:  queryCondition.IsStaff,
	}, nil
}

// BatchGetSysUserInfo 批量获取用户信息
func (a *CommonRepo) BatchGetSysUserInfo(ctx context.Context, userIdList []int32) (*[]biz.UserInfoStruct, error) {
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	queryCondition := userQuery.WithContext(ctx).
		Where(userQuery.ID.In(userIdList...))
	var ret []biz.UserInfoStruct
	err := queryCondition.Scan(&ret)
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (a *CommonRepo) GetCurrentUserInfo(ctx context.Context) (*biz.UserInfoGroupStruct, error) {
	uid, err := gtoken.GetUidFromToken(ctx)
	if err != nil {
		a.log.WithContext(ctx).Errorf("获取用户id失败: %v", err)
		return nil, authError.GetUidFromTokenFail
	}
	userInfo, err := a.GetSysUserInfo(ctx, int32(uid))
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户失败: %v", err)
		return nil, authError.QueryUserFail
	}
	groupQuery := query.Use(a.data.DbClient.DB(ctx)).SysRole
	userRoleQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserRole
	queryCondition := userRoleQuery.WithContext(ctx).
		LeftJoin(groupQuery, groupQuery.ID.EqCol(userRoleQuery.RoleID)).
		Where(userRoleQuery.UserID.Eq(userInfo.ID))
	var ret []biz.GroupInfo
	err = queryCondition.Select(groupQuery.ID, groupQuery.Code).Scan(&ret)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询用户组失败: %v", err)
		return nil, authError.QueryUserGroupFail
	}
	return &biz.UserInfoGroupStruct{
		ID:       userInfo.ID,
		UserName: userInfo.UserName,
		Email:    userInfo.Email,
		IsStaff:  userInfo.IsStaff,
		Group:    ret,
	}, nil
}

func (a *CommonRepo) GetResourcePoolOperationLogList(ctx context.Context, resourceId int32, resourceType string, pageNum, pageSize int32) ([]*model.ResourceOperationLog, *pb.Pagination, error) {
	logQuery := a.data.DbClient.DB(ctx).Model(&model.ResourceOperationLog{}).
		Where("resource_type = ?", resourceType).Where("opt_type = 'UPDATE'").Where("resource_id = ?", resourceId).Order("create_time desc")

	var count int64
	if err := logQuery.Count(&count).Error; err != nil {
		a.log.WithContext(ctx).Errorf("获取操作记录总数异常: %v", err)
		return nil, nil, commonError.OperationLogFail
	}

	if pageSize > 0 && pageNum > 0 {
		offset := (pageNum - 1) * pageSize
		logQuery = logQuery.Offset(int(offset)).Limit(int(pageSize))
	}

	var operationLogs []*model.ResourceOperationLog
	if err := logQuery.Find(&operationLogs).Error; err != nil {
		a.log.WithContext(ctx).Errorf("Failed to execute query: %v", err)
		return nil, nil, commonError.OperationLogFail
	}

	return operationLogs, &pb.Pagination{
		Total:    int32(count),
		PageSize: pageSize,
		PageNum:  pageNum,
	}, nil
}

func (a *CommonRepo) GetValueAliases(ctx context.Context, values []string) ([]*dictModel.SysDictManage, error) {
	var aliasesModel []*dictModel.SysDictManage
	dictManageQuery := dictQuery.Use(a.data.DbClient.DB(ctx)).SysDictManage
	err := dictManageQuery.WithContext(ctx).Where(dictManageQuery.LabelKey.In(values...)).Scan(&aliasesModel)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询基础字段信息失败: %v", err)
		return nil, projectError.ProjectGetFail
	}

	return aliasesModel, nil
}
func (a *CommonRepo) GetSysDictMapping(ctx context.Context, parentKey string) (map[string]map[string]string, error) {
	sysDictMapping, err := a.LoadSysDictMapping(ctx, false)
	if err != nil {
		return nil, err
	}
	if parentKey != "" {
		return map[string]map[string]string{parentKey: sysDictMapping[parentKey]}, nil
	}
	return sysDictMapping, nil
}
func (a *CommonRepo) GetReverseSysDictMapping(ctx context.Context, parentKey string) (map[string]map[string]string, error) {
	sysDictMapping, err := a.LoadSysDictMapping(ctx, true)
	if err != nil {
		return nil, err
	}
	if parentKey != "" {
		return map[string]map[string]string{parentKey: sysDictMapping[parentKey]}, nil
	}
	return sysDictMapping, nil
}

func (a *CommonRepo) LoadSysDictMapping(ctx context.Context, isReverse bool) (map[string]map[string]string, error) {
	// 查询字典信息并返回映射关系
	dictManageQuery := query.Use(a.data.DbClient.DB(ctx)).SysDictManage
	var dictData []commonModel.SysDictManage
	// 执行查询，获取字典标签、标签键和父键的字段
	err := dictManageQuery.WithContext(ctx).
		Select(dictManageQuery.Label, dictManageQuery.LabelKey, dictManageQuery.ParentKey).
		Scan(&dictData)
	if err != nil {
		// 如果查询失败，记录错误并返回失败信息
		a.log.WithContext(ctx).Errorf("查询字典信息失败: %v", err)
		return nil, projectError.ProjectGetFail
	}

	// 初始化返回的映射结构
	ret := make(map[string]map[string]string)
	// 存储所有标签键与标签的映射关系
	allMapping := make(map[string]string)

	// 遍历字典数据，构建父键与标签键的映射
	for _, v := range dictData {
		allMapping[v.LabelKey] = v.Label
		// 如果父键不存在，则初始化一个空的映射
		if _, ok := ret[v.ParentKey]; !ok {
			ret[v.ParentKey] = make(map[string]string)
		}
		// 将标签键与标签添加到父键的映射中
		if isReverse {
			ret[v.ParentKey][v.Label] = v.LabelKey
		} else {
			ret[v.ParentKey][v.LabelKey] = v.Label
		}
	}

	// 将所有标签键与标签的映射加入最终结果
	ret["all_mapping"] = allMapping

	// 返回字典映射
	return ret, nil

}
