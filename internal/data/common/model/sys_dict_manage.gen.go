// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameSysDictManage = "sys_dict_manage"

// SysDictManage mapped from table <sys_dict_manage>
type SysDictManage struct {
	ID         int32        `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:自增主键" json:"id"`                                                                           // 自增主键
	CreateTime sql.NullTime `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                               // 创建时间
	UpdateTime sql.NullTime `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                               // 更新时间
	Label      string       `gorm:"column:label;type:varchar(64);not null;comment:标签" json:"label"`                                                                                    // 标签
	LabelKey   string       `gorm:"column:label_key;type:varchar(64);not null;uniqueIndex:idx_label_key,priority:1;comment:标签key" json:"label_key"`                                    // 标签key
	LabelValue int32        `gorm:"column:label_value;type:int;not null;comment:标签值" json:"label_value"`                                                                               // 标签值
	ParentKey  string       `gorm:"column:parent_key;type:varchar(64);not null;uniqueIndex:idx_label_key,priority:2;index:idx_parent_key,priority:1;comment:父节点key" json:"parent_key"` // 父节点key
	Remark     string       `gorm:"column:remark;type:varchar(128);not null;comment:备注" json:"remark"`                                                                                 // 备注
	CreateUser string       `gorm:"column:create_user;type:varchar(64);not null;comment:创建人" json:"create_user"`                                                                       // 创建人
	UpdateUser string       `gorm:"column:update_user;type:varchar(64);not null;comment:更新人" json:"update_user"`                                                                       // 更新人
}

// TableName SysDictManage's table name
func (*SysDictManage) TableName() string {
	return TableNameSysDictManage
}
