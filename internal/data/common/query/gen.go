// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:            db,
		SysDictManage: newSysDictManage(db, opts...),
		SysRole:       newSysRole(db, opts...),
		SysUser:       newSysUser(db, opts...),
		SysUserRole:   newSysUserRole(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	SysDictManage sysDictManage
	SysRole       sysRole
	SysUser       sysUser
	SysUserRole   sysUserRole
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:            db,
		SysDictManage: q.SysDictManage.clone(db),
		SysRole:       q.SysRole.clone(db),
		SysUser:       q.SysUser.clone(db),
		SysUserRole:   q.SysUserRole.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:            db,
		SysDictManage: q.SysDictManage.replaceDB(db),
		SysRole:       q.SysRole.replaceDB(db),
		SysUser:       q.SysUser.replaceDB(db),
		SysUserRole:   q.SysUserRole.replaceDB(db),
	}
}

type queryCtx struct {
	SysDictManage *sysDictManageDo
	SysRole       *sysRoleDo
	SysUser       *sysUserDo
	SysUserRole   *sysUserRoleDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		SysDictManage: q.SysDictManage.WithContext(ctx),
		SysRole:       q.SysRole.WithContext(ctx),
		SysUser:       q.SysUser.WithContext(ctx),
		SysUserRole:   q.SysUserRole.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
