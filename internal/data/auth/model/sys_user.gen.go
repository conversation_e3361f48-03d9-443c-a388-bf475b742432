// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameSysUser = "sys_user"

// SysUser mapped from table <sys_user>
type SysUser struct {
	ID             int32        `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	CreateTime     sql.NullTime `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`          // 创建时间
	UpdateTime     sql.NullTime `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`          // 更新时间
	CreateUser     string       `gorm:"column:create_user;type:varchar(64);not null;comment:创建人" json:"create_user"`                                  // 创建人
	UpdateUser     string       `gorm:"column:update_user;type:varchar(64);not null;comment:更新人" json:"update_user"`                                  // 更新人
	Password       string       `gorm:"column:password;type:varchar(128);not null;comment:用户登录密码" json:"password"`                                    // 用户登录密码
	LastLogin      sql.NullTime `gorm:"column:last_login;type:datetime(6);comment:最新登录时间" json:"last_login"`                                          // 最新登录时间
	IsSuperuser    int8         `gorm:"column:is_superuser;type:tinyint(1);not null;comment:是否超级用户" json:"is_superuser"`                              // 是否超级用户
	FirstName      string       `gorm:"column:first_name;type:varchar(64);not null;comment:名" json:"first_name"`                                      // 名
	LastName       string       `gorm:"column:last_name;type:varchar(64);not null;comment:姓" json:"last_name"`                                        // 姓
	Email          string       `gorm:"column:email;type:varchar(128);not null;uniqueIndex:idx_email,priority:1;comment:邮箱" json:"email"`             // 邮箱
	IsStaff        int8         `gorm:"column:is_staff;type:tinyint(1);not null;comment:是否管理员" json:"is_staff"`                                       // 是否管理员
	IsActive       int8         `gorm:"column:is_active;type:tinyint(1);not null;comment:是否活跃" json:"is_active"`                                      // 是否活跃
	UserName       string       `gorm:"column:user_name;type:varchar(128);not null;comment:用户名" json:"user_name"`                                     // 用户名
	OrgID          int32        `gorm:"column:org_id;type:int;not null;comment:用户主组织ID(逻辑FK至org.id)" json:"org_id"`                                   // 用户主组织ID(逻辑FK至org.id)
	Phone          string       `gorm:"column:phone;type:varchar(128);not null;comment:手机号" json:"phone"`                                             // 手机号
	Avatar         string       `gorm:"column:avatar;type:varchar(256);not null;comment:用户头像72*72" json:"avatar"`                                     // 用户头像72*72
	UnionID        string       `gorm:"column:union_id;type:varchar(64);not null;comment:用户对ISV的唯一标识，对于同一个ISV，用户在其名下所有应用的union_id相同" json:"union_id"` // 用户对ISV的唯一标识，对于同一个ISV，用户在其名下所有应用的union_id相同
	OpenID         string       `gorm:"column:open_id;type:varchar(64);not null;comment:用户在应用内的唯一标识" json:"open_id"`                                  // 用户在应用内的唯一标识
	UserID         string       `gorm:"column:user_id;type:varchar(64);not null;comment:用户 user_id" json:"user_id"`                                   // 用户 user_id
	JobTitle       string       `gorm:"column:job_title;type:varchar(256);not null;comment:职称" json:"job_title"`                                      // 职称
	Department     string       `gorm:"column:department;type:varchar(128);not null;comment:所属部门" json:"department"`                                  // 所属部门
	DepartmentPath string       `gorm:"column:department_path;type:varchar(512);not null;default:‘’;comment:所属部门全路径" json:"department_path"`          // 所属部门全路径
}

// TableName SysUser's table name
func (*SysUser) TableName() string {
	return TableNameSysUser
}
