// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameSysRole = "sys_role"

// SysRole mapped from table <sys_role>
type SysRole struct {
	ID          int32        `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:自增主键" json:"id"`                                          // 自增主键
	CreateTime  sql.NullTime `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`              // 创建时间
	UpdateTime  sql.NullTime `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`              // 更新时间
	CreateUser  string       `gorm:"column:create_user;type:varchar(64);not null;comment:创建人" json:"create_user"`                                      // 创建人
	UpdateUser  string       `gorm:"column:update_user;type:varchar(64);not null;comment:更新人" json:"update_user"`                                      // 更新人
	Name        string       `gorm:"column:name;type:varchar(64);not null;uniqueIndex:index_name,priority:1;comment:角色名称(如: 管理员, 客服, 销售)" json:"name"` // 角色名称(如: 管理员, 客服, 销售)
	Code        string       `gorm:"column:code;type:varchar(64);not null;comment:角色编码(如: admin, customer_service)" json:"code"`                       // 角色编码(如: admin, customer_service)
	Description string       `gorm:"column:description;type:varchar(256);not null;comment:角色描述" json:"description"`                                    // 角色描述
}

// TableName SysRole's table name
func (*SysRole) TableName() string {
	return TableNameSysRole
}
