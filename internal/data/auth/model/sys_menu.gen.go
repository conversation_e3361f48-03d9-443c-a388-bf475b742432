// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameSysMenu = "sys_menu"

// SysMenu mapped from table <sys_menu>
type SysMenu struct {
	ID           int32        `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:自增主键" json:"id"`                                           // 自增主键
	CreateTime   sql.NullTime `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`               // 创建时间
	UpdateTime   sql.NullTime `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`               // 更新时间
	CreateUser   string       `gorm:"column:create_user;type:varchar(64);not null;comment:创建人" json:"create_user"`                                       // 创建人
	UpdateUser   string       `gorm:"column:update_user;type:varchar(64);not null;comment:更新人" json:"update_user"`                                       // 更新人
	MenuName     string       `gorm:"column:menu_name;type:varchar(255);not null;comment:菜单名称" json:"menu_name"`                                         // 菜单名称
	ParentID     int32        `gorm:"column:parent_id;type:int;not null;index:idx_parent_id,priority:1;comment:上级菜单ID(parent menu.id)" json:"parent_id"` // 上级菜单ID(parent menu.id)
	APIName      string       `gorm:"column:api_name;type:varchar(255);not null;comment:菜单路由标识" json:"api_name"`                                         // 菜单路由标识
	FrontAPIName string       `gorm:"column:front_api_name;type:varchar(255);not null;comment:前端路由标识" json:"front_api_name"`                             // 前端路由标识
}

// TableName SysMenu's table name
func (*SysMenu) TableName() string {
	return TableNameSysMenu
}
