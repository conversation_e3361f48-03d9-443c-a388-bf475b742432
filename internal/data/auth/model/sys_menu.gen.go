// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameSysMenu = "sys_menu"

// SysMenu mapped from table <sys_menu>
type SysMenu struct {
	ID         int32        `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:自增主键" json:"id"`                                                  // 自增主键
	CreateTime sql.NullTime `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                      // 创建时间
	UpdateTime sql.NullTime `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                      // 更新时间
	CreateUser string       `gorm:"column:create_user;type:varchar(64);not null;comment:创建人" json:"create_user"`                                              // 创建人
	UpdateUser string       `gorm:"column:update_user;type:varchar(64);not null;comment:更新人" json:"update_user"`                                              // 更新人
	MenuName   string       `gorm:"column:menu_name;type:varchar(255);not null;comment:菜单名称" json:"menu_name"`                                                // 菜单名称
	MenuType   int8         `gorm:"column:menu_type;type:tinyint(1);not null;comment:0：目录，1：菜单，2：按钮，3：链接" json:"menu_type"`                                   // 0：目录，1：菜单，2：按钮，3：链接
	ParentID   int32        `gorm:"column:parent_id;type:int;not null;index:idx_parent_id,priority:1;comment:上级菜单ID(parent menu.id)" json:"parent_id"`        // 上级菜单ID(parent menu.id)
	Target     string       `gorm:"column:target;type:varchar(32);default:_self;comment:_blank：新窗口打开，_self：本页打开" json:"target"`                               // _blank：新窗口打开，_self：本页打开
	URL        string       `gorm:"column:url;type:varchar(256);comment:当 type=3 时用于指定跳转地址" json:"url"`                                                       // 当 type=3 时用于指定跳转地址
	MenuKey    string       `gorm:"column:menu_key;type:varchar(255);not null;comment:菜单路由标识" json:"menu_key"`                                                // 菜单路由标识
	Path       string       `gorm:"column:path;type:varchar(255);not null;comment:前端路由标识" json:"path"`                                                        // 前端路由标识
	Icon       string       `gorm:"column:icon;type:varchar(64);not null;comment:图标" json:"icon"`                                                             // 图标
	Sort       int32        `gorm:"column:sort;type:int;not null;comment:排序" json:"sort"`                                                                     // 排序
	IsDeleted  int8         `gorm:"column:is_deleted;type:tinyint(1);not null;index:idx_is_deleted,priority:1;default:1;comment:0-正常，1-删除" json:"is_deleted"` // 0-正常，1-删除
}

// TableName SysMenu's table name
func (*SysMenu) TableName() string {
	return TableNameSysMenu
}
