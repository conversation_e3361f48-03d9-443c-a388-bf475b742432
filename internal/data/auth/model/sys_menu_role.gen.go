// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameSysMenuRole = "sys_menu_role"

// SysMenuRole mapped from table <sys_menu_role>
type SysMenuRole struct {
	ID         int32        `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:自增主键" json:"id"`                                                              // 自增主键
	CreateTime sql.NullTime `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                  // 创建时间
	UpdateTime sql.NullTime `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                  // 更新时间
	CreateUser string       `gorm:"column:create_user;type:varchar(64);not null;comment:创建人" json:"create_user"`                                                          // 创建人
	UpdateUser string       `gorm:"column:update_user;type:varchar(64);not null;comment:更新人" json:"update_user"`                                                          // 更新人
	RoleID     int32        `gorm:"column:role_id;type:int;not null;uniqueIndex:idx_role_id_menu_id,priority:1;comment:角色id" json:"role_id"`                              // 角色id
	MenuID     int32        `gorm:"column:menu_id;type:int;not null;uniqueIndex:idx_role_id_menu_id,priority:2;index:idx_menu_id,priority:1;comment:菜单id" json:"menu_id"` // 菜单id
}

// TableName SysMenuRole's table name
func (*SysMenuRole) TableName() string {
	return TableNameSysMenuRole
}
