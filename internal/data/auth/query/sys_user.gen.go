// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/auth/model"
)

func newSysUser(db *gorm.DB, opts ...gen.DOOption) sysUser {
	_sysUser := sysUser{}

	_sysUser.sysUserDo.UseDB(db, opts...)
	_sysUser.sysUserDo.UseModel(&model.SysUser{})

	tableName := _sysUser.sysUserDo.TableName()
	_sysUser.ALL = field.NewAsterisk(tableName)
	_sysUser.ID = field.NewInt32(tableName, "id")
	_sysUser.CreateTime = field.NewField(tableName, "create_time")
	_sysUser.UpdateTime = field.NewField(tableName, "update_time")
	_sysUser.CreateUser = field.NewString(tableName, "create_user")
	_sysUser.UpdateUser = field.NewString(tableName, "update_user")
	_sysUser.Password = field.NewString(tableName, "password")
	_sysUser.LastLogin = field.NewField(tableName, "last_login")
	_sysUser.IsSuperuser = field.NewInt8(tableName, "is_superuser")
	_sysUser.FirstName = field.NewString(tableName, "first_name")
	_sysUser.LastName = field.NewString(tableName, "last_name")
	_sysUser.Email = field.NewString(tableName, "email")
	_sysUser.IsStaff = field.NewInt8(tableName, "is_staff")
	_sysUser.IsActive = field.NewInt8(tableName, "is_active")
	_sysUser.UserName = field.NewString(tableName, "user_name")
	_sysUser.OrgID = field.NewInt32(tableName, "org_id")
	_sysUser.Phone = field.NewString(tableName, "phone")
	_sysUser.Avatar = field.NewString(tableName, "avatar")
	_sysUser.UnionID = field.NewString(tableName, "union_id")
	_sysUser.OpenID = field.NewString(tableName, "open_id")
	_sysUser.UserID = field.NewString(tableName, "user_id")
	_sysUser.JobTitle = field.NewString(tableName, "job_tile")
	_sysUser.Department = field.NewString(tableName, "department")
	_sysUser.DepartmentPath = field.NewString(tableName, "department_path")

	_sysUser.fillFieldMap()

	return _sysUser
}

type sysUser struct {
	sysUserDo sysUserDo

	ALL            field.Asterisk
	ID             field.Int32
	CreateTime     field.Field  // 创建时间
	UpdateTime     field.Field  // 更新时间
	CreateUser     field.String // 创建人
	UpdateUser     field.String // 更新人
	Password       field.String // 用户登录密码
	LastLogin      field.Field  // 最新登录时间
	IsSuperuser    field.Int8   // 是否超级用户
	FirstName      field.String // 名
	LastName       field.String // 姓
	Email          field.String // 邮箱
	IsStaff        field.Int8   // 是否管理员
	IsActive       field.Int8   // 是否活跃
	UserName       field.String // 用户名
	OrgID          field.Int32  // 用户主组织ID(逻辑FK至org.id)
	Phone          field.String // 手机号
	Avatar         field.String // 用户头像72*72
	UnionID        field.String // 用户对ISV的唯一标识，对于同一个ISV，用户在其名下所有应用的union_id相同
	OpenID         field.String // 用户在应用内的唯一标识
	UserID         field.String // 用户 user_id
	JobTitle       field.String // 职称
	Department     field.String // 所属部门
	DepartmentPath field.String // 所属部门全路径

	fieldMap map[string]field.Expr
}

func (s sysUser) Table(newTableName string) *sysUser {
	s.sysUserDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysUser) As(alias string) *sysUser {
	s.sysUserDo.DO = *(s.sysUserDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysUser) updateTableName(table string) *sysUser {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.CreateTime = field.NewField(table, "create_time")
	s.UpdateTime = field.NewField(table, "update_time")
	s.CreateUser = field.NewString(table, "create_user")
	s.UpdateUser = field.NewString(table, "update_user")
	s.Password = field.NewString(table, "password")
	s.LastLogin = field.NewField(table, "last_login")
	s.IsSuperuser = field.NewInt8(table, "is_superuser")
	s.FirstName = field.NewString(table, "first_name")
	s.LastName = field.NewString(table, "last_name")
	s.Email = field.NewString(table, "email")
	s.IsStaff = field.NewInt8(table, "is_staff")
	s.IsActive = field.NewInt8(table, "is_active")
	s.UserName = field.NewString(table, "user_name")
	s.OrgID = field.NewInt32(table, "org_id")
	s.Phone = field.NewString(table, "phone")
	s.Avatar = field.NewString(table, "avatar")
	s.UnionID = field.NewString(table, "union_id")
	s.OpenID = field.NewString(table, "open_id")
	s.UserID = field.NewString(table, "user_id")
	s.JobTitle = field.NewString(table, "job_tile")
	s.Department = field.NewString(table, "department")
	s.DepartmentPath = field.NewString(table, "department_path")

	s.fillFieldMap()

	return s
}

func (s *sysUser) WithContext(ctx context.Context) *sysUserDo { return s.sysUserDo.WithContext(ctx) }

func (s sysUser) TableName() string { return s.sysUserDo.TableName() }

func (s sysUser) Alias() string { return s.sysUserDo.Alias() }

func (s sysUser) Columns(cols ...field.Expr) gen.Columns { return s.sysUserDo.Columns(cols...) }

func (s *sysUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysUser) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 23)
	s.fieldMap["id"] = s.ID
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["create_user"] = s.CreateUser
	s.fieldMap["update_user"] = s.UpdateUser
	s.fieldMap["password"] = s.Password
	s.fieldMap["last_login"] = s.LastLogin
	s.fieldMap["is_superuser"] = s.IsSuperuser
	s.fieldMap["first_name"] = s.FirstName
	s.fieldMap["last_name"] = s.LastName
	s.fieldMap["email"] = s.Email
	s.fieldMap["is_staff"] = s.IsStaff
	s.fieldMap["is_active"] = s.IsActive
	s.fieldMap["user_name"] = s.UserName
	s.fieldMap["org_id"] = s.OrgID
	s.fieldMap["phone"] = s.Phone
	s.fieldMap["avatar"] = s.Avatar
	s.fieldMap["union_id"] = s.UnionID
	s.fieldMap["open_id"] = s.OpenID
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["job_title"] = s.JobTitle
	s.fieldMap["department"] = s.Department
	s.fieldMap["department_path"] = s.DepartmentPath
}

func (s sysUser) clone(db *gorm.DB) sysUser {
	s.sysUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysUser) replaceDB(db *gorm.DB) sysUser {
	s.sysUserDo.ReplaceDB(db)
	return s
}

type sysUserDo struct{ gen.DO }

func (s sysUserDo) Debug() *sysUserDo {
	return s.withDO(s.DO.Debug())
}

func (s sysUserDo) WithContext(ctx context.Context) *sysUserDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysUserDo) ReadDB() *sysUserDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysUserDo) WriteDB() *sysUserDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysUserDo) Session(config *gorm.Session) *sysUserDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysUserDo) Clauses(conds ...clause.Expression) *sysUserDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysUserDo) Returning(value interface{}, columns ...string) *sysUserDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysUserDo) Not(conds ...gen.Condition) *sysUserDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysUserDo) Or(conds ...gen.Condition) *sysUserDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysUserDo) Select(conds ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysUserDo) Where(conds ...gen.Condition) *sysUserDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysUserDo) Order(conds ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysUserDo) Distinct(cols ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysUserDo) Omit(cols ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysUserDo) Join(table schema.Tabler, on ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysUserDo) Group(cols ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysUserDo) Having(conds ...gen.Condition) *sysUserDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysUserDo) Limit(limit int) *sysUserDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysUserDo) Offset(offset int) *sysUserDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysUserDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysUserDo) Unscoped() *sysUserDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysUserDo) Create(values ...*model.SysUser) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysUserDo) CreateInBatches(values []*model.SysUser, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysUserDo) Save(values ...*model.SysUser) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysUserDo) First() (*model.SysUser, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUser), nil
	}
}

func (s sysUserDo) Take() (*model.SysUser, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUser), nil
	}
}

func (s sysUserDo) Last() (*model.SysUser, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUser), nil
	}
}

func (s sysUserDo) Find() ([]*model.SysUser, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysUser), err
}

func (s sysUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysUser, err error) {
	buf := make([]*model.SysUser, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysUserDo) FindInBatches(result *[]*model.SysUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysUserDo) Attrs(attrs ...field.AssignExpr) *sysUserDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysUserDo) Assign(attrs ...field.AssignExpr) *sysUserDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysUserDo) Joins(fields ...field.RelationField) *sysUserDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysUserDo) Preload(fields ...field.RelationField) *sysUserDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysUserDo) FirstOrInit() (*model.SysUser, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUser), nil
	}
}

func (s sysUserDo) FirstOrCreate() (*model.SysUser, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUser), nil
	}
}

func (s sysUserDo) FindByPage(offset int, limit int) (result []*model.SysUser, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysUserDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysUserDo) Delete(models ...*model.SysUser) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysUserDo) withDO(do gen.Dao) *sysUserDo {
	s.DO = *do.(*gen.DO)
	return s
}
