// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/auth/model"
)

func newSysMenuRole(db *gorm.DB, opts ...gen.DOOption) sysMenuRole {
	_sysMenuRole := sysMenuRole{}

	_sysMenuRole.sysMenuRoleDo.UseDB(db, opts...)
	_sysMenuRole.sysMenuRoleDo.UseModel(&model.SysMenuRole{})

	tableName := _sysMenuRole.sysMenuRoleDo.TableName()
	_sysMenuRole.ALL = field.NewAsterisk(tableName)
	_sysMenuRole.ID = field.NewInt32(tableName, "id")
	_sysMenuRole.CreateTime = field.NewField(tableName, "create_time")
	_sysMenuRole.UpdateTime = field.NewField(tableName, "update_time")
	_sysMenuRole.CreateUser = field.NewString(tableName, "create_user")
	_sysMenuRole.UpdateUser = field.NewString(tableName, "update_user")
	_sysMenuRole.RoleID = field.NewInt32(tableName, "role_id")
	_sysMenuRole.MenuID = field.NewInt32(tableName, "menu_id")

	_sysMenuRole.fillFieldMap()

	return _sysMenuRole
}

type sysMenuRole struct {
	sysMenuRoleDo sysMenuRoleDo

	ALL        field.Asterisk
	ID         field.Int32  // 自增主键
	CreateTime field.Field  // 创建时间
	UpdateTime field.Field  // 更新时间
	CreateUser field.String // 创建人
	UpdateUser field.String // 更新人
	RoleID     field.Int32  // 角色id
	MenuID     field.Int32  // 菜单id

	fieldMap map[string]field.Expr
}

func (s sysMenuRole) Table(newTableName string) *sysMenuRole {
	s.sysMenuRoleDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysMenuRole) As(alias string) *sysMenuRole {
	s.sysMenuRoleDo.DO = *(s.sysMenuRoleDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysMenuRole) updateTableName(table string) *sysMenuRole {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.CreateTime = field.NewField(table, "create_time")
	s.UpdateTime = field.NewField(table, "update_time")
	s.CreateUser = field.NewString(table, "create_user")
	s.UpdateUser = field.NewString(table, "update_user")
	s.RoleID = field.NewInt32(table, "role_id")
	s.MenuID = field.NewInt32(table, "menu_id")

	s.fillFieldMap()

	return s
}

func (s *sysMenuRole) WithContext(ctx context.Context) *sysMenuRoleDo {
	return s.sysMenuRoleDo.WithContext(ctx)
}

func (s sysMenuRole) TableName() string { return s.sysMenuRoleDo.TableName() }

func (s sysMenuRole) Alias() string { return s.sysMenuRoleDo.Alias() }

func (s sysMenuRole) Columns(cols ...field.Expr) gen.Columns { return s.sysMenuRoleDo.Columns(cols...) }

func (s *sysMenuRole) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysMenuRole) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 7)
	s.fieldMap["id"] = s.ID
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["create_user"] = s.CreateUser
	s.fieldMap["update_user"] = s.UpdateUser
	s.fieldMap["role_id"] = s.RoleID
	s.fieldMap["menu_id"] = s.MenuID
}

func (s sysMenuRole) clone(db *gorm.DB) sysMenuRole {
	s.sysMenuRoleDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysMenuRole) replaceDB(db *gorm.DB) sysMenuRole {
	s.sysMenuRoleDo.ReplaceDB(db)
	return s
}

type sysMenuRoleDo struct{ gen.DO }

func (s sysMenuRoleDo) Debug() *sysMenuRoleDo {
	return s.withDO(s.DO.Debug())
}

func (s sysMenuRoleDo) WithContext(ctx context.Context) *sysMenuRoleDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysMenuRoleDo) ReadDB() *sysMenuRoleDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysMenuRoleDo) WriteDB() *sysMenuRoleDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysMenuRoleDo) Session(config *gorm.Session) *sysMenuRoleDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysMenuRoleDo) Clauses(conds ...clause.Expression) *sysMenuRoleDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysMenuRoleDo) Returning(value interface{}, columns ...string) *sysMenuRoleDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysMenuRoleDo) Not(conds ...gen.Condition) *sysMenuRoleDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysMenuRoleDo) Or(conds ...gen.Condition) *sysMenuRoleDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysMenuRoleDo) Select(conds ...field.Expr) *sysMenuRoleDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysMenuRoleDo) Where(conds ...gen.Condition) *sysMenuRoleDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysMenuRoleDo) Order(conds ...field.Expr) *sysMenuRoleDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysMenuRoleDo) Distinct(cols ...field.Expr) *sysMenuRoleDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysMenuRoleDo) Omit(cols ...field.Expr) *sysMenuRoleDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysMenuRoleDo) Join(table schema.Tabler, on ...field.Expr) *sysMenuRoleDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysMenuRoleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysMenuRoleDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysMenuRoleDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysMenuRoleDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysMenuRoleDo) Group(cols ...field.Expr) *sysMenuRoleDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysMenuRoleDo) Having(conds ...gen.Condition) *sysMenuRoleDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysMenuRoleDo) Limit(limit int) *sysMenuRoleDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysMenuRoleDo) Offset(offset int) *sysMenuRoleDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysMenuRoleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysMenuRoleDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysMenuRoleDo) Unscoped() *sysMenuRoleDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysMenuRoleDo) Create(values ...*model.SysMenuRole) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysMenuRoleDo) CreateInBatches(values []*model.SysMenuRole, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysMenuRoleDo) Save(values ...*model.SysMenuRole) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysMenuRoleDo) First() (*model.SysMenuRole, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysMenuRole), nil
	}
}

func (s sysMenuRoleDo) Take() (*model.SysMenuRole, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysMenuRole), nil
	}
}

func (s sysMenuRoleDo) Last() (*model.SysMenuRole, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysMenuRole), nil
	}
}

func (s sysMenuRoleDo) Find() ([]*model.SysMenuRole, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysMenuRole), err
}

func (s sysMenuRoleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysMenuRole, err error) {
	buf := make([]*model.SysMenuRole, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysMenuRoleDo) FindInBatches(result *[]*model.SysMenuRole, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysMenuRoleDo) Attrs(attrs ...field.AssignExpr) *sysMenuRoleDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysMenuRoleDo) Assign(attrs ...field.AssignExpr) *sysMenuRoleDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysMenuRoleDo) Joins(fields ...field.RelationField) *sysMenuRoleDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysMenuRoleDo) Preload(fields ...field.RelationField) *sysMenuRoleDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysMenuRoleDo) FirstOrInit() (*model.SysMenuRole, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysMenuRole), nil
	}
}

func (s sysMenuRoleDo) FirstOrCreate() (*model.SysMenuRole, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysMenuRole), nil
	}
}

func (s sysMenuRoleDo) FindByPage(offset int, limit int) (result []*model.SysMenuRole, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysMenuRoleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysMenuRoleDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysMenuRoleDo) Delete(models ...*model.SysMenuRole) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysMenuRoleDo) withDO(do gen.Dao) *sysMenuRoleDo {
	s.DO = *do.(*gen.DO)
	return s
}
