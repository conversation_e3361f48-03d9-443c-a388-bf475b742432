// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:            db,
		SysGroup:      newSysGroup(db, opts...),
		SysMenu:       newSysMenu(db, opts...),
		SysMenuGroup:  newSysMenuGroup(db, opts...),
		SysOrg:        newSysOrg(db, opts...),
		SysPermission: newSysPermission(db, opts...),
		SysUser:       newSysUser(db, opts...),
		SysUserGroup:  newSysUserGroup(db, opts...),
		SysUserOrg:    newSysUserOrg(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	SysGroup      sysGroup
	SysMenu       sysMenu
	SysMenuGroup  sysMenuGroup
	SysOrg        sysOrg
	SysPermission sysPermission
	SysUser       sysUser
	SysUserGroup  sysUserGroup
	SysUserOrg    sysUserOrg
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:            db,
		SysGroup:      q.SysGroup.clone(db),
		SysMenu:       q.SysMenu.clone(db),
		SysMenuGroup:  q.SysMenuGroup.clone(db),
		SysOrg:        q.SysOrg.clone(db),
		SysPermission: q.SysPermission.clone(db),
		SysUser:       q.SysUser.clone(db),
		SysUserGroup:  q.SysUserGroup.clone(db),
		SysUserOrg:    q.SysUserOrg.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:            db,
		SysGroup:      q.SysGroup.replaceDB(db),
		SysMenu:       q.SysMenu.replaceDB(db),
		SysMenuGroup:  q.SysMenuGroup.replaceDB(db),
		SysOrg:        q.SysOrg.replaceDB(db),
		SysPermission: q.SysPermission.replaceDB(db),
		SysUser:       q.SysUser.replaceDB(db),
		SysUserGroup:  q.SysUserGroup.replaceDB(db),
		SysUserOrg:    q.SysUserOrg.replaceDB(db),
	}
}

type queryCtx struct {
	SysGroup      *sysGroupDo
	SysMenu       *sysMenuDo
	SysMenuGroup  *sysMenuGroupDo
	SysOrg        *sysOrgDo
	SysPermission *sysPermissionDo
	SysUser       *sysUserDo
	SysUserGroup  *sysUserGroupDo
	SysUserOrg    *sysUserOrgDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		SysGroup:      q.SysGroup.WithContext(ctx),
		SysMenu:       q.SysMenu.WithContext(ctx),
		SysMenuGroup:  q.SysMenuGroup.WithContext(ctx),
		SysOrg:        q.SysOrg.WithContext(ctx),
		SysPermission: q.SysPermission.WithContext(ctx),
		SysUser:       q.SysUser.WithContext(ctx),
		SysUserGroup:  q.SysUserGroup.WithContext(ctx),
		SysUserOrg:    q.SysUserOrg.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
