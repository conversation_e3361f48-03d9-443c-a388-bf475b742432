// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:            db,
		SysMenu:       newSysMenu(db, opts...),
		SysMenuGroup:  newSysMenuGroup(db, opts...),
		SysMenuRole:   newSysMenuRole(db, opts...),
		SysOrg:        newSysOrg(db, opts...),
		SysPermission: newSysPermission(db, opts...),
		SysRole:       newSysRole(db, opts...),
		SysUser:       newSysUser(db, opts...),
		SysUserOrg:    newSysUserOrg(db, opts...),
		SysUserRole:   newSysUserRole(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	SysMenu       sysMenu
	SysMenuGroup  sysMenuGroup
	SysMenuRole   sysMenuRole
	SysOrg        sysOrg
	SysPermission sysPermission
	SysRole       sysRole
	SysUser       sysUser
	SysUserOrg    sysUserOrg
	SysUserRole   sysUserRole
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:            db,
		SysMenu:       q.SysMenu.clone(db),
		SysMenuGroup:  q.SysMenuGroup.clone(db),
		SysMenuRole:   q.SysMenuRole.clone(db),
		SysOrg:        q.SysOrg.clone(db),
		SysPermission: q.SysPermission.clone(db),
		SysRole:       q.SysRole.clone(db),
		SysUser:       q.SysUser.clone(db),
		SysUserOrg:    q.SysUserOrg.clone(db),
		SysUserRole:   q.SysUserRole.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:            db,
		SysMenu:       q.SysMenu.replaceDB(db),
		SysMenuGroup:  q.SysMenuGroup.replaceDB(db),
		SysMenuRole:   q.SysMenuRole.replaceDB(db),
		SysOrg:        q.SysOrg.replaceDB(db),
		SysPermission: q.SysPermission.replaceDB(db),
		SysRole:       q.SysRole.replaceDB(db),
		SysUser:       q.SysUser.replaceDB(db),
		SysUserOrg:    q.SysUserOrg.replaceDB(db),
		SysUserRole:   q.SysUserRole.replaceDB(db),
	}
}

type queryCtx struct {
	SysMenu       *sysMenuDo
	SysMenuGroup  *sysMenuGroupDo
	SysMenuRole   *sysMenuRoleDo
	SysOrg        *sysOrgDo
	SysPermission *sysPermissionDo
	SysRole       *sysRoleDo
	SysUser       *sysUserDo
	SysUserOrg    *sysUserOrgDo
	SysUserRole   *sysUserRoleDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		SysMenu:       q.SysMenu.WithContext(ctx),
		SysMenuGroup:  q.SysMenuGroup.WithContext(ctx),
		SysMenuRole:   q.SysMenuRole.WithContext(ctx),
		SysOrg:        q.SysOrg.WithContext(ctx),
		SysPermission: q.SysPermission.WithContext(ctx),
		SysRole:       q.SysRole.WithContext(ctx),
		SysUser:       q.SysUser.WithContext(ctx),
		SysUserOrg:    q.SysUserOrg.WithContext(ctx),
		SysUserRole:   q.SysUserRole.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
