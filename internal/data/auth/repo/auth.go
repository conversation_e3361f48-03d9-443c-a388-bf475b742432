package repo

import (
	"context"
	"errors"
	"fmt"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	pb "vision_hub/api/auth/v1"
	biz "vision_hub/internal/biz/auth"
	commonBiz "vision_hub/internal/biz/common"
	"vision_hub/internal/conf"
	"vision_hub/internal/data/auth/model"
	"vision_hub/internal/data/auth/query"
	"vision_hub/internal/data/common"
	authError "vision_hub/internal/errors/auth"
	"vision_hub/internal/middleware"
	"vision_hub/internal/util"
)

type AuthRepo struct {
	conf *conf.BizConf
	log  *common.DataLogHelper
	data *common.Data
}

func NewAuthRepo(conf *conf.BizConf, logger *common.DataLogHelper, data *common.Data) biz.IAuthRepo {
	return &AuthRepo{
		conf: conf,
		log:  logger,
		data: data,
	}
}

// ValidateUser 检验用户
func (a *AuthRepo) ValidateUser(ctx context.Context, userID int32, password string) (bool, error) {
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	queryCondition, err := userQuery.WithContext(ctx).
		Where(userQuery.ID.Eq(userID)).
		Select(userQuery.Password).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, errors.New(fmt.Sprintf("用户不存在: %d", userID))
		}
		return false, err
	}
	hashedPassword := queryCondition.Password
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password)) == nil, nil
}

// CreateUserTx 创建用户
func (a *AuthRepo) CreateUserTx(ctx context.Context, user *model.SysUser, roleIds []int32) error {
	// 用户基础信息
	userBaseInfo, err := middleware.GetUserInfo(ctx)
	if err != nil {
		return nil
	}

	err = a.data.DbClient.DB(ctx).Transaction(func(tx *gorm.DB) error {
		userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
		err := userQuery.WithContext(ctx).Create(user)
		if err != nil {
			a.log.WithContext(ctx).Errorf("创建用户失败: %v", err)
			return err
		}

		// 用户部门关系
		sysUserOrg := &model.SysUserOrg{
			CreateUser: userBaseInfo.Email,
			UpdateUser: userBaseInfo.Email,
			UserID:     user.ID,
			OrgID:      user.OrgID,
		}
		if roleIds != nil && len(roleIds) > 0 {
			err := a.CreateUserRole(ctx, user.ID, roleIds, userBaseInfo.Email)
			if err != nil {
				return authError.CreateRoleUser
			}
		}
		if err := a.AddUserOrg(ctx, sysUserOrg); err != nil {
			a.log.WithContext(ctx).Errorf("创建用户部门关联关系失败: %v", err)
			return authError.DataCreateUserFail
		}

		return nil
	})
	return err
}

func (a *AuthRepo) GetUserInfoByID(ctx context.Context, userID int32) (*biz.UserInfo, error) {
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	queryCondition, err := userQuery.WithContext(ctx).
		Where(userQuery.ID.Eq(userID)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &biz.UserInfo{
		ID:        queryCondition.ID,
		Name:      queryCondition.UserName,
		Email:     queryCondition.Email,
		Password:  queryCondition.Password,
		LastName:  queryCondition.LastName,
		FirstName: queryCondition.FirstName,
		Phone:     queryCondition.Phone,
		IsStaff:   queryCondition.IsStaff,
	}, nil
}

func (a *AuthRepo) GetUserInfoByEmail(ctx context.Context, email string, id int64) (*biz.UserInfo, error) {
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	queryCondition := userQuery.WithContext(ctx)
	queryCondition = queryCondition.Where(userQuery.Email.Eq(email))
	if id != 0 {
		queryCondition = queryCondition.Where(userQuery.ID.Neq(int32(id)))
	}
	userModel, err := queryCondition.First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &biz.UserInfo{
		ID:       userModel.ID,
		Name:     userModel.UserName,
		Email:    userModel.Email,
		Password: userModel.Password,
		Avatar:   userModel.Avatar,
		Unionid:  userModel.UnionID,
		Openid:   userModel.OpenID,
		Userid:   userModel.UserID,
		IsStaff:  userModel.IsStaff,
	}, nil
}

func (a *AuthRepo) GetGroupsByUserID(ctx context.Context, userID int32) (*[]biz.Group, error) {
	userRoleQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserRole
	roleQuery := query.Use(a.data.DbClient.DB(ctx)).SysRole
	queryCondition := userRoleQuery.WithContext(ctx).
		LeftJoin(roleQuery, roleQuery.ID.EqCol(userRoleQuery.RoleID)).
		Where(userRoleQuery.UserID.Eq(userID))
	var groups []biz.Group
	err := queryCondition.Select(userRoleQuery.ID, userRoleQuery.UserID, userRoleQuery.RoleID, roleQuery.Code).
		Scan(&groups)
	if err != nil {
		return nil, err
	}
	return &groups, nil
}

// UpdateUser 更新用户信息
func (a *AuthRepo) UpdateUser(ctx context.Context, userID int32, updateParams *map[string]interface{}) error {
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	_, err := userQuery.WithContext(ctx).
		Where(userQuery.ID.Eq(userID)).
		Updates(updateParams)
	if err != nil {
		return err
	}
	return nil
}

func (a *AuthRepo) DeleteUserTx(ctx context.Context, userIds []int32) error {
	err := a.data.DbClient.DB(ctx).Transaction(func(tx *gorm.DB) error {
		err := a.DeleteUserByUserIds(ctx, userIds)
		if err != nil {
			a.log.WithContext(ctx).Errorf("删除用户失败: %v", err)
			return err
		}
		err = a.DeleteUserOrgByUserIds(ctx, userIds)
		if err != nil {
			a.log.WithContext(ctx).Errorf("删除用户部门关联关系失败: %v", err)
			return err
		}
		err = a.DeleteUserGroupByUserIds(ctx, userIds)
		if err != nil {
			a.log.WithContext(ctx).Errorf("删除用户角色关联关系失败: %v", err)
			return err
		}
		return nil
	})
	return err
}

func (a *AuthRepo) DeleteUserByUserIds(ctx context.Context, userIds []int32) error {
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	_, err := userQuery.WithContext(ctx).Where(userQuery.ID.In(userIds...)).Delete(&model.SysUser{})
	if err != nil {
		return err
	}
	return nil
}

func (a *AuthRepo) DeleteUserOrgByUserIds(ctx context.Context, userIds []int32) error {
	userOrgQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserOrg
	_, err := userOrgQuery.WithContext(ctx).Where(userOrgQuery.UserID.In(userIds...)).Delete(&model.SysUserOrg{})
	if err != nil {
		return err
	}
	return nil
}

func (a *AuthRepo) DeleteUserGroupByUserIds(ctx context.Context, userIds []int32) error {
	userRoleQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserRole
	_, err := userRoleQuery.WithContext(ctx).Where(userRoleQuery.UserID.In(userIds...)).Delete(&model.SysUserRole{})
	if err != nil {
		return err
	}
	return nil
}

func (a *AuthRepo) AddOrg(ctx context.Context, org *model.SysOrg) error {
	orgQuery := query.Use(a.data.DbClient.DB(ctx)).SysOrg
	err := orgQuery.WithContext(ctx).Create(org)
	if err != nil {
		return err
	}
	return nil
}

func (a *AuthRepo) UpdateOrg(ctx context.Context, orgID int32, updateParams *map[string]interface{}) error {
	orgQuery := query.Use(a.data.DbClient.DB(ctx)).SysOrg
	_, err := orgQuery.WithContext(ctx).
		Where(orgQuery.ID.Eq(orgID)).
		Updates(updateParams)
	if err != nil {
		return err
	}
	return nil
}

func (a *AuthRepo) GetOrgListByParentID(ctx context.Context, parentID int64) (*[]biz.Org, error) {
	orgQuery := query.Use(a.data.DbClient.DB(ctx)).SysOrg
	queryCondition := orgQuery.WithContext(ctx).Where(orgQuery.ParentID.Eq(parentID))
	var orgSlice []biz.Org
	err := queryCondition.Scan(&orgSlice)
	if err != nil {
		return nil, err
	}
	return &orgSlice, nil
}

func (a *AuthRepo) GetOrgByOrgName(ctx context.Context, orgName string) (*biz.Org, error) {
	orgQuery := query.Use(a.data.DbClient.DB(ctx)).SysOrg
	queryCondition, err := orgQuery.WithContext(ctx).
		Where(orgQuery.OrgName.Eq(orgName), orgQuery.IsDeleted.Eq(0)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &biz.Org{
		ID:       queryCondition.ID,
		OrgName:  queryCondition.OrgName,
		OrgCode:  queryCondition.OrgCode,
		ParentId: queryCondition.ParentID,
		Level:    queryCondition.OrgLevel,
	}, nil
}

func (a *AuthRepo) GetOrgUserByUserName(ctx context.Context, userName string) ([]*model.SysUser, error) {
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	var orgUser []*model.SysUser
	err := userQuery.WithContext(ctx).
		Where(userQuery.UserName.Like(util.FormatLikeParam(userName, true, true))).Scan(&orgUser)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return orgUser, nil
}

func (a *AuthRepo) GetOrgByID(ctx context.Context, Id int32) (*biz.Org, error) {
	orgQuery := query.Use(a.data.DbClient.DB(ctx)).SysOrg
	queryCondition, err := orgQuery.WithContext(ctx).
		Where(orgQuery.ID.Eq(Id)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &biz.Org{
		ID:       queryCondition.ID,
		OrgName:  queryCondition.OrgName,
		OrgCode:  queryCondition.OrgCode,
		ParentId: queryCondition.ParentID,
		Level:    queryCondition.OrgLevel,
	}, nil
}

// GetUserPageList 获取用户分页列表
func (a *AuthRepo) GetUserPageList(ctx context.Context, params *biz.UserPageListParams) (*[]biz.UserListItem, *pb.Pagination, error) {
	// 设置默认值， 如果没有传参则使用默认值
	if params.PageNum == 0 {
		params.PageNum = 1
	}
	if params.PageSize == 0 {
		params.PageSize = 20
	}
	offset := (params.PageNum - 1) * params.PageSize

	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	userOrgQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserOrg
	orgQuery := query.Use(a.data.DbClient.DB(ctx)).SysOrg
	queryCondition := userQuery.WithContext(ctx)
	queryCondition = queryCondition.
		LeftJoin(userOrgQuery, userOrgQuery.UserID.EqCol(userQuery.ID)).
		LeftJoin(orgQuery, orgQuery.ID.EqCol(userOrgQuery.OrgID)).
		Select(userQuery.ID, userQuery.Email, userQuery.UserName, userOrgQuery.OrgID, orgQuery.OrgName,
			userQuery.Phone, userQuery.FirstName, userQuery.LastName, userQuery.CreateTime, userQuery.UpdateTime)
	if params.Email != "" {
		queryCondition = queryCondition.Where(userQuery.Email.Like(util.FormatLikeParam(params.Email, true, true)))
	}
	if params.UserName != "" {
		queryCondition = queryCondition.Where(userQuery.UserName.Like(util.FormatLikeParam(params.UserName, true, true)))
	}
	if params.OrgID != 0 {
		queryCondition = queryCondition.Where(userOrgQuery.OrgID.Eq(params.OrgID))
	}
	if params.UserID != 0 {
		queryCondition = queryCondition.Where(userQuery.ID.Eq(params.UserID))
	}
	// 获取数据总数
	total, err := queryCondition.Count()
	if err != nil {
		return nil, nil, err
	}

	var userSlice []biz.UserListItem
	err = queryCondition.Order(userQuery.ID.Desc()).Offset(int(offset)).Limit(int(params.PageSize)).Scan(&userSlice)
	if err != nil {
		return nil, nil, err
	}
	// 获取所有用户的ID
	userIDs := make([]int32, 0, len(userSlice))
	userMap := make(map[int32]*biz.UserListItem)
	for i := range userSlice {
		userIDs = append(userIDs, userSlice[i].ID)
		userMap[userSlice[i].ID] = &userSlice[i]
		userSlice[i].RoleIds = []int32{} // 初始化空数组
	}

	// 批量查询用户角色
	if len(userIDs) > 0 {
		userRoleQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserRole
		roleQuery := query.Use(a.data.DbClient.DB(ctx)).SysRole
		var userRoles []struct {
			UserID   int32
			RoleID   int32
			RoleName string `gorm:"column:name"`
		}
		err = userRoleQuery.WithContext(ctx).
			LeftJoin(roleQuery, roleQuery.ID.EqCol(userRoleQuery.RoleID)).
			Where(userRoleQuery.UserID.In(userIDs...)).
			Select(userRoleQuery.UserID, userRoleQuery.RoleID, roleQuery.Name).
			Scan(&userRoles)
		if err != nil {
			return nil, nil, err
		}

		// 将角色ID分组到相应用户
		for _, ur := range userRoles {
			if user, ok := userMap[ur.UserID]; ok {
				user.RoleIds = append(user.RoleIds, ur.RoleID)
				user.RoleNames = append(user.RoleNames, ur.RoleName)
			}
		}
	}
	// 返回分页数据和分页信息
	return &userSlice, &pb.Pagination{
		Total:    int32(total),
		PageSize: params.PageSize,
		PageNum:  params.PageNum,
	}, nil
}

// GetUserSelect 获取用户选择列表, 输入的可能是名称，也可能是邮件，支持模糊查询
func (a *AuthRepo) GetUserSelect(ctx context.Context, search string, ids []int32, pageSize int32) (*[]commonBiz.UserInfoStruct, error) {
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 500 {
		pageSize = 500
	}
	userQuery := query.Use(a.data.DbClient.DB(ctx)).SysUser
	likeSearch := util.FormatLikeParam(search, true, true)
	queryCondition := userQuery.WithContext(ctx)
	if search != "" {
		queryCondition = queryCondition.Where(userQuery.UserName.Like(likeSearch)).
			Or(userQuery.Email.Like(likeSearch))
	}
	if len(ids) > 0 {
		queryCondition = queryCondition.Where(userQuery.ID.In(ids...))
	}
	var ret []commonBiz.UserInfoStruct
	err := queryCondition.Order(userQuery.CreateTime.Desc()).Limit(int(pageSize)).Scan(&ret)
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (a *AuthRepo) GetOrgUserByOrgId(ctx context.Context, orgId []int32, req *pb.QueryOrgByUserRequest) ([]*model.SysUser, error) {
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 500 {
		req.PageSize = 500
	}
	likeSearch := util.FormatLikeParam(req.Search, true, true)

	var orgUser []*model.SysUser
	sourceDb := a.data.DbClient.DB(ctx)
	uQuery := query.Use(sourceDb).SysUser
	uoQuery := query.Use(sourceDb).SysUserOrg

	queryCondition := uQuery.WithContext(ctx)
	queryCondition = queryCondition.
		LeftJoin(uoQuery, uoQuery.UserID.EqCol(uQuery.ID)).
		Where(uoQuery.OrgID.In(orgId...)).
		Select(uQuery.ID, uQuery.UserName, uQuery.Email)
	if req.Search != "" {
		queryCondition = queryCondition.Where(uQuery.UserName.Like(likeSearch))
	}
	if len(req.Ids) > 0 {
		queryCondition = queryCondition.Where(uQuery.ID.In(req.Ids...))
	}
	err := queryCondition.Scan(&orgUser)
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门成员信息失败: %v", err)
		return nil, err
	}
	return orgUser, nil
}

func (a *AuthRepo) AddUserOrg(ctx context.Context, orgUser *model.SysUserOrg) error {
	orgUserQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserOrg
	err := orgUserQuery.WithContext(ctx).Create(orgUser)
	if err != nil {
		return err
	}
	return nil
}

// GetOrgList 获取部门列表包含本身
func (a *AuthRepo) GetOrgList(ctx context.Context, params *biz.OrgListParams) (*[]biz.OrgTreeData, error) {
	if params.OrgId == 0 {
		params.OrgId = 4
	}
	var orgData *[]biz.OrgTreeData
	sourceQuery := a.data.DbClient.DB(ctx)
	var search string
	if params.Search != "" {
		search = fmt.Sprintf("org_name like '%%%s%%' and", params.Search)
	}
	queryRaw := fmt.Sprintf(`
				WITH RECURSIVE org_hierarchy AS (
					SELECT id, org_name, parent_id, org_level, is_deleted 
					FROM sys_org
					WHERE id = %d and is_deleted = 0
					
					UNION ALL
				
					SELECT o.id, o.org_name, o.parent_id, o.org_level, o.is_deleted
					FROM sys_org o
					INNER JOIN org_hierarchy oh ON o.parent_id = oh.id
					where oh.is_deleted = 0
				)
				SELECT * FROM org_hierarchy where %s is_deleted = 0 order by org_level asc, id asc;
			`, params.OrgId, search)
	err := sourceQuery.Raw(queryRaw).Scan(&orgData).Error
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门信息失败: %v", err)
		return nil, err
	}

	return orgData, nil
}

// GetParentOrgList 获取父级部门列表包含本身
func (a *AuthRepo) GetParentOrgList(ctx context.Context, params *biz.OrgListParams) (*[]biz.OrgTreeData, error) {
	if params.OrgId == 0 {
		params.OrgId = 4
	}
	var orgData *[]biz.OrgTreeData
	sourceQuery := a.data.DbClient.DB(ctx)
	var search string
	if params.Search != "" {
		search = fmt.Sprintf("org_name like '%%%s%%' and", params.Search)
	}
	queryRaw := fmt.Sprintf(`
				WITH RECURSIVE org_hierarchy AS (
					SELECT id, org_name, parent_id, org_level, is_deleted 
					FROM sys_org
					WHERE id = %d and is_deleted = 0
				
					UNION ALL
				
					SELECT o.id, o.org_name, o.parent_id, o.org_level, o.is_deleted
					FROM sys_org o
					INNER JOIN org_hierarchy oh ON o.id = oh.parent_id
					where oh.is_deleted = 0
				)
				SELECT * FROM org_hierarchy where %s is_deleted = 0 order by org_level asc, id asc;
			`, params.OrgId, search)
	err := sourceQuery.Raw(queryRaw).Scan(&orgData).Error
	if err != nil {
		a.log.WithContext(ctx).Errorf("查询部门信息失败: %v", err)
		return nil, err
	}

	return orgData, nil
}

func (a *AuthRepo) DeleteOrg(ctx context.Context, orgIds []int32) error {
	orgQuery := query.Use(a.data.DbClient.DB(ctx)).SysOrg
	_, err := orgQuery.WithContext(ctx).Where(orgQuery.ID.In(orgIds...)).Updates(map[string]interface{}{"is_deleted": 1})
	if err != nil {
		return err
	}

	return nil
}

func (a *AuthRepo) GetUserOrgByUserId(ctx context.Context, userId int64) (*model.SysUserOrg, error) {
	userOrgQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserOrg
	queryCondition, err := userOrgQuery.WithContext(ctx).
		Where(userOrgQuery.UserID.Eq(int32(userId))).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			a.log.WithContext(ctx).Warnf("用户部门关联信息不存在: %v", err)
			return nil, nil
		}
		return nil, err
	}

	return queryCondition, nil
}

func (a *AuthRepo) UpdateUserOrg(ctx context.Context, userId int32, orgUser *map[string]interface{}) error {
	userOrgQuery := query.Use(a.data.DbClient.DB(ctx)).SysUserOrg
	_, err := userOrgQuery.WithContext(ctx).
		Where(userOrgQuery.UserID.Eq(userId)).
		Updates(orgUser)

	if err != nil {
		return err
	}

	return nil
}

// DeleteUserRole 删除角色对应菜单关系
func (a *AuthRepo) DeleteUserRole(ctx context.Context, userId int32) error {
	q := query.Use(a.data.DbClient.DB(ctx)).SysUserRole
	_, err := q.WithContext(ctx).Where(q.UserID.Eq(userId)).Delete()
	return err
}

// GetMenuTree 获取菜单树
func (a *AuthRepo) GetMenuTree(ctx context.Context, userId int32, isStaff int32) ([]*biz.MenuTree, error) {
	ret := make([]*model.SysMenu, 0)
	a.data.DbClient.DB(ctx)
	sysMenuQuery := a.data.DbClient.DB(ctx).Model(&model.SysMenu{}).
		Select("DISTINCT sys_menu.*").
		Joins(`LEFT JOIN sys_menu_role smr ON smr.menu_id = sys_menu.id`).
		Joins("LEFT JOIN sys_user_role sur ON sur.role_id = smr.role_id")
	if isStaff == 1 {
		sysMenuQuery = sysMenuQuery.Where("sys_menu.is_deleted = 0")
	}
	if isStaff == 0 {
		sysMenuQuery = sysMenuQuery.Where("sur.user_id = ? and sys_menu.is_deleted = 0", userId)
	}
	err := sysMenuQuery.Scan(&ret).Error
	if err != nil {
		return nil, err
	}
	tree := a.buildMenuTree(ret)
	return tree, nil
}

// 2. 构建树形结构
func (a *AuthRepo) buildMenuTree(sysMenus []*model.SysMenu) []*biz.MenuTree {

	menus := make([]*biz.MenuTree, 0)
	for _, sysMenu := range sysMenus {
		menus = append(menus, &biz.MenuTree{
			MenuId:   sysMenu.ID,
			Icon:     sysMenu.Icon,
			MenuType: int32(sysMenu.MenuType),
			MenuName: sysMenu.MenuName,
			Path:     sysMenu.Path,
			Url:      sysMenu.URL,
			Target:   sysMenu.Target,
			ParentID: sysMenu.ParentID,
			MenuKey:  sysMenu.MenuKey,
		})
	}

	menuMap := make(map[int32]*biz.MenuTree)
	for i := range menus {
		menuMap[menus[i].MenuId] = menus[i]
	}

	// 构建树
	var tree []*biz.MenuTree
	for i := range menus {
		menu := menus[i]
		if menu.ParentID == 0 {
			tree = append(tree, menu)
		} else {
			if parent, ok := menuMap[menu.ParentID]; ok {
				parent.Children = append(parent.Children, menu)
			}
		}
	}
	return tree
}

func (a *AuthRepo) GetOrgNamesByUserID(ctx context.Context, userID int64) (orgName string, err error) {

	sql := fmt.Sprintf("SELECT so.org_name FROM sys_user_org suo JOIN sys_org so ON suo.org_id = so.id WHERE suo.user_id = %d", userID)
	err = a.data.DbClient.DB(ctx).Raw(sql).Scan(&orgName).Error
	if err != nil {
		return "", err
	}
	return orgName, nil
}

func (r *AuthRepo) CreateUserRole(ctx context.Context, userId int32, roleIds []int32, createUser string) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserRole
	var menuRoles []*model.SysUserRole
	for _, roleId := range roleIds {
		menuRoles = append(menuRoles, &model.SysUserRole{
			UserID:     userId,
			RoleID:     roleId,
			CreateUser: createUser,
		})
	}
	if len(menuRoles) > 0 {
		if err := q.WithContext(ctx).CreateInBatches(menuRoles, 500); err != nil {
			return err
		}
	}
	return nil
}
