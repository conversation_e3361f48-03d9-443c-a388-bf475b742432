package repo

import (
	"context"
	"encoding/json"
	authBiz "vision_hub/internal/biz/auth"
	"vision_hub/internal/data/auth/model"
	"vision_hub/internal/data/auth/query"
	"vision_hub/internal/data/common"
	"vision_hub/internal/errors/auth"
)

// AuthRoleRepo implements the IAuthRoleRepo interface.
type AuthRoleRepo struct {
	log  *common.DataLogHelper
	data *common.Data
}

// NewAuthRoleRepo creates a new AuthRoleRepo.
func NewAuthRoleRepo(log *common.DataLogHelper, data *common.Data) authBiz.IAuthRoleRepo {
	return &AuthRoleRepo{
		log:  log,
		data: data,
	}
}

// Create 创建角色
func (r *AuthRoleRepo) Create(ctx context.Context, req *model.SysRole) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysRole
	return q.WithContext(ctx).Create(req)
}

func (r *AuthRoleRepo) CreateMenuRole(ctx context.Context, roleId int32, menuIds []int32, createUser, updateUser string) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysMenuRole
	var menuRoles []*model.SysMenuRole
	for _, menuId := range menuIds {
		menuRoles = append(menuRoles, &model.SysMenuRole{
			MenuID:     menuId,
			RoleID:     roleId,
			CreateUser: createUser,
			UpdateUser: updateUser,
		})
	}
	if len(menuRoles) > 0 {
		if err := q.WithContext(ctx).CreateInBatches(menuRoles, 500); err != nil {
			return err
		}
	}
	return nil
}

// Update 更新角色
func (r *AuthRoleRepo) Update(ctx context.Context, req *model.SysRole) error {
	return r.data.DbClient.DB(ctx).Model(&model.SysRole{}).
		Select("name", "code", "description").
		Where("id = ?", req.ID).
		Updates(req).Error
}

// GetMenuByKeys menuKeys
func (r *AuthRoleRepo) GetMenuByKeys(ctx context.Context, menuKeys []string) (menuIds []int32, err error) {

	sysMenu := query.Use(r.data.DbClient.DB(ctx)).SysMenu
	err = sysMenu.WithContext(ctx).
		Select(sysMenu.ID).
		Where(sysMenu.MenuKey.In(menuKeys...)).Scan(&menuIds)
	if err != nil {
		return nil, err
	}
	return menuIds, nil
}

// DeleteRoleUser 删除角色对应关系
func (r *AuthRoleRepo) DeleteRoleUser(ctx context.Context, roleId int32) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserRole
	_, err := q.WithContext(ctx).Where(q.RoleID.Eq(roleId)).Delete()
	return err
}

func (r *AuthRoleRepo) GetRoleUser(ctx context.Context, roleId int32) ([]*model.SysUserRole, error) {
	userRole := make([]*model.SysUserRole, 0)
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserRole
	err := q.WithContext(ctx).Where(q.RoleID.Eq(roleId)).Scan(&userRole)
	if err != nil {
		return nil, err
	}
	return userRole, err
}

// DeleteRoleMenu 删除角色对应菜单关系
func (r *AuthRoleRepo) DeleteRoleMenu(ctx context.Context, roleId int32) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysMenuRole
	_, err := q.WithContext(ctx).Where(q.RoleID.Eq(roleId)).Delete()
	return err
}

// AddRoleUser 创建用户和角色关系
func (r *AuthRoleRepo) AddRoleUser(ctx context.Context, roleId int32, userIds []int32, createUser string) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserRole
	var auths []*model.SysUserRole
	for _, userId := range userIds {
		auths = append(auths, &model.SysUserRole{
			UserID:     userId,
			RoleID:     roleId,
			CreateUser: createUser,
		})
	}
	if len(auths) > 0 {
		if err := q.WithContext(ctx).CreateInBatches(auths, 500); err != nil {
			return err
		}
	}
	return nil
}

// CreateRole creates a new auth role.
func (r *AuthRoleRepo) CreateRole(ctx context.Context, req *authBiz.CreateOrUpdateRoleReq) error {
	role := req.SysRole
	// 获取并且判断menu_id_keys是否全部存在
	menuIds, err := r.GetMenuByKeys(ctx, req.MenuKeys)
	if err != nil {
		return err
	}
	if len(menuIds) != len(req.MenuKeys) {
		return auth.MenuNotExists
	}
	return r.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 创建角色
		if err = r.Create(ctx, role); err != nil {
			return err
		}

		// 创建角色与菜单关系
		return r.CreateMenuRole(ctx, role.ID, menuIds, req.CreateUser, req.UpdateUser)
	})
}

// UpdateRole updates an existing auth role.
func (r *AuthRoleRepo) UpdateRole(ctx context.Context, req *authBiz.CreateOrUpdateRoleReq) error {
	role := req.SysRole
	// 获取并且判断menu_id_keys是否全部存在
	menuIds, err := r.GetMenuByKeys(ctx, req.MenuKeys)
	if err != nil {
		return err
	}
	if len(menuIds) != len(req.MenuKeys) {
		return auth.MenuNotExists
	}
	return r.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {
		// 更新角色
		if err := r.Update(ctx, role); err != nil {
			return err
		}

		// 删除角色和菜单关系
		if err := r.DeleteRoleMenu(ctx, role.ID); err != nil {
			return err
		}

		// 创建新的菜单和角色关系
		return r.CreateMenuRole(ctx, role.ID, menuIds, req.CreateUser, req.UpdateUser)
	})
}

// DeleteRole deletes an auth role.
func (r *AuthRoleRepo) DeleteRole(ctx context.Context, roleId int32) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysRole
	return r.data.TsManagers.Db.DoTx(ctx, func(ctx context.Context) error {

		// 删除角色和菜单关系
		if err := r.DeleteRoleMenu(ctx, roleId); err != nil {
			return err
		}
		// 删除角色
		if _, err := q.WithContext(ctx).Where(q.ID.Eq(roleId)).Delete(); err != nil {
			return err
		}
		return nil
	})
}

// GetRoleDetail retrieves the details of an auth Role.
func (r *AuthRoleRepo) GetRoleDetail(ctx context.Context, roleId int32) (*authBiz.RoleDetail, error) {
	var roleDetail authBiz.RoleDetail

	if err := r.data.DbClient.DB(ctx).Model(&model.SysRole{}).
		Select("sys_role.*,JSON_ARRAYAGG(JSON_OBJECT('user_id',sys_user.id, 'user_name',sys_user.user_name,'user_email',sys_user.email)) AS users").
		Joins("LEFT JOIN sys_user_role ON sys_role.id = sys_user_role.role_id").
		Joins("LEFT JOIN sys_user ON sys_user_role.user_id = sys_user.id").
		Where("sys_role.id = ?", roleId).
		Group("sys_role.id").
		Find(&roleDetail).Error; err != nil {
		return nil, err
	}
	if roleDetail.UsersBytes != nil {
		if err := json.Unmarshal(roleDetail.UsersBytes, &roleDetail.Users); err != nil {
			return nil, err
		}
	}

	menus, err := r.GetMenuTreeByRole(ctx, roleId)
	if err != nil {
		return nil, err
	}
	roleDetail.Menus = menus

	return &roleDetail, nil
}

// QueryRoleList retrieves a list of auth Role.
func (r *AuthRoleRepo) QueryRoleList(ctx context.Context, req *authBiz.QueryRoleListReq) (*authBiz.RoleList, error) {
	offset := (req.PageNum - 1) * req.PageSize
	var role []authBiz.RoleDetail
	tmpQuery := r.data.DbClient.DB(ctx).Model(&model.SysRole{}).
		Select("sys_role.*,JSON_ARRAYAGG(JSON_OBJECT('user_id',sys_user.id, 'user_name',sys_user.user_name,'user_email',sys_user.email)) AS users").
		Joins("LEFT JOIN sys_user_role ON sys_role.id = sys_user_role.role_id").
		Joins("LEFT JOIN sys_user ON sys_user_role.user_id = sys_user.id").
		Group("sys_role.id")
	if req.RoleName != "" {
		tmpQuery = tmpQuery.Where("sys_role.name LIKE ?", "%"+req.RoleName+"%")
	}
	// 获取数据总数
	var count int64
	if err := tmpQuery.Count(&count).Error; err != nil {
		r.log.WithContext(ctx).Errorf("获取总数异常: %v", err)
		return nil, err
	}
	// 查询客户项目详情
	if offset == 0 {
		if err := tmpQuery.Find(&role).Error; err != nil {
			return nil, err
		}
	} else {
		if err := tmpQuery.Limit(int(req.PageSize)).Offset(int(offset)).Find(&role).Error; err != nil {
			return nil, err
		}
	}
	for k, item := range role {
		if item.UsersBytes != nil {
			if err := json.Unmarshal(item.UsersBytes, &role[k].Users); err != nil {
				return nil, err
			}
		}
	}
	return &authBiz.RoleList{Total: int32(count), PageSize: req.PageSize, PageNum: req.PageNum, List: role}, nil
}

// CheckRoleExists checks if a Role with the given name or code exists.
func (r *AuthRoleRepo) CheckRoleExists(ctx context.Context, roleName, roleCode string) (bool, bool, error) {
	q := query.Use(r.data.DbClient.DB(ctx)).SysRole
	nameQuery := q.WithContext(ctx)
	codeQuery := q.WithContext(ctx)
	var nameCount int64
	nameCount, err := nameQuery.Where(q.Name.Eq(roleName)).Count()
	if err != nil {
		return false, false, err
	}
	codeCount, err := codeQuery.Where(q.Code.Eq(roleCode)).Count()
	if err != nil {
		return false, false, err
	}
	return nameCount > 0, codeCount > 0, nil
}

// GetMenuTreeByRole 获取菜单树
func (r *AuthRoleRepo) GetMenuTreeByRole(ctx context.Context, roleId int32) ([]*model.SysMenu, error) {
	ret := make([]*model.SysMenu, 0)
	err := r.data.DbClient.DB(ctx).Model(&model.SysMenu{}).
		Select("DISTINCT sys_menu.*").
		Joins(`LEFT JOIN sys_menu_role smr ON smr.menu_id = sys_menu.id`).
		Where("smr.role_id = ?", roleId).
		Scan(&ret).Error
	if err != nil {
		return nil, err
	}
	return ret, nil
}
