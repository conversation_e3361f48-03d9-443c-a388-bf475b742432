package data

import (
	"github.com/google/wire"
	"vision_hub/internal/data/auth/repo"
	authorizationRepo "vision_hub/internal/data/authorization/repo"
	"vision_hub/internal/data/client"
	"vision_hub/internal/data/common"
	commonRepo "vision_hub/internal/data/common/repo"
	customerProjectRepo "vision_hub/internal/data/customer_project/repo"
	customerProjectSpaceRepo "vision_hub/internal/data/customer_project_space"
	adsAccountRepo "vision_hub/internal/data/customer_project_space/ads_account/repo"
	materialRepo "vision_hub/internal/data/customer_project_space/material_manage/repo"
	customerResourceRepo "vision_hub/internal/data/customer_project_space/resource/repo"
	dictManageRepo "vision_hub/internal/data/dict_manage/repo"
	rsRepo "vision_hub/internal/data/resource_pool/repo"
	taskRepo "vision_hub/internal/data/task/repo"
	userActivityResp "vision_hub/internal/data/user_activity/repo"
)

// DataProviderSet is data providers.
var DataProviderSet = wire.NewSet(
	common.NewDataLogHelper,
	wire.Struct(new(common.Data), "*"),
	repo.NewAuthRepo,
	repo.NewAuthGroupRepo,
	repo.NewAuthThirdPartyRepo,
	authorizationRepo.NewAuthorizationRepo,
	commonRepo.NewCommonRepo,
	commonRepo.NewThirdPartyRepo,
	dictManageRepo.NewDictManageRepo,
	rsRepo.NewPrResourceRepo,
	rsRepo.NewPublisherResourceRepo,
	rsRepo.NewSupplierResourceRepo,
	rsRepo.NewIpResourceRepo,
	rsRepo.NewOutdoorScreenResourceRepo,
	rsRepo.NewReporterResourceRepo,
	rsRepo.NewResourceRepo,
	client.NewClientPortalSignClient,
	taskRepo.NewTaskRepo,
	customerProjectRepo.NewCustomerProjectRepo,
	materialRepo.NewMaterialManageRepo,
	userActivityResp.NewUserActivityRepo,
	adsAccountRepo.NewAdsAccountRepo,
	customerProjectSpaceRepo.NewCustomerProjectCommRepo,
	customerResourceRepo.NewCustomerProResourceRepo,
)
