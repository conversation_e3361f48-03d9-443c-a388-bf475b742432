// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/authorization/model"
)

func newSysUserAuthorizationOptLog(db *gorm.DB, opts ...gen.DOOption) sysUserAuthorizationOptLog {
	_sysUserAuthorizationOptLog := sysUserAuthorizationOptLog{}

	_sysUserAuthorizationOptLog.sysUserAuthorizationOptLogDo.UseDB(db, opts...)
	_sysUserAuthorizationOptLog.sysUserAuthorizationOptLogDo.UseModel(&model.SysUserAuthorizationOptLog{})

	tableName := _sysUserAuthorizationOptLog.sysUserAuthorizationOptLogDo.TableName()
	_sysUserAuthorizationOptLog.ALL = field.NewAsterisk(tableName)
	_sysUserAuthorizationOptLog.CreateTime = field.NewField(tableName, "create_time")
	_sysUserAuthorizationOptLog.UpdateTime = field.NewField(tableName, "update_time")
	_sysUserAuthorizationOptLog.OptUser = field.NewString(tableName, "opt_user")
	_sysUserAuthorizationOptLog.OptType = field.NewString(tableName, "opt_type")
	_sysUserAuthorizationOptLog.AuthorizationID = field.NewInt32(tableName, "authorization_id")
	_sysUserAuthorizationOptLog.OriginalNote = field.NewBytes(tableName, "original_note")
	_sysUserAuthorizationOptLog.UpdateNote = field.NewBytes(tableName, "update_note")

	_sysUserAuthorizationOptLog.fillFieldMap()

	return _sysUserAuthorizationOptLog
}

type sysUserAuthorizationOptLog struct {
	sysUserAuthorizationOptLogDo sysUserAuthorizationOptLogDo

	ALL             field.Asterisk
	CreateTime      field.Field  // 录入时间
	UpdateTime      field.Field  // 更新时间
	OptUser         field.String // 操作人
	OptType         field.String // 操作类型：CREATE|UPDATE|DELETE
	AuthorizationID field.Int32  // sys_user_authorization主键ID
	OriginalNote    field.Bytes  // 操作内容，json格式的数据
	UpdateNote      field.Bytes  // 操作内容，json格式的数据

	fieldMap map[string]field.Expr
}

func (s sysUserAuthorizationOptLog) Table(newTableName string) *sysUserAuthorizationOptLog {
	s.sysUserAuthorizationOptLogDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysUserAuthorizationOptLog) As(alias string) *sysUserAuthorizationOptLog {
	s.sysUserAuthorizationOptLogDo.DO = *(s.sysUserAuthorizationOptLogDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysUserAuthorizationOptLog) updateTableName(table string) *sysUserAuthorizationOptLog {
	s.ALL = field.NewAsterisk(table)
	s.CreateTime = field.NewField(table, "create_time")
	s.UpdateTime = field.NewField(table, "update_time")
	s.OptUser = field.NewString(table, "opt_user")
	s.OptType = field.NewString(table, "opt_type")
	s.AuthorizationID = field.NewInt32(table, "authorization_id")
	s.OriginalNote = field.NewBytes(table, "original_note")
	s.UpdateNote = field.NewBytes(table, "update_note")

	s.fillFieldMap()

	return s
}

func (s *sysUserAuthorizationOptLog) WithContext(ctx context.Context) *sysUserAuthorizationOptLogDo {
	return s.sysUserAuthorizationOptLogDo.WithContext(ctx)
}

func (s sysUserAuthorizationOptLog) TableName() string {
	return s.sysUserAuthorizationOptLogDo.TableName()
}

func (s sysUserAuthorizationOptLog) Alias() string { return s.sysUserAuthorizationOptLogDo.Alias() }

func (s sysUserAuthorizationOptLog) Columns(cols ...field.Expr) gen.Columns {
	return s.sysUserAuthorizationOptLogDo.Columns(cols...)
}

func (s *sysUserAuthorizationOptLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysUserAuthorizationOptLog) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 7)
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["opt_user"] = s.OptUser
	s.fieldMap["opt_type"] = s.OptType
	s.fieldMap["authorization_id"] = s.AuthorizationID
	s.fieldMap["original_note"] = s.OriginalNote
	s.fieldMap["update_note"] = s.UpdateNote
}

func (s sysUserAuthorizationOptLog) clone(db *gorm.DB) sysUserAuthorizationOptLog {
	s.sysUserAuthorizationOptLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysUserAuthorizationOptLog) replaceDB(db *gorm.DB) sysUserAuthorizationOptLog {
	s.sysUserAuthorizationOptLogDo.ReplaceDB(db)
	return s
}

type sysUserAuthorizationOptLogDo struct{ gen.DO }

func (s sysUserAuthorizationOptLogDo) Debug() *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Debug())
}

func (s sysUserAuthorizationOptLogDo) WithContext(ctx context.Context) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysUserAuthorizationOptLogDo) ReadDB() *sysUserAuthorizationOptLogDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysUserAuthorizationOptLogDo) WriteDB() *sysUserAuthorizationOptLogDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysUserAuthorizationOptLogDo) Session(config *gorm.Session) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysUserAuthorizationOptLogDo) Clauses(conds ...clause.Expression) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysUserAuthorizationOptLogDo) Returning(value interface{}, columns ...string) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysUserAuthorizationOptLogDo) Not(conds ...gen.Condition) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysUserAuthorizationOptLogDo) Or(conds ...gen.Condition) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysUserAuthorizationOptLogDo) Select(conds ...field.Expr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysUserAuthorizationOptLogDo) Where(conds ...gen.Condition) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysUserAuthorizationOptLogDo) Order(conds ...field.Expr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysUserAuthorizationOptLogDo) Distinct(cols ...field.Expr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysUserAuthorizationOptLogDo) Omit(cols ...field.Expr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysUserAuthorizationOptLogDo) Join(table schema.Tabler, on ...field.Expr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysUserAuthorizationOptLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysUserAuthorizationOptLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysUserAuthorizationOptLogDo) Group(cols ...field.Expr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysUserAuthorizationOptLogDo) Having(conds ...gen.Condition) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysUserAuthorizationOptLogDo) Limit(limit int) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysUserAuthorizationOptLogDo) Offset(offset int) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysUserAuthorizationOptLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysUserAuthorizationOptLogDo) Unscoped() *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysUserAuthorizationOptLogDo) Create(values ...*model.SysUserAuthorizationOptLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysUserAuthorizationOptLogDo) CreateInBatches(values []*model.SysUserAuthorizationOptLog, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysUserAuthorizationOptLogDo) Save(values ...*model.SysUserAuthorizationOptLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysUserAuthorizationOptLogDo) First() (*model.SysUserAuthorizationOptLog, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorizationOptLog), nil
	}
}

func (s sysUserAuthorizationOptLogDo) Take() (*model.SysUserAuthorizationOptLog, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorizationOptLog), nil
	}
}

func (s sysUserAuthorizationOptLogDo) Last() (*model.SysUserAuthorizationOptLog, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorizationOptLog), nil
	}
}

func (s sysUserAuthorizationOptLogDo) Find() ([]*model.SysUserAuthorizationOptLog, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysUserAuthorizationOptLog), err
}

func (s sysUserAuthorizationOptLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysUserAuthorizationOptLog, err error) {
	buf := make([]*model.SysUserAuthorizationOptLog, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysUserAuthorizationOptLogDo) FindInBatches(result *[]*model.SysUserAuthorizationOptLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysUserAuthorizationOptLogDo) Attrs(attrs ...field.AssignExpr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysUserAuthorizationOptLogDo) Assign(attrs ...field.AssignExpr) *sysUserAuthorizationOptLogDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysUserAuthorizationOptLogDo) Joins(fields ...field.RelationField) *sysUserAuthorizationOptLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysUserAuthorizationOptLogDo) Preload(fields ...field.RelationField) *sysUserAuthorizationOptLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysUserAuthorizationOptLogDo) FirstOrInit() (*model.SysUserAuthorizationOptLog, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorizationOptLog), nil
	}
}

func (s sysUserAuthorizationOptLogDo) FirstOrCreate() (*model.SysUserAuthorizationOptLog, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorizationOptLog), nil
	}
}

func (s sysUserAuthorizationOptLogDo) FindByPage(offset int, limit int) (result []*model.SysUserAuthorizationOptLog, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysUserAuthorizationOptLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysUserAuthorizationOptLogDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysUserAuthorizationOptLogDo) Delete(models ...*model.SysUserAuthorizationOptLog) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysUserAuthorizationOptLogDo) withDO(do gen.Dao) *sysUserAuthorizationOptLogDo {
	s.DO = *do.(*gen.DO)
	return s
}
