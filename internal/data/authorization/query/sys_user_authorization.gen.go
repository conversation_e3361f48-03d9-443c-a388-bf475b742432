// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"vision_hub/internal/data/authorization/model"
)

func newSysUserAuthorization(db *gorm.DB, opts ...gen.DOOption) sysUserAuthorization {
	_sysUserAuthorization := sysUserAuthorization{}

	_sysUserAuthorization.sysUserAuthorizationDo.UseDB(db, opts...)
	_sysUserAuthorization.sysUserAuthorizationDo.UseModel(&model.SysUserAuthorization{})

	tableName := _sysUserAuthorization.sysUserAuthorizationDo.TableName()
	_sysUserAuthorization.ALL = field.NewAsterisk(tableName)
	_sysUserAuthorization.ID = field.NewInt32(tableName, "id")
	_sysUserAuthorization.CreateTime = field.NewField(tableName, "create_time")
	_sysUserAuthorization.UpdateTime = field.NewField(tableName, "update_time")
	_sysUserAuthorization.CreateUserID = field.NewInt32(tableName, "create_user_id")
	_sysUserAuthorization.DisplayName = field.NewString(tableName, "display_name")
	_sysUserAuthorization.AuthorizationKey = field.NewString(tableName, "authorization_key")
	_sysUserAuthorization.AuthorizationType = field.NewString(tableName, "authorization_type")
	_sysUserAuthorization.AuthorizationPlatform = field.NewString(tableName, "authorization_platform")
	_sysUserAuthorization.AuthorizationConfig = field.NewBytes(tableName, "authorization_config")
	_sysUserAuthorization.Status = field.NewString(tableName, "status")
	_sysUserAuthorization.ExpiresAt = field.NewField(tableName, "expires_at")

	_sysUserAuthorization.fillFieldMap()

	return _sysUserAuthorization
}

type sysUserAuthorization struct {
	sysUserAuthorizationDo sysUserAuthorizationDo

	ALL                   field.Asterisk
	ID                    field.Int32  // 自增主键
	CreateTime            field.Field  // 创建时间
	UpdateTime            field.Field  // 更新时间
	CreateUserID          field.Int32  // 创建人
	DisplayName           field.String // 名称
	AuthorizationKey      field.String // 第三方平台唯一信息
	AuthorizationType     field.String // 授权类型:OAuth2
	AuthorizationPlatform field.String // 授权平台
	AuthorizationConfig   field.Bytes  // 授权配置
	Status                field.String // 授权状态：ACTIVE|DISABLE|EXPIRED
	ExpiresAt             field.Field  // 过期时间

	fieldMap map[string]field.Expr
}

func (s sysUserAuthorization) Table(newTableName string) *sysUserAuthorization {
	s.sysUserAuthorizationDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysUserAuthorization) As(alias string) *sysUserAuthorization {
	s.sysUserAuthorizationDo.DO = *(s.sysUserAuthorizationDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysUserAuthorization) updateTableName(table string) *sysUserAuthorization {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.CreateTime = field.NewField(table, "create_time")
	s.UpdateTime = field.NewField(table, "update_time")
	s.CreateUserID = field.NewInt32(table, "create_user_id")
	s.DisplayName = field.NewString(table, "display_name")
	s.AuthorizationKey = field.NewString(table, "authorization_key")
	s.AuthorizationType = field.NewString(table, "authorization_type")
	s.AuthorizationPlatform = field.NewString(table, "authorization_platform")
	s.AuthorizationConfig = field.NewBytes(table, "authorization_config")
	s.Status = field.NewString(table, "status")
	s.ExpiresAt = field.NewField(table, "expires_at")

	s.fillFieldMap()

	return s
}

func (s *sysUserAuthorization) WithContext(ctx context.Context) *sysUserAuthorizationDo {
	return s.sysUserAuthorizationDo.WithContext(ctx)
}

func (s sysUserAuthorization) TableName() string { return s.sysUserAuthorizationDo.TableName() }

func (s sysUserAuthorization) Alias() string { return s.sysUserAuthorizationDo.Alias() }

func (s sysUserAuthorization) Columns(cols ...field.Expr) gen.Columns {
	return s.sysUserAuthorizationDo.Columns(cols...)
}

func (s *sysUserAuthorization) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysUserAuthorization) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 11)
	s.fieldMap["id"] = s.ID
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["create_user_id"] = s.CreateUserID
	s.fieldMap["display_name"] = s.DisplayName
	s.fieldMap["authorization_key"] = s.AuthorizationKey
	s.fieldMap["authorization_type"] = s.AuthorizationType
	s.fieldMap["authorization_platform"] = s.AuthorizationPlatform
	s.fieldMap["authorization_config"] = s.AuthorizationConfig
	s.fieldMap["status"] = s.Status
	s.fieldMap["expires_at"] = s.ExpiresAt
}

func (s sysUserAuthorization) clone(db *gorm.DB) sysUserAuthorization {
	s.sysUserAuthorizationDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysUserAuthorization) replaceDB(db *gorm.DB) sysUserAuthorization {
	s.sysUserAuthorizationDo.ReplaceDB(db)
	return s
}

type sysUserAuthorizationDo struct{ gen.DO }

func (s sysUserAuthorizationDo) Debug() *sysUserAuthorizationDo {
	return s.withDO(s.DO.Debug())
}

func (s sysUserAuthorizationDo) WithContext(ctx context.Context) *sysUserAuthorizationDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysUserAuthorizationDo) ReadDB() *sysUserAuthorizationDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysUserAuthorizationDo) WriteDB() *sysUserAuthorizationDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysUserAuthorizationDo) Session(config *gorm.Session) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysUserAuthorizationDo) Clauses(conds ...clause.Expression) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysUserAuthorizationDo) Returning(value interface{}, columns ...string) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysUserAuthorizationDo) Not(conds ...gen.Condition) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysUserAuthorizationDo) Or(conds ...gen.Condition) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysUserAuthorizationDo) Select(conds ...field.Expr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysUserAuthorizationDo) Where(conds ...gen.Condition) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysUserAuthorizationDo) Order(conds ...field.Expr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysUserAuthorizationDo) Distinct(cols ...field.Expr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysUserAuthorizationDo) Omit(cols ...field.Expr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysUserAuthorizationDo) Join(table schema.Tabler, on ...field.Expr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysUserAuthorizationDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysUserAuthorizationDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysUserAuthorizationDo) Group(cols ...field.Expr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysUserAuthorizationDo) Having(conds ...gen.Condition) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysUserAuthorizationDo) Limit(limit int) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysUserAuthorizationDo) Offset(offset int) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysUserAuthorizationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysUserAuthorizationDo) Unscoped() *sysUserAuthorizationDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysUserAuthorizationDo) Create(values ...*model.SysUserAuthorization) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysUserAuthorizationDo) CreateInBatches(values []*model.SysUserAuthorization, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysUserAuthorizationDo) Save(values ...*model.SysUserAuthorization) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysUserAuthorizationDo) First() (*model.SysUserAuthorization, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorization), nil
	}
}

func (s sysUserAuthorizationDo) Take() (*model.SysUserAuthorization, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorization), nil
	}
}

func (s sysUserAuthorizationDo) Last() (*model.SysUserAuthorization, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorization), nil
	}
}

func (s sysUserAuthorizationDo) Find() ([]*model.SysUserAuthorization, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysUserAuthorization), err
}

func (s sysUserAuthorizationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysUserAuthorization, err error) {
	buf := make([]*model.SysUserAuthorization, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysUserAuthorizationDo) FindInBatches(result *[]*model.SysUserAuthorization, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysUserAuthorizationDo) Attrs(attrs ...field.AssignExpr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysUserAuthorizationDo) Assign(attrs ...field.AssignExpr) *sysUserAuthorizationDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysUserAuthorizationDo) Joins(fields ...field.RelationField) *sysUserAuthorizationDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysUserAuthorizationDo) Preload(fields ...field.RelationField) *sysUserAuthorizationDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysUserAuthorizationDo) FirstOrInit() (*model.SysUserAuthorization, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorization), nil
	}
}

func (s sysUserAuthorizationDo) FirstOrCreate() (*model.SysUserAuthorization, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysUserAuthorization), nil
	}
}

func (s sysUserAuthorizationDo) FindByPage(offset int, limit int) (result []*model.SysUserAuthorization, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysUserAuthorizationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysUserAuthorizationDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysUserAuthorizationDo) Delete(models ...*model.SysUserAuthorization) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysUserAuthorizationDo) withDO(do gen.Dao) *sysUserAuthorizationDo {
	s.DO = *do.(*gen.DO)
	return s
}
