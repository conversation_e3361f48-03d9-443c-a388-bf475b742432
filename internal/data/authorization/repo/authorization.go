package repo

import (
	"context"
	"gorm.io/gorm/clause"
	pb "vision_hub/api/authorization/v1"
	authorizationBiz "vision_hub/internal/biz/authorization"
	"vision_hub/internal/data/authorization/model"
	"vision_hub/internal/data/authorization/query"
	"vision_hub/internal/data/common"
	"vision_hub/internal/util"
)

// AuthorizationRepo 授权数据访问实现
type AuthorizationRepo struct {
	log  *common.DataLogHelper
	data *common.Data
}

// NewAuthorizationRepo 创建授权数据访问实例
func NewAuthorizationRepo(log *common.DataLogHelper, data *common.Data) authorizationBiz.IAuthorizationRepo {
	return &AuthorizationRepo{
		log:  log,
		data: data,
	}
}

// UpsertAuthorization 根据name存在则更新authorization_config，不存在则新增记录
func (r *AuthorizationRepo) UpsertAuthorization(ctx context.Context, auth *model.SysUserAuthorization) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserAuthorization
	return q.WithContext(ctx).Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "name"},
			{Name: "create_user_id"},
			{Name: "authorization_platform"},
		},
		DoUpdates: clause.AssignmentColumns([]string{"authorization_config"}),
	}).Create(auth)
}

// CreateAuthorization 创建授权
func (r *AuthorizationRepo) CreateAuthorization(ctx context.Context, auth *model.SysUserAuthorization) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserAuthorization
	return q.WithContext(ctx).Create(auth)
}

// UpdateAuthorization 更新授权
func (r *AuthorizationRepo) UpdateAuthorization(ctx context.Context, id int32, updateParams map[string]interface{}) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserAuthorization
	_, err := q.WithContext(ctx).Where(q.ID.Eq(id)).Updates(updateParams)
	return err
}

// GetAuthorizationByID 根据ID获取授权
func (r *AuthorizationRepo) GetAuthorizationByID(ctx context.Context, id int32) (*model.SysUserAuthorization, error) {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserAuthorization
	return q.WithContext(ctx).Where(q.ID.Eq(id)).First()
}

// ListAuthorization 获取授权列表
func (r *AuthorizationRepo) ListAuthorization(ctx context.Context, req *authorizationBiz.ListAuthorizationReq) ([]*model.SysUserAuthorization, *pb.Pagination, error) {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserAuthorization

	// 构建查询
	queryBuilder := q.WithContext(ctx)

	// 添加查询条件
	if req.Name != "" {
		queryBuilder = queryBuilder.Where(q.Name.Like(util.FormatLikeParam(req.Name, true, true)))
	}
	if len(req.AuthorizationPlatform) > 0 {
		queryBuilder = queryBuilder.Where(q.AuthorizationPlatform.In(req.AuthorizationPlatform...))
	}
	if req.CreateUserID > 0 {
		queryBuilder = queryBuilder.Where(q.CreateUserID.Eq(req.CreateUserID))
	}

	// 获取总数
	total, err := queryBuilder.Count()
	if err != nil {
		r.log.WithContext(ctx).Errorf("获取授权总数失败: %v", err)
		return nil, nil, err
	}

	// 分页查询
	offset := (req.PageNum - 1) * req.PageSize
	auths, err := queryBuilder.Offset(int(offset)).Limit(int(req.PageSize)).Find()
	if err != nil {
		r.log.WithContext(ctx).Errorf("查询授权列表失败: %v", err)
		return nil, nil, err
	}

	pagination := &pb.Pagination{
		Total:    int32(total),
		PageNum:  req.PageNum,
		PageSize: req.PageSize,
	}

	return auths, pagination, nil
}

// DeleteAuthorization 删除授权
func (r *AuthorizationRepo) DeleteAuthorization(ctx context.Context, id int32) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserAuthorization
	_, err := q.WithContext(ctx).Where(q.ID.Eq(id)).Delete()
	return err
}

// CheckAuthorizationExists 检查授权是否存在（在指定用户范围内）
func (r *AuthorizationRepo) CheckAuthorizationExists(ctx context.Context, name string, createUserID int32, excludeID int32) (bool, error) {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserAuthorization

	queryBuilder := q.WithContext(ctx).Where(q.Name.Eq(name)).Where(q.CreateUserID.Eq(createUserID))
	if excludeID > 0 {
		queryBuilder = queryBuilder.Where(q.ID.Neq(excludeID))
	}

	count, err := queryBuilder.Count()
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CreateAuthorizationOptLog 创建操作日志
func (r *AuthorizationRepo) CreateAuthorizationOptLog(ctx context.Context, log *model.SysUserAuthorizationOptLog) error {
	q := query.Use(r.data.DbClient.DB(ctx)).SysUserAuthorizationOptLog
	return q.WithContext(ctx).Create(log)
}
