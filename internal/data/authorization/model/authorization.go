package model

import (
	"database/sql"
	"time"
)

// Authorization 授权信息表
type Authorization struct {
	ID                    int32        `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:授权ID" json:"id"`
	CreateTime            sql.NullTime `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`
	UpdateTime            sql.NullTime `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`
	Name                  string       `gorm:"column:name;type:varchar(128);not null;comment:授权名称" json:"name"`
	AuthorizationType     string       `gorm:"column:authorization_type;type:varchar(64);not null;comment:授权类型" json:"authorization_type"`
	AuthorizationPlatform string       `gorm:"column:authorization_platform;type:varchar(64);not null;comment:授权平台" json:"authorization_platform"`
	AuthorizationConfig   string       `gorm:"column:authorization_config;type:json;comment:授权配置" json:"authorization_config"`
	Status                string       `gorm:"column:status;type:varchar(32);not null;default:'active';comment:状态" json:"status"`
	ExpiresAt             *time.Time   `gorm:"column:expires_at;type:datetime;comment:过期时间" json:"expires_at"`
	CreateUserID          int32        `gorm:"column:create_user_id;type:int;not null;comment:创建用户ID" json:"create_user_id"`
}

// TableName Authorization's table name
func (*Authorization) TableName() string {
	return "authorization"
}
