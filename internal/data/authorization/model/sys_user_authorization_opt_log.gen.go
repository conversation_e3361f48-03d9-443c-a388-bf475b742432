// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"database/sql"
	"encoding/json"
)

const TableNameSysUserAuthorizationOptLog = "sys_user_authorization_opt_log"

// SysUserAuthorizationOptLog mapped from table <sys_user_authorization_opt_log>
type SysUserAuthorizationOptLog struct {
	CreateTime      sql.NullTime    `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:录入时间" json:"create_time"`                                        // 录入时间
	UpdateTime      sql.NullTime    `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                        // 更新时间
	OptUser         string          `gorm:"column:opt_user;type:varchar(64);not null;comment:操作人" json:"opt_user"`                                                                      // 操作人
	OptType         string          `gorm:"column:opt_type;type:varchar(64);not null;comment:操作类型：CREATE|UPDATE|DELETE" json:"opt_type"`                                                // 操作类型：CREATE|UPDATE|DELETE
	AuthorizationID int32           `gorm:"column:authorization_id;type:int;not null;index:idx_authorization_id,priority:1;comment:sys_user_authorization主键ID" json:"authorization_id"` // sys_user_authorization主键ID
	OriginalNote    json.RawMessage `gorm:"column:original_note;type:json;not null;comment:操作内容，json格式的数据" json:"original_note"`                                                        // 操作内容，json格式的数据
	UpdateNote      json.RawMessage `gorm:"column:update_note;type:json;not null;comment:操作内容，json格式的数据" json:"update_note"`                                                            // 操作内容，json格式的数据
}

// TableName SysUserAuthorizationOptLog's table name
func (*SysUserAuthorizationOptLog) TableName() string {
	return TableNameSysUserAuthorizationOptLog
}
