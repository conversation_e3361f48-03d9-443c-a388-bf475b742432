// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"database/sql"
	"encoding/json"
)

const TableNameSysUserAuthorization = "sys_user_authorization"

// SysUserAuthorization mapped from table <sys_user_authorization>
type SysUserAuthorization struct {
	ID                    int32           `gorm:"column:id;type:int;primaryKey;autoIncrement:true;comment:自增主键" json:"id"`                                                                                                        // 自增主键
	CreateTime            sql.NullTime    `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                                                            // 创建时间
	UpdateTime            sql.NullTime    `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                                                            // 更新时间
	CreateUserID          int32           `gorm:"column:create_user_id;type:int;not null;uniqueIndex:idx_create_user_id_authorization_key,priority:1;index:idx_create_user_id_plat,priority:1;comment:创建人" json:"create_user_id"` // 创建人
	DisplayName           string          `gorm:"column:display_name;type:varchar(64);not null;comment:名称" json:"display_name"`                                                                                                   // 名称
	AuthorizationKey      string          `gorm:"column:authorization_key;type:varchar(128);not null;uniqueIndex:idx_create_user_id_authorization_key,priority:2;comment:第三方平台唯一信息" json:"authorization_key"`                     // 第三方平台唯一信息
	AuthorizationType     string          `gorm:"column:authorization_type;type:varchar(32);not null;comment:授权类型:OAuth2" json:"authorization_type"`                                                                              // 授权类型:OAuth2
	AuthorizationPlatform string          `gorm:"column:authorization_platform;type:varchar(32);not null;index:idx_create_user_id_plat,priority:2;comment:授权平台" json:"authorization_platform"`                                    // 授权平台
	AuthorizationConfig   json.RawMessage `gorm:"column:authorization_config;type:json;not null;comment:授权配置" json:"authorization_config"`                                                                                        // 授权配置
	Status                string          `gorm:"column:status;type:varchar(32);not null;default:ACTIVE;comment:授权状态：ACTIVE|DISABLE|EXPIRED" json:"status"`                                                                       // 授权状态：ACTIVE|DISABLE|EXPIRED
	ExpiresAt             sql.NullTime    `gorm:"column:expires_at;type:datetime;comment:过期时间" json:"expires_at"`                                                                                                                 // 过期时间
}

// TableName SysUserAuthorization's table name
func (*SysUserAuthorization) TableName() string {
	return TableNameSysUserAuthorization
}
