// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: internal/conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BizConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Client      *Client      `protobuf:"bytes,1,opt,name=client,proto3" json:"client,omitempty"`
	RocketMq    *RocketMq    `protobuf:"bytes,2,opt,name=rocketMq,proto3" json:"rocketMq,omitempty"`
	LoginConf   *LoginConf   `protobuf:"bytes,3,opt,name=login_conf,json=loginConf,proto3" json:"login_conf,omitempty"`
	TencentOss  *TencentOss  `protobuf:"bytes,4,opt,name=tencent_oss,json=tencentOss,proto3" json:"tencent_oss,omitempty"`
	Feishu      *Feishu      `protobuf:"bytes,5,opt,name=feishu,proto3" json:"feishu,omitempty"`
	TiktokVideo *TiktokVideo `protobuf:"bytes,6,opt,name=tiktokVideo,proto3" json:"tiktokVideo,omitempty"`
	SysConf     *SysConf     `protobuf:"bytes,7,opt,name=sys_conf,json=sysConf,proto3" json:"sys_conf,omitempty"`
	TiktokToken *TiktokToken `protobuf:"bytes,8,opt,name=tiktok_token,json=tiktokToken,proto3" json:"tiktok_token,omitempty"`
	TiktokOauth *TiktokOAuth `protobuf:"bytes,9,opt,name=tiktok_oauth,json=tiktokOauth,proto3" json:"tiktok_oauth,omitempty"`
}

func (x *BizConf) Reset() {
	*x = BizConf{}
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BizConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizConf) ProtoMessage() {}

func (x *BizConf) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizConf.ProtoReflect.Descriptor instead.
func (*BizConf) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *BizConf) GetClient() *Client {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *BizConf) GetRocketMq() *RocketMq {
	if x != nil {
		return x.RocketMq
	}
	return nil
}

func (x *BizConf) GetLoginConf() *LoginConf {
	if x != nil {
		return x.LoginConf
	}
	return nil
}

func (x *BizConf) GetTencentOss() *TencentOss {
	if x != nil {
		return x.TencentOss
	}
	return nil
}

func (x *BizConf) GetFeishu() *Feishu {
	if x != nil {
		return x.Feishu
	}
	return nil
}

func (x *BizConf) GetTiktokVideo() *TiktokVideo {
	if x != nil {
		return x.TiktokVideo
	}
	return nil
}

func (x *BizConf) GetSysConf() *SysConf {
	if x != nil {
		return x.SysConf
	}
	return nil
}

func (x *BizConf) GetTiktokToken() *TiktokToken {
	if x != nil {
		return x.TiktokToken
	}
	return nil
}

func (x *BizConf) GetTiktokOauth() *TiktokOAuth {
	if x != nil {
		return x.TiktokOauth
	}
	return nil
}

type Client struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Funds        string        `protobuf:"bytes,1,opt,name=funds,proto3" json:"funds,omitempty"`
	PayCenter    *PayCenter    `protobuf:"bytes,2,opt,name=pay_center,json=payCenter,proto3" json:"pay_center,omitempty"`
	ClientPortal *ClientPortal `protobuf:"bytes,3,opt,name=client_portal,json=clientPortal,proto3" json:"client_portal,omitempty"`
}

func (x *Client) Reset() {
	*x = Client{}
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Client) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Client) ProtoMessage() {}

func (x *Client) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Client.ProtoReflect.Descriptor instead.
func (*Client) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Client) GetFunds() string {
	if x != nil {
		return x.Funds
	}
	return ""
}

func (x *Client) GetPayCenter() *PayCenter {
	if x != nil {
		return x.PayCenter
	}
	return nil
}

func (x *Client) GetClientPortal() *ClientPortal {
	if x != nil {
		return x.ClientPortal
	}
	return nil
}

type RocketMq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopicTaskName string `protobuf:"bytes,1,opt,name=topic_task_name,json=topicTaskName,proto3" json:"topic_task_name,omitempty"`
	GroupTaskName string `protobuf:"bytes,2,opt,name=group_task_name,json=groupTaskName,proto3" json:"group_task_name,omitempty"`
	TagTaskName   string `protobuf:"bytes,3,opt,name=tag_task_name,json=tagTaskName,proto3" json:"tag_task_name,omitempty"`
}

func (x *RocketMq) Reset() {
	*x = RocketMq{}
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RocketMq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RocketMq) ProtoMessage() {}

func (x *RocketMq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RocketMq.ProtoReflect.Descriptor instead.
func (*RocketMq) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *RocketMq) GetTopicTaskName() string {
	if x != nil {
		return x.TopicTaskName
	}
	return ""
}

func (x *RocketMq) GetGroupTaskName() string {
	if x != nil {
		return x.GroupTaskName
	}
	return ""
}

func (x *RocketMq) GetTagTaskName() string {
	if x != nil {
		return x.TagTaskName
	}
	return ""
}

type PayCenter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoint string               `protobuf:"bytes,1,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	Timeout  *durationpb.Duration `protobuf:"bytes,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *PayCenter) Reset() {
	*x = PayCenter{}
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayCenter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayCenter) ProtoMessage() {}

func (x *PayCenter) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayCenter.ProtoReflect.Descriptor instead.
func (*PayCenter) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{3}
}

func (x *PayCenter) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *PayCenter) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type ClientPortal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoint string               `protobuf:"bytes,1,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	Timeout  *durationpb.Duration `protobuf:"bytes,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *ClientPortal) Reset() {
	*x = ClientPortal{}
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPortal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPortal) ProtoMessage() {}

func (x *ClientPortal) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPortal.ProtoReflect.Descriptor instead.
func (*ClientPortal) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{4}
}

func (x *ClientPortal) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *ClientPortal) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type LoginConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Secret string `protobuf:"bytes,1,opt,name=secret,proto3" json:"secret,omitempty"`
}

func (x *LoginConf) Reset() {
	*x = LoginConf{}
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginConf) ProtoMessage() {}

func (x *LoginConf) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginConf.ProtoReflect.Descriptor instead.
func (*LoginConf) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{5}
}

func (x *LoginConf) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

type TencentOss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BucketUrl     string `protobuf:"bytes,1,opt,name=bucket_url,json=bucketUrl,proto3" json:"bucket_url,omitempty"`
	SecretId      string `protobuf:"bytes,2,opt,name=secret_id,json=secretId,proto3" json:"secret_id,omitempty"`
	SecretKey     string `protobuf:"bytes,3,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	Region        string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Scheme        string `protobuf:"bytes,5,opt,name=scheme,proto3" json:"scheme,omitempty"`
	Appid         string `protobuf:"bytes,6,opt,name=appid,proto3" json:"appid,omitempty"`
	BucketName    string `protobuf:"bytes,7,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	Prefix        string `protobuf:"bytes,8,opt,name=prefix,proto3" json:"prefix,omitempty"`
	BucketBaseUrl string `protobuf:"bytes,9,opt,name=bucket_base_url,json=bucketBaseUrl,proto3" json:"bucket_base_url,omitempty"`
	CdnName       string `protobuf:"bytes,10,opt,name=cdn_name,json=cdnName,proto3" json:"cdn_name,omitempty"`
}

func (x *TencentOss) Reset() {
	*x = TencentOss{}
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TencentOss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TencentOss) ProtoMessage() {}

func (x *TencentOss) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TencentOss.ProtoReflect.Descriptor instead.
func (*TencentOss) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{6}
}

func (x *TencentOss) GetBucketUrl() string {
	if x != nil {
		return x.BucketUrl
	}
	return ""
}

func (x *TencentOss) GetSecretId() string {
	if x != nil {
		return x.SecretId
	}
	return ""
}

func (x *TencentOss) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *TencentOss) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *TencentOss) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *TencentOss) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *TencentOss) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *TencentOss) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *TencentOss) GetBucketBaseUrl() string {
	if x != nil {
		return x.BucketBaseUrl
	}
	return ""
}

func (x *TencentOss) GetCdnName() string {
	if x != nil {
		return x.CdnName
	}
	return ""
}

type Feishu struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginAppId            string `protobuf:"bytes,1,opt,name=login_app_id,json=loginAppId,proto3" json:"login_app_id,omitempty"`
	LoginAppSecret        string `protobuf:"bytes,2,opt,name=login_app_secret,json=loginAppSecret,proto3" json:"login_app_secret,omitempty"`
	GetAppAccessTokenUrl  string `protobuf:"bytes,3,opt,name=get_app_access_token_url,json=getAppAccessTokenUrl,proto3" json:"get_app_access_token_url,omitempty"`
	GetUserAccessTokenUrl string `protobuf:"bytes,4,opt,name=get_user_access_token_url,json=getUserAccessTokenUrl,proto3" json:"get_user_access_token_url,omitempty"`
	GetUserInfoUrl        string `protobuf:"bytes,5,opt,name=get_user_info_url,json=getUserInfoUrl,proto3" json:"get_user_info_url,omitempty"`
	BusinessChatId        string `protobuf:"bytes,6,opt,name=business_chat_id,json=businessChatId,proto3" json:"business_chat_id,omitempty"`
}

func (x *Feishu) Reset() {
	*x = Feishu{}
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Feishu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feishu) ProtoMessage() {}

func (x *Feishu) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feishu.ProtoReflect.Descriptor instead.
func (*Feishu) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{7}
}

func (x *Feishu) GetLoginAppId() string {
	if x != nil {
		return x.LoginAppId
	}
	return ""
}

func (x *Feishu) GetLoginAppSecret() string {
	if x != nil {
		return x.LoginAppSecret
	}
	return ""
}

func (x *Feishu) GetGetAppAccessTokenUrl() string {
	if x != nil {
		return x.GetAppAccessTokenUrl
	}
	return ""
}

func (x *Feishu) GetGetUserAccessTokenUrl() string {
	if x != nil {
		return x.GetUserAccessTokenUrl
	}
	return ""
}

func (x *Feishu) GetGetUserInfoUrl() string {
	if x != nil {
		return x.GetUserInfoUrl
	}
	return ""
}

func (x *Feishu) GetBusinessChatId() string {
	if x != nil {
		return x.BusinessChatId
	}
	return ""
}

type TiktokVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseUrl      string `protobuf:"bytes,1,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	Token        string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	AdvertiserId string `protobuf:"bytes,3,opt,name=advertiser_id,json=advertiserId,proto3" json:"advertiser_id,omitempty"`
}

func (x *TiktokVideo) Reset() {
	*x = TiktokVideo{}
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TiktokVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TiktokVideo) ProtoMessage() {}

func (x *TiktokVideo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TiktokVideo.ProtoReflect.Descriptor instead.
func (*TiktokVideo) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{8}
}

func (x *TiktokVideo) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *TiktokVideo) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TiktokVideo) GetAdvertiserId() string {
	if x != nil {
		return x.AdvertiserId
	}
	return ""
}

type SysConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Proxy string `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`
	Env   string `protobuf:"bytes,2,opt,name=env,proto3" json:"env,omitempty"`
}

func (x *SysConf) Reset() {
	*x = SysConf{}
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SysConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SysConf) ProtoMessage() {}

func (x *SysConf) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SysConf.ProtoReflect.Descriptor instead.
func (*SysConf) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{9}
}

func (x *SysConf) GetProxy() string {
	if x != nil {
		return x.Proxy
	}
	return ""
}

func (x *SysConf) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

type TiktokToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bf   string `protobuf:"bytes,1,opt,name=bf,proto3" json:"bf,omitempty"`
	Biz  string `protobuf:"bytes,2,opt,name=biz,proto3" json:"biz,omitempty"`
	Biz1 string `protobuf:"bytes,3,opt,name=biz1,proto3" json:"biz1,omitempty"`
	Biz2 string `protobuf:"bytes,4,opt,name=biz2,proto3" json:"biz2,omitempty"`
	Biz3 string `protobuf:"bytes,5,opt,name=biz3,proto3" json:"biz3,omitempty"`
	Biz4 string `protobuf:"bytes,6,opt,name=biz4,proto3" json:"biz4,omitempty"`
	Biz5 string `protobuf:"bytes,7,opt,name=biz5,proto3" json:"biz5,omitempty"`
	Biz6 string `protobuf:"bytes,8,opt,name=biz6,proto3" json:"biz6,omitempty"`
	Biz7 string `protobuf:"bytes,9,opt,name=biz7,proto3" json:"biz7,omitempty"`
}

func (x *TiktokToken) Reset() {
	*x = TiktokToken{}
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TiktokToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TiktokToken) ProtoMessage() {}

func (x *TiktokToken) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TiktokToken.ProtoReflect.Descriptor instead.
func (*TiktokToken) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{10}
}

func (x *TiktokToken) GetBf() string {
	if x != nil {
		return x.Bf
	}
	return ""
}

func (x *TiktokToken) GetBiz() string {
	if x != nil {
		return x.Biz
	}
	return ""
}

func (x *TiktokToken) GetBiz1() string {
	if x != nil {
		return x.Biz1
	}
	return ""
}

func (x *TiktokToken) GetBiz2() string {
	if x != nil {
		return x.Biz2
	}
	return ""
}

func (x *TiktokToken) GetBiz3() string {
	if x != nil {
		return x.Biz3
	}
	return ""
}

func (x *TiktokToken) GetBiz4() string {
	if x != nil {
		return x.Biz4
	}
	return ""
}

func (x *TiktokToken) GetBiz5() string {
	if x != nil {
		return x.Biz5
	}
	return ""
}

func (x *TiktokToken) GetBiz6() string {
	if x != nil {
		return x.Biz6
	}
	return ""
}

func (x *TiktokToken) GetBiz7() string {
	if x != nil {
		return x.Biz7
	}
	return ""
}

type TiktokOAuth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Secret  string `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty"`
	BaseUrl string `protobuf:"bytes,3,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
}

func (x *TiktokOAuth) Reset() {
	*x = TiktokOAuth{}
	mi := &file_internal_conf_conf_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TiktokOAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TiktokOAuth) ProtoMessage() {}

func (x *TiktokOAuth) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TiktokOAuth.ProtoReflect.Descriptor instead.
func (*TiktokOAuth) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{11}
}

func (x *TiktokOAuth) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TiktokOAuth) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *TiktokOAuth) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

var File_internal_conf_conf_proto protoreflect.FileDescriptor

var file_internal_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe5, 0x03, 0x0a, 0x07, 0x42, 0x69, 0x7a, 0x43, 0x6f,
	0x6e, 0x66, 0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x30,
	0x0a, 0x08, 0x72, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x4d, 0x71, 0x52, 0x08, 0x72, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x71,
	0x12, 0x34, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x09, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x37, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e,
	0x74, 0x5f, 0x6f, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74,
	0x4f, 0x73, 0x73, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x73, 0x73, 0x12,
	0x2a, 0x0a, 0x06, 0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x65, 0x69,
	0x73, 0x68, 0x75, 0x52, 0x06, 0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x12, 0x39, 0x0a, 0x0b, 0x74,
	0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x69,
	0x6b, 0x74, 0x6f, 0x6b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x0b, 0x74, 0x69, 0x6b, 0x74, 0x6f,
	0x6b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x79, 0x73, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x79, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x07, 0x73,
	0x79, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x3a, 0x0a, 0x0c, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x0b, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x3a, 0x0a, 0x0c, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x5f, 0x6f, 0x61, 0x75,
	0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x4f, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x0b, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x22, 0x93,
	0x01, 0x0a, 0x06, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x75, 0x6e,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x12,
	0x34, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x50, 0x61, 0x79, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x52, 0x09, 0x70, 0x61, 0x79, 0x43,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x50, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x22, 0x7e, 0x0a, 0x08, 0x52, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x71,
	0x12, 0x26, 0x0a, 0x0f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x54, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x0d, 0x74, 0x61, 0x67, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x61, 0x67, 0x54, 0x61, 0x73, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5c, 0x0a, 0x09, 0x50, 0x61, 0x79, 0x43, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x33, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x22, 0x5f, 0x0a, 0x0c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x33,
	0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x22, 0x23, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0xa9, 0x02, 0x0a, 0x0a, 0x54, 0x65, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x4f, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b,
	0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65,
	0x66, 0x69, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69,
	0x78, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x42, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x64, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x64, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9b, 0x02, 0x0a, 0x06, 0x46, 0x65, 0x69, 0x73, 0x68, 0x75, 0x12,
	0x20, 0x0a, 0x0c, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x41, 0x70, 0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x36, 0x0a, 0x18, 0x67,
	0x65, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x67,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x38, 0x0a, 0x19, 0x67, 0x65, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a,
	0x11, 0x67, 0x65, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x74,
	0x49, 0x64, 0x22, 0x63, 0x0a, 0x0b, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x07, 0x53, 0x79, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x22, 0xbb, 0x01, 0x0a, 0x0b, 0x54,
	0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x66,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x62, 0x66, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69,
	0x7a, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x7a, 0x12, 0x12, 0x0a, 0x04,
	0x62, 0x69, 0x7a, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x69, 0x7a, 0x31,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x69, 0x7a, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x62, 0x69, 0x7a, 0x32, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x69, 0x7a, 0x33, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x62, 0x69, 0x7a, 0x33, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x69, 0x7a, 0x34,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x69, 0x7a, 0x34, 0x12, 0x12, 0x0a, 0x04,
	0x62, 0x69, 0x7a, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x69, 0x7a, 0x35,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x69, 0x7a, 0x36, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x62, 0x69, 0x7a, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x69, 0x7a, 0x37, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x62, 0x69, 0x7a, 0x37, 0x22, 0x57, 0x0a, 0x0b, 0x54, 0x69, 0x6b, 0x74,
	0x6f, 0x6b, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72,
	0x6c, 0x42, 0x1b, 0x5a, 0x19, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x3b, 0x63, 0x6f, 0x6e, 0x66, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_internal_conf_conf_proto_rawDescOnce sync.Once
	file_internal_conf_conf_proto_rawDescData = file_internal_conf_conf_proto_rawDesc
)

func file_internal_conf_conf_proto_rawDescGZIP() []byte {
	file_internal_conf_conf_proto_rawDescOnce.Do(func() {
		file_internal_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_conf_conf_proto_rawDescData)
	})
	return file_internal_conf_conf_proto_rawDescData
}

var file_internal_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_internal_conf_conf_proto_goTypes = []any{
	(*BizConf)(nil),             // 0: kratos.api.BizConf
	(*Client)(nil),              // 1: kratos.api.Client
	(*RocketMq)(nil),            // 2: kratos.api.RocketMq
	(*PayCenter)(nil),           // 3: kratos.api.PayCenter
	(*ClientPortal)(nil),        // 4: kratos.api.ClientPortal
	(*LoginConf)(nil),           // 5: kratos.api.LoginConf
	(*TencentOss)(nil),          // 6: kratos.api.TencentOss
	(*Feishu)(nil),              // 7: kratos.api.Feishu
	(*TiktokVideo)(nil),         // 8: kratos.api.TiktokVideo
	(*SysConf)(nil),             // 9: kratos.api.SysConf
	(*TiktokToken)(nil),         // 10: kratos.api.TiktokToken
	(*TiktokOAuth)(nil),         // 11: kratos.api.TiktokOAuth
	(*durationpb.Duration)(nil), // 12: google.protobuf.Duration
}
var file_internal_conf_conf_proto_depIdxs = []int32{
	1,  // 0: kratos.api.BizConf.client:type_name -> kratos.api.Client
	2,  // 1: kratos.api.BizConf.rocketMq:type_name -> kratos.api.RocketMq
	5,  // 2: kratos.api.BizConf.login_conf:type_name -> kratos.api.LoginConf
	6,  // 3: kratos.api.BizConf.tencent_oss:type_name -> kratos.api.TencentOss
	7,  // 4: kratos.api.BizConf.feishu:type_name -> kratos.api.Feishu
	8,  // 5: kratos.api.BizConf.tiktokVideo:type_name -> kratos.api.TiktokVideo
	9,  // 6: kratos.api.BizConf.sys_conf:type_name -> kratos.api.SysConf
	10, // 7: kratos.api.BizConf.tiktok_token:type_name -> kratos.api.TiktokToken
	11, // 8: kratos.api.BizConf.tiktok_oauth:type_name -> kratos.api.TiktokOAuth
	3,  // 9: kratos.api.Client.pay_center:type_name -> kratos.api.PayCenter
	4,  // 10: kratos.api.Client.client_portal:type_name -> kratos.api.ClientPortal
	12, // 11: kratos.api.PayCenter.timeout:type_name -> google.protobuf.Duration
	12, // 12: kratos.api.ClientPortal.timeout:type_name -> google.protobuf.Duration
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_internal_conf_conf_proto_init() }
func file_internal_conf_conf_proto_init() {
	if File_internal_conf_conf_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_conf_conf_proto_goTypes,
		DependencyIndexes: file_internal_conf_conf_proto_depIdxs,
		MessageInfos:      file_internal_conf_conf_proto_msgTypes,
	}.Build()
	File_internal_conf_conf_proto = out.File
	file_internal_conf_conf_proto_rawDesc = nil
	file_internal_conf_conf_proto_goTypes = nil
	file_internal_conf_conf_proto_depIdxs = nil
}
