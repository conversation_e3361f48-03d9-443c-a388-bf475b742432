syntax = "proto3";
package kratos.api;

option go_package = "server/internal/conf;conf";
import "google/protobuf/duration.proto";

message BizConf {
  Client  client = 1;
  RocketMq rocketMq = 2;
  LoginConf login_conf = 3;
  TencentOss tencent_oss = 4;
  Feishu feishu = 5;
  TiktokVideo tiktokVideo = 6;
  SysConf sys_conf = 7;
  TiktokToken tiktok_token = 8;
  TiktokOAuth tiktok_oauth = 9;
}


message  Client{
  string funds = 1;
  PayCenter  pay_center = 2;
  ClientPortal client_portal = 3;
}

message RocketMq {
  string topic_task_name = 1;
  string group_task_name = 2;
  string tag_task_name = 3;
}

message PayCenter {
  string endpoint = 1;
  google.protobuf.Duration timeout = 2;
}

message ClientPortal {
  string endpoint = 1;
  google.protobuf.Duration timeout = 2;
}

message LoginConf {
  string secret = 1;
}

message TencentOss {
  string bucket_url = 1;
  string secret_id = 2;
  string secret_key = 3;
  string region = 4;
  string scheme = 5;
  string appid = 6;
  string bucket_name = 7;
  string prefix = 8;
  string bucket_base_url = 9;
  string cdn_name = 10;
}

message Feishu {
  string login_app_id = 1;
  string login_app_secret = 2;
  string get_app_access_token_url = 3;
  string get_user_access_token_url = 4;
  string get_user_info_url = 5;
  string business_chat_id = 6;
}

message TiktokVideo {
  string base_url = 1;
  string token = 2;
  string advertiser_id = 3;
}

message SysConf {
  string proxy = 1;
  string env = 2;
}

message TiktokToken {
  string bf = 1;
  string biz = 2;
  string biz1 = 3;
  string biz2 = 4;
  string biz3 = 5;
  string biz4 = 6;
  string biz5 = 7;
  string biz6 = 8;
  string biz7 = 9;
}

message TiktokOAuth {
  string app_id = 1;
  string secret = 2;
  string base_url = 3;
}