package authorization

import (
	kxerrors "git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxerror"
)

var (
	// 授权配置相关错误 (70201xxx)
	AuthConfigInvalidJSON    = kxerrors.NewMsError(1, 70201001, "AUTH_CONFIG_INVALID_JSON", "授权配置JSON格式错误")
	AuthConfigMissingField   = kxerrors.NewMsError(1, ********, "AUTH_CONFIG_MISSING_FIELD", "授权配置缺少必填字段")
	AuthConfigValidationFail = kxerrors.NewMsError(1, ********, "AUTH_CONFIG_VALIDATION_FAIL", "授权配置验证失败")

	// 平台相关错误 (70202xxx)
	UnsupportedAuthPlatform = kxerrors.NewMsError(1, ********, "UNSUPPORTED_AUTH_PLATFORM", "不支持的授权平台")

	// Impact平台错误 (70203xxx)
	ImpactAuthFail           = kxerrors.NewMsError(1, ********, "IMPACT_AUTH_FAIL", "Impact授权验证失败")
	ImpactMissingAccountSID  = kxerrors.NewMsError(1, ********, "IMPACT_MISSING_ACCOUNT_SID", "Impact配置缺少account_sid")
	ImpactMissingAuthToken   = kxerrors.NewMsError(1, ********, "IMPACT_MISSING_AUTH_TOKEN", "Impact配置缺少auth_token")
	ImpactAPIConnectionFail  = kxerrors.NewMsError(1, ********, "IMPACT_API_CONNECTION_FAIL", "Impact API连接失败")
	ImpactInvalidCredentials = kxerrors.NewMsError(1, ********, "IMPACT_INVALID_CREDENTIALS", "Impact授权凭据无效")

	// TikTok平台错误 (70204xxx)
	TikTokAuthFail            = kxerrors.NewMsError(1, ********, "TIKTOK_AUTH_FAIL", "TikTok授权验证失败")
	TikTokMissingAuthCode     = kxerrors.NewMsError(1, ********, "TIKTOK_MISSING_AUTH_CODE", "TikTok配置缺少auth_code")
	TikTokAPIConnectionFail   = kxerrors.NewMsError(1, ********, "TIKTOK_API_CONNECTION_FAIL", "TikTok API连接失败")
	TikTokTokenExchangeFail   = kxerrors.NewMsError(1, ********, "TIKTOK_TOKEN_EXCHANGE_FAIL", "TikTok授权码换取token失败")
	TikTokInvalidAuthCode     = kxerrors.NewMsError(1, ********, "TIKTOK_INVALID_AUTH_CODE", "TikTok授权码无效")
	TikTokConfigMissingAppID  = kxerrors.NewMsError(1, 70204006, "TIKTOK_CONFIG_MISSING_APP_ID", "TikTok配置缺少app_id")
	TikTokConfigMissingSecret = kxerrors.NewMsError(1, 70204007, "TIKTOK_CONFIG_MISSING_SECRET", "TikTok配置缺少secret")

	// 授权管理相关错误 (70205xxx)
	AuthorizationNameExists   = kxerrors.NewMsError(1, 70205001, "AUTHORIZATION_NAME_EXISTS", "授权名称已存在")
	AuthorizationNotFound     = kxerrors.NewMsError(1, 70205002, "AUTHORIZATION_NOT_FOUND", "授权不存在")
	AuthorizationCreateFail   = kxerrors.NewMsError(1, 70205003, "AUTHORIZATION_CREATE_FAIL", "创建授权失败")
	AuthorizationUpdateFail   = kxerrors.NewMsError(1, 70205004, "AUTHORIZATION_UPDATE_FAIL", "更新授权失败")
	AuthorizationDeleteFail   = kxerrors.NewMsError(1, 70205005, "AUTHORIZATION_DELETE_FAIL", "删除授权失败")
	AuthorizationQueryFail    = kxerrors.NewMsError(1, 70205006, "AUTHORIZATION_QUERY_FAIL", "查询授权失败")
	AuthorizationNoPermission = kxerrors.NewMsError(1, 70205007, "AUTHORIZATION_NO_PERMISSION", "您无权限操作该授权")
)
