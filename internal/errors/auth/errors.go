package auth

import (
	kxerrors "git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxerror"
)

var (
	QueryEmailFail     = kxerrors.NewMsError(1, 70101001, "QUERY_ERROR", "查询邮箱失败")
	QueryUserFail      = kxerrors.NewMsError(1, 70101008, "QUERY_USER_FAIL", "查询用户失败")
	ExistEmailFail     = kxerrors.NewMsError(1, 70102001, "EXIST_EMAIL", "邮箱已存在")
	PasswdEncryptFail  = kxerrors.NewMsError(1, 70102002, "PASSWD_ENCRYPT_FAIL", "密码加密失败")
	DataCreateUserFail = kxerrors.NewMsError(1, 70102003, "CREATE_USER_FAIL", "创建用户失败")
	QueryUserGroupFail = kxerrors.NewMsError(1, 70101010, "QUERY_USER_GROUP_FAIL", "查询用户组失败")
	DataUpdateUserFail = kxerrors.NewMsError(1, 70102010, "UPDATE_USER_FAIL", "更新用户失败")
	DataDeleteUserFail = kxerrors.NewMsError(1, 70102011, "DELETE_USER_FAIL", "删除用户失败")
	UserNotPermission  = kxerrors.NewMsError(1, 70102012, "USER_NOT_PERMISSION", "用户没有权限")
	QueryMenuTreeFail  = kxerrors.NewMsError(1, 70102013, "QUERY_MENU_TREE_FAIL", "查询菜单树失败")

	UserPageListFail = kxerrors.NewMsError(1, 70101006, "USER_PAGE_LIST_FAIL", "用户分页查询失败")
	UserSelectFail   = kxerrors.NewMsError(1, 70101009, "USER_SELECT_FAIL", "用户查询失败")

	RegisterFirstNameRequired = kxerrors.NewMsError(1, 70103006, "FIRST_NAME_REQUIRED", "名不能为空")
	RegisterLastNameRequired  = kxerrors.NewMsError(1, 70103007, "LAST_NAME_REQUIRED", "姓不能为空")
	RegisterPhoneRequired     = kxerrors.NewMsError(1, 70103008, "PHONE_REQUIRED", "手机号不能为空")

	LoginUserNotExist      = kxerrors.NewMsError(1, 70102004, "USER_NOT_EXIST", "用户不存在")
	LoginPasswdFail        = kxerrors.NewMsError(1, 70102005, "PASSWD_FAIL", "密码错误")
	LoginGetRoleFail       = kxerrors.NewMsError(1, 70102006, "GET_ROLE_FAIL", "获取用户角色失败")
	LoginGenerateTokenFail = kxerrors.NewMsError(1, 70102007, "GENERATE_TOKEN_FAIL", "生成token失败")
	LoginUserEmailRequired = kxerrors.NewMsError(1, 70103001, "USER_EMAIL_REQUIRED", "用户邮箱不能为空")
	LoginPasswdRequired    = kxerrors.NewMsError(1, 70103002, "PASSWD_REQUIRED", "密码不能为空")
	LoginCodeRequired      = kxerrors.NewMsError(1, 70103011, "CODE_REQUIRED", "飞书登录code不能为空")
	LoginSourceUrlRequired = kxerrors.NewMsError(1, 70103012, "SOURCE_URL_REQUIRED", "飞书登录source_url不能为空")

	FeishuLoginGetAppAccessTokenFail  = kxerrors.NewMsError(1, 70104001, "FEISHU_LOGIN_GET_APP_ACCESS_TOKEN_FAIL", "获取飞书登录凭证失败")
	FeishuLoginGetUserAccessTokenFail = kxerrors.NewMsError(1, 70104002, "FEISHU_LOGIN_GET_USER_ACCESS_TOKEN_FAIL", "获取飞书用户访问凭证失败")
	FeishuLoginGetUserInfoFail        = kxerrors.NewMsError(1, 70104003, "FEISHU_LOGIN_GET_USER_INFO_FAIL", "获取飞书用户信息失败")
	FeishuLoginGetUserEmailFail       = kxerrors.NewMsError(1, 70104004, "FEISHU_LOGIN_GET_USER_EMAIL_FAIL", "获取飞书用户邮箱失败")

	OrgNameRequired     = kxerrors.NewMsError(1, 70103003, "ORG_NAME_REQUIRED", "部门名称不能为空")
	OrgParentIDRequired = kxerrors.NewMsError(1, 70103004, "ORG_PARENT_ID_REQUIRED", "部门父ID不能为空")
	OrgAddFail          = kxerrors.NewMsError(1, 70101002, "ORG_ADD_FAIL", "部门添加失败")
	OrgIDRequired       = kxerrors.NewMsError(1, 70103005, "ORG_ID_REQUIRED", "部门ID不能为空")
	OrgUpdateFail       = kxerrors.NewMsError(1, 70101003, "ORG_UPDATE_FAIL", "部门更新失败")
	OrgNameQueryFail    = kxerrors.NewMsError(1, 70101004, "ORG_NAME_QUERY_FAIL", "部门名称查询失败")
	OrgNameExistFail    = kxerrors.NewMsError(1, 70102008, "ORG_NAME_EXIST_FAIL", "部门名称已存在")
	OrgQueryFail        = kxerrors.NewMsError(1, 70101005, "ORG_QUERY_FAIL", "部门查询失败")
	OryNotExist         = kxerrors.NewMsError(1, 70101011, "ORY_IS_EMPTY", "部门不存在")
	OrgDeleteFail       = kxerrors.NewMsError(1, 70101012, "ORG_DELETE_FAIL", "部门删除失败")

	ResetPasswordConfirmPasswdRequired = kxerrors.NewMsError(1, 70103009, "RESET_PASSWORD_CONFIRM_PASSWD_REQUIRED", "确认密码不能为空")
	ResetPasswordPasswdNotEqual        = kxerrors.NewMsError(1, 70103010, "RESET_PASSWORD_PASSWD_NOT_EQUAL", "两次密码不一致")
	ResetPasswordFail                  = kxerrors.NewMsError(1, 70101007, "RESET_PASSWORD_FAIL", "重置密码失败")

	GetUidFromTokenFail  = kxerrors.NewMsError(1, 70102009, "GET_UID_FROM_TOKEN_FAIL", "获取uid失败")
	OrgHasUserDeleteFail = kxerrors.NewMsError(1, 70101013, "ORG_HAS_USER_DELETE_FAIL", "当前部门及子部门存在用户，删除失败")

	// GroupNameExists 角色相关错误定义
	GroupNameExists = kxerrors.NewMsError(1, 70101014, "GROUP_NAME_EXISTS", "角色名称已存在")
	GroupCodeExists = kxerrors.NewMsError(1, 70101015, "GROUP_CODE_EXISTS", "角色Code已存在")
)
