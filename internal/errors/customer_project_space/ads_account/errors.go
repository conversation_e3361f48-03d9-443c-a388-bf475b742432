package ads_account

import (
	"fmt"
	kxerrors "git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxerror"
	"strings"
)

var (
	AdsAccountExistsErr    = kxerrors.NewMsError(1, ********, "ADS_ACCOUNT_EXIST_ERR", "下列%v账号已关联")
	AdsAccountNotExistsErr = kxerrors.NewMsError(1, ********, "ADS_ACCOUNT_NOT_EXIST_ERR", "下列%v账号不存在")
)

func NewAdsAccountExistsErr(adsAccounts []string) *kxerrors.MsError {
	msg := fmt.Sprintf("下列账号已关联\n%s", strings.Join(adsAccounts, "\n"))
	return kxerrors.NewMsError(1, ********, "ADS_ACCOUNT_EXIST_ERR", msg)
}

func NewAdsAccountNotExistsErr(adsAccounts []string) *kxerrors.MsError {
	msg := fmt.Sprintf("下列账号不存在于当前项目中\n%s", strings.Join(adsAccounts, "\n"))
	return kxerrors.NewMsError(1, ********, "ADS_ACCOUNT_NOT_EXIST_ERR", msg)
}
