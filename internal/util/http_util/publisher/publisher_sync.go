package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
)

type Response struct {
	ErrID   int    `json:"errId"`
	ErrCode string `json:"errCode"`
	ErrMsg  string `json:"errMsg"`
}

func main() {

	url := "http://**********:8888/v1/resource/publisher_add"
	method := "POST"

	// 读取文件
	jsonData, err := os.ReadFile("publisher.json")
	if err != nil {
		fmt.Printf("读取文件失败: %v\n", err)
		return
	}
	// 解析JSON数组
	var requests []map[string]interface{}
	if err := json.Unmarshal(jsonData, &requests); err != nil {
		fmt.Println("Error parsing JSON array:", err)
		return
	}

	outputFile := "./publisher_response.json"
	// 打开文件并设置追加模式，如果文件不存在则创建，权限为 0644
	file, err := os.OpenFile(outputFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		// 处理错误
		panic(err)
	}
	defer file.Close() // 确保在函数结束时关闭文件

	// 创建HTTP客户端
	client := &http.Client{}

	// 遍历数组，逐个发送请求
	for i, reqBody := range requests {
		baseInfo := reqBody["base_info"].(map[string]interface{})
		name := baseInfo["name"].(string)
		websiteUrl := baseInfo["website_url"].(string)

		// 将单个请求体转换为JSON
		jsonData, err := json.Marshal(reqBody)
		if err != nil {
			fmt.Printf("Error marshaling request %d: %v\n", i+1, err)
			continue
		}

		// 创建HTTP请求
		req, err := http.NewRequest(method, url, bytes.NewBuffer(jsonData))
		if err != nil {
			fmt.Printf("Error creating request %d: %v\n", i+1, err)
			continue
		}

		// 设置请求头
		req.Header.Add("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.JfeLIQMJYdHw9eHso3puxp3EuQRv7n7-5at2LAXAMSE")
		req.Header.Add("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "*/*")
		req.Header.Add("Host", "**********:8888")
		req.Header.Add("Connection", "keep-alive")

		// 发送请求
		res, err := client.Do(req)
		if err != nil {
			fmt.Printf("Error sending request %d: %v\n", i+1, err)
			continue
		}
		defer res.Body.Close()

		// 读取响应
		responseBody, err := ioutil.ReadAll(res.Body)
		if err != nil {
			fmt.Printf("Error reading response body for request %d: %v\n", i+1, err)
			continue
		}

		// 解析JSON响应
		var resp Response
		err = json.Unmarshal(responseBody, &resp)
		if err != nil {
			fmt.Println("解析JSON失败:", err)
			return
		}
		if resp.ErrID != 0 {
			output := fmt.Sprintf("DisplayName: %s, WebsiteUrl: %s, Response: %s\n", name, websiteUrl, responseBody)
			if _, err := file.Write([]byte(output)); err != nil {
				fmt.Println("写入文件失败:", err)
				return
			}
		}
	}
}
