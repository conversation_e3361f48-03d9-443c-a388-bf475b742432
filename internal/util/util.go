package util

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	kxerrors "git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxerror"
	"github.com/google/uuid"
	"net/url"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"
	UErrors "vision_hub/internal/errors"
	"vision_hub/internal/util/time_util"
)

type CommonError struct {
	ErrId      int
	ErrCode    string
	ErrMessage string
}

// Retry 重试函数 retries：重试次数 delay：重试间隔 fn：重试函数
func Retry(retries int, delay time.Duration, fn func() error) error {
	var err error
	for i := 0; i < retries; i++ {
		err = fn()
		if err == nil {
			return nil
		}
		fmt.Printf("Attempt %d failed: %v\n", i+1, err)
		time.Sleep(delay)
	}
	return fmt.Errorf("all %d attempts failed: %w", retries, err)
}
func GenerateUuidNumberStr() string {
	// 生成 UUID
	uuid := uuid.New()
	// 将 UUID 转换为字符串，并去掉连接符
	orderNumber := uuid.String()
	return orderNumber
}
func GenerateUuidNumber() string {
	// 生成 UUID
	uuid := uuid.New()
	// 将 UUID 转换为字符串，并去掉连接符
	orderNumber := uuid.String()
	orderNumber = orderNumber[:8] + orderNumber[9:13] + orderNumber[14:18] + orderNumber[19:23] + orderNumber[24:]
	return orderNumber
}

func MarshalNonEmptyForObj(v interface{}) ([]byte, error) {
	if v == nil {
		return []byte("{}"), nil
	}
	return json.Marshal(v)
}

func MarshalNonEmptyTwoForObj(v, v1 interface{}) ([]byte, error) {
	if v == nil && v1 == nil {
		return []byte("{}"), nil
	}

	return json.Marshal(v)
}

func MarshalNonEmptySlice(v interface{}) ([]byte, error) {
	// 使用反射检查 v 的类型
	value := reflect.ValueOf(v)
	if value.Kind() != reflect.Slice {
		// 如果不是切片类型，返回默认值 []
		return []byte("[]"), nil
	}

	// 检查切片是否为空
	if value.Len() == 0 {
		// 如果切片为空，返回默认值 []
		return []byte("[]"), nil
	}

	// 如果是切片且不为空，进行 JSON 序列化
	return json.Marshal(v)
}

func MarshalNonTime(dateString, format string) (time.Time, error) {
	parseTime, _ := time_util.ParseStringToTime(dateString, format)
	if parseTime.IsZero() {
		parseTime = time.Now()
	}
	return parseTime, nil
}

// FindDuplicates 检查字符串切片中是否存在重复的元素，并返回重复的元素
func FindDuplicates(arr []string) []string {
	// 创建一个 map 来存储已经出现过的元素及其出现次数
	seen := make(map[string]int)
	// 用于存储重复的元素
	var duplicates []string

	for _, item := range arr {
		// 如果元素已经存在于 map 中，说明有重复
		if seen[item] > 0 {
			// 如果这是该元素第一次被发现重复，将其添加到 duplicates 切片中
			if seen[item] == 1 {
				duplicates = append(duplicates, item)
			}
		}
		// 记录元素出现的次数
		seen[item]++
	}
	// 返回所有重复的元素
	return duplicates
}

// RemoveDuplicates 移除字符串切片中的重复元素
func RemoveDuplicates(arr []string) []string {
	seen := make(map[string]bool)
	var result []string
	for _, id := range arr {
		if !seen[id] {
			seen[id] = true
			result = append(result, id)
		}
	}
	return result
}

// FindDuplicatesAndFilter 找出两个切片中的重复数据，并返回重复的数据和过滤后的数据
// sourceSlice 是原始切片，comparisonData 是要比较的数据
func FindDuplicatesAndFilter(sourceSlice, comparisonData []string) ([]string, []string) {

	var (
		duplicates []string // 用于存储重复的数据
		filtered   []string // 用于存储过滤后的数据
	)

	// 使用一个map来记录slice1中的元素
	seen := make(map[string]bool)
	for _, value := range sourceSlice {
		seen[value] = true
	}

	// 遍历slice2，检查是否在slice1中出现过
	for _, value := range comparisonData {
		if seen[value] {
			// 如果出现过，则添加到重复数据中
			duplicates = append(duplicates, value)
		} else {
			// 如果没有出现过，则添加到过滤后的数据中
			filtered = append(filtered, value)
		}
	}

	return duplicates, filtered
}

// ToString 安全地将任意类型转换为字符串
func ToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case fmt.Stringer:
		return v.String()
	case []byte:
		return string(v)
	case error:
		return v.Error()
	default:
		return fmt.Sprintf("%v", value)
	}
}

func HandleError(err error) *CommonError {
	if err == nil {
		return nil
	}
	var kxErr *kxerrors.MsError
	if errors.As(err, &kxErr) {
		return &CommonError{
			ErrId:      kxErr.ErrId,
			ErrCode:    kxErr.ErrCode,
			ErrMessage: kxErr.ErrMsg,
		}
	}
	return &CommonError{
		ErrId:      UErrors.UnknownErrorID,
		ErrCode:    UErrors.UnknownErrorCode,
		ErrMessage: err.Error(),
	}
}

// InsertAndRemoveDuplicates 插入元素并去重
func InsertAndRemoveDuplicates[T comparable](arr []T, item T) []T {
	seen := make(map[T]bool)
	var result []T

	// 去重原数组
	for _, v := range arr {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}

	// 插入新元素（如果不存在）
	if !seen[item] {
		result = append(result, item)
	}

	return result
}

// Contains 检查切片中是否包含指定元素
func Contains[T comparable](arr []T, target T) bool {
	for _, v := range arr {
		if v == target {
			return true
		}
	}
	return false
}

// ExtractDomain 提取URL中的域名部分
func ExtractDomain(rawURL string) (string, error) {
	// 如果URL没有协议前缀，添加http://以便正确解析
	if !strings.Contains(rawURL, "://") {
		rawURL = "http://" + rawURL
	}

	u, err := url.Parse(rawURL)
	if err != nil {
		return "", err
	}

	host := u.Hostname()
	if host == "" {
		return "", fmt.Errorf("无法解析域名")
	}

	return host, nil
}

// StringToIntSlice 将字符串转换为整数切片
func StringToIntSlice(input string) ([]int32, error) {
	// 处理空字符串情况
	if input == "" {
		return []int32{}, nil
	}

	// 按逗号分割字符串
	strNumbers := strings.Split(input, ",")

	// 创建结果切片
	numbers := make([]int32, 0, len(strNumbers))

	// 遍历每个字符串数字
	for _, strNum := range strNumbers {
		// 去除可能的空白字符
		strNum = strings.TrimSpace(strNum)
		if strNum == "" {
			continue
		}

		// 转换为整数
		num, err := strconv.ParseInt(strNum, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("'%s' 不是有效的整数", strNum)
		}

		// 添加到结果切片
		numbers = append(numbers, int32(num))
	}

	return numbers, nil
}

// IsVideoFile 判断是否是视频文件
func IsVideoFile(path string) bool {
	ext := strings.ToLower(filepath.Ext(path))
	videoExts := []string{".mp4", ".avi", ".mov", ".flv", ".wmv", ".mkv", ".webm"}
	for _, ve := range videoExts {
		if ext == ve {
			return true
		}
	}
	return false
}

func IsImageFile(path string) bool {
	ext := strings.ToLower(filepath.Ext(path))
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}
	for _, ie := range imageExts {
		if ext == ie {
			return true
		}
	}
	return false
}

// MD5Hash 文件的MD5值
func MD5Hash(text string) string {
	// 创建一个MD5哈希对象
	hash := md5.New()

	// 写入要加密的数据
	hash.Write([]byte(text))

	// 计算哈希值
	hashBytes := hash.Sum(nil)

	// 将哈希值转换为16进制字符串
	hashString := hex.EncodeToString(hashBytes)

	return hashString
}
