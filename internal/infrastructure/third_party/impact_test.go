package third_party

import (
	"testing"
)

func TestImpactStatusConstants(t *testing.T) {
	// 测试Impact状态常量是否正确定义
	tests := []struct {
		name     string
		constant string
		expected string
	}{
		{
			name:     "Impact OK status",
			constant: ImpactStatusOK,
			expected: "OK",
		},
		{
			name:     "Impact QUEUED status",
			constant: ImpactStatusQueued,
			expected: "QUEUED",
		},
		{
			name:     "Impact ERROR status",
			constant: ImpactStatusError,
			expected: "ERROR",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.constant != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, tt.constant)
			}
		})
	}
}

func TestImpactAPI_NewImpactAPI(t *testing.T) {
	tests := []struct {
		name        string
		baseURL     string
		proxyURL    string
		expectedURL string
	}{
		{
			name:        "Default base URL",
			baseURL:     "",
			proxyURL:    "http://proxy.example.com",
			expectedURL: "https://api.impact.com",
		},
		{
			name:        "Custom base URL",
			baseURL:     "https://custom.impact.com",
			proxyURL:    "http://proxy.example.com",
			expectedURL: "https://custom.impact.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			api := NewImpactAPI(tt.baseURL, tt.proxyURL)
			if api.BaseURL != tt.expectedURL {
				t.Errorf("Expected BaseURL %s, got %s", tt.expectedURL, api.BaseURL)
			}
			if api.ProxyURL != tt.proxyURL {
				t.Errorf("Expected ProxyURL %s, got %s", tt.proxyURL, api.ProxyURL)
			}
		})
	}
}
