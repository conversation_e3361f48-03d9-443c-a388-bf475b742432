package types

// ImpactAddress 地址信息
type ImpactAddress struct {
	AddressLine1 string `json:"AddressLine1"`
	AddressLine2 string `json:"AddressLine2"`
	City         string `json:"City"`
	State        string `json:"State"`
	PostalCode   string `json:"PostalCode"`
	Country      string `json:"Country"`
}

// ImpactContact 联系人信息
type ImpactContact struct {
	UserID                 string `json:"UserId"`
	Name                   string `json:"DisplayName"`
	Email                  string `json:"Email"`
	WorkPhoneNumber        string `json:"WorkPhoneNumber"`
	WorkPhoneNumberCountry string `json:"WorkPhoneNumberCountry"`
	CellPhoneNumber        string `json:"CellPhoneNumber"`
	CellPhoneNumberCountry string `json:"CellPhoneNumberCountry"`
}

// ImpactIndustry 行业信息
type ImpactIndustry struct {
	IndustryID   string `json:"IndustryId"`
	IndustryName string `json:"IndustryName"`
}

// ImpactCompanyResponse Impact授权验证响应
type ImpactCompanyResponse struct {
	CompanyName                 string         `json:"CompanyName"`
	Industry                    ImpactIndustry `json:"Industry"`
	Website                     string         `json:"Website"`
	PrimaryPhoneNumber          string         `json:"PrimaryPhoneNumber"`
	PrimaryPhoneNumberCountry   string         `json:"PrimaryPhoneNumberCountry"`
	SecondaryPhoneNumber        string         `json:"SecondaryPhoneNumber"`
	SecondaryPhoneNumberCountry string         `json:"SecondaryPhoneNumberCountry"`
	MinimumContactRating        string         `json:"MinimumContactRating"`
	Timezone                    string         `json:"Timezone"`
	Currency                    string         `json:"Currency"`
	RegisteredForIndirectTax    string         `json:"RegisteredForIndirectTax"`
	IndirectTaxNumber           string         `json:"IndirectTaxNumber"`
	OrganizationType            string         `json:"OrganizationType"`
	EinSsnForeignTaxID          string         `json:"EinSsnForeignTaxId"`
	CorporateAddress            ImpactAddress  `json:"CorporateAddress"`
	BillingAddress              ImpactAddress  `json:"BillingAddress"`
	FinancialContact            ImpactContact  `json:"FinancialContact"`
	TechnicalContact            ImpactContact  `json:"TechnicalContact"`
	SecurityContact             ImpactContact  `json:"SecurityContact"`
	CommercialContact           ImpactContact  `json:"CommercialContact"`
	URI                         string         `json:"Uri"`
}

// ImpactAccountInfo Impact账户信息
type ImpactAccountInfo struct {
	AccountID   string `json:"account_id"`
	AccountName string `json:"account_name"`
	Status      string `json:"status"`
	Type        string `json:"type"`
	Currency    string `json:"currency"`
	Timezone    string `json:"timezone"`
}

// ImpactCampaign Campaign信息
type ImpactCampaign struct {
	ID   string `json:"Id"`
	Name string `json:"DisplayName"`
}

// ImpactCampaignsResponse Campaign列表响应
type ImpactCampaignsResponse struct {
	Page         string           `json:"@page"`
	NumPages     string           `json:"@numpages"`
	PageSize     string           `json:"@pagesize"`
	Total        string           `json:"@total"`
	Start        string           `json:"@start"`
	End          string           `json:"@end"`
	URI          string           `json:"@uri"`
	FirstPageURI string           `json:"@firstpageuri"`
	PrevPageURI  string           `json:"@previouspageuri"`
	NextPageURI  string           `json:"@nextpageuri"`
	LastPageURI  string           `json:"@lastpageuri"`
	Campaigns    []ImpactCampaign `json:"Campaigns"`
}
