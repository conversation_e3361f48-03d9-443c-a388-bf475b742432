package third_party

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

// TikTokAPI 封装 TikTok API 相关请求
type TikTokAPI struct {
	BaseURL  string
	Token    string
	ProxyURL string
}

const UploadTypeUploadByUrl = "UPLOAD_BY_URL"

// NewTikTokAPI 创建新的 TikTok API 实例
func NewTikTokAPI(baseURL, token, proxyURL string) *TikTokAPI {
	return &TikTokAPI{
		BaseURL:  baseURL,
		Token:    token,
		ProxyURL: proxyURL,
	}
}

// VideoFixUpload 视频自动修复上传
func (t *TikTokAPI) VideoFixUpload(ctx context.Context, param UploadParam) (VideoFixUploadResponse, error) {
	url := fmt.Sprintf("%s/v1.3/file/video/ad/upload/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
		"Content-Type": "application/json",
	}
	// 不同账号的 token 不同，需要传入
	if param.TiktokToken != "" {
		headers["Access-Token"] = param.TiktokToken
	}
	payload := map[string]interface{}{
		"advertiser_id":     param.AdvertiserID,
		"file_name":         param.FileName,
		"upload_type":       UploadTypeUploadByUrl,
		"video_url":         param.FileURL,
		"flaw_detect":       true,
		"auto_fix_enabled":  true,
		"auto_bind_enabled": true,
	}
	if param.UseScenario != "" {
		payload["use_scenario"] = param.UseScenario
	}
	var response VideoFixUploadResponse
	var tikTokCommonResponse TikTokCommonResponse
	data, err := NoParsePost(ctx, url, headers, payload, t.ProxyURL, 10*time.Second)

	if err != nil {
		return VideoFixUploadResponse{}, err
	}
	err = json.Unmarshal(data, &tikTokCommonResponse)
	if err != nil {
		return VideoFixUploadResponse{}, err
	}
	if tikTokCommonResponse.Code != 0 {
		return VideoFixUploadResponse{TikTokCommonResponse: tikTokCommonResponse}, fmt.Errorf("tiktok api error: %s", tikTokCommonResponse.Message)
	}

	err = json.Unmarshal(data, &response)
	if err != nil {
		return VideoFixUploadResponse{}, err
	}
	return response, nil
}

// VideoFixSearch 视频自动修复查询
func (t *TikTokAPI) VideoFixSearch(ctx context.Context, advertiserID string, params VideoFixSearch, page, pageSize int32) (VideoFixSearchResponse, error) {
	url := fmt.Sprintf("%s/v1.3/file/video/ad/search/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
	}

	pathParam := map[string]string{
		"advertiser_id": advertiserID,
		"page":          fmt.Sprintf("%d", page),
		"page_size":     fmt.Sprintf("%d", pageSize),
	}

	if len(params.VideoIds) > 0 || len(params.MaterialIds) > 0 || params.VideoName != "" || len(params.VideoMaterialSources) > 0 {
		filtering := VideoFixSearch{
			VideoIds:             params.VideoIds,
			MaterialIds:          params.MaterialIds,
			VideoName:            params.VideoName,
			VideoMaterialSources: params.VideoMaterialSources,
		}
		filteringByte, err := json.Marshal(filtering)
		if err != nil {
			return VideoFixSearchResponse{}, err
		}
		pathParam["filtering"] = string(filteringByte)
	}

	var response VideoFixSearchResponse
	err := Get(ctx, url, headers, pathParam, &response, time.Second*5, t.ProxyURL)
	if err != nil {
		return VideoFixSearchResponse{}, err
	}
	return response, nil

}

// GetTiktokDigitalAvatar 获取可用数字人
func (t *TikTokAPI) GetTiktokDigitalAvatar(ctx context.Context, page, pageSize int32) (TikTokDigitalAvatarResponse, error) {
	url := fmt.Sprintf("%s/v1.3/creative/digital_avatar/get/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
	}
	pathParam := map[string]string{
		"page":      fmt.Sprintf("%d", page),
		"page_size": fmt.Sprintf("%d", pageSize),
	}
	var response TikTokDigitalAvatarResponse
	err := Get(ctx, url, headers, pathParam, &response, time.Second*10, t.ProxyURL)
	if err != nil {
		return TikTokDigitalAvatarResponse{}, err
	}

	return response, nil
}

// GetTiktokDigitalAvatarTask 获取数字人任务列表
func (t *TikTokAPI) GetTiktokDigitalAvatarTask(
	ctx context.Context,
	page, pageSize int32,
	avatarId, startDate, endDate string) (TikTokDigitalAvatarTaskResponse, error) {
	url := fmt.Sprintf("%s/v1.3/creative/digital_avatar/video/list/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
	}
	pathParam := map[string]string{
		"page":      fmt.Sprintf("%d", page),
		"page_size": fmt.Sprintf("%d", pageSize),
	}
	if avatarId != "" || startDate != "" || endDate != "" {
		filtering := TikTokDigitalAvatarTaskFiltering{
			AvatarId:  avatarId,
			StartDate: startDate,
			EndDate:   endDate,
		}
		filteringByte, err := json.Marshal(filtering)
		if err != nil {
			return TikTokDigitalAvatarTaskResponse{}, err
		}
		pathParam["filtering"] = string(filteringByte)
	}

	var response TikTokDigitalAvatarTaskResponse
	err := Get(ctx, url, headers, pathParam, &response, time.Second*10, t.ProxyURL)
	if err != nil {
		return TikTokDigitalAvatarTaskResponse{}, err
	}

	return response, nil
}

// CreateTiktokDigitalAvatarTask 创建数字人任务
func (t *TikTokAPI) CreateTiktokDigitalAvatarTask(ctx context.Context, req []MaterialPackages) (CreateTiktokDigitalAvatarTaskResponse, error) {
	url := fmt.Sprintf("%s/v1.3/creative/digital_avatar/video/task/create/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
		"Content-Type": "application/json",
	}
	body := map[string]interface{}{
		"material_packages": req,
	}
	var response CreateTiktokDigitalAvatarTaskResponse
	err := Post(ctx, url, headers, body, &response, t.ProxyURL, time.Second*5)
	if err != nil {
		return CreateTiktokDigitalAvatarTaskResponse{}, err
	}

	return response, nil
}

// CreateVideoAIGCTask 创建视频任务
func (t *TikTokAPI) CreateVideoAIGCTask(ctx context.Context, req interface{}) (VideoAIGCTaskResponse, error) {
	url := fmt.Sprintf("%s/v1.3/creative/aigc/video/task/create/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
		"Content-Type": "application/json",
	}
	var response VideoAIGCTaskResponse
	err := Post(ctx, url, headers, req, &response, t.ProxyURL, time.Second*10)
	if err != nil {
		return VideoAIGCTaskResponse{}, err
	}
	return response, nil
}

// GetVideoAIGCTask 获取视频翻牌任务
func (t *TikTokAPI) GetVideoAIGCTask(ctx context.Context, aigcVideoTypes []string, pageSize, pageNum int32) (TiktokAIGCVideoTaskResponse, error) {
	url := fmt.Sprintf("%s/v1.3/creative/aigc/video/list/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
		"Content-Type": "application/json",
	}
	if len(aigcVideoTypes) == 0 {
		return TiktokAIGCVideoTaskResponse{}, nil
	}
	aigcVideoTypesBytes, err := json.Marshal(aigcVideoTypes)
	if err != nil {
		return TiktokAIGCVideoTaskResponse{}, err
	}
	pathParam := map[string]string{
		"aigc_video_types": string(aigcVideoTypesBytes),
		"page":             fmt.Sprintf("%d", pageNum),
		"page_size":        fmt.Sprintf("%d", pageSize),
	}
	var response TiktokAIGCVideoTaskResponse
	err = Get(ctx, url, headers, pathParam, &response, time.Second*5, t.ProxyURL)
	if err != nil {
		return TiktokAIGCVideoTaskResponse{}, err
	}
	return response, nil
}

// UploadImage 上传图片到Tiktok素材库
func (t *TikTokAPI) UploadImage(ctx context.Context, param UploadParam) (TiktokImageUploadResponse, error) {
	url := fmt.Sprintf("%s/v1.3/file/image/ad/upload/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
		"Content-Type": "application/json",
	}
	// 不同账号的 token 不同，需要传入
	if param.TiktokToken != "" {
		headers["Access-Token"] = param.TiktokToken
	}
	payload := map[string]interface{}{
		"advertiser_id": param.AdvertiserID,
		"file_name":     param.FileName,
		"upload_type":   "UPLOAD_BY_URL",
		"image_url":     param.FileURL,
	}
	var response TiktokImageUploadResponse
	err := Post(ctx, url, headers, payload, &response, t.ProxyURL, time.Second*10)
	if err != nil {
		return TiktokImageUploadResponse{}, err
	}
	return response, nil
}

// CheckAIGCVideoTaskStatus 获取AIGC视频任务状态
func (t *TikTokAPI) CheckAIGCVideoTaskStatus(ctx context.Context, taskIds []string, aigcVideoType string) (CheckAIGCVideoTaskResponse, error) {
	url := fmt.Sprintf("%s/v1.3/creative/aigc/video/task/list/", t.BaseURL)
	headers := map[string]string{
		"Access-Token": t.Token,
		"Content-Type": "application/json",
	}
	if len(taskIds) == 0 {
		return CheckAIGCVideoTaskResponse{}, nil
	}
	taskIdsBytes, err := json.Marshal(taskIds)
	if err != nil {
		return CheckAIGCVideoTaskResponse{}, err
	}
	pathParam := map[string]string{
		"aigc_video_type": aigcVideoType,
		"task_ids":        string(taskIdsBytes),
	}

	var response CheckAIGCVideoTaskResponse
	err = Get(ctx, url, headers, pathParam, &response, time.Second*5, t.ProxyURL)
	if err != nil {
		return CheckAIGCVideoTaskResponse{}, err
	}
	return response, nil
}

// GetAccessTokenByAuthCode 通过auth_code换取access_token
func (t *TikTokAPI) GetAccessTokenByAuthCode(ctx context.Context, authCode, appID, secret string) (TikTokTokenResponse, error) {
	url := fmt.Sprintf("%s/open_api/v1.3/oauth2/access_token/", t.BaseURL)

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	payload := map[string]interface{}{
		"app_id":     appID,
		"secret":     secret,
		"auth_code":  authCode,
		"grant_type": "authorization_code",
	}

	var response TikTokTokenResponse
	err := Post(ctx, url, headers, payload, &response, t.ProxyURL, 30*time.Second)
	if err != nil {
		return response, fmt.Errorf("获取TikTok access_token失败: %v", err)
	}

	if response.Code != 0 {
		return response, fmt.Errorf("TikTok API错误: %s (code: %d)", response.Message, response.Code)
	}

	return response, nil
}
