package third_party

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"
)

// ImpactAPI Impact API客户端
type ImpactAPI struct {
	BaseURL  string
	ProxyURL string
}

// NewImpactAPI 创建Impact API实例
func NewImpactAPI(baseURL, proxyURL string) *ImpactAPI {
	if baseURL == "" {
		baseURL = "https://api.impact.com"
	}
	return &ImpactAPI{
		BaseURL:  baseURL,
		ProxyURL: proxyURL,
	}
}

// ValidateAuth 验证Impact授权信息
func (i *ImpactAPI) ValidateAuth(ctx context.Context, accountSID, authToken string) (ImpactAuthResponse, error) {
	// 构建API URL - 获取账户信息来验证授权
	url := fmt.Sprintf("%s/Mediapartners/%s/Accounts", i.BaseURL, accountSID)

	// 构建Basic Auth头
	auth := base64.StdEncoding.EncodeToString([]byte(accountSID + ":" + authToken))
	headers := map[string]string{
		"Authorization": "Basic " + auth,
		"Accept":        "application/json",
		"Content-Type":  "application/json",
	}

	var response ImpactAuthResponse
	err := Get(ctx, url, headers, nil, &response, 30*time.Second, i.ProxyURL)
	if err != nil {
		return response, fmt.Errorf("验证Impact授权失败: %v", err)
	}

	return response, nil
}

// ImpactAuthResponse Impact授权验证响应
type ImpactAuthResponse struct {
	Status  string            `json:"status"`
	Message string            `json:"message,omitempty"`
	Data    ImpactAccountInfo `json:"data,omitempty"`
}

// ImpactAccountInfo Impact账户信息
type ImpactAccountInfo struct {
	AccountID   string `json:"account_id"`
	AccountName string `json:"account_name"`
	Status      string `json:"status"`
	Type        string `json:"type"`
	Currency    string `json:"currency"`
	Timezone    string `json:"timezone"`
}
