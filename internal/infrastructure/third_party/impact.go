package third_party

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"
	"vision_hub/internal/infrastructure/third_party/types"
)

// Impact平台状态枚举
const (
	// ImpactStatusOK Impact平台正常状态
	ImpactStatusOK = "OK"
	// ImpactStatusQueued Impact平台排队状态
	ImpactStatusQueued = "QUEUED"
	// ImpactStatusError Impact平台错误状态
	ImpactStatusError = "ERROR"

	ImpactBaseURL = "https://api.impact.com"
)

// ImpactAPI Impact API客户端
type ImpactAPI struct {
	BaseURL  string
	ProxyURL string
}

// NewImpactAPI 创建Impact API实例
func NewImpactAPI(baseURL, proxyURL string) *ImpactAPI {
	if baseURL == "" {
		baseURL = ImpactBaseURL
	}
	return &ImpactAPI{
		BaseURL:  baseURL,
		ProxyURL: proxyURL,
	}
}

// GetCompanyName 验证Impact授权信息
func (i *ImpactAPI) GetCompanyName(ctx context.Context, accountSID, authToken string) (types.ImpactCompanyResponse, error) {
	// 构建API URL - 获取账户信息来验证授权
	url := fmt.Sprintf("%s/Advertisers/%s/CompanyInformation", i.BaseURL, accountSID)

	// 构建Basic Auth头
	auth := base64.StdEncoding.EncodeToString([]byte(accountSID + ":" + authToken))
	headers := map[string]string{
		"Authorization": "Basic " + auth,
		"Accept":        "application/json",
		"Content-Type":  "application/json",
	}
	var response types.ImpactCompanyResponse
	err := Get(ctx, url, headers, nil, &response, 30*time.Second, i.ProxyURL)
	if err != nil {
		return response, fmt.Errorf("验证Impact授权失败: %v", err)
	}
	return response, nil
}

// GetCampaigns 获取AccountSID下的Campaign列表
func (i *ImpactAPI) GetCampaigns(ctx context.Context, accountSID, authToken string) ([]types.ImpactCampaign, error) {
	// 构建API URL
	url := fmt.Sprintf("%s/Advertisers/%s/Campaigns", i.BaseURL, accountSID)

	// 构建Basic Auth头
	auth := base64.StdEncoding.EncodeToString([]byte(accountSID + ":" + authToken))
	headers := map[string]string{
		"Authorization": "Basic " + auth,
		"Accept":        "application/json",
	}

	var response types.ImpactCampaignsResponse
	err := Get(ctx, url, headers, nil, &response, 30*time.Second, i.ProxyURL)
	if err != nil {
		return nil, fmt.Errorf("获取Impact Campaign列表失败: %v", err)
	}

	return response.Campaigns, nil
}

//
