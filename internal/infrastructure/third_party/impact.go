package third_party

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"
)

// Impact平台状态枚举
const (
	// ImpactStatusOK Impact平台正常状态
	ImpactStatusOK = "OK"
	// ImpactStatusQueued Impact平台排队状态
	ImpactStatusQueued = "QUEUED"
	// ImpactStatusError Impact平台错误状态
	ImpactStatusError = "ERROR"
)

// ImpactAPI Impact API客户端
type ImpactAPI struct {
	BaseURL  string
	ProxyURL string
}

// NewImpactAPI 创建Impact API实例
func NewImpactAPI(baseURL, proxyURL string) *ImpactAPI {
	if baseURL == "" {
		baseURL = "https://api.impact.com"
	}
	return &ImpactAPI{
		BaseURL:  baseURL,
		ProxyURL: proxyURL,
	}
}

// ValidateAuth 验证Impact授权信息
func (i *ImpactAPI) ValidateAuth(ctx context.Context, accountSID, authToken string) (ImpactAuthResponse, error) {
	// 构建API URL - 获取账户信息来验证授权
	url := fmt.Sprintf("%s/Mediapartners/%s/Accounts", i.BaseURL, accountSID)

	// 构建Basic Auth头
	auth := base64.StdEncoding.EncodeToString([]byte(accountSID + ":" + authToken))
	headers := map[string]string{
		"Authorization": "Basic " + auth,
		"Accept":        "application/json",
		"Content-Type":  "application/json",
	}

	// 循环处理QUEUED状态
	for {
		var response ImpactAuthResponse
		err := Get(ctx, url, headers, nil, &response, 30*time.Second, i.ProxyURL)
		if err != nil {
			return response, fmt.Errorf("验证Impact授权失败: %v", err)
		}

		switch response.Status {
		case ImpactStatusOK:
			// 正常状态，直接返回
			return response, nil
		case ImpactStatusQueued:
			// 排队状态，等待1秒后重试
			time.Sleep(1 * time.Second)
			continue
		case ImpactStatusError:
			// 错误状态，打印错误信息并返回
			return response, fmt.Errorf("Impact API返回错误状态: %s", response.Message)
		default:
			// 未知状态，返回错误
			return response, fmt.Errorf("Impact API返回未知状态: %s", response.Status)
		}
	}
}

// ImpactAuthResponse Impact授权验证响应
type ImpactAuthResponse struct {
	Status  string            `json:"status"`
	Message string            `json:"message,omitempty"`
	Data    ImpactAccountInfo `json:"data,omitempty"`
}

// ImpactAccountInfo Impact账户信息
type ImpactAccountInfo struct {
	AccountID   string `json:"account_id"`
	AccountName string `json:"account_name"`
	Status      string `json:"status"`
	Type        string `json:"type"`
	Currency    string `json:"currency"`
	Timezone    string `json:"timezone"`
}
