package third_party

type TikTokCommonResponse struct {
	Code      int32  `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
}
type TikTokCommonPage struct {
	Page        int32 `json:"page"`
	TotalNumber int32 `json:"total_number"`
	TotalPage   int32 `json:"total_page"`
	PageSize    int32 `json:"page_size"`
}

type TikTokDigitalAvatarList struct {
	AvatarId         string `json:"avatar_id"`
	AvatarPreviewUrl string `json:"avatar_preview_url"`
	AvatarName       string `json:"avatar_name"`
	AvatarThumbnail  string `json:"avatar_thumbnail"`
}
type TikTokDigitalAvatarData struct {
	List     []TikTokDigitalAvatarList `json:"list"`
	PageInfo TikTokCommonPage          `json:"page_info"`
}
type TikTokDigitalAvatarResponse struct {
	TikTokCommonResponse
	Data TikTokDigitalAvatarData `json:"data"`
}

type TikTokDigitalAvatarTaskFiltering struct {
	AvatarId  string `json:"avatar_id,omitempty"`
	StartDate string `json:"start_date,omitempty"`
	EndDate   string `json:"end_date,omitempty"`
}
type TikTokDigitalAvatarTaskList struct {
	PreviewUrl    string `json:"preview_url"`
	VideoName     string `json:"video_name"`
	CreateTime    string `json:"create_time"`
	AvatarVideoId string `json:"avatar_video_id"`
	AvatarId      string `json:"avatar_id"`
	VideoCoverUrl string `json:"video_cover_url"`
}
type TikTokDigitalAvatarTaskData struct {
	List     []TikTokDigitalAvatarTaskList `json:"list"`
	PageInfo TikTokCommonPage              `json:"page_info"`
}
type TikTokDigitalAvatarTaskResponse struct {
	TikTokCommonResponse
	Data TikTokDigitalAvatarTaskData `json:"data"`
}

type MaterialPackages struct {
	AvatarId  string `json:"avatar_id"`
	Script    string `json:"script"`
	PackageId string `json:"package_id,omitempty"`
	VideoName string `json:"video_name,omitempty"`
}
type CreateTiktokDigitalAvatarTaskList struct {
	PackageId string `json:"package_id"`
	TaskId    string `json:"task_id"`
}
type CreateTiktokDigitalAvatarTaskData struct {
	List []CreateTiktokDigitalAvatarTaskList `json:"list"`
}
type CreateTiktokDigitalAvatarTaskResponse struct {
	TikTokCommonResponse
	Data CreateTiktokDigitalAvatarTaskData `json:"data"`
}

type VideoFixUploadResponse struct {
	TikTokCommonResponse
	VideoFixUploadResponseData []VideoFixUploadResponseItem `json:"data,omitempty"`
}
type VideoFixUploadResponseItem struct {
	FixTaskID     string   `json:"fix_task_id"`
	FlawTypes     []string `json:"flaw_types"`
	PreviewUrl    string   `json:"preview_url"`
	VideoCoverUrl string   `json:"video_cover_url"`
	Format        string   `json:"format"`
	VideoId       string   `json:"video_id"`
}

type VideoFixSearchResponse struct {
	TikTokCommonResponse
	Data VideoFixSearchResponseData `json:"data"`
}

type VideoFixSearchResponseData struct {
	List     []VideoFixSearchResponseItem `json:"list"`
	PageInfo TikTokCommonPage             `json:"page_info"`
}

type VideoFixSearchResponseItem struct {
	VideoID              string  `json:"video_id"`
	VideoCoverUrl        string  `json:"video_cover_url"`
	Format               string  `json:"format"`
	PreviewUrl           string  `json:"preview_url"`
	PreviewUrlExpireTime string  `json:"preview_url_expire_time"`
	Duration             float64 `json:"duration"`
	BitRate              int32   `json:"bit_rate"`
	Width                int32   `json:"width"`
	Height               int32   `json:"height"`
	Size                 int32   `json:"size"`
	MaterialId           string  `json:"material_id"`
	FileName             string  `json:"file_name"`
	CreateTime           string  `json:"create_time"`
	ModifyTime           string  `json:"modify_time"`
}

type VideoFixSearch struct {
	VideoIds             []string `json:"video_ids,omitempty"`
	MaterialIds          []string `json:"material_ids,omitempty"`
	VideoName            string   `json:"video_name,omitempty"`
	VideoMaterialSources []string `json:"video_material_sources,omitempty"`
}

type VideoAIGCTaskRequest struct {
	AigcVideoType    string      `json:"aigc_video_type"`
	DubbingVideoInfo []VideoInfo `json:"dubbing_video_info"`
}

type VideoInfo struct {
	VideoUrl        string            `json:"video_url"`
	TargetLanguages []string          `json:"target_languages"`
	LipsyncEnabled  bool              `json:"lipsync_enabled"`
	VideoName       string            `json:"video_name"`
	SubtitleEnabled bool              `json:"subtitle_enabled"`
	Scripts         []VideoInfoScript `json:"scripts,omitempty"`
}

type VideoInfoScript struct {
	Text          string `json:"text"`
	TextStartTime string `json:"text_start_time"`
	TextEndTime   string `json:"text_end_time"`
}

type VideoAIGCTaskResponse struct {
	TikTokCommonResponse
	Data VideoAIGCTaskResponseData `json:"data"`
}

type VideoAIGCTaskResponseData struct {
	TaskIds []string `json:"task_ids"`
}

type AIGCVideoTask struct {
	AigcVideoType string      `json:"aigc_video_type"`
	CreateTime    string      `json:"create_time"`
	DubbingInfo   DubbingInfo `json:"dubbing_info"`
	PreviewUrl    string      `json:"preview_url"`
	VideoId       string      `json:"video_id"`
	VideoName     string      `json:"video_name"`
}

type DubbingInfo struct {
	OriginalScripts   []Scripts `json:"original_scripts"`
	TargetLanguage    string    `json:"target_language"`
	TranslatedScripts []Scripts `json:"translated_scripts"`
}

type Scripts struct {
	Text          string `json:"text"`
	TextEndTime   int    `json:"text_end_time"`
	TextStartTime int    `json:"text_start_time"`
}

type TiktokAIGCVideoTaskData struct {
	List     []AIGCVideoTask  `json:"list"`
	PageInfo TikTokCommonPage `json:"page_info"`
}

type TiktokAIGCVideoTaskResponse struct {
	TikTokCommonResponse
	Data TiktokAIGCVideoTaskData `json:"data"`
}

type TiktokImageUploadData struct {
	Displayable      bool   `json:"displayable"` // 图片能否在平台展示
	Width            int    `json:"width"`
	Height           int    `json:"height"`
	Signature        string `json:"signature"` // 图片MD5
	FileName         string `json:"file_name"`
	ImageId          string `json:"image_id"`  // 图片ID，用于创建广告
	ImageUrl         string `json:"image_url"` // 图片URL，一个小时有效期
	Format           string `json:"format"`    // 图片格式：jpg、png
	CreateTime       string `json:"create_time"`
	ModifyTime       string `json:"modify_time"`
	IsCarouselUsable bool   `json:"is_carousel_usable"` // 是否可用于轮播广告，有时差10s后获取更准
	MaterialId       string `json:"material_id"`        // 素材ID
	Size             int    `json:"size"`               // 图片大小，单位byte
}

type TiktokImageUploadResponse struct {
	TikTokCommonResponse
	Data TiktokImageUploadData `json:"data"`
}

type UploadParam struct {
	FileName     string
	FileURL      string
	AdvertiserID string
	UseScenario  string
	TiktokToken  string
}
type ProductVideoInfo struct {
	VideoGenerationCount int               `json:"video_generation_count"`
	TargetLanguage       string            `json:"target_language"`
	ProductInfoList      []ProductInfoList `json:"product_info_list"`          // 产品信息，最大数量为1
	InputImageList       InputImageList    `json:"input_image_list,omitempty"` // 数字人必填
	InputVideoList       InputVideoList    `json:"input_video_list,omitempty"` // 非数字人时，必填
	AvatarInfo           AvatarInfo        `json:"avatar_info,omitempty"`      // 数字人必填
}
type ProductInfoList struct {
	SourceLanguage string   `json:"source_language"` // 商品或服务的信息的原始语言
	ProductName    string   `json:"product_name"`    // 商品或服务的名称
	Title          string   `json:"title"`           // 商品或服务的标题
	Description    string   `json:"description"`     // 商品或服务的描述
	SellingPoints  []string `json:"selling_points"`  // 包含商品或服务的卖点的字符串数组，最大数量为10
	Brand          string   `json:"brand"`           // 商品或服务的品牌，100字符限制
	Price          float64  `json:"price"`           // 商品或服务的价格，保留两位小数
	Currency       string   `json:"currency"`        // 价格对应的币种，ISO 4217，示例：USD
}
type InputImageList struct {
	ImageUrlList []string `json:"image_url_list"` // 有效的可下载的图片地址
}
type InputVideoList struct {
	VideoIdList []string `json:"video_id_list"` // 上传到TT后的素材id
}
type AvatarInfo struct {
	AvatarId string `json:"avatar_id"` // 数字人的ID
}

type QueryAIGCVideoTask struct {
	AigcVideoType string `json:"aigc_video_type"`
	PreviewUrl    string `json:"preview_url"`
	Status        string `json:"status"`
	TaskId        string `json:"task_id"`
	VideoId       string `json:"video_id"`
}
type CheckAIGCVideoTask struct {
	List []QueryAIGCVideoTask `json:"list"`
}

type CheckAIGCVideoTaskResponse struct {
	TikTokCommonResponse
	Data CheckAIGCVideoTask `json:"data"`
}

// TikTokTokenResponse TikTok token响应
type TikTokTokenResponse struct {
	TikTokCommonResponse
	Data TikTokTokenData `json:"data"`
}

// TikTokTokenData TikTok token数据
type TikTokTokenData struct {
	AccessToken   string   `json:"access_token"`
	AdvertiserIDs []string `json:"advertiser_ids"`
	Scope         []string `json:"scope"`
}

// TikTokUserInfoResponse TikTok用户信息响应
type TikTokUserInfoResponse struct {
	TikTokCommonResponse
	Data TikTokUserInfoData `json:"data"`
}

// TikTokUserInfoData TikTok用户信息数据
type TikTokUserInfoData struct {
	DisplayName string `json:"display_name"`
	Email       string `json:"email"`
}

type TikTokUserInfoResponse struct {
	TikTokCommonResponse
	Data TikTokUserInfoData `json:"data"`
}

type TikTokUserInfoData struct {
	Id          int64  `json:"id"`
	CreateTime  int    `json:"create_time"`
	DisplayName string `json:"display_name"`
	Email       string `json:"email"`
}
