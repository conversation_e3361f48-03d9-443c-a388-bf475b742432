package service

import (
	demo "git.domob-inc.cn/bluevision/bv_commons/app/demo/service"
	"github.com/google/wire"
	"vision_hub/internal/service/common"
	consumerService "vision_hub/internal/service/consumer"
	"vision_hub/internal/service/cron"
	service "vision_hub/internal/service/http"
	"vision_hub/internal/service/http/customer_project_space"
	"vision_hub/internal/service/http/internal_service"
	taskService "vision_hub/internal/service/task"
)

// ServiceProviderSet is service providers.
var ServiceProviderSet = wire.NewSet(
	common.NewServiceLogHelper, demo.NewGreeterService, demo.NewTaskService, taskService.NewTaskTestService,
	common.NewBaseService, service.NewAuthService, service.NewDictManageService,
	service.NewCommonService, service.NewResourceService, service.NewVideoAIGCService, service.NewTaskApiService,
	consumerService.NewTaskService, service.NewCustomerProjectService, internal_service.NewClientPortalService,
	service.NewAuthGroupService, service.NewAuthorizationService, customer_project_space.NewMaterialManageService, customer_project_space.NewAdsAccountService,
	service.NewUserActivityService, cron.NewMaterialMediumUploadService, cron.NewTiktokCheckAIGCTaskService, cron.NewCronServiceLogHelper, cron.NewMaterialMediumUploadOssService,
	service.NewCallbackService, customer_project_space.NewCustomerProResourceService,
)
