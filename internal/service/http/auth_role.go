package service

import (
	"context"
	authBiz "vision_hub/internal/biz/auth"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/service/common"
	"vision_hub/internal/util"

	pb "vision_hub/api/auth/v1"
)

type AuthRoleService struct {
	log          *bizcommon.BizLogHelper
	base         *common.BaseService
	authRoleCase *authBiz.AuthRoleCase
}

func NewAuthRoleService(logger *bizcommon.BizLogHelper, authGroupCase *authBiz.AuthRoleCase, base *common.BaseService) *AuthRoleService {
	return &AuthRoleService{
		log:          logger,
		base:         base,
		authRoleCase: authGroupCase,
	}
}

func (s *AuthRoleService) CreateAuthRole(ctx context.Context, req *pb.AddOrUpdateRoleRequest) (*pb.CommonResponse, error) {
	// Convert Request
	request, err := authBiz.CreateOrUpdateRoleRequestToBo(req)
	if err != nil {
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	// 创建角色
	err = s.authRoleCase.CreateRole(ctx, request)
	if err != nil {
		s.log.Errorf("创建角色失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	return &pb.CommonResponse{}, nil
}
func (s *AuthRoleService) UpdateAuthRole(ctx context.Context, req *pb.AddOrUpdateRoleRequest) (*pb.CommonResponse, error) {
	// Convert Request
	request, err := authBiz.CreateOrUpdateRoleRequestToBo(req)
	if err != nil {
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	// 更新角色
	err = s.authRoleCase.UpdateRole(ctx, request)
	if err != nil {
		s.log.Errorf("更新角色失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	return &pb.CommonResponse{}, nil
}
func (s *AuthRoleService) DeleteAuthRole(ctx context.Context, req *pb.GetOrDelRoleRequest) (*pb.CommonResponse, error) {
	// Call business logic
	err := s.authRoleCase.DeleteRole(ctx, req.Id)
	if err != nil {
		s.log.Errorf("删除角色失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	s.log.Infof("删除角色成功，id: %d", req.Id)
	return &pb.CommonResponse{}, nil
}
func (s *AuthRoleService) GetAuthRole(ctx context.Context, req *pb.GetOrDelRoleRequest) (*pb.GetAuthRoleReply, error) {
	// Call business logic
	data, err := s.authRoleCase.GetRoleDetail(ctx, req.Id)
	if err != nil {
		s.log.Errorf("获取角色详情失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.GetAuthRoleReply{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	reply := authBiz.ConvertRoleDetailToPb(data)
	return &pb.GetAuthRoleReply{Data: reply}, nil
}
func (s *AuthRoleService) ListAuthRole(ctx context.Context, req *pb.ListAuthRoleRequest) (*pb.ListAuthRoleReply, error) {
	request := authBiz.ConvertRoleListReqToBo(req)
	data, err := s.authRoleCase.QueryRoleList(ctx, request)
	if err != nil {
		s.log.Errorf("获取角色列表失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.ListAuthRoleReply{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	reply := authBiz.ConvertRoleListToPb(data)
	return &pb.ListAuthRoleReply{Data: reply}, nil
}
