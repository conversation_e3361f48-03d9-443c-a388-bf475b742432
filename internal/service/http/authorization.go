package service

import (
	"context"
	pb "vision_hub/api/authorization/v1"
	authorizationBiz "vision_hub/internal/biz/authorization"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/service/common"
	"vision_hub/internal/util"
)

// AuthorizationService 授权服务
type AuthorizationService struct {
	log               *bizcommon.BizLogHelper
	base              *common.BaseService
	authorizationCase *authorizationBiz.AuthorizationCase
}

// NewAuthorizationService 创建授权服务实例
func NewAuthorizationService(logger *bizcommon.BizLogHelper, authorizationCase *authorizationBiz.AuthorizationCase, base *common.BaseService) *AuthorizationService {
	return &AuthorizationService{
		log:               logger,
		authorizationCase: authorizationCase,
		base:              base,
	}
}

// CreateAuthorization 创建授权
func (s *AuthorizationService) CreateAuthorization(ctx context.Context, req *pb.CreateAuthorizationRequest) (*pb.CommonResponse, error) {
	// 转换请求
	bizReq, err := authorizationBiz.ConvertCreateRequestToBiz(req)
	if err != nil {
		s.log.Errorf("转换创建授权请求失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	// 调用业务逻辑
	err = s.authorizationCase.CreateAuthorization(ctx, bizReq)
	if err != nil {
		s.log.Errorf("创建授权失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	return &pb.CommonResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "创建成功",
	}, nil
}

// UpdateAuthorization 更新授权
func (s *AuthorizationService) UpdateAuthorization(ctx context.Context, req *pb.UpdateAuthorizationRequest) (*pb.CommonResponse, error) {
	// 转换请求
	bizReq, err := authorizationBiz.ConvertUpdateRequestToBiz(req)
	if err != nil {
		s.log.Errorf("转换更新授权请求失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	// 调用业务逻辑
	err = s.authorizationCase.UpdateAuthorization(ctx, bizReq)
	if err != nil {
		s.log.Errorf("更新授权失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	return &pb.CommonResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "更新成功",
	}, nil
}

// ListAuthorization 获取授权列表
func (s *AuthorizationService) ListAuthorization(ctx context.Context, req *pb.ListAuthorizationRequest) (*pb.ListAuthorizationResponse, error) {
	// 转换请求
	bizReq := authorizationBiz.ConvertListRequestToBiz(req)

	// 调用业务逻辑
	data, err := s.authorizationCase.ListAuthorization(ctx, bizReq)
	if err != nil {
		s.log.Errorf("获取授权列表失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.ListAuthorizationResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	// 转换响应
	pbData, err := authorizationBiz.ConvertAuthorizationListToPb(data)
	if err != nil {
		s.log.Errorf("转换授权列表响应失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.ListAuthorizationResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	return &pb.ListAuthorizationResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "查询成功",
		Data:    pbData,
	}, nil
}

// GetAuthorizationDetail 获取授权详情
func (s *AuthorizationService) GetAuthorizationDetail(ctx context.Context, req *pb.GetAuthorizationDetailRequest) (*pb.GetAuthorizationDetailResponse, error) {
	// 调用业务逻辑
	authInfo, err := s.authorizationCase.GetAuthorizationDetail(ctx, req.Id)
	if err != nil {
		s.log.Errorf("获取授权详情失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.GetAuthorizationDetailResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	// 转换为proto对象
	pbAuth, err := authorizationBiz.ConvertAuthorizationToPb(authInfo)
	if err != nil {
		s.log.Errorf("转换授权详情响应失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.GetAuthorizationDetailResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	return &pb.GetAuthorizationDetailResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "查询成功",
		Data:    pbAuth,
	}, nil
}

// RemoveAuthorization 删除授权
func (s *AuthorizationService) RemoveAuthorization(ctx context.Context, req *pb.RemoveAuthorizationRequest) (*pb.CommonResponse, error) {
	// 调用业务逻辑
	err := s.authorizationCase.RemoveAuthorization(ctx, req.Id)
	if err != nil {
		s.log.Errorf("删除授权失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	return &pb.CommonResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "删除成功",
	}, nil
}
