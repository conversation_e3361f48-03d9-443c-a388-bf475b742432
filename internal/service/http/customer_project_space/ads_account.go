package customer_project_space

import (
	"context"
	"git.domob-inc.cn/bluevision/bv_interface_go/client_portal_server/enums"
	pb "vision_hub/api/customer_project_space/v1"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/biz/customer_project_space/ads_account"
	"vision_hub/internal/service/common"
	"vision_hub/internal/util"
)

type AdsAccountService struct {
	log            *bizcommon.BizLogHelper
	base           *common.BaseService
	adsAccountCase *ads_account.AdsAccountCase
}

func NewAdsAccountService(logger *bizcommon.BizLogHelper, adsAccountCase *ads_account.AdsAccountCase, base *common.BaseService) *AdsAccountService {
	return &AdsAccountService{
		log:            logger,
		adsAccountCase: adsAccountCase,
		base:           base,
	}
}

func (s *AdsAccountService) GetAdsAccountList(ctx context.Context, req *pb.GetAdsAccountListRequest) (*pb.GetAdsAccountListResponse, error) {
	params := &ads_account.GetAdsAccountListParams{
		CustomerProjectID: req.CustomerProjectId,
		AdsAccountID:      req.AdsAccountId,
		Status:            req.Status,
		PageSize:          req.PageSize,
		PageNum:           req.PageNum,
		Platform:          req.Platform,
	}
	mediums := make([]int32, 0)
	for _, v := range req.Medium {
		mediums = append(mediums, enums.MediumTypeEnum_MediumType_value[v])
	}
	params.Medium = mediums
	list, pager, err := s.adsAccountCase.GetAdsAccountList(ctx, params)
	if err != nil {
		s.log.Errorf("获取广告账号列表失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.GetAdsAccountListResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	pbList := ads_account.ConvertToPbAdsAccountList(list)
	return &pb.GetAdsAccountListResponse{
		ErrId:  0,
		ErrMsg: "成功",
		Data: &pb.AdsAccountData{
			Pagination: &pb.Pagination{
				Total:    pager.Total,
				PageSize: pager.PageSize,
				PageNum:  pager.PageNum,
			},
			List: pbList,
		},
	}, nil
}

func (s *AdsAccountService) AddAdsAccount(ctx context.Context, req *pb.AddAdsAccountRequest) (*pb.CommonResponse, error) {
	err := s.adsAccountCase.AddAdsAccount(ctx, req.CustomerProjectId, req.AdsAccountList, req.Platform)
	if err != nil {
		s.log.Errorf("添加广告账号失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	return &pb.CommonResponse{ErrId: 0, ErrMsg: "成功"}, nil
}

func (s *AdsAccountService) EnOrDisableAdsAccount(ctx context.Context, req *pb.EnOrDisableAdsAccountRequest) (*pb.CommonResponse, error) {
	err := s.adsAccountCase.EnOrDisableAdsAccount(ctx, req.CustomerProjectId, req.AdsAccountList, req.Status)
	if err != nil {
		s.log.Errorf("启用/禁用广告账号失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	return &pb.CommonResponse{ErrId: 0, ErrMsg: "成功"}, nil
}

func (s *AdsAccountService) PreviewAdsAccount(ctx context.Context, req *pb.PreviewAdsAccountRequest) (*pb.PreviewAdsAccountResponse, error) {
	data, err := s.adsAccountCase.PreviewAdsAccount(ctx, req.AdsAccountList)
	if err != nil {
		s.log.Errorf("预览广告账号签约信息失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.PreviewAdsAccountResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	pbList := ads_account.ConvertToPbAdsAccountSignInfo(data)
	return &pb.PreviewAdsAccountResponse{ErrId: 0, ErrMsg: "成功", Data: &pb.AdsAccountSignData{
		List: pbList,
	}}, nil
}

func (s *AdsAccountService) RemoveAdsAccount(ctx context.Context, req *pb.RemoveAdsAccountRequest) (*pb.CommonResponse, error) {
	err := s.adsAccountCase.RemoveAdsAccount(ctx, req.CustomerProjectId, req.AdsAccountList, req.Platform)
	if err != nil {
		s.log.Errorf("移除广告账号失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	return &pb.CommonResponse{ErrId: 0, ErrMsg: "成功"}, nil
}

func (s *AdsAccountService) OpenAPIGetEnableAdsAccountList(ctx context.Context, req *pb.OpenApiGetAdsAccountListRequest) (*pb.OpenApiGetAdsAccountListResponse, error) {
	data, err := s.adsAccountCase.OpenAPIGetEnableAdsAccountList(ctx, req.CustomerProjectNumber)
	if err != nil {
		s.log.Errorf("获取广告账号信息失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.OpenApiGetAdsAccountListResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}
	pbList := ads_account.ConvertAdsAccountToOpenAPIPb(data)
	return &pb.OpenApiGetAdsAccountListResponse{ErrId: 0, ErrMsg: "成功", Data: pbList}, nil
}
