package customer_project_space

import (
	"context"
	pb "vision_hub/api/customer_project_space/v1"
	bizcommon "vision_hub/internal/biz/common"
	configBiz "vision_hub/internal/biz/customer_project_space/config"
	"vision_hub/internal/service/common"
	"vision_hub/internal/util"
)

// CustomerProConfigService 项目配置服务
type CustomerProConfigService struct {
	log        *bizcommon.BizLogHelper
	base       *common.BaseService
	configCase *configBiz.CustomerProjectConfigCase
}

// NewCustomerProConfigService 创建项目配置服务实例
func NewCustomerProConfigService(logger *bizcommon.BizLogHelper, configCase *configBiz.CustomerProjectConfigCase, base *common.BaseService) *CustomerProConfigService {
	return &CustomerProConfigService{
		log:        logger,
		configCase: configCase,
		base:       base,
	}
}

// GetCustomerProConfig 获取项目配置
func (s *CustomerProConfigService) GetCustomerProConfig(ctx context.Context, req *pb.GetCustomerProConfigRequest) (*pb.GetCustomerProConfigResponse, error) {
	// 转换请求
	bizReq := configBiz.ConvertGetRequestToBiz(req)

	// 调用业务逻辑
	data, err := s.configCase.GetCustomerProConfig(ctx, bizReq)
	if err != nil {
		s.log.Errorf("获取项目配置失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.GetCustomerProConfigResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	// 转换响应
	pbList, err := configBiz.ConvertConfigListToPb(data)
	if err != nil {
		s.log.Errorf("转换项目配置响应失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.GetCustomerProConfigResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	return &pb.GetCustomerProConfigResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "查询成功",
		Data:    pbList,
	}, nil
}

// SaveCustomerProConfig 保存项目配置
func (s *CustomerProConfigService) SaveCustomerProConfig(ctx context.Context, req *pb.AddCustomerProConfigRequest) (*pb.CommonResponse, error) {
	// 转换请求
	bizReq := configBiz.ConvertAddRequestToBiz(req)

	// 调用业务逻辑
	err := s.configCase.SaveCustomerProConfig(ctx, bizReq)
	if err != nil {
		s.log.Errorf("保存项目配置失败，err: %v", err)
		cErr := util.HandleError(err)
		return &pb.CommonResponse{
			ErrId:   int32(cErr.ErrId),
			ErrCode: cErr.ErrCode,
			ErrMsg:  cErr.ErrMessage,
		}, nil
	}

	return &pb.CommonResponse{
		ErrId:   0,
		ErrCode: "",
		ErrMsg:  "保存成功",
	}, nil
}
