package service

import (
	"io"
	"net/http"
	bizcommon "vision_hub/internal/biz/common"
	"vision_hub/internal/service/common"
)

type CallbackService struct {
	log  *bizcommon.BizLogHelper
	base *common.BaseService
}

func NewCallbackService(logger *bizcommon.BizLogHelper, base *common.BaseService) *CallbackService {
	return &CallbackService{
		log:  logger,
		base: base,
	}
}

func (s *CallbackService) CallbackHandler(w http.ResponseWriter, r *http.Request) {
	// 读取原始 body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "read body error", http.StatusInternalServerError)
		return
	}
	defer r.Body.Close()

	// 获取 URL 路径和 query 参数
	urlPath := r.URL.Path
	query := r.URL.RawQuery

	// 打印日志
	s.log.Infof("callback: url=%s, query=%s, body=%s", urlPath, query, string(body))

	// 处理授权回调
	err = s.handleAuthCallback(ctx, r)
	if err != nil {
		s.log.Errorf("处理授权回调失败: %v", err)
		http.Error(w, "callback processing failed", http.StatusInternalServerError)
		return
	}

	// 设置响应头（可根据需要设置 Content-Type）
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write([]byte(`{"status": "success", "message": "callback processed successfully"}`)
}
