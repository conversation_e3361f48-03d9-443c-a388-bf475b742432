package constants

// 授权平台常量
const (
	// AuthPlatformImpact Impact平台
	AuthPlatformImpact = "impact"
	// AuthPlatformTikTok TikTok平台
	AuthPlatformTikTok = "tiktok"
)

// 授权类型常量
const (
	// AuthTypeOAuth2 OAuth2授权类型
	AuthTypeOAuth2 = "oauth2"
	// AuthTypeAPIKey API Key授权类型
	AuthTypeAPIKey = "api_key"
)

// 授权状态常量
const (
	// AuthStatusActive 激活状态
	AuthStatusActive = "active"
	// AuthStatusInactive 非激活状态
	AuthStatusInactive = "inactive"
	// AuthStatusExpired 已过期状态
	AuthStatusExpired = "expired"
)

// GetSupportedAuthPlatforms 获取支持的授权平台列表
func GetSupportedAuthPlatforms() []string {
	return []string{
		AuthPlatformImpact,
		AuthPlatformTikTok,
	}
}

// IsValidAuthPlatform 检查是否为有效的授权平台
func IsValidAuthPlatform(platform string) bool {
	supportedPlatforms := GetSupportedAuthPlatforms()
	for _, p := range supportedPlatforms {
		if p == platform {
			return true
		}
	}
	return false
}
