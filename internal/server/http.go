package server

import (
	authPb "vision_hub/api/auth/v1"
	authorizationPb "vision_hub/api/authorization/v1"
	commonPb "vision_hub/api/common/v1"
	customerPb "vision_hub/api/customer_project/v1"
	customerProjectSpacePb "vision_hub/api/customer_project_space/v1"
	dictManagePb "vision_hub/api/dict_manage/v1"
	internalPb "vision_hub/api/internal_service/v1"
	resourcePb "vision_hub/api/resource_manager/v1"
	taskPb "vision_hub/api/task/v1"
	userActivityPb "vision_hub/api/user_activity/v1"
	videoAIGCPb "vision_hub/api/video_aigc/v1"
	service "vision_hub/internal/service/http"
	"vision_hub/internal/service/http/customer_project_space"
	"vision_hub/internal/service/http/internal_service"

	demo "git.domob-inc.cn/bluevision/bv_commons/app/demo/service"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x"
	greeter "git.domob-inc.cn/bluevision/bv_commons/testdata/helloworld"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type HTTPServerOptions struct {
	Greeter                    *demo.GreeterService
	AuthService                *service.AuthService
	ResourceService            *service.ResourceService
	DictManageService          *service.DictManageService
	CommonService              *service.CommonService
	VideoAIGCService           *service.VideoAIGCService
	TaskApiService             *service.TaskApiService
	CustomerProjectService     *service.CustomerProjectService
	ClientPortalService        *internal_service.ClientPortalService
	MaterialManageService      *customer_project_space.MaterialManageService
	AdsAccountManageService    *customer_project_space.AdsAccountService
	AuthorizationService       *service.AuthorizationService
	CustomerProConfigService   *customer_project_space.CustomerProConfigService
	UserActivityService        *service.UserActivityService
	CallbackService            *service.CallbackService
	CustomerProResourceService *customer_project_space.CustomerProResourceService
	AuthRoleService            *service.AuthRoleService
}

// NewHTTPServer new an HTTP server.
func NewHTTPServer(kxCore *gkratos_x.KxCore, options HTTPServerOptions) *http.Server {
	srv := kxCore.HttpSrv.Srv
	greeter.RegisterGreeterHTTPServer(srv, options.Greeter)
	resourcePb.RegisterResourceManagerPrServiceHTTPServer(srv, options.ResourceService)
	resourcePb.RegisterResourceManagerIpServiceHTTPServer(srv, options.ResourceService)
	resourcePb.RegisterResourceManagerPublisherServiceHTTPServer(srv, options.ResourceService)
	resourcePb.RegisterResourceManagerSupplierServiceHTTPServer(srv, options.ResourceService)
	resourcePb.RegisterResourceManagerOutdoorScreenServiceHTTPServer(srv, options.ResourceService)
	resourcePb.RegisterResourceManagerReporterServiceHTTPServer(srv, options.ResourceService)
	resourcePb.RegisterResourceManagerServiceHTTPServer(srv, options.ResourceService)
	authPb.RegisterAuthServiceHTTPServer(srv, options.AuthService)
	authorizationPb.RegisterAuthorizationServiceHTTPServer(srv, options.AuthorizationService)
	dictManagePb.RegisterDictManageServiceHTTPServer(srv, options.DictManageService)
	commonPb.RegisterCommonServiceHTTPServer(srv, options.CommonService)
	videoAIGCPb.RegisterTiktokServiceHTTPServer(srv, options.VideoAIGCService)
	taskPb.RegisterTaskServiceHTTPServer(srv, options.TaskApiService)
	customerPb.RegisterCustomerProjectServiceHTTPServer(srv, options.CustomerProjectService)
	internalPb.RegisterClientPortalServiceHTTPServer(srv, options.ClientPortalService)
	customerProjectSpacePb.RegisterMaterialManageServiceHTTPServer(srv, options.MaterialManageService)
	userActivityPb.RegisterUserActivityHTTPServer(srv, options.UserActivityService)
	customerProjectSpacePb.RegisterAdsAccountManageServiceHTTPServer(srv, options.AdsAccountManageService)
	customerProjectSpacePb.RegisterCustomerProConfigServiceHTTPServer(srv, options.CustomerProConfigService)
	customerProjectSpacePb.RegisterCustomerProResourceServiceHTTPServer(srv, options.CustomerProResourceService)
	authPb.RegisterAuthRoleHTTPServer(srv, options.AuthRoleService)
	// 注册文件上传路由
	srv.HandleFunc("/v2/file/upload", options.CommonService.UploadFileHandler)
	// 注册回调路由
	srv.HandleFunc("/v1/callback", options.CallbackService.CallbackHandler)
	return srv
}
