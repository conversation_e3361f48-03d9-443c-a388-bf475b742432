package main

import (
	"fmt"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x"
	"git.domob-inc.cn/bluevision/bv_commons/gkratos_x/kxconfig"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
	"io"
	"log"
	"os"
	"regexp"
	"strings"
)

//go:generate go run generate.go

func dataTypeMapping() map[string]func(detailType gorm.ColumnType) (dataType string) {
	return map[string]func(detailType gorm.ColumnType) (dataType string){
		"bigint": func(detailType gorm.ColumnType) (dataType string) {
			return "int64"
		},
		"int": func(detailType gorm.ColumnType) (dataType string) {
			return "int32"
		},
		"json": func(detailType gorm.ColumnType) (dataType string) {
			return "json.RawMessage"
		},
		// bool mapping
		"tinyint": func(detailType gorm.ColumnType) (dataType string) {
			return "int8"
		},
		"datetime": func(detailType gorm.ColumnType) (dataType string) { return "sql.NullTime" },
	}
}

func extractExistingTables(genContent string) map[string]bool {
	tables := make(map[string]bool)
	re := regexp.MustCompile(`(\w+):\s+new\w+$begin:math:text$db, opts...$end:math:text$,`) // 匹配 `TableName: newTableName(db, opts...)`
	matches := re.FindAllStringSubmatch(genContent, -1)
	for _, match := range matches {
		tables[match[1]] = true
	}
	return tables
}

// copyFile 复制文件内容从 src 到 dst。如果目标文件已存在，会覆盖它。
func copyFile(src, dst string) error {
	// 打开源文件
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	// 创建目标文件
	destinationFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destinationFile.Close()

	// 将源文件内容复制到目标文件
	_, err = io.Copy(destinationFile, sourceFile)
	if err != nil {
		return err
	}

	return nil
}

func mergeTables(existingContent, tableName, newContent string) string {
	// 检查是否已有表定义
	if strings.Contains(existingContent, tableName+":") {
		return existingContent
	}

	// 合并 Query 结构定义
	queryStructPattern := `type Query struct {`
	newStructEntry := fmt.Sprintf("\t%s %s\n", tableName, strings.ToLower(tableName))
	existingContent = strings.Replace(existingContent, queryStructPattern, queryStructPattern+"\n"+newStructEntry, 1)

	// 合并 Use 函数定义
	useFuncPattern := `return &Query{`
	newUseEntry := fmt.Sprintf("\t\t%s: new%s(db, opts...),\n", tableName, tableName)
	existingContent = strings.Replace(existingContent, useFuncPattern, useFuncPattern+"\n"+newUseEntry, 1)

	return existingContent
}

// NewGenerate 生成指定表的 model 和 query
func NewGenerate() {
	// 获取环境变量或输入参数，传入 db 和 table 信息
	dbName := os.Getenv("db_name")
	tableName := os.Getenv("table_name")
	genPath := os.Getenv("path") // genPath ./internal/data/auth

	// 检查参数有效性
	if dbName == "" || tableName == "" || genPath == "" {
		log.Fatal("DB_NAME, TABLE_NAME, and PATH environment variables must be set")
	}

	// 初始化代码生成器
	g := gen.NewGenerator(gen.Config{
		OutPath:           fmt.Sprintf("%s/query", genPath), // 输出路径
		ModelPkgPath:      fmt.Sprintf("%s/model", genPath), // Model 包路径
		FieldWithIndexTag: true,                             // 添加索引 Tag
		FieldWithTypeTag:  true,                             // 添加类型 Tag
	})

	// 初始化 App 和配置
	app := &gkratos_x.App{}
	app.FWorkspace = "."
	c, bc, _ := gkratos_x.Bootstrap(app)
	defer c.Close()

	// 根据 dbName 获取对应的数据源
	var source string
	switch dbName {
	case "vision_hub":
		source = bc.Data.Database.Source
	case "bv_crm":
		source = bc.Data.Database.BackupSource
	default:
		log.Fatalf("未知db: %s", dbName)
	}

	// 打开数据库连接
	db, err := gorm.Open(mysql.Open(source))
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 设置数据库连接和类型映射
	g.UseDB(db)
	g.WithDataTypeMap(dataTypeMapping())

	// 备份现有的 gen.go 文件
	genFilePath := fmt.Sprintf("%s/query/gen.go", genPath)
	backupGenFile := fmt.Sprintf("%s.bak", genFilePath)
	if err := copyFile(genFilePath, backupGenFile); err != nil {
		log.Printf("No existing gen.go file to backup. Proceeding with generation.")
	}

	// 读取现有的 gen.go 文件内容（如果存在）
	existingGenContent := ""
	if _, err := os.Stat(genFilePath); err == nil { // 检查 gen.go 文件是否存在
		content, err := os.ReadFile(genFilePath)
		if err != nil {
			log.Fatalf("Failed to read existing gen.go: %v", err)
		}
		existingGenContent = string(content)
	}

	// 提取现有表定义
	existingTables := extractExistingTables(existingGenContent)

	// 检查新表是否已存在
	if existingTables[tableName] {
		log.Printf("Table '%s' already exists in gen.go. Skipping generation.", tableName)
		return
	}

	// 生成指定表的 Model 和 Query
	g.ApplyBasic(g.GenerateModel(tableName))
	g.Execute()

	// 读取生成的 gen.go 文件内容
	newGenContent, err := os.ReadFile(genFilePath)
	if err != nil {
		log.Fatalf("Failed to read newly generated gen.go: %v", err)
	}

	// 合并新表定义到现有的 gen.go 文件内容
	updatedGenContent := mergeTables(existingGenContent, tableName, string(newGenContent))

	// 写回合并后的内容到 gen.go 文件
	if err := os.WriteFile(genFilePath, []byte(updatedGenContent), 0644); err != nil {
		log.Fatalf("Failed to write updated gen.go: %v", err)
	}

	log.Printf("Successfully generated and merged model and query for table '%s' in database '%s'.", tableName, dbName)
}

func GeneTable(path string, tableListMap map[string][]string, bc *kxconfig.Bootstrap) {
	g := gen.NewGenerator(gen.Config{
		OutPath:      path + "/query",
		ModelPkgPath: path + "/model",
		// cmd model global configuration
		//FieldNullable:     true, // cmd pointer when field is nullable
		//FieldCoverable:    true, // cmd pointer when field has default value
		FieldWithIndexTag: true, // cmd with gorm index tag
		FieldWithTypeTag:  true, // cmd with gorm column type tag
	})

	var source string
	for keyword, tableList := range tableListMap {
		switch keyword {
		case "vision_hub":
			source = bc.Data.Database.Source
		case "bv_crm":
			source = bc.Data.Database.BackupSource
		default:
			source = bc.Data.Database.Source
		}

		db, err := gorm.Open(mysql.Open(source))
		if err != nil {
			panic(err)
		}
		g.UseDB(db)
		// 类型映射
		g.WithDataTypeMap(dataTypeMapping())
		for _, table := range tableList {
			g.ApplyBasic(g.GenerateModel(table))
		}
		g.Execute()
	}
}

// OldGenerate 生成所有 model 和 query
func OldGenerate() {
	app := &gkratos_x.App{}
	app.FWorkspace = "."
	c, bc, _ := gkratos_x.Bootstrap(app)
	defer c.Close()

	// GeneTable 划分维度为按照领域划分
	//GeneTable("./internal/data/common", map[string][]string{
	//	"bv_crm": {
	//		"sign",
	//		"settlement",
	//		"contract",
	//	},
	//	"vision_hub": {
	//		"sys_user",
	//		"sys_group",
	//		"sys_user_groups",
	//	},
	//}, bc)

	// 生成 资源池相关表的 model 和 query
	//GeneTable("./internal/data/resource_pool", map[string][]string{
	//	"vision_hub": {
	//		"resource_contact",
	//		"resource_operation_log",
	//		"resource_pr_base",
	//		"resource_pr_brand_business",
	//		"resource_pr_brand_tag",
	//		"resource_pr_affiliate_business",
	//		"resource_pr_affiliate_platform",
	//		"resource_pr_affiliate_channel",
	//		"resource_ip_base",
	//		"resource_ip_brand_business",
	//		"resource_ip_affiliate_business",
	//		"resource_ip_affiliate_platform",
	//		"resource_ip_affiliate_channel",
	//		"resource_publisher_base",
	//		"resource_publisher_link",
	//		"resource_publisher_business",
	//		"resource_publisher_channel",
	//		"resource_supplier_base",
	//		"resource_supplier_brand_business",
	//		"resource_outdoor_screen_base",
	//		"resource_outdoor_screen_brand_business",
	//		"resource_integration",
	//
	//		// 记者
	//		"resource_reporter_base",
	//		"resource_reporter_media",
	//		"resource_reporter_article",
	//		"resource_reporter_relation",
	//
	//		// 联盟客
	//		"resource_publisher_link",
	//	},
	//}, bc)

	// 生成 字典管理相关的表的 model 和 query
	//GeneTable("./internal/data/dict_manage", map[string][]string{
	//	"vision_hub": {
	//		"sys_dict_manage",
	//	},
	//}, bc)

	// 生成 项目管理相关的表的 model 和 query
	//GeneTable("./internal/data/project_management", map[string][]string{
	//	"vision_hub": {
	//		"project_management",
	//		"project_management_account",
	//		"project_management_member",
	//		"project_management_record",
	//	},
	//	"bv_crm": {
	//		"sign",
	//		"account",
	//		"settlement",
	//		"contract",
	//		"User",
	//	},
	//}, bc)

	// 生成任务管理相关的 model 和 query
	//GeneTable("./internal/data/customer_project", map[string][]string{
	//	"vision_hub": {
	//		"customer_project_operation_log",
	//		"customer_project",
	//		"customer_project_auth",
	//		"customer_project_operation_log",
	//		"customer_project_settlement",
	//		"settlement",
	//	},
	//}, bc)

	// 生成部门管理相关的 model 和 query
	//GeneTable("./internal/data/auth", map[string][]string{
	//	"vision_hub": {
	//		"sys_user",
	//		"sys_group",
	//		"sys_user_groups",
	//		"sys_menu",
	//		"sys_org",
	//		"sys_permission",
	//		"sys_user_org",
	//		"sys_menu_group",
	//	},
	//}, bc)

	//GeneTable("./internal/data/customer_project_space", map[string][]string{
	//	"vision_hub": {
	//		"material",
	//		"material_upload_log",
	//		"customer_project_resource",
	//		"customer_project_auth",
	//		"ads_account_material",
	//		"customer_project",
	//	},
	//}, bc)

	GeneTable("./internal/data/authorization", map[string][]string{
		"vision_hub": {
			"sys_user_authorization",
			"sys_user_authorization_opt_log",
		},
	}, bc)
}

func main() {
	// 生成所有model和query
	OldGenerate()

	// 生成指定表的model和query 探索只生成指定表结构，暂不可用！！！
	//NewGenerate()
}
