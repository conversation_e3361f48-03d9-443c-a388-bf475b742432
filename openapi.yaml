# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /authorization/v1/authorization:
        get:
            tags:
                - AuthorizationService
            operationId: AuthorizationService_ListAuthorization
            parameters:
                - name: name
                  in: query
                  schema:
                    type: string
                - name: authorization_platform
                  in: query
                  schema:
                    type: array
                    items:
                        type: string
                - name: page_num
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListAuthorizationResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        put:
            tags:
                - AuthorizationService
            operationId: AuthorizationService_UpdateAuthorization
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateAuthorizationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - AuthorizationService
            operationId: AuthorizationService_CreateAuthorization
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateAuthorizationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - AuthorizationService
            operationId: AuthorizationService_RemoveAuthorization
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /authorization/v1/authorization/detail:
        get:
            tags:
                - AuthorizationService
            operationId: AuthorizationService_GetAuthorizationDetail
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAuthorizationDetailResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /open_api/v1/customer_project/ads_account_list:
        get:
            tags:
                - AdsAccountManageService
            description: todo 切换成open api 获取所有启用的广告账号
            operationId: AdsAccountManageService_OpenAPIGetEnableAdsAccountList
            parameters:
                - name: customer_project_number
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/OpenApiGetAdsAccountListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /open_api/v1/resource/search:
        post:
            tags:
                - ResourceManagerService
            description: 获取资源列表
            operationId: ResourceManagerService_GetResourceList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetResourceListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetResourceListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/add:
        post:
            tags:
                - AuthService
            operationId: AuthService_AddUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/delete:
        post:
            tags:
                - AuthService
            operationId: AuthService_DeleteUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeleteUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/feishu_login:
        post:
            tags:
                - AuthService
            operationId: AuthService_FeishuLogin
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/FeishuLoginRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LoginResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/list:
        post:
            tags:
                - AuthService
            operationId: AuthService_UserPageList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserPageListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserPageListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/login:
        post:
            tags:
                - AuthService
            description: 用户管理
            operationId: AuthService_Login
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LoginRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LoginResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/reset_password:
        post:
            tags:
                - AuthService
            operationId: AuthService_ResetPassword
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ResetPasswordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/select:
        post:
            tags:
                - AuthService
            operationId: AuthService_GetUserSelectList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetUserSelectListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUserSelectListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/update:
        post:
            tags:
                - AuthService
            operationId: AuthService_UpdateUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/user_info:
        get:
            tags:
                - AuthService
            operationId: AuthService_GetUserInfo
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserInfoResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/common/cos_upload_config:
        post:
            tags:
                - CommonService
            operationId: CommonService_CosUploadConfig
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CosUploadConfigRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CosUploadConfigResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/common/operation_log:
        post:
            tags:
                - CommonService
            operationId: CommonService_OperationLog
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OperationLogRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/OperationLogResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/able_ads_account:
        post:
            tags:
                - AdsAccountManageService
            description: 启用禁用
            operationId: AdsAccountManageService_EnOrDisableAdsAccount
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/EnOrDisableAdsAccountRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/account:
        post:
            tags:
                - AdsAccountManageService
            description: 获取关联账号(通用版本)
            operationId: AdsAccountManageService_GetCustomerAccount
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetCustomerAccountRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAccountListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/account_by_auth:
        post:
            tags:
                - AdsAccountManageService
            description: 获取授权下的账号
            operationId: AdsAccountManageService_GetAccountByAuth
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetAccountByAuthRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAccountByAuthResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/add:
        post:
            tags:
                - CustomerProjectService
            description: 创建项目
            operationId: CustomerProjectService_CreateCustomerProject
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpOrCrCustomerProjectRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/add_ads_account:
        post:
            tags:
                - AdsAccountManageService
            description: 新增关联账号
            operationId: AdsAccountManageService_AddAdsAccount
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddAdsAccountRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/add_resource:
        post:
            tags:
                - CustomerProResourceService
            description: 新增项目资源
            operationId: CustomerProResourceService_AddCustomerProResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddOrRemoveCustomerProResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/ads_account_list:
        post:
            tags:
                - AdsAccountManageService
            description: 获取项目已关联的账号列表
            operationId: AdsAccountManageService_GetAdsAccountList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetAdsAccountListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAdsAccountListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/config_list:
        post:
            tags:
                - CustomerProConfigService
            description: 获取项目设置
            operationId: CustomerProConfigService_GetCustomerProConfig
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetCustomerProConfigRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetCustomerProConfigResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/delete:
        delete:
            tags:
                - CustomerProjectService
            description: 删除项目
            operationId: CustomerProjectService_DeleteCustomerProject
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/detail:
        get:
            tags:
                - CustomerProjectService
            description: 获取项目详情
            operationId: CustomerProjectService_GetCustomerProjectDetail
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetCustomerProjectDetailReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/list:
        post:
            tags:
                - CustomerProjectService
            description: 获取项目列表
            operationId: CustomerProjectService_GetCustomerProjectList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetCustomerProjectListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetCustomerProjectListReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/material_del:
        post:
            tags:
                - MaterialManageService
            operationId: MaterialManageService_DelMaterial
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DelMaterialIdRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/material_detail:
        get:
            tags:
                - MaterialManageService
            operationId: MaterialManageService_GetMaterialDetail
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: customer_project_id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetMaterialDetailResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/material_list:
        post:
            tags:
                - MaterialManageService
            operationId: MaterialManageService_GetMaterialList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetMaterialListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetMaterialListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/material_medium_upload:
        post:
            tags:
                - MaterialManageService
            operationId: MaterialManageService_UploadMaterialToMedium
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UploadMaterialToMediumRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/material_medium_upload_log:
        get:
            tags:
                - MaterialManageService
            operationId: MaterialManageService_UploadMaterialToMediumLog
            parameters:
                - name: page_size
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_num
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: customer_project_id
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: material_type
                  in: query
                  schema:
                    type: string
                - name: task_id
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: task_status
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadMaterialToMediumLogResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/material_tags:
        get:
            tags:
                - MaterialManageService
            operationId: MaterialManageService_GetMaterialTags
            parameters:
                - name: customer_project_id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetMaterialTagsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/material_upload:
        post:
            tags:
                - MaterialManageService
            operationId: MaterialManageService_UploadMaterialBatch
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UploadMaterialBatchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/material_upload_check:
        post:
            tags:
                - MaterialManageService
            operationId: MaterialManageService_UploadMaterialBatchCheck
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UploadMaterialBatchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadMaterialBatchCheckResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/preview_ads_account:
        post:
            tags:
                - AdsAccountManageService
            description: 预览广告账户的签约信息
            operationId: AdsAccountManageService_PreviewAdsAccount
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PreviewAdsAccountRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PreviewAdsAccountResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/remove_ads_account:
        post:
            tags:
                - AdsAccountManageService
            description: 移除账号
            operationId: AdsAccountManageService_RemoveAdsAccount
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/RemoveAdsAccountRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/remove_resource:
        post:
            tags:
                - CustomerProResourceService
            description: 移除项目资源
            operationId: CustomerProResourceService_RemoveCustomerProResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddOrRemoveCustomerProResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/resource_export:
        post:
            tags:
                - CustomerProResourceService
            description: 导出项目资源
            operationId: CustomerProResourceService_ExportCustomerProResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ExportCustomerProResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/resource_list:
        post:
            tags:
                - CustomerProResourceService
            description: 项目资源列表
            operationId: CustomerProResourceService_GetCustomerProResourceList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetCustomerProResourceListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetCustomerProResourceListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/save_config:
        post:
            tags:
                - CustomerProConfigService
            description: 新增项目设置
            operationId: CustomerProConfigService_SaveCustomerProConfig
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddCustomerProConfigRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/customer_project/update:
        put:
            tags:
                - CustomerProjectService
            description: 更新项目
            operationId: CustomerProjectService_UpdateCustomerProject
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpOrCrCustomerProjectRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/dict/add:
        post:
            tags:
                - DictManageService
            operationId: DictManageService_AddDict
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddDictRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/dict/enum:
        get:
            tags:
                - DictManageService
            operationId: DictManageService_DictList
            parameters:
                - name: parentKey
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DictListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/dict/grandson_enum:
        get:
            tags:
                - DictManageService
            operationId: DictManageService_GetGrandsonEnum
            parameters:
                - name: parentKey
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DictListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/dict/list:
        post:
            tags:
                - DictManageService
            operationId: DictManageService_DictManagePageList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DictManagePageListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DictManagePageListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/dict/tree:
        get:
            tags:
                - DictManageService
            operationId: DictManageService_DictTree
            parameters:
                - name: parentKey
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DictTreeResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/dict/update:
        post:
            tags:
                - DictManageService
            operationId: DictManageService_UpdateDictManage
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateDictManageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/file/generate_sign_url:
        post:
            tags:
                - CommonService
            operationId: CommonService_GenerateSignURL
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GenerateSignURLRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GenerateSignURLResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/file/upload:
        post:
            tags:
                - CommonService
            description: 上传文件
            operationId: CommonService_UploadFile
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UploadFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadFileResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/group/add:
        post:
            tags:
                - AuthGroupService
            description: 创建角色
            operationId: AuthGroupService_AddGroup
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddOrUpdateGroupRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/group/delete:
        delete:
            tags:
                - AuthGroupService
            description: 删除角色
            operationId: AuthGroupService_DeleteGroup
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/group/detail:
        get:
            tags:
                - AuthGroupService
            description: 获取角色详情
            operationId: AuthGroupService_GetGroupDetail
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetGroupDetailResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/group/list:
        post:
            tags:
                - AuthGroupService
            description: 获取角色列表
            operationId: AuthGroupService_QueryGroupList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/QueryGroupListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryGroupListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/group/update:
        put:
            tags:
                - AuthGroupService
            description: 更新角色
            operationId: AuthGroupService_UpdateGroup
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddOrUpdateGroupRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/internal/portal_settlement:
        post:
            tags:
                - ClientPortalService
            operationId: ClientPortalService_GetSettlementList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetSettlementListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetSettlementListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/menu/tree:
        get:
            tags:
                - AuthService
            description: 获取菜单树
            operationId: AuthService_QueryMenuTree
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryMenuTreeResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/org/add:
        post:
            tags:
                - AuthService
            description: 组织管理
            operationId: AuthService_AddOrg
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddOrgRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/org/delete:
        post:
            tags:
                - AuthService
            operationId: AuthService_DeleteOrg
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeleteOrgRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/org/list:
        get:
            tags:
                - AuthService
            description: QueryOrgList 获取部门列表
            operationId: AuthService_QueryOrgList
            parameters:
                - name: org_id
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: search
                  in: query
                  schema:
                    type: string
                - name: is_show_user
                  in: query
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryOrgListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/org/query_by_user:
        post:
            tags:
                - AuthService
            description: QueryOrg 获取同级部门人员及下级
            operationId: AuthService_QueryOrgByUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/QueryOrgByUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryOrgByUserResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/org/update:
        post:
            tags:
                - AuthService
            operationId: AuthService_UpdateOrg
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateOrgRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/ip_add:
        post:
            tags:
                - ResourceManagerIpService
            operationId: ResourceManagerIpService_AddIpResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/IpAddRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/ip_delete:
        delete:
            tags:
                - ResourceManagerIpService
            operationId: ResourceManagerIpService_DeleteIpResource
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/ip_detail:
        get:
            tags:
                - ResourceManagerIpService
            operationId: ResourceManagerIpService_GetIpResourceDetail
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IpResourceDetailResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/ip_list:
        post:
            tags:
                - ResourceManagerIpService
            operationId: ResourceManagerIpService_GetIpResourceList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/IpListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IpResourceListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/ip_update:
        put:
            tags:
                - ResourceManagerIpService
            operationId: ResourceManagerIpService_UpdateIpResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/IpUpdateRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/outdoor_screen_add:
        post:
            tags:
                - ResourceManagerOutdoorScreenService
            operationId: ResourceManagerOutdoorScreenService_CreateOutdoorScreen
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateOutdoorScreenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/outdoor_screen_delete:
        delete:
            tags:
                - ResourceManagerOutdoorScreenService
            operationId: ResourceManagerOutdoorScreenService_DeleteOutdoorScreen
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/outdoor_screen_detail:
        get:
            tags:
                - ResourceManagerOutdoorScreenService
            operationId: ResourceManagerOutdoorScreenService_GetOutdoorScreen
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetOutdoorScreenReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/outdoor_screen_list:
        post:
            tags:
                - ResourceManagerOutdoorScreenService
            operationId: ResourceManagerOutdoorScreenService_ListOutdoorScreen
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ListOutdoorScreenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListOutdoorScreenReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/outdoor_screen_update:
        put:
            tags:
                - ResourceManagerOutdoorScreenService
            operationId: ResourceManagerOutdoorScreenService_UpdateOutdoorScreen
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateOutdoorScreenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/pr_add:
        post:
            tags:
                - ResourceManagerPrService
            operationId: ResourceManagerPrService_AddPrResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PrAddRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/pr_delete:
        delete:
            tags:
                - ResourceManagerPrService
            operationId: ResourceManagerPrService_DeletePrResource
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/pr_detail:
        get:
            tags:
                - ResourceManagerPrService
            operationId: ResourceManagerPrService_GetPrResourceDetail
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PrResourceDetailResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/pr_list:
        post:
            tags:
                - ResourceManagerPrService
            operationId: ResourceManagerPrService_GetPrResourceList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PrListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PrResourceListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/pr_update:
        put:
            tags:
                - ResourceManagerPrService
            operationId: ResourceManagerPrService_UpdatePrResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PrUpdateRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/publisher_add:
        post:
            tags:
                - ResourceManagerPublisherService
            operationId: ResourceManagerPublisherService_CreatePublisher
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CrOrUpPublisherRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/publisher_delete:
        delete:
            tags:
                - ResourceManagerPublisherService
            operationId: ResourceManagerPublisherService_DeletePublisher
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/publisher_detail:
        get:
            tags:
                - ResourceManagerPublisherService
            operationId: ResourceManagerPublisherService_GetPublisher
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetPublisherReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/publisher_list:
        post:
            tags:
                - ResourceManagerPublisherService
            operationId: ResourceManagerPublisherService_ListPublisher
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ListPublisherRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListPublisherReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/publisher_update:
        put:
            tags:
                - ResourceManagerPublisherService
            operationId: ResourceManagerPublisherService_UpdatePublisher
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CrOrUpPublisherRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/reporter_add:
        post:
            tags:
                - ResourceManagerReporterService
            description: 添加记者资源
            operationId: ResourceManagerReporterService_AddReporterResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ReporterAddOrUpdateRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/reporter_article:
        get:
            tags:
                - ResourceManagerReporterService
            description: 获取记者文章，分页获取
            operationId: ResourceManagerReporterService_GetReporterArticle
            parameters:
                - name: page_size
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_num
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: reporter_id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetReporterArticleResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/reporter_delete:
        delete:
            tags:
                - ResourceManagerReporterService
            description: 删除记者资源
            operationId: ResourceManagerReporterService_DeleteReporterResource
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/reporter_detail:
        get:
            tags:
                - ResourceManagerReporterService
            description: 获取记者资源详情
            operationId: ResourceManagerReporterService_GetReporterResourceDetail
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ReporterResourceDetailResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/reporter_list:
        post:
            tags:
                - ResourceManagerReporterService
            description: 获取记者资源列表
            operationId: ResourceManagerReporterService_GetReporterResourceList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ReporterListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ReporterResourceListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/reporter_update:
        put:
            tags:
                - ResourceManagerReporterService
            description: 更新记者资源
            operationId: ResourceManagerReporterService_UpdateReporterResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ReporterAddOrUpdateRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/supplier_add:
        post:
            tags:
                - ResourceManagerSupplierService
            operationId: ResourceManagerSupplierService_CreateSupplier
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateSupplierRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/supplier_delete:
        delete:
            tags:
                - ResourceManagerSupplierService
            operationId: ResourceManagerSupplierService_DeleteSupplier
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/supplier_detail:
        get:
            tags:
                - ResourceManagerSupplierService
            operationId: ResourceManagerSupplierService_GetSupplier
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetSupplierReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/supplier_list:
        post:
            tags:
                - ResourceManagerSupplierService
            operationId: ResourceManagerSupplierService_ListSupplier
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ListSupplierRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListSupplierReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/resource/supplier_update:
        put:
            tags:
                - ResourceManagerSupplierService
            operationId: ResourceManagerSupplierService_UpdateSupplier
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateSupplierRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/task/create_export:
        post:
            tags:
                - TaskService
            description: 任务管理
            operationId: TaskService_CreateExportTask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateExportTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateExportTaskResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/task/list:
        post:
            tags:
                - TaskService
            operationId: TaskService_TaskPageList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/TaskPageListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TaskPageListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/tiktok/digital_avatar:
        get:
            tags:
                - TiktokService
            description: 数字人-获取可用数字人列表
            operationId: TiktokService_GetTiktokDigitalAvatarList
            parameters:
                - name: page_num
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetTiktokDigitalAvatarListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/tiktok/digital_avatar_task:
        get:
            tags:
                - TiktokService
            description: 数字人-获取数字人任务列表
            operationId: TiktokService_GetTiktokDigitalAvatarTask
            parameters:
                - name: page_num
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: avatar_id
                  in: query
                  schema:
                    type: string
                - name: start_date
                  in: query
                  schema:
                    type: string
                - name: end_date
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetTiktokDigitalAvatarTaskResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - TiktokService
            description: 数字人-创建数字人任务
            operationId: TiktokService_CreateTiktokDigitalAvatarTask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateTiktokDigitalAvatarTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateTiktokDigitalAvatarTaskResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/tiktok/mix_video_aigc_task:
        post:
            tags:
                - TiktokService
            description: 创建视频混剪任务
            operationId: TiktokService_CreateMixVideoAIGCTask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateMixVideoAIGCTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateVideoAIGCTaskResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/tiktok/video_aigc_task:
        post:
            tags:
                - TiktokService
            description: 视频翻配-创建视频翻配任务
            operationId: TiktokService_CreateVideoAIGCTask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateVideoAIGCTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateVideoAIGCTaskResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/tiktok/video_aigc_task_list:
        post:
            tags:
                - TiktokService
            description: 视频翻配-获取视频翻配任务列表
            operationId: TiktokService_GetVideoAIGCTask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetVideoAIGCTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetVideoAIGCTaskResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/tiktok/video_fix_search:
        post:
            tags:
                - TiktokService
            operationId: TiktokService_VideoFixSearch
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VideoFixSearchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VideoFixSearchResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/tiktok/video_fix_upload:
        post:
            tags:
                - TiktokService
            operationId: TiktokService_VideoFixUpload
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VideoFixUploadRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VideoFixUploadResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user_activity/add:
        post:
            tags:
                - UserActivity
            operationId: UserActivity_CreateUserActivity
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateUserActivityRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateUserActivityReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        AccountByAuthData:
            type: object
            properties:
                config_key:
                    type: string
                account_item:
                    type: array
                    items:
                        $ref: '#/components/schemas/AccountItem'
        AccountData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/AccountListItem'
        AccountItem:
            type: object
            properties:
                account_id:
                    type: string
                account_name:
                    type: string
        AccountListItem:
            type: object
            properties:
                medium:
                    type: string
                account_id:
                    type: string
                account_name:
                    type: string
                status:
                    type: string
                account_extra_info:
                    type: object
        AddAdsAccountRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    format: int32
                ad_account_list:
                    type: array
                    items:
                        type: string
                platform:
                    type: string
        AddCustomerProConfigRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    format: int32
                config_key:
                    type: string
                config_value:
                    type: string
        AddDictRequest:
            type: object
            properties:
                label:
                    type: string
                labelKey:
                    type: string
                labelValue:
                    type: integer
                    format: int32
                parentKey:
                    type: string
                remark:
                    type: string
        AddOrRemoveCustomerProResourceRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    description: 项目ID
                    format: int32
                resource_type:
                    type: string
                    description: 资源类型
                resource_id_list:
                    type: array
                    items:
                        type: string
                    description: 资源ID
        AddOrUpdateArticle:
            type: object
            properties:
                article_title:
                    type: string
                article_url:
                    type: string
        AddOrUpdateGroupRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                code:
                    type: string
                description:
                    type: string
                user_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
            description: ================ 请求定义 START ======================
        AddOrUpdateReportMedia:
            type: object
            properties:
                media_name:
                    type: string
                muckrack_url:
                    type: string
                site_url:
                    type: string
        AddOrgRequest:
            type: object
            properties:
                org_name:
                    type: string
                parent_id:
                    type: integer
                    format: int32
                user_ids:
                    type: array
                    items:
                        type: string
        AddUserRequest:
            type: object
            properties:
                password:
                    type: string
                email:
                    type: string
                first_name:
                    type: string
                last_name:
                    type: string
                phone:
                    type: string
                org_id:
                    type: integer
                    format: int32
        AdsAccountData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/AdsAccountListItem'
        AdsAccountListItem:
            type: object
            properties:
                medium:
                    type: string
                ads_account_id:
                    type: string
                status:
                    type: string
                sign_info:
                    $ref: '#/components/schemas/SignInfo'
        AdsAccountSignData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/AdsAccountListItem'
        AffiliateBusiness:
            type: object
            properties:
                cooperation_requirements:
                    type: string
                media_kit:
                    type: array
                    items:
                        type: string
                price:
                    $ref: '#/components/schemas/Price'
                remarks:
                    type: string
            description: 联盟商务信息
        AffiliateBusinessInfo:
            type: object
            properties:
                business:
                    $ref: '#/components/schemas/AffiliateBusiness'
                contact:
                    $ref: '#/components/schemas/Contact'
            description: 联盟商务信息 + 联系人
        AffiliateChannelInfo:
            type: object
            properties:
                channel_type:
                    type: array
                    items:
                        type: string
                promotion_category:
                    type: array
                    items:
                        type: string
                promotion_type:
                    type: array
                    items:
                        type: string
                customer_journey_stage:
                    type: string
                marketing_objectives:
                    type: array
                    items:
                        type: string
                size:
                    type: string
                tag:
                    type: array
                    items:
                        type: string
                media_viscosity:
                    type: string
            description: 联盟渠道信息
        AffiliateInfo:
            type: object
            properties:
                business_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/AffiliateBusinessInfo'
                platform_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/PlatformInfo'
                channel_info:
                    $ref: '#/components/schemas/AffiliateChannelInfo'
            description: 联盟参数信息
        AffiliateParam:
            type: object
            properties:
                aff_platform_name_list:
                    type: array
                    items:
                        type: string
                channel_type_list:
                    type: array
                    items:
                        type: string
                promotion_category_list:
                    type: array
                    items:
                        type: string
                promotion_type_list:
                    type: array
                    items:
                        type: string
                customer_journey_stage_list:
                    type: array
                    items:
                        type: string
                marketing_objectives_list:
                    type: array
                    items:
                        type: string
                size_list:
                    type: array
                    items:
                        type: string
                tag_list:
                    type: array
                    items:
                        type: string
                aff_platform_publisher_id:
                    type: string
                aff_platform_publisher_name:
                    type: string
                size:
                    type: string
            description: 联盟参数
        Article:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                article_url:
                    type: string
                article_title:
                    type: string
                article_publish_time:
                    type: string
                article_description:
                    type: string
                media_name:
                    type: string
        Authorization:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                authorization_type:
                    type: string
                authorization_platform:
                    type: string
                authorization_config:
                    type: object
                status:
                    type: string
                expires_at:
                    type: string
                create_user_id:
                    type: integer
                    format: int32
            description: 授权信息
        AuthorizationListData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/Authorization'
                pagination:
                    $ref: '#/components/schemas/Pagination'
        AvatarInfo:
            type: object
            properties:
                avatar_id:
                    type: string
        BaseParam:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
                name:
                    type: string
                price:
                    $ref: '#/components/schemas/Price'
                size_list:
                    type: array
                    items:
                        type: string
                type_list:
                    type: array
                    items:
                        type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                age_list:
                    type: array
                    items:
                        type: string
                gender_ratio:
                    $ref: '#/components/schemas/GenderRatio'
                country_top:
                    type: array
                    items:
                        type: string
                main_business_list:
                    type: array
                    items:
                        type: string
                advantage_industry_list:
                    type: array
                    items:
                        type: string
                supplier_attributes:
                    type: string
                language_list:
                    type: array
                    items:
                        type: string
                    description: pr additional param
                product_type_list:
                    type: array
                    items:
                        type: string
                release_speed:
                    $ref: '#/components/schemas/Interval'
                has_translate:
                    type: string
                has_bottom_info:
                    type: string
                supplier_list:
                    type: array
                    items:
                        type: string
            description: 通用基础请求参数
        BrandBusiness:
            type: object
            properties:
                contact_type:
                    type: string
                service_content:
                    type: string
                price:
                    $ref: '#/components/schemas/Price'
                payment_terms:
                    type: integer
                    format: int32
                remarks:
                    type: string
                operate_method:
                    type: string
            description: 品牌商务信息
        BrandBusinessInfo:
            type: object
            properties:
                business:
                    $ref: '#/components/schemas/BrandBusiness'
                contact:
                    $ref: '#/components/schemas/Contact'
            description: 品牌商务信息 + 联系人
        BrandInfo:
            type: object
            properties:
                business_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/BrandBusinessInfo'
                tag_info:
                    $ref: '#/components/schemas/BrandTagInfo'
            description: 品牌参数信息
        BrandParam:
            type: object
            properties:
                contact_type:
                    type: string
                payment_terms_list:
                    type: array
                    items:
                        type: integer
                        format: int32
                tag_list:
                    type: array
                    items:
                        type: string
                size_list:
                    type: array
                    items:
                        type: string
                service_content:
                    type: array
                    items:
                        type: string
            description: 品牌参数
        BrandTagInfo:
            type: object
            properties:
                name:
                    type: array
                    items:
                        type: string
            description: 品牌渠道信息
        CommonResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
        CommonUserInfo:
            type: object
            properties:
                user_id:
                    type: integer
                    format: int32
                user_name:
                    type: string
                user_email:
                    type: string
            description: 通用用户信息
        Contact:
            type: object
            properties:
                name:
                    type: string
                contact_info:
                    $ref: '#/components/schemas/ContactInfo'
                remarks:
                    type: string
                responsible_person:
                    $ref: '#/components/schemas/ContactInfo'
            description: 联系人
        ContactInfo:
            type: object
            properties:
                email:
                    type: string
                phone:
                    type: string
                wechat:
                    type: string
                whatsapp:
                    type: string
                name:
                    type: string
            description: 联系信息
        CosUploadConfig:
            type: object
            properties:
                tmp_secret_id:
                    type: string
                tmp_secret_key:
                    type: string
                session_token:
                    type: string
                signature:
                    type: string
                expire:
                    type: string
                bucket:
                    type: string
                dir:
                    type: string
                cdn_domain:
                    type: string
                region:
                    type: string
        CosUploadConfigRequest:
            type: object
            properties:
                dir:
                    type: string
        CosUploadConfigResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/CosUploadConfig'
        CrOrUpPublisherRequest:
            type: object
            properties:
                base_info:
                    $ref: '#/components/schemas/PublisherResource'
                affiliate_info:
                    $ref: '#/components/schemas/AffiliateInfo'
                contact:
                    type: array
                    items:
                        $ref: '#/components/schemas/PublisherContactInfo'
            description: 新增
        CreateAuthorizationRequest:
            type: object
            properties:
                name:
                    type: string
                authorization_type:
                    type: string
                authorization_platform:
                    type: string
                authorization_config:
                    type: object
            description: 新增授权
        CreateExportTaskRequest:
            type: object
            properties:
                taskType:
                    type: string
                queryParams:
                    type: string
                taskName:
                    type: string
            description: ================ 请求定义 START ======================
        CreateExportTaskResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/CreateTaskInfo'
        CreateMixVideoAIGCTaskRequest:
            type: object
            properties:
                aigc_video_type:
                    type: string
                product_video_info:
                    $ref: '#/components/schemas/ProductVideoInfo'
        CreateOutdoorScreenRequest:
            type: object
            properties:
                baseInfo:
                    $ref: '#/components/schemas/OutdoorScreenResource'
                brandInfo:
                    $ref: '#/components/schemas/BrandInfo'
            description: 新增
        CreateSupplierRequest:
            type: object
            properties:
                base_info:
                    $ref: '#/components/schemas/SupplierResource'
                brand_info:
                    $ref: '#/components/schemas/BrandInfo'
            description: 新增
        CreateTaskInfo:
            type: object
            properties:
                task_id:
                    type: integer
                    format: int32
        CreateTiktokDigitalAvatar:
            type: object
            properties:
                package_id:
                    type: string
                task_id:
                    type: string
            description: 创建数字人任务
        CreateTiktokDigitalAvatarData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/CreateTiktokDigitalAvatar'
        CreateTiktokDigitalAvatarTask:
            type: object
            properties:
                avatar_id:
                    type: string
                script:
                    type: string
                package_id:
                    type: string
                video_name:
                    type: string
        CreateTiktokDigitalAvatarTaskRequest:
            type: object
            properties:
                tasks:
                    type: array
                    items:
                        $ref: '#/components/schemas/CreateTiktokDigitalAvatarTask'
        CreateTiktokDigitalAvatarTaskResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/CreateTiktokDigitalAvatarData'
        CreateUserActivityReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
        CreateUserActivityRequest:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/UserActivityEvent'
        CreateVideoAIGCTaskData:
            type: object
            properties:
                task_ids:
                    type: array
                    items:
                        type: string
        CreateVideoAIGCTaskRequest:
            type: object
            properties:
                aigc_video_type:
                    type: string
                dubbing_video_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/DubbingVideoInfo'
        CreateVideoAIGCTaskResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/CreateVideoAIGCTaskData'
        CustomerProConfigData:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                config_key:
                    type: string
                config_status:
                    type: string
                config_display_name:
                    type: string
                config_value:
                    type: string
        CustomerProjectInfo:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                project_id:
                    type: string
                project_name:
                    type: string
                project_description:
                    type: string
                tags:
                    type: array
                    items:
                        type: string
                end_date:
                    type: string
                start_date:
                    type: string
                owner_user:
                    $ref: '#/components/schemas/UserInfo'
                customer_entity:
                    type: integer
                    format: int32
                customer_entity_title:
                    type: string
                customer_entity_short_title:
                    type: string
                project_members:
                    type: array
                    items:
                        $ref: '#/components/schemas/UserInfo'
                org_ids:
                    type: array
                    items:
                        type: string
                settlement_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
                our_company_name:
                    type: string
                sign:
                    allOf:
                        - $ref: '#/components/schemas/Sign'
                    description: 中台签约信息
            description: 基础设施项目信息
        CustomerProjectList:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/CustomerProjectInfo'
            description: 基础设施项目列表
        DelMaterialIdRequest:
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: integer
                        format: int32
                customer_project_id:
                    type: integer
                    format: int32
        DeleteOrgRequest:
            type: object
            properties:
                org_id:
                    type: integer
                    format: int32
        DeleteUserRequest:
            type: object
            properties:
                user_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
        DictListData:
            type: object
            properties:
                label:
                    type: string
                label_key:
                    type: string
                label_value:
                    type: integer
                    format: int32
        DictListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/DictListData'
        DictManagePageListData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/DictManagePageListItem'
                pagination:
                    $ref: '#/components/schemas/Pagination'
        DictManagePageListItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                create_time:
                    type: string
                update_time:
                    type: string
                create_user:
                    type: string
                update_user:
                    type: string
                label:
                    type: string
                label_key:
                    type: string
                label_value:
                    type: integer
                    format: int32
                remark:
                    type: string
                parent_key:
                    type: string
        DictManagePageListRequest:
            type: object
            properties:
                parentKey:
                    type: string
                labelKey:
                    type: string
                pageNum:
                    type: integer
                    format: int32
                pageSize:
                    type: integer
                    format: int32
        DictManagePageListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/DictManagePageListData'
        DictTreeData:
            type: object
            properties:
                label:
                    type: string
                label_key:
                    type: string
                label_value:
                    type: integer
                    format: int32
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/DictTreeData'
        DictTreeResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/DictTreeData'
        DubbingInfo:
            type: object
            properties:
                original_scripts:
                    type: array
                    items:
                        $ref: '#/components/schemas/Script'
                target_language:
                    type: string
                translated_scripts:
                    type: array
                    items:
                        $ref: '#/components/schemas/Script'
        DubbingVideoInfo:
            type: object
            properties:
                video_url:
                    type: string
                target_languages:
                    type: array
                    items:
                        type: string
                video_name:
                    type: string
                subtitle_enabled:
                    type: boolean
                script:
                    type: array
                    items:
                        $ref: '#/components/schemas/Script'
        EnOrDisableAdsAccountRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    format: int32
                ads_account_list:
                    type: array
                    items:
                        type: string
                status:
                    type: string
        ExportCustomerProResourceRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    description: 项目ID
                    format: int32
                resource_type:
                    type: string
                    description: 资源类型
                task_name:
                    type: string
                    description: 任务名称
                resource_name:
                    type: string
                    description: 名称
        FeishuLoginRequest:
            type: object
            properties:
                code:
                    type: string
                source_url:
                    type: string
        GenderRatio:
            type: object
            properties:
                male:
                    type: number
                    format: float
                female:
                    type: number
                    format: float
            description: 性别比例
        GenerateSignURLRequest:
            type: object
            properties:
                fileUri:
                    type: string
        GenerateSignURLResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/SignFileURL'
        GetAccountByAuthRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    format: int32
                config_key:
                    type: string
        GetAccountByAuthResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/AccountByAuthData'
        GetAccountListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/AccountData'
        GetAdsAccountListRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    format: int32
                ads_account_id:
                    type: string
                medium:
                    type: array
                    items:
                        type: string
                status:
                    type: string
                page_size:
                    type: integer
                    format: int32
                page_num:
                    type: integer
                    format: int32
        GetAdsAccountListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/AdsAccountData'
        GetAuthorizationDetailResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/Authorization'
        GetCustomerAccountRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    format: int32
                account_id:
                    type: string
                medium:
                    type: array
                    items:
                        type: string
                status:
                    type: string
                page_size:
                    type: integer
                    format: int32
                page_num:
                    type: integer
                    format: int32
                platform:
                    type: string
        GetCustomerProConfigRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    format: int32
                config_key:
                    type: integer
                    format: int32
                config_status:
                    type: string
        GetCustomerProConfigResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/CustomerProConfigData'
        GetCustomerProResourceListData:
            type: object
            properties:
                ip_resp:
                    $ref: '#/components/schemas/IpResourceListResponse'
                pr_resp:
                    $ref: '#/components/schemas/PrResourceListResponse'
                outdoor_screen_resp:
                    $ref: '#/components/schemas/ListOutdoorScreenReply'
                supplier_resp:
                    $ref: '#/components/schemas/ListSupplierReply'
                reporter_resp:
                    $ref: '#/components/schemas/ReporterResourceListResponse'
                publisher_resp:
                    $ref: '#/components/schemas/ListPublisherReply'
        GetCustomerProResourceListRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    description: 项目ID
                    format: int32
                resource_type:
                    type: string
                    description: 资源类型
                page_size:
                    type: integer
                    description: 分页参数
                    format: int32
                page_num:
                    type: integer
                    description: 页码
                    format: int32
                resource_name:
                    type: string
                    description: 名称
        GetCustomerProResourceListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/GetCustomerProResourceListData'
        GetCustomerProjectDetailReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/CustomerProjectInfo'
            description: 获取基础设施项目详情的响应
        GetCustomerProjectListReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/CustomerProjectList'
            description: 获取基础设施项目列表的响应
        GetCustomerProjectListRequest:
            type: object
            properties:
                page_size:
                    type: integer
                    format: int32
                page_num:
                    type: integer
                    format: int32
                project_name:
                    type: string
                customer_entity:
                    type: integer
                    format: int32
                user_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
            description: 获取基础设施项目列表的请求
        GetGroupDetailResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/Group'
            description: ================ 响应定义 START ======================
        GetMaterialDetailResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/Material'
        GetMaterialListRequest:
            type: object
            properties:
                page_size:
                    type: integer
                    format: int32
                page_num:
                    type: integer
                    format: int32
                px_list:
                    type: array
                    items:
                        type: string
                material_name:
                    type: string
                material_tags:
                    type: array
                    items:
                        type: string
                upload_start_time:
                    type: string
                upload_end_time:
                    type: string
                customer_project_id:
                    type: integer
                    format: int32
                material_type:
                    type: string
            description: ================ 请求定义 START ======================
        GetMaterialListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/MaterialData'
        GetMaterialTagsResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        type: string
        GetOutdoorScreenReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/OutdoorScreenData'
        GetPublisherReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/PublisherData'
        GetReporterArticleResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/ReporterArticleData'
        GetResourceListRequest:
            type: object
            properties:
                resource_type:
                    type: string
                resource_name:
                    type: string
                resource_desc:
                    type: string
                resource_number:
                    type: string
        GetResourceListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/ResourceInfo'
        GetSettlementListRequest:
            type: object
            properties:
                page_size:
                    type: integer
                    format: int32
                page_num:
                    type: integer
                    format: int32
                search:
                    type: string
                settlement_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
            description: ============== 服务定义END ===========================
        GetSettlementListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/SettlementListData'
        GetSupplierReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/SupplierData'
        GetTiktokDigitalAvatarListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/TiktokDigitalAvatarData'
            description: 可用数字人响应
        GetTiktokDigitalAvatarTaskResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/TiktokDigitalAvatarTaskData'
        GetUserSelectListRequest:
            type: object
            properties:
                search:
                    type: string
                page_size:
                    type: integer
                    format: int32
                ids:
                    type: array
                    items:
                        type: integer
                        format: int32
        GetUserSelectListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/CommonUserInfo'
        GetVideoAIGCTaskRequest:
            type: object
            properties:
                aigc_video_types:
                    type: array
                    items:
                        type: string
                page_size:
                    type: integer
                    format: int32
                page_num:
                    type: integer
                    format: int32
        GetVideoAIGCTaskResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/VideoAIGCTaskData'
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        Group:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                code:
                    type: string
                description:
                    type: string
                group_users:
                    type: array
                    items:
                        $ref: '#/components/schemas/CommonUserInfo'
            description: 业务上单个实体的标准返回
        GroupListItem:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/Group'
        Indicator:
            type: object
            properties:
                mozSpamScore:
                    type: integer
                    format: int32
                mozDomainAuthority:
                    type: integer
                    format: int32
                similarWebGlobalRank:
                    type: integer
                    format: int32
                similarWebVerified:
                    type: boolean
                averageViews:
                    type: integer
                    format: int32
                averageShare:
                    type: integer
                    format: int32
                averageLikes:
                    type: integer
                    format: int32
                averageReviews:
                    type: integer
                    format: int32
                averageInteractionRate:
                    type: integer
                    format: int32
                averageFavorites:
                    type: integer
                    format: int32
        InputImageList:
            type: object
            properties:
                image_url_list:
                    type: array
                    items:
                        type: string
        InputVideoList:
            type: object
            properties:
                video_id_list:
                    type: array
                    items:
                        type: string
        Interval:
            type: object
            properties:
                min:
                    type: number
                    format: float
                max:
                    type: number
                    format: float
                unit:
                    type: string
        IpAddRequest:
            type: object
            properties:
                baseInfo:
                    $ref: '#/components/schemas/IpResource'
                brandInfo:
                    $ref: '#/components/schemas/BrandInfo'
                affiliateInfo:
                    $ref: '#/components/schemas/AffiliateInfo'
            description: PR添加请求
        IpData:
            type: object
            properties:
                base_info:
                    $ref: '#/components/schemas/IpResource'
                brand_info:
                    $ref: '#/components/schemas/BrandInfo'
                affiliate_info:
                    $ref: '#/components/schemas/AffiliateInfo'
            description: PR资源详情返回
        IpListData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/IpResource'
            description: PR资源列表返回
        IpListRequest:
            type: object
            properties:
                base_param:
                    $ref: '#/components/schemas/BaseParam'
                brand_param:
                    $ref: '#/components/schemas/BrandParam'
                affiliate_param:
                    $ref: '#/components/schemas/AffiliateParam'
                customer_project_id:
                    type: integer
                    format: int32
            description: PR列表请求
        IpResource:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                type:
                    type: array
                    items:
                        type: string
                homepage:
                    type: string
                introduction:
                    type: string
                introduction_date:
                    type: string
                supplier:
                    type: array
                    items:
                        type: string
                external_brands:
                    type: array
                    items:
                        type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                update_time:
                    type: string
            description: PR资源
        IpResourceDetailResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/IpData'
        IpResourceListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/IpListData'
        IpUpdateRequest:
            type: object
            properties:
                baseInfo:
                    $ref: '#/components/schemas/IpResource'
                brandInfo:
                    $ref: '#/components/schemas/BrandInfo'
                affiliateInfo:
                    $ref: '#/components/schemas/AffiliateInfo'
            description: PR添加返回
        ListAuthorizationResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/AuthorizationListData'
        ListOutdoorScreenReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/OutdoorScreenList'
        ListOutdoorScreenRequest:
            type: object
            properties:
                base_param:
                    $ref: '#/components/schemas/BaseParam'
                brand_param:
                    $ref: '#/components/schemas/BrandParam'
                customer_project_id:
                    type: integer
                    format: int32
        ListPublisherReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/PublisherList'
        ListPublisherRequest:
            type: object
            properties:
                base_param:
                    $ref: '#/components/schemas/BaseParam'
                affiliate_param:
                    $ref: '#/components/schemas/AffiliateParam'
                order_param:
                    $ref: '#/components/schemas/OrderParam'
                min_fans:
                    type: integer
                    description: publisher additional param
                    format: int32
                max_fans:
                    type: integer
                    format: int32
                min_monthly_visits:
                    type: integer
                    format: int32
                max_monthly_visits:
                    type: integer
                    format: int32
                country_top:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                publisher_link:
                    type: string
                type_list:
                    type: array
                    items:
                        type: string
                customer_project_id:
                    type: integer
                    format: int32
        ListSupplierReply:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/SupplierList'
        ListSupplierRequest:
            type: object
            properties:
                base_param:
                    $ref: '#/components/schemas/BaseParam'
                affiliate_param:
                    $ref: '#/components/schemas/AffiliateParam'
                customer_project_id:
                    type: integer
                    format: int32
        LoginInfo:
            type: object
            properties:
                token:
                    type: string
                email:
                    type: string
                user_name:
                    type: string
        LoginRequest:
            type: object
            properties:
                email:
                    type: string
                password:
                    type: string
        LoginResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/LoginInfo'
        Material:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                material_name:
                    type: string
                language:
                    type: string
                material_tags:
                    type: array
                    items:
                        type: string
                material_px:
                    type: string
                size:
                    type: string
                create_time:
                    type: string
                create_user:
                    type: string
                material_url:
                    type: string
                material_time:
                    type: string
                material_cover_url:
                    type: string
        MaterialData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/MaterialListItem'
        MaterialListItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                material_name:
                    type: string
                material_px:
                    type: string
                material_time:
                    type: string
                material_cover_url:
                    type: string
                material_url:
                    type: string
        MediaIndicator:
            type: object
            properties:
                average_views:
                    type: integer
                    format: int32
                average_share:
                    type: integer
                    format: int32
                average_likes:
                    type: integer
                    format: int32
                average_reviews:
                    type: integer
                    format: int32
                average_interaction_rate:
                    type: integer
                    format: int32
                average_favorites:
                    type: integer
                    format: int32
        MenuTree:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                menu_name:
                    type: string
                parent_id:
                    type: integer
                    format: int32
                api_name:
                    type: string
                front_api_name:
                    type: string
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/MenuTree'
        OpenApiGetAdsAccountListData:
            type: object
            properties:
                medium:
                    type: string
                ads_account_ids:
                    type: array
                    items:
                        type: string
        OpenApiGetAdsAccountListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/OpenApiGetAdsAccountListData'
        OperationDetail:
            type: object
            properties:
                field_name:
                    type: string
                old_value:
                    type: string
                new_value:
                    type: string
                operator:
                    type: string
                operation_time:
                    type: string
        OperationLogData:
            type: object
            properties:
                category:
                    type: integer
                    format: enum
                subtype:
                    type: integer
                    format: enum
                detail:
                    type: array
                    items:
                        $ref: '#/components/schemas/OperationDetail'
                operation_id:
                    type: integer
                    format: int32
        OperationLogList:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/OperationLogData'
        OperationLogRequest:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
                operation_module:
                    type: integer
                    description: 校验 operation_module 必须是定义的 enum 值之一
                    format: enum
                operation_submodule:
                    type: integer
                    description: 校验 operation_submodule 必须是定义的 enum 值之一
                    format: enum
                id:
                    type: integer
                    format: int32
        OperationLogResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/OperationLogList'
        OrderParam:
            type: object
            properties:
                field:
                    type: string
                order:
                    type: string
            description: 排序参数
        OrgData:
            type: object
            properties:
                org_name:
                    type: string
                org_id:
                    type: integer
                    format: int32
                org_user:
                    type: array
                    items:
                        $ref: '#/components/schemas/OrgUser'
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/OrgData'
            description: 组织数据结构
        OrgUser:
            type: object
            properties:
                user_id:
                    type: integer
                    format: int32
                email:
                    type: string
                user_name:
                    type: string
        OutdoorScreenData:
            type: object
            properties:
                base_info:
                    $ref: '#/components/schemas/OutdoorScreenResource'
                brand_info:
                    $ref: '#/components/schemas/BrandInfo'
        OutdoorScreenList:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/OutdoorScreenResource'
            description: 列表
        OutdoorScreenResource:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                type:
                    type: array
                    items:
                        type: string
                introduction_date:
                    type: string
                supplier:
                    type: array
                    items:
                        type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                update_time:
                    type: string
            description: |-
                详情
                 OutdoorScreen资源
        Pagination:
            type: object
            properties:
                total:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
                page_num:
                    type: integer
                    format: int32
            description: 分页参数
        PlatformInfo:
            type: object
            properties:
                aff_platform_name:
                    type: string
                aff_platform_publisher_id:
                    type: string
                aff_platform_publisher_name:
                    type: string
            description: 联盟平台信息
        PrAddRequest:
            type: object
            properties:
                baseInfo:
                    $ref: '#/components/schemas/PrResource'
                brandInfo:
                    $ref: '#/components/schemas/BrandInfo'
                affiliateInfo:
                    $ref: '#/components/schemas/AffiliateInfo'
            description: PR添加请求
        PrData:
            type: object
            properties:
                base_info:
                    $ref: '#/components/schemas/PrResource'
                brand_info:
                    $ref: '#/components/schemas/BrandInfo'
                affiliate_info:
                    $ref: '#/components/schemas/AffiliateInfo'
            description: PR资源详情返回
        PrListData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/PrResource'
            description: PR资源列表返回
        PrListRequest:
            type: object
            properties:
                base_param:
                    $ref: '#/components/schemas/BaseParam'
                brand_param:
                    $ref: '#/components/schemas/BrandParam'
                affiliate_param:
                    $ref: '#/components/schemas/AffiliateParam'
                customer_project_id:
                    type: integer
                    format: int32
            description: PR列表请求
        PrResource:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                type:
                    type: array
                    items:
                        type: string
                country_top:
                    type: array
                    items:
                        type: string
                homepage:
                    type: string
                introduction:
                    type: string
                size:
                    type: string
                monthly_visit:
                    type: integer
                    format: int32
                gender_ratio:
                    $ref: '#/components/schemas/GenderRatio'
                introduction_date:
                    type: string
                supplier:
                    type: array
                    items:
                        type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                age_top:
                    type: array
                    items:
                        type: string
                update_time:
                    type: string
                language_category:
                    type: array
                    items:
                        type: string
                product_type:
                    type: string
                release_speed:
                    $ref: '#/components/schemas/Interval'
                include_status:
                    type: string
                has_translate:
                    type: integer
                    format: int32
                has_bottom_info:
                    type: integer
                    format: int32
                image_cost:
                    type: string
                product_detail:
                    type: string
                service_process:
                    type: string
                promotion_report:
                    type: array
                    items:
                        type: string
                specific_requirement:
                    type: string
            description: PR资源
        PrResourceDetailResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/PrData'
        PrResourceListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/PrListData'
        PrUpdateRequest:
            type: object
            properties:
                baseInfo:
                    $ref: '#/components/schemas/PrResource'
                brandInfo:
                    $ref: '#/components/schemas/BrandInfo'
                affiliateInfo:
                    $ref: '#/components/schemas/AffiliateInfo'
            description: PR添加返回
        PreviewAdsAccountRequest:
            type: object
            properties:
                ads_account_list:
                    type: array
                    items:
                        type: string
        PreviewAdsAccountResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/AdsAccountSignData'
        Price:
            type: object
            properties:
                min:
                    type: number
                    format: float
                max:
                    type: number
                    format: float
                min_usd:
                    type: number
                    format: float
                max_usd:
                    type: number
                    format: float
                currency:
                    type: string
            description: 价格
        ProductInfoList:
            type: object
            properties:
                source_language:
                    type: string
                product_name:
                    type: string
                title:
                    type: string
                description:
                    type: string
                selling_points:
                    type: array
                    items:
                        type: string
                brand:
                    type: string
                price:
                    type: number
                    format: double
                currency:
                    type: string
        ProductVideoInfo:
            type: object
            properties:
                video_generation_count:
                    type: integer
                    format: int32
                target_language:
                    type: string
                product_info_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/ProductInfoList'
                input_image_list:
                    $ref: '#/components/schemas/InputImageList'
                input_video_list:
                    $ref: '#/components/schemas/InputVideoList'
                avatar_info:
                    $ref: '#/components/schemas/AvatarInfo'
        PublisherContactInfo:
            type: object
            properties:
                type:
                    type: string
                value:
                    type: string
                name:
                    type: string
        PublisherData:
            type: object
            properties:
                base_info:
                    $ref: '#/components/schemas/PublisherResource'
                affiliate_info:
                    $ref: '#/components/schemas/AffiliateInfo'
                contact_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/ContactInfo'
        PublisherLink:
            type: object
            properties:
                link:
                    type: string
                fans:
                    type: integer
                    format: int32
                monthly_visits:
                    type: string
                link_type:
                    type: string
                website_indicator:
                    $ref: '#/components/schemas/WebsiteIndicator'
                media_indicator:
                    $ref: '#/components/schemas/MediaIndicator'
                indicator:
                    $ref: '#/components/schemas/Indicator'
        PublisherList:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/PublisherListItem'
            description: 列表
        PublisherListItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                introduction:
                    type: string
                size:
                    type: string
                type:
                    type: string
                logo_url:
                    type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                publisher_plat_name:
                    type: string
                publisher_plat:
                    type: string
                links:
                    type: array
                    items:
                        type: string
        PublisherResource:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                age_top:
                    type: array
                    items:
                        type: string
                country_top:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                traffic:
                    type: integer
                    format: int32
                introduction:
                    type: string
                gender_ratio:
                    $ref: '#/components/schemas/GenderRatio'
                supplier:
                    type: array
                    items:
                        type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                update_time:
                    type: string
                logo_url:
                    type: string
                publisher_plat:
                    type: string
                publisher_plat_id:
                    type: string
                publisher_plat_name:
                    type: string
                language:
                    type: array
                    items:
                        type: string
                rela_link:
                    type: array
                    items:
                        type: string
                size:
                    type: string
                links:
                    type: array
                    items:
                        $ref: '#/components/schemas/PublisherLink'
                type:
                    type: string
                media_viscosity:
                    type: string
            description: |-
                详情
                 Publisher资源
        QueryGroupListRequest:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
                group_name:
                    type: string
        QueryGroupListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/GroupListItem'
        QueryMenuTreeResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/MenuTree'
        QueryOrgByUserRequest:
            type: object
            properties:
                search:
                    type: string
                page_size:
                    type: integer
                    format: int32
                ids:
                    type: array
                    items:
                        type: integer
                        format: int32
        QueryOrgByUserResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/OrgUser'
        QueryOrgListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/OrgData'
        RemoveAdsAccountRequest:
            type: object
            properties:
                customer_project_id:
                    type: integer
                    format: int32
                ads_account_list:
                    type: array
                    items:
                        type: string
                platform:
                    type: string
        ReportMedia:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                media_name:
                    type: string
                media_profile:
                    type: string
                site_url:
                    type: string
                muckrack_url:
                    type: string
                muckrack_id:
                    type: string
                scope:
                    type: string
                language:
                    type: array
                    items:
                        type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                similarweb_uvm:
                    type: integer
                    format: int32
                comscore_uvm:
                    type: integer
                    format: int32
                domain_authority:
                    type: string
                spam_score:
                    type: string
                publish_frequency:
                    type: string
                publish_days:
                    type: string
                social_medias:
                    type: array
                    items:
                        $ref: '#/components/schemas/SocialMedia'
                update_time:
                    type: string
                city:
                    type: string
                state:
                    type: string
                email:
                    type: string
                telephone:
                    type: string
            description: 媒体信息
        ReporterAddOrUpdateRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                first_name:
                    type: string
                last_name:
                    type: string
                title:
                    type: string
                muckrack_url:
                    type: string
                introduction:
                    type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                beats:
                    type: array
                    items:
                        type: string
                city:
                    type: string
                state:
                    type: string
                publish_frequency:
                    type: string
                is_establish_conn:
                    type: integer
                    format: int32
                tags:
                    type: array
                    items:
                        type: string
                background_check:
                    type: string
                contact:
                    type: array
                    items:
                        $ref: '#/components/schemas/ReporterContactInfo'
                social_medias:
                    type: array
                    items:
                        $ref: '#/components/schemas/SocialMedia'
                report_media:
                    type: array
                    items:
                        $ref: '#/components/schemas/AddOrUpdateReportMedia'
                articles:
                    type: array
                    items:
                        $ref: '#/components/schemas/AddOrUpdateArticle'
            description: Reporter新增/编辑请求
        ReporterArticleData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/Article'
        ReporterContactInfo:
            type: object
            properties:
                type:
                    type: string
                value:
                    type: string
                remark:
                    type: string
        ReporterData:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                title:
                    type: string
                muckrack_url:
                    type: string
                muckrack_id:
                    type: string
                beats:
                    type: array
                    items:
                        type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                tags:
                    type: array
                    items:
                        type: string
                introduction:
                    type: string
                city:
                    type: string
                state:
                    type: string
                publish_frequency:
                    type: string
                background_check:
                    type: string
                is_establish_conn:
                    type: integer
                    format: int32
                establish_conn_user:
                    type: string
                social_medias:
                    type: array
                    items:
                        $ref: '#/components/schemas/SocialMedia'
                contact:
                    type: array
                    items:
                        $ref: '#/components/schemas/ReporterContactInfo'
                report_media:
                    type: array
                    items:
                        $ref: '#/components/schemas/ReportMedia'
                first_name:
                    type: string
                last_name:
                    type: string
                contact_info:
                    allOf:
                        - $ref: '#/components/schemas/ContactInfo'
                    description: 详情的联系方式特别拿出来
            description: Reporter资源详情返回
        ReporterListData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/ReporterResource'
            description: 记者资源列表返回
        ReporterListRequest:
            type: object
            properties:
                name:
                    type: string
                tag:
                    type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                beats:
                    type: array
                    items:
                        type: string
                is_establish_conn:
                    type: integer
                    format: int32
                city:
                    type: string
                page_size:
                    type: integer
                    format: int32
                page_num:
                    type: integer
                    format: int32
                media_name:
                    type: string
                customer_project_id:
                    type: integer
                    format: int32
            description: 记者列表请求
        ReporterResource:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                title:
                    type: string
                beats:
                    type: array
                    items:
                        type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                tags:
                    type: array
                    items:
                        type: string
                report_media:
                    type: array
                    items:
                        $ref: '#/components/schemas/ReportMedia'
                is_establish_conn:
                    type: integer
                    format: int32
                establish_conn_user:
                    type: string
            description: 记者资源
        ReporterResourceDetailResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/ReporterData'
        ReporterResourceListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/ReporterListData'
        ResetPasswordRequest:
            type: object
            properties:
                user_id:
                    type: integer
                    format: int32
                password:
                    type: string
                confirm_password:
                    type: string
        ResourceInfo:
            type: object
            properties:
                resource_id:
                    type: integer
                    format: int32
                resource_number:
                    type: string
                resource_name:
                    type: string
                resource_type:
                    type: string
                resource_desc:
                    type: string
                resource_sub_type:
                    type: string
        Script:
            type: object
            properties:
                text:
                    type: string
                text_start_time:
                    type: integer
                    format: int32
                text_end_time:
                    type: integer
                    format: int32
        Settlement:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                active:
                    type: boolean
                created_time:
                    type: string
                updated_time:
                    type: string
                title:
                    type: string
                short_title:
                    type: string
                is_brand:
                    type: integer
                    format: int32
                company_id:
                    type: integer
                    format: int32
                status:
                    type: string
                customer_type:
                    type: string
                industry:
                    type: string
                sub_industry:
                    type: string
                our_company_name:
                    type: string
                    description: 我方主体名称
                sign:
                    allOf:
                        - $ref: '#/components/schemas/Sign'
                    description: 签约信息
        SettlementListData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/Settlement'
        Sign:
            type: object
            properties:
                id:
                    type: integer
                    description: 签约 ID
                    format: int32
                settlementId:
                    type: integer
                    description: 结算主体 ID
                    format: int32
                ourSideEntity:
                    type: integer
                    description: 我方主体
                    format: int32
                signSaleId:
                    type: integer
                    description: 合同销售 ID
                    format: int32
                signSaleName:
                    type: string
                    description: 合同销售名称
                signSaleEmail:
                    type: string
                    description: 合同销售邮箱
                saleId:
                    type: integer
                    description: 负责销售 ID
                    format: int32
                saleName:
                    type: string
                    description: 负责销售名称
                saleEmail:
                    type: string
                    description: 负责销售邮箱
                status:
                    type: string
                    description: 状态
                isPrepay:
                    type: integer
                    description: 付款类别
                    format: int32
                hasSurety:
                    type: integer
                    description: 有无担保
                    format: int32
                createdTime:
                    type: string
                    description: 创建时间
                updatedTime:
                    type: string
                    description: 更新时间
                title:
                    type: string
                    description: 结算主体全称
                shortTitle:
                    type: string
                    description: 结算主体简称
                aeId:
                    type: integer
                    description: 负责AE ID
                    format: int32
                aeName:
                    type: string
                    description: 负责AE 名称
                aeEmail:
                    type: string
                    description: 负责AE 邮箱
                companyId:
                    type: integer
                    description: 业务单元id
                    format: int32
                certificationStatus:
                    type: string
                    description: 企业认证状态： Uncertified/Certified 未认证/已认证
                certificationRequired:
                    type: string
                    description: '是否需要认证: Yes/ No 是 / 否'
                companyName:
                    type: string
                    description: 业务单元名称
        SignFileURL:
            type: object
            properties:
                sign_file_url:
                    type: string
        SignInfo:
            type: object
            properties:
                ae_email:
                    type: string
                ae_id:
                    type: integer
                    format: int32
                ae_name:
                    type: string
                company_id:
                    type: integer
                    format: int32
                our_side_entity:
                    type: string
                sale_email:
                    type: string
                sale_id:
                    type: integer
                    format: int32
                sale_name:
                    type: string
                settlement:
                    type: string
                settlement_id:
                    type: integer
                    format: int32
                sign_id:
                    type: integer
                    format: int32
                sign_sale_email:
                    type: string
                sign_sale_id:
                    type: integer
                    format: int32
                sign_sale_name:
                    type: string
                our_side_entity_name:
                    type: string
                company_name:
                    type: string
            description: |-
                ================ 业务通用message ======================
                 签约信息
        SocialMedia:
            type: object
            properties:
                media:
                    type: string
                url:
                    type: string
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        StringArray:
            type: object
            properties:
                value:
                    type: array
                    items:
                        type: string
        SupplierData:
            type: object
            properties:
                base_info:
                    $ref: '#/components/schemas/SupplierResource'
                brand_info:
                    $ref: '#/components/schemas/BrandInfo'
        SupplierList:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/SupplierResource'
            description: 列表
        SupplierResource:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                type:
                    type: array
                    items:
                        type: string
                main_business:
                    type: array
                    items:
                        type: string
                advantage_industry:
                    type: array
                    items:
                        type: string
                supplier_attributes:
                    type: string
                company_introduction:
                    type: string
                remarks:
                    type: string
                introduction_date:
                    type: string
                region_country:
                    type: array
                    items:
                        $ref: '#/components/schemas/StringArray'
                update_time:
                    type: string
            description: |-
                详情
                 Supplier资源
        TaskPageListData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/TaskPageListDataItem'
                pagination:
                    $ref: '#/components/schemas/Pagination'
        TaskPageListDataItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                task_type:
                    type: string
                query_params:
                    type: string
                status:
                    type: string
                create_time:
                    type: string
                update_time:
                    type: string
                create_user:
                    type: string
                file_uri:
                    type: string
                err_reason:
                    type: string
                update_user:
                    type: string
                api_endpoint:
                    type: string
                task_name:
                    type: string
        TaskPageListRequest:
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: integer
                        format: int32
                status:
                    type: array
                    items:
                        type: string
                pageNum:
                    type: integer
                    format: int32
                pageSize:
                    type: integer
                    format: int32
        TaskPageListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/TaskPageListData'
        TiktokDigitalAvatar:
            type: object
            properties:
                avatar_id:
                    type: string
                avatar_preview_url:
                    type: string
                avatar_name:
                    type: string
                avatar_thumbnail:
                    type: string
            description: |-
                ================ 响应定义 START ======================
                 可用数字人
        TiktokDigitalAvatarData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/TiktokDigitalAvatar'
        TiktokDigitalAvatarTask:
            type: object
            properties:
                preview_url:
                    type: string
                video_name:
                    type: string
                create_time:
                    type: string
                avatar_video_id:
                    type: string
                avatar_id:
                    type: string
                video_cover_url:
                    type: string
            description: 数字人任务列表
        TiktokDigitalAvatarTaskData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/TiktokDigitalAvatarTask'
        UpOrCrCustomerProjectRequest:
            type: object
            properties:
                project_id:
                    type: string
                project_name:
                    type: string
                project_description:
                    type: string
                tags:
                    type: array
                    items:
                        type: string
                end_date:
                    type: string
                start_date:
                    type: string
                customer_entity:
                    type: integer
                    format: int32
                user_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
                org_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
                    description: 预留字段
                settlement_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
                id:
                    type: integer
                    format: int32
        UpdateAuthorizationRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                authorization_type:
                    type: string
                authorization_platform:
                    type: string
                authorization_config:
                    type: object
                status:
                    type: string
            description: 更新授权
        UpdateDictManageRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                label:
                    type: string
                remark:
                    type: string
        UpdateOrgRequest:
            type: object
            properties:
                org_id:
                    type: integer
                    format: int32
                org_name:
                    type: string
                user_ids:
                    type: array
                    items:
                        type: string
        UpdateOutdoorScreenRequest:
            type: object
            properties:
                baseInfo:
                    $ref: '#/components/schemas/OutdoorScreenResource'
                brandInfo:
                    $ref: '#/components/schemas/BrandInfo'
            description: 编辑
        UpdateSupplierRequest:
            type: object
            properties:
                base_info:
                    $ref: '#/components/schemas/SupplierResource'
                brand_info:
                    $ref: '#/components/schemas/BrandInfo'
            description: 编辑
        UpdateUserRequest:
            type: object
            properties:
                id:
                    type: string
                email:
                    type: string
                first_name:
                    type: string
                last_name:
                    type: string
                phone:
                    type: string
                org_id:
                    type: integer
                    format: int32
        UploadFileRequest:
            type: object
            properties:
                file:
                    type: string
                    format: bytes
                filename:
                    type: string
        UploadFileResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/UploadFileUri'
        UploadFileUri:
            type: object
            properties:
                file_uri:
                    type: string
        UploadMaterialBatchCheckItem:
            type: object
            properties:
                file_name:
                    type: string
                tags:
                    type: array
                    items:
                        type: string
                language:
                    type: string
                status:
                    type: boolean
                err_msg:
                    type: string
        UploadMaterialBatchCheckResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/UploadMaterialBatchCheckItem'
        UploadMaterialBatchRequest:
            type: object
            properties:
                material_uri:
                    type: array
                    items:
                        type: string
                material_tags_uri:
                    type: string
                customer_project_id:
                    type: integer
                    format: int32
        UploadMaterialToMediumLogData:
            type: object
            properties:
                pagination:
                    $ref: '#/components/schemas/Pagination'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/UploadMaterialToMediumLogItem'
        UploadMaterialToMediumLogItem:
            type: object
            properties:
                task_id:
                    type: integer
                    format: int32
                task_status:
                    type: string
                material_name:
                    type: string
                ads_account_id:
                    type: string
                upload_error:
                    type: string
                medium:
                    type: string
                upload_time:
                    type: string
                material_id:
                    type: integer
                    format: int32
        UploadMaterialToMediumLogResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/UploadMaterialToMediumLogData'
        UploadMaterialToMediumRequest:
            type: object
            properties:
                material_type:
                    type: string
                material_ids:
                    type: array
                    items:
                        type: integer
                        format: int32
                medium:
                    type: string
                ad_account_list:
                    type: array
                    items:
                        type: string
                customer_project_id:
                    type: integer
                    format: int32
        UserActivityEvent:
            type: object
            properties:
                eventType:
                    type: string
                timeStamp:
                    type: string
                eventKey:
                    type: string
                eventName:
                    type: string
        UserInfo:
            type: object
            properties:
                user_id:
                    type: integer
                    format: int32
                user_name:
                    type: string
                user_email:
                    type: string
            description: 用户信息
        UserInfoResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/UserInfo'
        UserPageListData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/UserPageListDataItem'
                pagination:
                    $ref: '#/components/schemas/Pagination'
        UserPageListDataItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                email:
                    type: string
                user_name:
                    type: string
                org_id:
                    type: integer
                    format: int32
                org_name:
                    type: string
                phone:
                    type: string
                create_time:
                    type: string
                update_time:
                    type: string
                first_name:
                    type: string
                last_name:
                    type: string
        UserPageListRequest:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
                email:
                    type: string
                user_name:
                    type: string
                org_id:
                    type: integer
                    format: int32
                user_id:
                    type: integer
                    format: int32
        UserPageListResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/UserPageListData'
        VideoAIGCTask:
            type: object
            properties:
                aigc_video_type:
                    type: string
                create_time:
                    type: string
                dubbing_info:
                    $ref: '#/components/schemas/DubbingInfo'
                preview_url:
                    type: string
                video_id:
                    type: string
                video_name:
                    type: string
        VideoAIGCTaskData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/VideoAIGCTask'
                pagination:
                    $ref: '#/components/schemas/Pagination'
        VideoFixSearch:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/VideoFixSearchItem'
                pagination:
                    $ref: '#/components/schemas/Pagination'
        VideoFixSearchItem:
            type: object
            properties:
                video_id:
                    type: string
                video_cover_url:
                    type: string
                format:
                    type: string
                preview_url:
                    type: string
                preview_url_expire_time:
                    type: string
                duration:
                    type: number
                    format: double
                bit_rate:
                    type: integer
                    format: int32
                width:
                    type: integer
                    format: int32
                height:
                    type: integer
                    format: int32
                signature:
                    type: string
                size:
                    type: integer
                    format: int32
                material_id:
                    type: string
                file_name:
                    type: string
                create_time:
                    type: string
                modify_time:
                    type: string
        VideoFixSearchRequest:
            type: object
            properties:
                video_ids:
                    type: array
                    items:
                        type: string
                material_ids:
                    type: array
                    items:
                        type: string
                video_name:
                    type: string
                video_material_sources:
                    type: array
                    items:
                        type: string
                page_num:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
        VideoFixSearchResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    $ref: '#/components/schemas/VideoFixSearch'
        VideoFixUpload:
            type: object
            properties:
                fix_task_id:
                    type: string
                flaw_types:
                    type: array
                    items:
                        type: string
                preview_url:
                    type: string
                video_cover_url:
                    type: string
                format:
                    type: string
                video_id:
                    type: string
        VideoFixUploadRequest:
            type: object
            properties:
                file_name:
                    type: string
                video_url:
                    type: string
                use_scenario:
                    type: string
        VideoFixUploadResponse:
            type: object
            properties:
                errId:
                    type: integer
                    format: int32
                errCode:
                    type: string
                errMsg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/VideoFixUpload'
        WebsiteIndicator:
            type: object
            properties:
                moz_spam_score:
                    type: integer
                    format: int32
                moz_domain_authority:
                    type: integer
                    format: int32
                similar_web_global_rank:
                    type: integer
                    format: int32
                similar_web_verified:
                    type: boolean
tags:
    - name: AdsAccountManageService
    - name: AuthGroupService
      description: |-
        ============== 服务定义START ========================
         角色管理
    - name: AuthService
    - name: AuthorizationService
    - name: ClientPortalService
    - name: CommonService
    - name: CustomerProConfigService
    - name: CustomerProResourceService
    - name: CustomerProjectService
    - name: DictManageService
    - name: MaterialManageService
    - name: ResourceManagerIpService
    - name: ResourceManagerOutdoorScreenService
      description: 户外大屏
    - name: ResourceManagerPrService
    - name: ResourceManagerPublisherService
      description: 联盟客
    - name: ResourceManagerReporterService
    - name: ResourceManagerService
    - name: ResourceManagerSupplierService
      description: 供应商服务
    - name: TaskService
    - name: TiktokService
      description: ============== 服务定义START ========================
    - name: UserActivity
      description: ================ 请求定义 START ======================
