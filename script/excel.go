package main

import (
	"fmt"
	"vision_hub/internal/util"
)

type User struct {
	ID     int
	Name   string
	Role   int
	Status int
}

func main() {
	// 示例数据
	users := []User{
		{ID: 1, Name: "张三", Role: 1, Status: 0},
		{ID: 2, Name: "李四", Role: 2, Status: 1},
		{ID: 3, Name: "王五", Role: 3, Status: 2},
	}
	// 表头映射
	headerMap := map[string]string{
		"ID":          "用户ID",
		"DisplayName": "姓名",
		"Role":        "角色",
		"Status":      "状态",
	}

	// 枚举值映射
	enumMap := map[string]map[interface{}]string{
		"Role": {
			1: "管理员",
			2: "编辑",
			3: "普通用户",
		},
		"Status": {
			0: "正常",
			1: "禁用",
			2: "审核中",
		},
	}
	filePath := "/Users/<USER>/Desktop/users.xlsx"
	err := util.ExportExcelToFilePath(users, headerMap, enumMap, filePath)
	if err != nil {
		fmt.Println("Excel 生成失败:", err)
	} else {
		fmt.Println("Excel 生成成功:", filePath)
	}
}
