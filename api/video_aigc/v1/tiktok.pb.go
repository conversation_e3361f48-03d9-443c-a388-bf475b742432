// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/video_aigc/v1/tiktok.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VideoFixUploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName    string `protobuf:"bytes,1,opt,name=file_name,proto3" json:"file_name,omitempty"`
	VideoUrl    string `protobuf:"bytes,2,opt,name=video_url,proto3" json:"video_url,omitempty"`
	UseScenario string `protobuf:"bytes,3,opt,name=use_scenario,proto3" json:"use_scenario,omitempty"` // AIGC 用于视频混剪
}

func (x *VideoFixUploadRequest) Reset() {
	*x = VideoFixUploadRequest{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoFixUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoFixUploadRequest) ProtoMessage() {}

func (x *VideoFixUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoFixUploadRequest.ProtoReflect.Descriptor instead.
func (*VideoFixUploadRequest) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{0}
}

func (x *VideoFixUploadRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *VideoFixUploadRequest) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *VideoFixUploadRequest) GetUseScenario() string {
	if x != nil {
		return x.UseScenario
	}
	return ""
}

type VideoFixSearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoIds             []string `protobuf:"bytes,1,rep,name=video_ids,proto3" json:"video_ids,omitempty"`
	MaterialIds          []string `protobuf:"bytes,2,rep,name=material_ids,proto3" json:"material_ids,omitempty"`
	VideoName            string   `protobuf:"bytes,3,opt,name=video_name,proto3" json:"video_name,omitempty"`
	VideoMaterialSources []string `protobuf:"bytes,4,rep,name=video_material_sources,proto3" json:"video_material_sources,omitempty"`
	PageNum              int32    `protobuf:"varint,5,opt,name=page_num,proto3" json:"page_num,omitempty"`
	PageSize             int32    `protobuf:"varint,6,opt,name=page_size,proto3" json:"page_size,omitempty"`
}

func (x *VideoFixSearchRequest) Reset() {
	*x = VideoFixSearchRequest{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoFixSearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoFixSearchRequest) ProtoMessage() {}

func (x *VideoFixSearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoFixSearchRequest.ProtoReflect.Descriptor instead.
func (*VideoFixSearchRequest) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{1}
}

func (x *VideoFixSearchRequest) GetVideoIds() []string {
	if x != nil {
		return x.VideoIds
	}
	return nil
}

func (x *VideoFixSearchRequest) GetMaterialIds() []string {
	if x != nil {
		return x.MaterialIds
	}
	return nil
}

func (x *VideoFixSearchRequest) GetVideoName() string {
	if x != nil {
		return x.VideoName
	}
	return ""
}

func (x *VideoFixSearchRequest) GetVideoMaterialSources() []string {
	if x != nil {
		return x.VideoMaterialSources
	}
	return nil
}

func (x *VideoFixSearchRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *VideoFixSearchRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetTiktokDigitalAvatarListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int32 `protobuf:"varint,1,opt,name=page_num,proto3" json:"page_num,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,proto3" json:"page_size,omitempty"`
}

func (x *GetTiktokDigitalAvatarListRequest) Reset() {
	*x = GetTiktokDigitalAvatarListRequest{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTiktokDigitalAvatarListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTiktokDigitalAvatarListRequest) ProtoMessage() {}

func (x *GetTiktokDigitalAvatarListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTiktokDigitalAvatarListRequest.ProtoReflect.Descriptor instead.
func (*GetTiktokDigitalAvatarListRequest) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{2}
}

func (x *GetTiktokDigitalAvatarListRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetTiktokDigitalAvatarListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type CreateTiktokDigitalAvatarTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AvatarId  string `protobuf:"bytes,1,opt,name=avatar_id,proto3" json:"avatar_id,omitempty"`
	Script    string `protobuf:"bytes,2,opt,name=script,proto3" json:"script,omitempty"`
	PackageId string `protobuf:"bytes,3,opt,name=package_id,proto3" json:"package_id,omitempty"`
	VideoName string `protobuf:"bytes,4,opt,name=video_name,proto3" json:"video_name,omitempty"`
}

func (x *CreateTiktokDigitalAvatarTask) Reset() {
	*x = CreateTiktokDigitalAvatarTask{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTiktokDigitalAvatarTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTiktokDigitalAvatarTask) ProtoMessage() {}

func (x *CreateTiktokDigitalAvatarTask) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTiktokDigitalAvatarTask.ProtoReflect.Descriptor instead.
func (*CreateTiktokDigitalAvatarTask) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{3}
}

func (x *CreateTiktokDigitalAvatarTask) GetAvatarId() string {
	if x != nil {
		return x.AvatarId
	}
	return ""
}

func (x *CreateTiktokDigitalAvatarTask) GetScript() string {
	if x != nil {
		return x.Script
	}
	return ""
}

func (x *CreateTiktokDigitalAvatarTask) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *CreateTiktokDigitalAvatarTask) GetVideoName() string {
	if x != nil {
		return x.VideoName
	}
	return ""
}

type CreateTiktokDigitalAvatarTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks []*CreateTiktokDigitalAvatarTask `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *CreateTiktokDigitalAvatarTaskRequest) Reset() {
	*x = CreateTiktokDigitalAvatarTaskRequest{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTiktokDigitalAvatarTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTiktokDigitalAvatarTaskRequest) ProtoMessage() {}

func (x *CreateTiktokDigitalAvatarTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTiktokDigitalAvatarTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateTiktokDigitalAvatarTaskRequest) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{4}
}

func (x *CreateTiktokDigitalAvatarTaskRequest) GetTasks() []*CreateTiktokDigitalAvatarTask {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type GetTiktokDigitalAvatarTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum   int32  `protobuf:"varint,1,opt,name=page_num,proto3" json:"page_num,omitempty"`
	PageSize  int32  `protobuf:"varint,2,opt,name=page_size,proto3" json:"page_size,omitempty"`
	AvatarId  string `protobuf:"bytes,3,opt,name=avatar_id,proto3" json:"avatar_id,omitempty"`
	StartDate string `protobuf:"bytes,4,opt,name=start_date,proto3" json:"start_date,omitempty"`
	EndDate   string `protobuf:"bytes,5,opt,name=end_date,proto3" json:"end_date,omitempty"`
}

func (x *GetTiktokDigitalAvatarTaskRequest) Reset() {
	*x = GetTiktokDigitalAvatarTaskRequest{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTiktokDigitalAvatarTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTiktokDigitalAvatarTaskRequest) ProtoMessage() {}

func (x *GetTiktokDigitalAvatarTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTiktokDigitalAvatarTaskRequest.ProtoReflect.Descriptor instead.
func (*GetTiktokDigitalAvatarTaskRequest) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{5}
}

func (x *GetTiktokDigitalAvatarTaskRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetTiktokDigitalAvatarTaskRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetTiktokDigitalAvatarTaskRequest) GetAvatarId() string {
	if x != nil {
		return x.AvatarId
	}
	return ""
}

func (x *GetTiktokDigitalAvatarTaskRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetTiktokDigitalAvatarTaskRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

type DubbingVideoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl        string    `protobuf:"bytes,1,opt,name=video_url,proto3" json:"video_url,omitempty"`
	TargetLanguages []string  `protobuf:"bytes,2,rep,name=target_languages,proto3" json:"target_languages,omitempty"`
	VideoName       string    `protobuf:"bytes,3,opt,name=video_name,proto3" json:"video_name,omitempty"`
	SubtitleEnabled bool      `protobuf:"varint,4,opt,name=subtitle_enabled,proto3" json:"subtitle_enabled,omitempty"`
	Script          []*Script `protobuf:"bytes,5,rep,name=script,proto3" json:"script,omitempty"`
}

func (x *DubbingVideoInfo) Reset() {
	*x = DubbingVideoInfo{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DubbingVideoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DubbingVideoInfo) ProtoMessage() {}

func (x *DubbingVideoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DubbingVideoInfo.ProtoReflect.Descriptor instead.
func (*DubbingVideoInfo) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{6}
}

func (x *DubbingVideoInfo) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *DubbingVideoInfo) GetTargetLanguages() []string {
	if x != nil {
		return x.TargetLanguages
	}
	return nil
}

func (x *DubbingVideoInfo) GetVideoName() string {
	if x != nil {
		return x.VideoName
	}
	return ""
}

func (x *DubbingVideoInfo) GetSubtitleEnabled() bool {
	if x != nil {
		return x.SubtitleEnabled
	}
	return false
}

func (x *DubbingVideoInfo) GetScript() []*Script {
	if x != nil {
		return x.Script
	}
	return nil
}

type Script struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text          string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	TextStartTime int32  `protobuf:"varint,2,opt,name=text_start_time,proto3" json:"text_start_time,omitempty"`
	TextEndTime   int32  `protobuf:"varint,3,opt,name=text_end_time,proto3" json:"text_end_time,omitempty"`
}

func (x *Script) Reset() {
	*x = Script{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Script) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Script) ProtoMessage() {}

func (x *Script) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Script.ProtoReflect.Descriptor instead.
func (*Script) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{7}
}

func (x *Script) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Script) GetTextStartTime() int32 {
	if x != nil {
		return x.TextStartTime
	}
	return 0
}

func (x *Script) GetTextEndTime() int32 {
	if x != nil {
		return x.TextEndTime
	}
	return 0
}

type CreateVideoAIGCTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AigcVideoType    string              `protobuf:"bytes,1,opt,name=aigc_video_type,proto3" json:"aigc_video_type,omitempty"`
	DubbingVideoInfo []*DubbingVideoInfo `protobuf:"bytes,2,rep,name=dubbing_video_info,proto3" json:"dubbing_video_info,omitempty"`
}

func (x *CreateVideoAIGCTaskRequest) Reset() {
	*x = CreateVideoAIGCTaskRequest{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVideoAIGCTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVideoAIGCTaskRequest) ProtoMessage() {}

func (x *CreateVideoAIGCTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVideoAIGCTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateVideoAIGCTaskRequest) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{8}
}

func (x *CreateVideoAIGCTaskRequest) GetAigcVideoType() string {
	if x != nil {
		return x.AigcVideoType
	}
	return ""
}

func (x *CreateVideoAIGCTaskRequest) GetDubbingVideoInfo() []*DubbingVideoInfo {
	if x != nil {
		return x.DubbingVideoInfo
	}
	return nil
}

type GetVideoAIGCTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AigcVideoTypes []string `protobuf:"bytes,1,rep,name=aigc_video_types,proto3" json:"aigc_video_types,omitempty"`
	PageSize       int32    `protobuf:"varint,2,opt,name=page_size,proto3" json:"page_size,omitempty"`
	PageNum        int32    `protobuf:"varint,3,opt,name=page_num,proto3" json:"page_num,omitempty"`
}

func (x *GetVideoAIGCTaskRequest) Reset() {
	*x = GetVideoAIGCTaskRequest{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVideoAIGCTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVideoAIGCTaskRequest) ProtoMessage() {}

func (x *GetVideoAIGCTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVideoAIGCTaskRequest.ProtoReflect.Descriptor instead.
func (*GetVideoAIGCTaskRequest) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{9}
}

func (x *GetVideoAIGCTaskRequest) GetAigcVideoTypes() []string {
	if x != nil {
		return x.AigcVideoTypes
	}
	return nil
}

func (x *GetVideoAIGCTaskRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetVideoAIGCTaskRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

type VideoFixUploadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32             `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string            `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string            `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    []*VideoFixUpload `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`       // 返回数据
}

func (x *VideoFixUploadResponse) Reset() {
	*x = VideoFixUploadResponse{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoFixUploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoFixUploadResponse) ProtoMessage() {}

func (x *VideoFixUploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoFixUploadResponse.ProtoReflect.Descriptor instead.
func (*VideoFixUploadResponse) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{10}
}

func (x *VideoFixUploadResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *VideoFixUploadResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *VideoFixUploadResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *VideoFixUploadResponse) GetData() []*VideoFixUpload {
	if x != nil {
		return x.Data
	}
	return nil
}

type VideoFixUpload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FixTaskId     string   `protobuf:"bytes,1,opt,name=fix_task_id,proto3" json:"fix_task_id,omitempty"` // 修复任务id
	FlawTypes     []string `protobuf:"bytes,2,rep,name=flaw_types,proto3" json:"flaw_types,omitempty"`
	PreviewUrl    string   `protobuf:"bytes,3,opt,name=preview_url,proto3" json:"preview_url,omitempty"`
	VideoCoverUrl string   `protobuf:"bytes,4,opt,name=video_cover_url,proto3" json:"video_cover_url,omitempty"`
	Format        string   `protobuf:"bytes,5,opt,name=format,proto3" json:"format,omitempty"`
	VideoId       string   `protobuf:"bytes,6,opt,name=video_id,proto3" json:"video_id,omitempty"`
}

func (x *VideoFixUpload) Reset() {
	*x = VideoFixUpload{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoFixUpload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoFixUpload) ProtoMessage() {}

func (x *VideoFixUpload) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoFixUpload.ProtoReflect.Descriptor instead.
func (*VideoFixUpload) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{11}
}

func (x *VideoFixUpload) GetFixTaskId() string {
	if x != nil {
		return x.FixTaskId
	}
	return ""
}

func (x *VideoFixUpload) GetFlawTypes() []string {
	if x != nil {
		return x.FlawTypes
	}
	return nil
}

func (x *VideoFixUpload) GetPreviewUrl() string {
	if x != nil {
		return x.PreviewUrl
	}
	return ""
}

func (x *VideoFixUpload) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *VideoFixUpload) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *VideoFixUpload) GetVideoId() string {
	if x != nil {
		return x.VideoId
	}
	return ""
}

type VideoFixSearchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32           `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string          `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string          `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *VideoFixSearch `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`       // 返回数据
}

func (x *VideoFixSearchResponse) Reset() {
	*x = VideoFixSearchResponse{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoFixSearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoFixSearchResponse) ProtoMessage() {}

func (x *VideoFixSearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoFixSearchResponse.ProtoReflect.Descriptor instead.
func (*VideoFixSearchResponse) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{12}
}

func (x *VideoFixSearchResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *VideoFixSearchResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *VideoFixSearchResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *VideoFixSearchResponse) GetData() *VideoFixSearch {
	if x != nil {
		return x.Data
	}
	return nil
}

type VideoFixSearch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*VideoFixSearchItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Pagination *Pagination           `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *VideoFixSearch) Reset() {
	*x = VideoFixSearch{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoFixSearch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoFixSearch) ProtoMessage() {}

func (x *VideoFixSearch) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoFixSearch.ProtoReflect.Descriptor instead.
func (*VideoFixSearch) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{13}
}

func (x *VideoFixSearch) GetList() []*VideoFixSearchItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoFixSearch) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type VideoFixSearchItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoId              string  `protobuf:"bytes,1,opt,name=video_id,proto3" json:"video_id,omitempty"`
	VideoCoverUrl        string  `protobuf:"bytes,2,opt,name=video_cover_url,proto3" json:"video_cover_url,omitempty"`
	Format               string  `protobuf:"bytes,3,opt,name=format,proto3" json:"format,omitempty"`
	PreviewUrl           string  `protobuf:"bytes,4,opt,name=preview_url,proto3" json:"preview_url,omitempty"`
	PreviewUrlExpireTime string  `protobuf:"bytes,5,opt,name=preview_url_expire_time,proto3" json:"preview_url_expire_time,omitempty"`
	Duration             float64 `protobuf:"fixed64,6,opt,name=duration,proto3" json:"duration,omitempty"`
	BitRate              int32   `protobuf:"varint,7,opt,name=bit_rate,proto3" json:"bit_rate,omitempty"`
	Width                int32   `protobuf:"varint,8,opt,name=width,proto3" json:"width,omitempty"`
	Height               int32   `protobuf:"varint,9,opt,name=height,proto3" json:"height,omitempty"`
	Signature            string  `protobuf:"bytes,10,opt,name=signature,proto3" json:"signature,omitempty"`
	Size                 int32   `protobuf:"varint,11,opt,name=size,proto3" json:"size,omitempty"`
	MaterialId           string  `protobuf:"bytes,12,opt,name=material_id,proto3" json:"material_id,omitempty"`
	FileName             string  `protobuf:"bytes,13,opt,name=file_name,proto3" json:"file_name,omitempty"`
	CreateTime           string  `protobuf:"bytes,14,opt,name=create_time,proto3" json:"create_time,omitempty"`
	ModifyTime           string  `protobuf:"bytes,15,opt,name=modify_time,proto3" json:"modify_time,omitempty"`
}

func (x *VideoFixSearchItem) Reset() {
	*x = VideoFixSearchItem{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoFixSearchItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoFixSearchItem) ProtoMessage() {}

func (x *VideoFixSearchItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoFixSearchItem.ProtoReflect.Descriptor instead.
func (*VideoFixSearchItem) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{14}
}

func (x *VideoFixSearchItem) GetVideoId() string {
	if x != nil {
		return x.VideoId
	}
	return ""
}

func (x *VideoFixSearchItem) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *VideoFixSearchItem) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *VideoFixSearchItem) GetPreviewUrl() string {
	if x != nil {
		return x.PreviewUrl
	}
	return ""
}

func (x *VideoFixSearchItem) GetPreviewUrlExpireTime() string {
	if x != nil {
		return x.PreviewUrlExpireTime
	}
	return ""
}

func (x *VideoFixSearchItem) GetDuration() float64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *VideoFixSearchItem) GetBitRate() int32 {
	if x != nil {
		return x.BitRate
	}
	return 0
}

func (x *VideoFixSearchItem) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *VideoFixSearchItem) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VideoFixSearchItem) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *VideoFixSearchItem) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *VideoFixSearchItem) GetMaterialId() string {
	if x != nil {
		return x.MaterialId
	}
	return ""
}

func (x *VideoFixSearchItem) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *VideoFixSearchItem) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *VideoFixSearchItem) GetModifyTime() string {
	if x != nil {
		return x.ModifyTime
	}
	return ""
}

// ================ 响应定义 START ======================
// 可用数字人
type TiktokDigitalAvatar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AvatarId         string `protobuf:"bytes,1,opt,name=avatar_id,proto3" json:"avatar_id,omitempty"`
	AvatarPreviewUrl string `protobuf:"bytes,2,opt,name=avatar_preview_url,proto3" json:"avatar_preview_url,omitempty"`
	AvatarName       string `protobuf:"bytes,3,opt,name=avatar_name,proto3" json:"avatar_name,omitempty"`
	AvatarThumbnail  string `protobuf:"bytes,4,opt,name=avatar_thumbnail,proto3" json:"avatar_thumbnail,omitempty"`
}

func (x *TiktokDigitalAvatar) Reset() {
	*x = TiktokDigitalAvatar{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TiktokDigitalAvatar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TiktokDigitalAvatar) ProtoMessage() {}

func (x *TiktokDigitalAvatar) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TiktokDigitalAvatar.ProtoReflect.Descriptor instead.
func (*TiktokDigitalAvatar) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{15}
}

func (x *TiktokDigitalAvatar) GetAvatarId() string {
	if x != nil {
		return x.AvatarId
	}
	return ""
}

func (x *TiktokDigitalAvatar) GetAvatarPreviewUrl() string {
	if x != nil {
		return x.AvatarPreviewUrl
	}
	return ""
}

func (x *TiktokDigitalAvatar) GetAvatarName() string {
	if x != nil {
		return x.AvatarName
	}
	return ""
}

func (x *TiktokDigitalAvatar) GetAvatarThumbnail() string {
	if x != nil {
		return x.AvatarThumbnail
	}
	return ""
}

type TiktokDigitalAvatarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination            `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*TiktokDigitalAvatar `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *TiktokDigitalAvatarData) Reset() {
	*x = TiktokDigitalAvatarData{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TiktokDigitalAvatarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TiktokDigitalAvatarData) ProtoMessage() {}

func (x *TiktokDigitalAvatarData) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TiktokDigitalAvatarData.ProtoReflect.Descriptor instead.
func (*TiktokDigitalAvatarData) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{16}
}

func (x *TiktokDigitalAvatarData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *TiktokDigitalAvatarData) GetList() []*TiktokDigitalAvatar {
	if x != nil {
		return x.List
	}
	return nil
}

// 可用数字人响应
type GetTiktokDigitalAvatarListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                    `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                   `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                   `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *TiktokDigitalAvatarData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetTiktokDigitalAvatarListResponse) Reset() {
	*x = GetTiktokDigitalAvatarListResponse{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTiktokDigitalAvatarListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTiktokDigitalAvatarListResponse) ProtoMessage() {}

func (x *GetTiktokDigitalAvatarListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTiktokDigitalAvatarListResponse.ProtoReflect.Descriptor instead.
func (*GetTiktokDigitalAvatarListResponse) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{17}
}

func (x *GetTiktokDigitalAvatarListResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetTiktokDigitalAvatarListResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetTiktokDigitalAvatarListResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetTiktokDigitalAvatarListResponse) GetData() *TiktokDigitalAvatarData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 创建数字人任务
type CreateTiktokDigitalAvatar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId string `protobuf:"bytes,1,opt,name=package_id,proto3" json:"package_id,omitempty"`
	TaskId    string `protobuf:"bytes,2,opt,name=task_id,proto3" json:"task_id,omitempty"`
}

func (x *CreateTiktokDigitalAvatar) Reset() {
	*x = CreateTiktokDigitalAvatar{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTiktokDigitalAvatar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTiktokDigitalAvatar) ProtoMessage() {}

func (x *CreateTiktokDigitalAvatar) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTiktokDigitalAvatar.ProtoReflect.Descriptor instead.
func (*CreateTiktokDigitalAvatar) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{18}
}

func (x *CreateTiktokDigitalAvatar) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *CreateTiktokDigitalAvatar) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type CreateTiktokDigitalAvatarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*CreateTiktokDigitalAvatar `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *CreateTiktokDigitalAvatarData) Reset() {
	*x = CreateTiktokDigitalAvatarData{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTiktokDigitalAvatarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTiktokDigitalAvatarData) ProtoMessage() {}

func (x *CreateTiktokDigitalAvatarData) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTiktokDigitalAvatarData.ProtoReflect.Descriptor instead.
func (*CreateTiktokDigitalAvatarData) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{19}
}

func (x *CreateTiktokDigitalAvatarData) GetList() []*CreateTiktokDigitalAvatar {
	if x != nil {
		return x.List
	}
	return nil
}

type CreateTiktokDigitalAvatarTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                          `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                         `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                         `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *CreateTiktokDigitalAvatarData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CreateTiktokDigitalAvatarTaskResponse) Reset() {
	*x = CreateTiktokDigitalAvatarTaskResponse{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTiktokDigitalAvatarTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTiktokDigitalAvatarTaskResponse) ProtoMessage() {}

func (x *CreateTiktokDigitalAvatarTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTiktokDigitalAvatarTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateTiktokDigitalAvatarTaskResponse) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{20}
}

func (x *CreateTiktokDigitalAvatarTaskResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *CreateTiktokDigitalAvatarTaskResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *CreateTiktokDigitalAvatarTaskResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *CreateTiktokDigitalAvatarTaskResponse) GetData() *CreateTiktokDigitalAvatarData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 数字人任务列表
type TiktokDigitalAvatarTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreviewUrl    string `protobuf:"bytes,1,opt,name=preview_url,proto3" json:"preview_url,omitempty"`
	VideoName     string `protobuf:"bytes,2,opt,name=video_name,proto3" json:"video_name,omitempty"`
	CreateTime    string `protobuf:"bytes,3,opt,name=create_time,proto3" json:"create_time,omitempty"`
	AvatarVideoId string `protobuf:"bytes,4,opt,name=avatar_video_id,proto3" json:"avatar_video_id,omitempty"`
	AvatarId      string `protobuf:"bytes,5,opt,name=avatar_id,proto3" json:"avatar_id,omitempty"`
	VideoCoverUrl string `protobuf:"bytes,6,opt,name=video_cover_url,proto3" json:"video_cover_url,omitempty"`
}

func (x *TiktokDigitalAvatarTask) Reset() {
	*x = TiktokDigitalAvatarTask{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TiktokDigitalAvatarTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TiktokDigitalAvatarTask) ProtoMessage() {}

func (x *TiktokDigitalAvatarTask) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TiktokDigitalAvatarTask.ProtoReflect.Descriptor instead.
func (*TiktokDigitalAvatarTask) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{21}
}

func (x *TiktokDigitalAvatarTask) GetPreviewUrl() string {
	if x != nil {
		return x.PreviewUrl
	}
	return ""
}

func (x *TiktokDigitalAvatarTask) GetVideoName() string {
	if x != nil {
		return x.VideoName
	}
	return ""
}

func (x *TiktokDigitalAvatarTask) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *TiktokDigitalAvatarTask) GetAvatarVideoId() string {
	if x != nil {
		return x.AvatarVideoId
	}
	return ""
}

func (x *TiktokDigitalAvatarTask) GetAvatarId() string {
	if x != nil {
		return x.AvatarId
	}
	return ""
}

func (x *TiktokDigitalAvatarTask) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

type TiktokDigitalAvatarTaskData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination                `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*TiktokDigitalAvatarTask `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *TiktokDigitalAvatarTaskData) Reset() {
	*x = TiktokDigitalAvatarTaskData{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TiktokDigitalAvatarTaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TiktokDigitalAvatarTaskData) ProtoMessage() {}

func (x *TiktokDigitalAvatarTaskData) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TiktokDigitalAvatarTaskData.ProtoReflect.Descriptor instead.
func (*TiktokDigitalAvatarTaskData) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{22}
}

func (x *TiktokDigitalAvatarTaskData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *TiktokDigitalAvatarTaskData) GetList() []*TiktokDigitalAvatarTask {
	if x != nil {
		return x.List
	}
	return nil
}

type GetTiktokDigitalAvatarTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                        `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                       `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                       `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *TiktokDigitalAvatarTaskData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetTiktokDigitalAvatarTaskResponse) Reset() {
	*x = GetTiktokDigitalAvatarTaskResponse{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTiktokDigitalAvatarTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTiktokDigitalAvatarTaskResponse) ProtoMessage() {}

func (x *GetTiktokDigitalAvatarTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTiktokDigitalAvatarTaskResponse.ProtoReflect.Descriptor instead.
func (*GetTiktokDigitalAvatarTaskResponse) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{23}
}

func (x *GetTiktokDigitalAvatarTaskResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetTiktokDigitalAvatarTaskResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetTiktokDigitalAvatarTaskResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetTiktokDigitalAvatarTaskResponse) GetData() *TiktokDigitalAvatarTaskData {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateVideoAIGCTaskData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskIds []string `protobuf:"bytes,1,rep,name=task_ids,proto3" json:"task_ids,omitempty"`
}

func (x *CreateVideoAIGCTaskData) Reset() {
	*x = CreateVideoAIGCTaskData{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVideoAIGCTaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVideoAIGCTaskData) ProtoMessage() {}

func (x *CreateVideoAIGCTaskData) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVideoAIGCTaskData.ProtoReflect.Descriptor instead.
func (*CreateVideoAIGCTaskData) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{24}
}

func (x *CreateVideoAIGCTaskData) GetTaskIds() []string {
	if x != nil {
		return x.TaskIds
	}
	return nil
}

type CreateVideoAIGCTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                    `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                   `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                   `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *CreateVideoAIGCTaskData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CreateVideoAIGCTaskResponse) Reset() {
	*x = CreateVideoAIGCTaskResponse{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVideoAIGCTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVideoAIGCTaskResponse) ProtoMessage() {}

func (x *CreateVideoAIGCTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVideoAIGCTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateVideoAIGCTaskResponse) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{25}
}

func (x *CreateVideoAIGCTaskResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *CreateVideoAIGCTaskResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *CreateVideoAIGCTaskResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *CreateVideoAIGCTaskResponse) GetData() *CreateVideoAIGCTaskData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DubbingInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OriginalScripts   []*Script `protobuf:"bytes,1,rep,name=original_scripts,proto3" json:"original_scripts,omitempty"`
	TargetLanguage    string    `protobuf:"bytes,2,opt,name=target_language,proto3" json:"target_language,omitempty"`
	TranslatedScripts []*Script `protobuf:"bytes,3,rep,name=translated_scripts,proto3" json:"translated_scripts,omitempty"`
}

func (x *DubbingInfo) Reset() {
	*x = DubbingInfo{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DubbingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DubbingInfo) ProtoMessage() {}

func (x *DubbingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DubbingInfo.ProtoReflect.Descriptor instead.
func (*DubbingInfo) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{26}
}

func (x *DubbingInfo) GetOriginalScripts() []*Script {
	if x != nil {
		return x.OriginalScripts
	}
	return nil
}

func (x *DubbingInfo) GetTargetLanguage() string {
	if x != nil {
		return x.TargetLanguage
	}
	return ""
}

func (x *DubbingInfo) GetTranslatedScripts() []*Script {
	if x != nil {
		return x.TranslatedScripts
	}
	return nil
}

type VideoAIGCTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AigcVideoType string       `protobuf:"bytes,1,opt,name=aigc_video_type,proto3" json:"aigc_video_type,omitempty"`
	CreateTime    string       `protobuf:"bytes,2,opt,name=create_time,proto3" json:"create_time,omitempty"`
	DubbingInfo   *DubbingInfo `protobuf:"bytes,3,opt,name=dubbing_info,proto3" json:"dubbing_info,omitempty"`
	PreviewUrl    string       `protobuf:"bytes,4,opt,name=preview_url,proto3" json:"preview_url,omitempty"`
	VideoId       string       `protobuf:"bytes,5,opt,name=video_id,proto3" json:"video_id,omitempty"`
	VideoName     string       `protobuf:"bytes,6,opt,name=video_name,proto3" json:"video_name,omitempty"`
}

func (x *VideoAIGCTask) Reset() {
	*x = VideoAIGCTask{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoAIGCTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoAIGCTask) ProtoMessage() {}

func (x *VideoAIGCTask) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoAIGCTask.ProtoReflect.Descriptor instead.
func (*VideoAIGCTask) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{27}
}

func (x *VideoAIGCTask) GetAigcVideoType() string {
	if x != nil {
		return x.AigcVideoType
	}
	return ""
}

func (x *VideoAIGCTask) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *VideoAIGCTask) GetDubbingInfo() *DubbingInfo {
	if x != nil {
		return x.DubbingInfo
	}
	return nil
}

func (x *VideoAIGCTask) GetPreviewUrl() string {
	if x != nil {
		return x.PreviewUrl
	}
	return ""
}

func (x *VideoAIGCTask) GetVideoId() string {
	if x != nil {
		return x.VideoId
	}
	return ""
}

func (x *VideoAIGCTask) GetVideoName() string {
	if x != nil {
		return x.VideoName
	}
	return ""
}

type VideoAIGCTaskData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*VideoAIGCTask `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Pagination *Pagination      `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *VideoAIGCTaskData) Reset() {
	*x = VideoAIGCTaskData{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoAIGCTaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoAIGCTaskData) ProtoMessage() {}

func (x *VideoAIGCTaskData) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoAIGCTaskData.ProtoReflect.Descriptor instead.
func (*VideoAIGCTaskData) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{28}
}

func (x *VideoAIGCTaskData) GetList() []*VideoAIGCTask {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoAIGCTaskData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetVideoAIGCTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32              `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string             `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string             `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *VideoAIGCTaskData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetVideoAIGCTaskResponse) Reset() {
	*x = GetVideoAIGCTaskResponse{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVideoAIGCTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVideoAIGCTaskResponse) ProtoMessage() {}

func (x *GetVideoAIGCTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVideoAIGCTaskResponse.ProtoReflect.Descriptor instead.
func (*GetVideoAIGCTaskResponse) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{29}
}

func (x *GetVideoAIGCTaskResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetVideoAIGCTaskResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetVideoAIGCTaskResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetVideoAIGCTaskResponse) GetData() *VideoAIGCTaskData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ProductVideoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoGenerationCount int32              `protobuf:"varint,1,opt,name=video_generation_count,proto3" json:"video_generation_count,omitempty"`
	TargetLanguage       string             `protobuf:"bytes,2,opt,name=target_language,proto3" json:"target_language,omitempty"`
	ProductInfoList      []*ProductInfoList `protobuf:"bytes,3,rep,name=product_info_list,proto3" json:"product_info_list,omitempty"`
	InputImageList       *InputImageList    `protobuf:"bytes,4,opt,name=input_image_list,proto3,oneof" json:"input_image_list,omitempty"`
	InputVideoList       *InputVideoList    `protobuf:"bytes,5,opt,name=input_video_list,proto3,oneof" json:"input_video_list,omitempty"`
	AvatarInfo           *AvatarInfo        `protobuf:"bytes,6,opt,name=avatar_info,proto3,oneof" json:"avatar_info,omitempty"`
}

func (x *ProductVideoInfo) Reset() {
	*x = ProductVideoInfo{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProductVideoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductVideoInfo) ProtoMessage() {}

func (x *ProductVideoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductVideoInfo.ProtoReflect.Descriptor instead.
func (*ProductVideoInfo) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{30}
}

func (x *ProductVideoInfo) GetVideoGenerationCount() int32 {
	if x != nil {
		return x.VideoGenerationCount
	}
	return 0
}

func (x *ProductVideoInfo) GetTargetLanguage() string {
	if x != nil {
		return x.TargetLanguage
	}
	return ""
}

func (x *ProductVideoInfo) GetProductInfoList() []*ProductInfoList {
	if x != nil {
		return x.ProductInfoList
	}
	return nil
}

func (x *ProductVideoInfo) GetInputImageList() *InputImageList {
	if x != nil {
		return x.InputImageList
	}
	return nil
}

func (x *ProductVideoInfo) GetInputVideoList() *InputVideoList {
	if x != nil {
		return x.InputVideoList
	}
	return nil
}

func (x *ProductVideoInfo) GetAvatarInfo() *AvatarInfo {
	if x != nil {
		return x.AvatarInfo
	}
	return nil
}

type ProductInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceLanguage string   `protobuf:"bytes,1,opt,name=source_language,proto3" json:"source_language,omitempty"`
	ProductName    string   `protobuf:"bytes,2,opt,name=product_name,proto3" json:"product_name,omitempty"`
	Title          string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description    string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	SellingPoints  []string `protobuf:"bytes,5,rep,name=selling_points,proto3" json:"selling_points,omitempty"`
	Brand          string   `protobuf:"bytes,6,opt,name=brand,proto3" json:"brand,omitempty"`
	Price          float64  `protobuf:"fixed64,7,opt,name=price,proto3" json:"price,omitempty"`
	Currency       string   `protobuf:"bytes,8,opt,name=currency,proto3" json:"currency,omitempty"`
}

func (x *ProductInfoList) Reset() {
	*x = ProductInfoList{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProductInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductInfoList) ProtoMessage() {}

func (x *ProductInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductInfoList.ProtoReflect.Descriptor instead.
func (*ProductInfoList) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{31}
}

func (x *ProductInfoList) GetSourceLanguage() string {
	if x != nil {
		return x.SourceLanguage
	}
	return ""
}

func (x *ProductInfoList) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *ProductInfoList) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ProductInfoList) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ProductInfoList) GetSellingPoints() []string {
	if x != nil {
		return x.SellingPoints
	}
	return nil
}

func (x *ProductInfoList) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *ProductInfoList) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ProductInfoList) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

type InputImageList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrlList []string `protobuf:"bytes,1,rep,name=image_url_list,proto3" json:"image_url_list,omitempty"`
}

func (x *InputImageList) Reset() {
	*x = InputImageList{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputImageList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputImageList) ProtoMessage() {}

func (x *InputImageList) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputImageList.ProtoReflect.Descriptor instead.
func (*InputImageList) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{32}
}

func (x *InputImageList) GetImageUrlList() []string {
	if x != nil {
		return x.ImageUrlList
	}
	return nil
}

type InputVideoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoIdList []string `protobuf:"bytes,1,rep,name=video_id_list,proto3" json:"video_id_list,omitempty"`
}

func (x *InputVideoList) Reset() {
	*x = InputVideoList{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputVideoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputVideoList) ProtoMessage() {}

func (x *InputVideoList) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputVideoList.ProtoReflect.Descriptor instead.
func (*InputVideoList) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{33}
}

func (x *InputVideoList) GetVideoIdList() []string {
	if x != nil {
		return x.VideoIdList
	}
	return nil
}

type AvatarInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AvatarId string `protobuf:"bytes,1,opt,name=avatar_id,proto3" json:"avatar_id,omitempty"`
}

func (x *AvatarInfo) Reset() {
	*x = AvatarInfo{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvatarInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarInfo) ProtoMessage() {}

func (x *AvatarInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarInfo.ProtoReflect.Descriptor instead.
func (*AvatarInfo) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{34}
}

func (x *AvatarInfo) GetAvatarId() string {
	if x != nil {
		return x.AvatarId
	}
	return ""
}

type CreateMixVideoAIGCTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AigcVideoType    string            `protobuf:"bytes,1,opt,name=aigc_video_type,proto3" json:"aigc_video_type,omitempty"` // AVATAR_PRODUCT、STOCK_VIDEO、VOICEOVER
	ProductVideoInfo *ProductVideoInfo `protobuf:"bytes,2,opt,name=product_video_info,proto3" json:"product_video_info,omitempty"`
}

func (x *CreateMixVideoAIGCTaskRequest) Reset() {
	*x = CreateMixVideoAIGCTaskRequest{}
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMixVideoAIGCTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMixVideoAIGCTaskRequest) ProtoMessage() {}

func (x *CreateMixVideoAIGCTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_video_aigc_v1_tiktok_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMixVideoAIGCTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateMixVideoAIGCTaskRequest) Descriptor() ([]byte, []int) {
	return file_api_video_aigc_v1_tiktok_proto_rawDescGZIP(), []int{35}
}

func (x *CreateMixVideoAIGCTaskRequest) GetAigcVideoType() string {
	if x != nil {
		return x.AigcVideoType
	}
	return ""
}

func (x *CreateMixVideoAIGCTaskRequest) GetProductVideoInfo() *ProductVideoInfo {
	if x != nil {
		return x.ProductVideoInfo
	}
	return nil
}

var File_api_video_aigc_v1_tiktok_proto protoreflect.FileDescriptor

var file_api_video_aigc_v1_tiktok_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x77, 0x0a,
	0x15, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75,
	0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72,
	0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x63,
	0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x22, 0xeb, 0x01, 0x0a, 0x15, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x46, 0x69, 0x78, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x73, 0x12, 0x22,
	0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x36, 0x0a, 0x16, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x16, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x22, 0x5d, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6b, 0x74, 0x6f,
	0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6a, 0x0a, 0x24, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74,
	0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44,
	0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x22, 0xd7, 0x01, 0x0a, 0x10, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x75, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2a, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x75, 0x62, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2d, 0x0a, 0x06,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x52, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x22, 0x6c, 0x0a, 0x06, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x1a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x69, 0x67, 0x63,
	0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x69, 0x67, 0x63, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x4f, 0x0a, 0x12, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x12, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x22, 0x7f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41,
	0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a,
	0x0a, 0x10, 0x61, 0x69, 0x67, 0x63, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x61, 0x69, 0x67, 0x63, 0x5f, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x22, 0x93, 0x01, 0x0a, 0x16, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69,
	0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69,
	0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd2, 0x01, 0x0a, 0x0e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x66, 0x69, 0x78, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x78, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x66, 0x6c, 0x61, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6c, 0x61, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72,
	0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x22,
	0x93, 0x01, 0x0a, 0x16, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72,
	0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d,
	0x73, 0x67, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x82, 0x01, 0x0a, 0x0e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46,
	0x69, 0x78, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x35, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61,
	0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xea, 0x03, 0x0a, 0x12, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x12, 0x28, 0x0a,
	0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72,
	0x6c, 0x12, 0x38, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c,
	0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x17, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x5f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x74, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x74, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x13, 0x54, 0x69, 0x6b, 0x74,
	0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x2e, 0x0a,
	0x12, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x20, 0x0a,
	0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x2a, 0x0a, 0x10, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e,
	0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x5f, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x22, 0x8c, 0x01, 0x0a, 0x17,
	0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xa8, 0x01, 0x0a, 0x22, 0x47,
	0x65, 0x74, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69,
	0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x55, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x22, 0x5d, 0x0a, 0x1d,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69,
	0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb1, 0x01, 0x0a, 0x25,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69,
	0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x40, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xef, 0x01, 0x0a, 0x17, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61,
	0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x1e, 0x0a,
	0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72,
	0x6c, 0x22, 0x94, 0x01, 0x0a, 0x1b, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69,
	0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x39, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69,
	0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6b, 0x74, 0x6f,
	0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xac, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x3e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69,
	0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69,
	0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x35, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x22, 0xa1,
	0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49,
	0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0xc1, 0x01, 0x0a, 0x0b, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x41, 0x0a, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x52, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x45, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x73, 0x22, 0xf9, 0x01, 0x0a, 0x0d, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x69, 0x67, 0x63,
	0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x69, 0x67, 0x63, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x75, 0x62, 0x62, 0x69,
	0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x11, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43,
	0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61,
	0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x98, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49,
	0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xde, 0x03, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a, 0x16, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a,
	0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x4c, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x10, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x10, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x4e, 0x0a, 0x10, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x01,
	0x52, 0x10, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x02, 0x52, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x13, 0x0a, 0x11,
	0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x22, 0x87, 0x02, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x73,
	0x65, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x22, 0x38, 0x0a, 0x0e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x36, 0x0a, 0x0e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x2a, 0x0a,
	0x0a, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x1d, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x78, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x61,
	0x69, 0x67, 0x63, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x69, 0x67, 0x63, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4f, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x32, 0xf2, 0x09, 0x0a, 0x0d, 0x54, 0x69, 0x6b, 0x74, 0x6f,
	0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x0e, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x46, 0x69, 0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x24, 0x2e, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x46, 0x69, 0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20,
	0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x2f,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x66, 0x69, 0x78, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x12, 0x85, 0x01, 0x0a, 0x0e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x12, 0x24, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x78, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46,
	0x69, 0x78, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x66, 0x69,
	0x78, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0xa4, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x30, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6b, 0x74, 0x6f,
	0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6b,
	0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b,
	0x2f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0xb5, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b,
	0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73,
	0x6b, 0x12, 0x33, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69,
	0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61,
	0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6b,
	0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6b,
	0x74, 0x6f, 0x6b, 0x2f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x12, 0xa9, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x30, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61,
	0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6b, 0x74, 0x6f, 0x6b,
	0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6b, 0x74,
	0x6f, 0x6b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x2f,
	0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x12, 0x93, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x29, 0x2e, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61,
	0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x61, 0x69, 0x67, 0x63, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x8f, 0x01, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x26,
	0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61,
	0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41,
	0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x31, 0x2f,
	0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67,
	0x63, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x9d, 0x01, 0x0a, 0x16,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x78, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49,
	0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2c, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61,
	0x69, 0x67, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x69, 0x78,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x41, 0x49, 0x47, 0x43, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x6b, 0x2f, 0x6d, 0x69, 0x78, 0x5f, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x42, 0x13, 0x5a, 0x11, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x69, 0x67, 0x63, 0x2f, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_video_aigc_v1_tiktok_proto_rawDescOnce sync.Once
	file_api_video_aigc_v1_tiktok_proto_rawDescData = file_api_video_aigc_v1_tiktok_proto_rawDesc
)

func file_api_video_aigc_v1_tiktok_proto_rawDescGZIP() []byte {
	file_api_video_aigc_v1_tiktok_proto_rawDescOnce.Do(func() {
		file_api_video_aigc_v1_tiktok_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_video_aigc_v1_tiktok_proto_rawDescData)
	})
	return file_api_video_aigc_v1_tiktok_proto_rawDescData
}

var file_api_video_aigc_v1_tiktok_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_api_video_aigc_v1_tiktok_proto_goTypes = []any{
	(*VideoFixUploadRequest)(nil),                 // 0: video_aigc.v1.VideoFixUploadRequest
	(*VideoFixSearchRequest)(nil),                 // 1: video_aigc.v1.VideoFixSearchRequest
	(*GetTiktokDigitalAvatarListRequest)(nil),     // 2: video_aigc.v1.GetTiktokDigitalAvatarListRequest
	(*CreateTiktokDigitalAvatarTask)(nil),         // 3: video_aigc.v1.CreateTiktokDigitalAvatarTask
	(*CreateTiktokDigitalAvatarTaskRequest)(nil),  // 4: video_aigc.v1.CreateTiktokDigitalAvatarTaskRequest
	(*GetTiktokDigitalAvatarTaskRequest)(nil),     // 5: video_aigc.v1.GetTiktokDigitalAvatarTaskRequest
	(*DubbingVideoInfo)(nil),                      // 6: video_aigc.v1.DubbingVideoInfo
	(*Script)(nil),                                // 7: video_aigc.v1.Script
	(*CreateVideoAIGCTaskRequest)(nil),            // 8: video_aigc.v1.CreateVideoAIGCTaskRequest
	(*GetVideoAIGCTaskRequest)(nil),               // 9: video_aigc.v1.GetVideoAIGCTaskRequest
	(*VideoFixUploadResponse)(nil),                // 10: video_aigc.v1.VideoFixUploadResponse
	(*VideoFixUpload)(nil),                        // 11: video_aigc.v1.VideoFixUpload
	(*VideoFixSearchResponse)(nil),                // 12: video_aigc.v1.VideoFixSearchResponse
	(*VideoFixSearch)(nil),                        // 13: video_aigc.v1.VideoFixSearch
	(*VideoFixSearchItem)(nil),                    // 14: video_aigc.v1.VideoFixSearchItem
	(*TiktokDigitalAvatar)(nil),                   // 15: video_aigc.v1.TiktokDigitalAvatar
	(*TiktokDigitalAvatarData)(nil),               // 16: video_aigc.v1.TiktokDigitalAvatarData
	(*GetTiktokDigitalAvatarListResponse)(nil),    // 17: video_aigc.v1.GetTiktokDigitalAvatarListResponse
	(*CreateTiktokDigitalAvatar)(nil),             // 18: video_aigc.v1.CreateTiktokDigitalAvatar
	(*CreateTiktokDigitalAvatarData)(nil),         // 19: video_aigc.v1.CreateTiktokDigitalAvatarData
	(*CreateTiktokDigitalAvatarTaskResponse)(nil), // 20: video_aigc.v1.CreateTiktokDigitalAvatarTaskResponse
	(*TiktokDigitalAvatarTask)(nil),               // 21: video_aigc.v1.TiktokDigitalAvatarTask
	(*TiktokDigitalAvatarTaskData)(nil),           // 22: video_aigc.v1.TiktokDigitalAvatarTaskData
	(*GetTiktokDigitalAvatarTaskResponse)(nil),    // 23: video_aigc.v1.GetTiktokDigitalAvatarTaskResponse
	(*CreateVideoAIGCTaskData)(nil),               // 24: video_aigc.v1.CreateVideoAIGCTaskData
	(*CreateVideoAIGCTaskResponse)(nil),           // 25: video_aigc.v1.CreateVideoAIGCTaskResponse
	(*DubbingInfo)(nil),                           // 26: video_aigc.v1.DubbingInfo
	(*VideoAIGCTask)(nil),                         // 27: video_aigc.v1.VideoAIGCTask
	(*VideoAIGCTaskData)(nil),                     // 28: video_aigc.v1.VideoAIGCTaskData
	(*GetVideoAIGCTaskResponse)(nil),              // 29: video_aigc.v1.GetVideoAIGCTaskResponse
	(*ProductVideoInfo)(nil),                      // 30: video_aigc.v1.ProductVideoInfo
	(*ProductInfoList)(nil),                       // 31: video_aigc.v1.ProductInfoList
	(*InputImageList)(nil),                        // 32: video_aigc.v1.InputImageList
	(*InputVideoList)(nil),                        // 33: video_aigc.v1.InputVideoList
	(*AvatarInfo)(nil),                            // 34: video_aigc.v1.AvatarInfo
	(*CreateMixVideoAIGCTaskRequest)(nil),         // 35: video_aigc.v1.CreateMixVideoAIGCTaskRequest
	(*Pagination)(nil),                            // 36: video_aigc.v1.Pagination
}
var file_api_video_aigc_v1_tiktok_proto_depIdxs = []int32{
	3,  // 0: video_aigc.v1.CreateTiktokDigitalAvatarTaskRequest.tasks:type_name -> video_aigc.v1.CreateTiktokDigitalAvatarTask
	7,  // 1: video_aigc.v1.DubbingVideoInfo.script:type_name -> video_aigc.v1.Script
	6,  // 2: video_aigc.v1.CreateVideoAIGCTaskRequest.dubbing_video_info:type_name -> video_aigc.v1.DubbingVideoInfo
	11, // 3: video_aigc.v1.VideoFixUploadResponse.data:type_name -> video_aigc.v1.VideoFixUpload
	13, // 4: video_aigc.v1.VideoFixSearchResponse.data:type_name -> video_aigc.v1.VideoFixSearch
	14, // 5: video_aigc.v1.VideoFixSearch.list:type_name -> video_aigc.v1.VideoFixSearchItem
	36, // 6: video_aigc.v1.VideoFixSearch.pagination:type_name -> video_aigc.v1.Pagination
	36, // 7: video_aigc.v1.TiktokDigitalAvatarData.pagination:type_name -> video_aigc.v1.Pagination
	15, // 8: video_aigc.v1.TiktokDigitalAvatarData.list:type_name -> video_aigc.v1.TiktokDigitalAvatar
	16, // 9: video_aigc.v1.GetTiktokDigitalAvatarListResponse.data:type_name -> video_aigc.v1.TiktokDigitalAvatarData
	18, // 10: video_aigc.v1.CreateTiktokDigitalAvatarData.list:type_name -> video_aigc.v1.CreateTiktokDigitalAvatar
	19, // 11: video_aigc.v1.CreateTiktokDigitalAvatarTaskResponse.data:type_name -> video_aigc.v1.CreateTiktokDigitalAvatarData
	36, // 12: video_aigc.v1.TiktokDigitalAvatarTaskData.pagination:type_name -> video_aigc.v1.Pagination
	21, // 13: video_aigc.v1.TiktokDigitalAvatarTaskData.list:type_name -> video_aigc.v1.TiktokDigitalAvatarTask
	22, // 14: video_aigc.v1.GetTiktokDigitalAvatarTaskResponse.data:type_name -> video_aigc.v1.TiktokDigitalAvatarTaskData
	24, // 15: video_aigc.v1.CreateVideoAIGCTaskResponse.data:type_name -> video_aigc.v1.CreateVideoAIGCTaskData
	7,  // 16: video_aigc.v1.DubbingInfo.original_scripts:type_name -> video_aigc.v1.Script
	7,  // 17: video_aigc.v1.DubbingInfo.translated_scripts:type_name -> video_aigc.v1.Script
	26, // 18: video_aigc.v1.VideoAIGCTask.dubbing_info:type_name -> video_aigc.v1.DubbingInfo
	27, // 19: video_aigc.v1.VideoAIGCTaskData.list:type_name -> video_aigc.v1.VideoAIGCTask
	36, // 20: video_aigc.v1.VideoAIGCTaskData.pagination:type_name -> video_aigc.v1.Pagination
	28, // 21: video_aigc.v1.GetVideoAIGCTaskResponse.data:type_name -> video_aigc.v1.VideoAIGCTaskData
	31, // 22: video_aigc.v1.ProductVideoInfo.product_info_list:type_name -> video_aigc.v1.ProductInfoList
	32, // 23: video_aigc.v1.ProductVideoInfo.input_image_list:type_name -> video_aigc.v1.InputImageList
	33, // 24: video_aigc.v1.ProductVideoInfo.input_video_list:type_name -> video_aigc.v1.InputVideoList
	34, // 25: video_aigc.v1.ProductVideoInfo.avatar_info:type_name -> video_aigc.v1.AvatarInfo
	30, // 26: video_aigc.v1.CreateMixVideoAIGCTaskRequest.product_video_info:type_name -> video_aigc.v1.ProductVideoInfo
	0,  // 27: video_aigc.v1.TiktokService.VideoFixUpload:input_type -> video_aigc.v1.VideoFixUploadRequest
	1,  // 28: video_aigc.v1.TiktokService.VideoFixSearch:input_type -> video_aigc.v1.VideoFixSearchRequest
	2,  // 29: video_aigc.v1.TiktokService.GetTiktokDigitalAvatarList:input_type -> video_aigc.v1.GetTiktokDigitalAvatarListRequest
	4,  // 30: video_aigc.v1.TiktokService.CreateTiktokDigitalAvatarTask:input_type -> video_aigc.v1.CreateTiktokDigitalAvatarTaskRequest
	5,  // 31: video_aigc.v1.TiktokService.GetTiktokDigitalAvatarTask:input_type -> video_aigc.v1.GetTiktokDigitalAvatarTaskRequest
	8,  // 32: video_aigc.v1.TiktokService.CreateVideoAIGCTask:input_type -> video_aigc.v1.CreateVideoAIGCTaskRequest
	9,  // 33: video_aigc.v1.TiktokService.GetVideoAIGCTask:input_type -> video_aigc.v1.GetVideoAIGCTaskRequest
	35, // 34: video_aigc.v1.TiktokService.CreateMixVideoAIGCTask:input_type -> video_aigc.v1.CreateMixVideoAIGCTaskRequest
	10, // 35: video_aigc.v1.TiktokService.VideoFixUpload:output_type -> video_aigc.v1.VideoFixUploadResponse
	12, // 36: video_aigc.v1.TiktokService.VideoFixSearch:output_type -> video_aigc.v1.VideoFixSearchResponse
	17, // 37: video_aigc.v1.TiktokService.GetTiktokDigitalAvatarList:output_type -> video_aigc.v1.GetTiktokDigitalAvatarListResponse
	20, // 38: video_aigc.v1.TiktokService.CreateTiktokDigitalAvatarTask:output_type -> video_aigc.v1.CreateTiktokDigitalAvatarTaskResponse
	23, // 39: video_aigc.v1.TiktokService.GetTiktokDigitalAvatarTask:output_type -> video_aigc.v1.GetTiktokDigitalAvatarTaskResponse
	25, // 40: video_aigc.v1.TiktokService.CreateVideoAIGCTask:output_type -> video_aigc.v1.CreateVideoAIGCTaskResponse
	29, // 41: video_aigc.v1.TiktokService.GetVideoAIGCTask:output_type -> video_aigc.v1.GetVideoAIGCTaskResponse
	25, // 42: video_aigc.v1.TiktokService.CreateMixVideoAIGCTask:output_type -> video_aigc.v1.CreateVideoAIGCTaskResponse
	35, // [35:43] is the sub-list for method output_type
	27, // [27:35] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_api_video_aigc_v1_tiktok_proto_init() }
func file_api_video_aigc_v1_tiktok_proto_init() {
	if File_api_video_aigc_v1_tiktok_proto != nil {
		return
	}
	file_api_video_aigc_v1_common_proto_init()
	file_api_video_aigc_v1_tiktok_proto_msgTypes[30].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_video_aigc_v1_tiktok_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_video_aigc_v1_tiktok_proto_goTypes,
		DependencyIndexes: file_api_video_aigc_v1_tiktok_proto_depIdxs,
		MessageInfos:      file_api_video_aigc_v1_tiktok_proto_msgTypes,
	}.Build()
	File_api_video_aigc_v1_tiktok_proto = out.File
	file_api_video_aigc_v1_tiktok_proto_rawDesc = nil
	file_api_video_aigc_v1_tiktok_proto_goTypes = nil
	file_api_video_aigc_v1_tiktok_proto_depIdxs = nil
}
