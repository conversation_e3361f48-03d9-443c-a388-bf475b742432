// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/customer_project_space/v1/common.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 空请求结构
type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_common_proto_rawDescGZIP(), []int{0}
}

// 通用返回
type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32  `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *CommonResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *CommonResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *CommonResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

// 通用用户信息
type CommonUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int32  `protobuf:"varint,1,opt,name=user_id,proto3" json:"user_id,omitempty"`
	UserName  string `protobuf:"bytes,2,opt,name=user_name,proto3" json:"user_name,omitempty"`
	UserEmail string `protobuf:"bytes,3,opt,name=user_email,proto3" json:"user_email,omitempty"`
}

func (x *CommonUserInfo) Reset() {
	*x = CommonUserInfo{}
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonUserInfo) ProtoMessage() {}

func (x *CommonUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonUserInfo.ProtoReflect.Descriptor instead.
func (*CommonUserInfo) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_common_proto_rawDescGZIP(), []int{2}
}

func (x *CommonUserInfo) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CommonUserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *CommonUserInfo) GetUserEmail() string {
	if x != nil {
		return x.UserEmail
	}
	return ""
}

// 分页参数
type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,proto3" json:"page_size,omitempty"`
	PageNum  int32 `protobuf:"varint,3,opt,name=page_num,proto3" json:"page_num,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_common_proto_rawDescGZIP(), []int{3}
}

func (x *Pagination) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Pagination) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *Pagination) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

// ================ 业务通用message ======================
// 签约信息
type SignInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AeEmail           string `protobuf:"bytes,1,opt,name=ae_email,proto3" json:"ae_email,omitempty"`
	AeId              int32  `protobuf:"varint,2,opt,name=ae_id,proto3" json:"ae_id,omitempty"`
	AeName            string `protobuf:"bytes,3,opt,name=ae_name,proto3" json:"ae_name,omitempty"`
	CompanyId         int32  `protobuf:"varint,4,opt,name=company_id,proto3" json:"company_id,omitempty"`
	OurSideEntity     string `protobuf:"bytes,5,opt,name=our_side_entity,proto3" json:"our_side_entity,omitempty"`
	SaleEmail         string `protobuf:"bytes,6,opt,name=sale_email,proto3" json:"sale_email,omitempty"`
	SaleId            int32  `protobuf:"varint,7,opt,name=sale_id,proto3" json:"sale_id,omitempty"`
	SaleName          string `protobuf:"bytes,8,opt,name=sale_name,proto3" json:"sale_name,omitempty"`
	Settlement        string `protobuf:"bytes,9,opt,name=settlement,proto3" json:"settlement,omitempty"`
	SettlementId      int32  `protobuf:"varint,10,opt,name=settlement_id,proto3" json:"settlement_id,omitempty"`
	SignId            int32  `protobuf:"varint,11,opt,name=sign_id,proto3" json:"sign_id,omitempty"`
	SignSaleEmail     string `protobuf:"bytes,12,opt,name=sign_sale_email,proto3" json:"sign_sale_email,omitempty"`
	SignSaleId        int32  `protobuf:"varint,13,opt,name=sign_sale_id,proto3" json:"sign_sale_id,omitempty"`
	SignSaleName      string `protobuf:"bytes,14,opt,name=sign_sale_name,proto3" json:"sign_sale_name,omitempty"`
	OurSideEntityName string `protobuf:"bytes,15,opt,name=our_side_entity_name,proto3" json:"our_side_entity_name,omitempty"`
	CompanyName       string `protobuf:"bytes,16,opt,name=company_name,proto3" json:"company_name,omitempty"`
}

func (x *SignInfo) Reset() {
	*x = SignInfo{}
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInfo) ProtoMessage() {}

func (x *SignInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInfo.ProtoReflect.Descriptor instead.
func (*SignInfo) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_common_proto_rawDescGZIP(), []int{4}
}

func (x *SignInfo) GetAeEmail() string {
	if x != nil {
		return x.AeEmail
	}
	return ""
}

func (x *SignInfo) GetAeId() int32 {
	if x != nil {
		return x.AeId
	}
	return 0
}

func (x *SignInfo) GetAeName() string {
	if x != nil {
		return x.AeName
	}
	return ""
}

func (x *SignInfo) GetCompanyId() int32 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SignInfo) GetOurSideEntity() string {
	if x != nil {
		return x.OurSideEntity
	}
	return ""
}

func (x *SignInfo) GetSaleEmail() string {
	if x != nil {
		return x.SaleEmail
	}
	return ""
}

func (x *SignInfo) GetSaleId() int32 {
	if x != nil {
		return x.SaleId
	}
	return 0
}

func (x *SignInfo) GetSaleName() string {
	if x != nil {
		return x.SaleName
	}
	return ""
}

func (x *SignInfo) GetSettlement() string {
	if x != nil {
		return x.Settlement
	}
	return ""
}

func (x *SignInfo) GetSettlementId() int32 {
	if x != nil {
		return x.SettlementId
	}
	return 0
}

func (x *SignInfo) GetSignId() int32 {
	if x != nil {
		return x.SignId
	}
	return 0
}

func (x *SignInfo) GetSignSaleEmail() string {
	if x != nil {
		return x.SignSaleEmail
	}
	return ""
}

func (x *SignInfo) GetSignSaleId() int32 {
	if x != nil {
		return x.SignSaleId
	}
	return 0
}

func (x *SignInfo) GetSignSaleName() string {
	if x != nil {
		return x.SignSaleName
	}
	return ""
}

func (x *SignInfo) GetOurSideEntityName() string {
	if x != nil {
		return x.OurSideEntityName
	}
	return ""
}

func (x *SignInfo) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

// Impact Campaign
type ImpactCampaign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CampaignID   string `protobuf:"bytes,1,opt,name=CampaignID,json=campaign_id,proto3" json:"CampaignID,omitempty"`
	CampaignName string `protobuf:"bytes,2,opt,name=CampaignName,json=campaign_name,proto3" json:"CampaignName,omitempty"`
}

func (x *ImpactCampaign) Reset() {
	*x = ImpactCampaign{}
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImpactCampaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImpactCampaign) ProtoMessage() {}

func (x *ImpactCampaign) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_common_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImpactCampaign.ProtoReflect.Descriptor instead.
func (*ImpactCampaign) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_common_proto_rawDescGZIP(), []int{5}
}

func (x *ImpactCampaign) GetCampaignID() string {
	if x != nil {
		return x.CampaignID
	}
	return ""
}

func (x *ImpactCampaign) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

var File_api_customer_project_space_v1_common_proto protoreflect.FileDescriptor

var file_api_customer_project_space_v1_common_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x58, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x68, 0x0a, 0x0e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x22, 0x5c, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x22, 0xa6, 0x04, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x61, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x65, 0x5f, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x6f,
	0x75, 0x72, 0x5f, 0x73, 0x69, 0x64, 0x65, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x75, 0x72, 0x5f, 0x73, 0x69, 0x64, 0x65, 0x5f, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x61, 0x6c, 0x65, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x28, 0x0a,
	0x0f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x61, 0x6c,
	0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x5f,
	0x73, 0x61, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73,
	0x69, 0x67, 0x6e, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73,
	0x69, 0x67, 0x6e, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x6f, 0x75, 0x72, 0x5f, 0x73, 0x69, 0x64, 0x65, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x6f, 0x75, 0x72, 0x5f, 0x73, 0x69, 0x64, 0x65, 0x5f, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x56, 0x0a, 0x0e, 0x49,
	0x6d, 0x70, 0x61, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12, 0x1f, 0x0a,
	0x0a, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x23,
	0x0a, 0x0c, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x1f, 0x5a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_customer_project_space_v1_common_proto_rawDescOnce sync.Once
	file_api_customer_project_space_v1_common_proto_rawDescData = file_api_customer_project_space_v1_common_proto_rawDesc
)

func file_api_customer_project_space_v1_common_proto_rawDescGZIP() []byte {
	file_api_customer_project_space_v1_common_proto_rawDescOnce.Do(func() {
		file_api_customer_project_space_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_customer_project_space_v1_common_proto_rawDescData)
	})
	return file_api_customer_project_space_v1_common_proto_rawDescData
}

var file_api_customer_project_space_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_customer_project_space_v1_common_proto_goTypes = []any{
	(*Empty)(nil),          // 0: customer_project_space.v1.Empty
	(*CommonResponse)(nil), // 1: customer_project_space.v1.CommonResponse
	(*CommonUserInfo)(nil), // 2: customer_project_space.v1.CommonUserInfo
	(*Pagination)(nil),     // 3: customer_project_space.v1.Pagination
	(*SignInfo)(nil),       // 4: customer_project_space.v1.SignInfo
	(*ImpactCampaign)(nil), // 5: customer_project_space.v1.ImpactCampaign
}
var file_api_customer_project_space_v1_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_customer_project_space_v1_common_proto_init() }
func file_api_customer_project_space_v1_common_proto_init() {
	if File_api_customer_project_space_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_customer_project_space_v1_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_customer_project_space_v1_common_proto_goTypes,
		DependencyIndexes: file_api_customer_project_space_v1_common_proto_depIdxs,
		MessageInfos:      file_api_customer_project_space_v1_common_proto_msgTypes,
	}.Build()
	File_api_customer_project_space_v1_common_proto = out.File
	file_api_customer_project_space_v1_common_proto_rawDesc = nil
	file_api_customer_project_space_v1_common_proto_goTypes = nil
	file_api_customer_project_space_v1_common_proto_depIdxs = nil
}
