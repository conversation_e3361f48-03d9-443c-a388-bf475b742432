// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.29.3
// source: api/customer_project_space/v1/ads_account.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAdsAccountManageServiceAddAdsAccount = "/customer_project_space.v1.AdsAccountManageService/AddAdsAccount"
const OperationAdsAccountManageServiceEnOrDisableAdsAccount = "/customer_project_space.v1.AdsAccountManageService/EnOrDisableAdsAccount"
const OperationAdsAccountManageServiceGetAccountByAuth = "/customer_project_space.v1.AdsAccountManageService/GetAccountByAuth"
const OperationAdsAccountManageServiceGetAdsAccountList = "/customer_project_space.v1.AdsAccountManageService/GetAdsAccountList"
const OperationAdsAccountManageServiceOpenAPIGetEnableAdsAccountList = "/customer_project_space.v1.AdsAccountManageService/OpenAPIGetEnableAdsAccountList"
const OperationAdsAccountManageServicePreviewAdsAccount = "/customer_project_space.v1.AdsAccountManageService/PreviewAdsAccount"
const OperationAdsAccountManageServiceRemoveAdsAccount = "/customer_project_space.v1.AdsAccountManageService/RemoveAdsAccount"

type AdsAccountManageServiceHTTPServer interface {
	// AddAdsAccount 新增关联账号
	AddAdsAccount(context.Context, *AddAdsAccountRequest) (*CommonResponse, error)
	// EnOrDisableAdsAccount 启用禁用
	EnOrDisableAdsAccount(context.Context, *EnOrDisableAdsAccountRequest) (*CommonResponse, error)
	// GetAccountByAuth 获取授权下的账号
	GetAccountByAuth(context.Context, *GetAccountByAuthRequest) (*GetAccountByAuthResponse, error)
	// GetAdsAccountList 获取项目已关联的账号列表
	GetAdsAccountList(context.Context, *GetAdsAccountListRequest) (*GetAdsAccountListResponse, error)
	// OpenAPIGetEnableAdsAccountList todo 切换成open api 获取所有启用的广告账号
	OpenAPIGetEnableAdsAccountList(context.Context, *OpenApiGetAdsAccountListRequest) (*OpenApiGetAdsAccountListResponse, error)
	// PreviewAdsAccount 预览广告账户的签约信息
	PreviewAdsAccount(context.Context, *PreviewAdsAccountRequest) (*PreviewAdsAccountResponse, error)
	// RemoveAdsAccount 移除账号
	RemoveAdsAccount(context.Context, *RemoveAdsAccountRequest) (*CommonResponse, error)
}

func RegisterAdsAccountManageServiceHTTPServer(s *http.Server, srv AdsAccountManageServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/customer_project/ads_account_list", _AdsAccountManageService_GetAdsAccountList0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/add_ads_account", _AdsAccountManageService_AddAdsAccount0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/able_ads_account", _AdsAccountManageService_EnOrDisableAdsAccount0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/preview_ads_account", _AdsAccountManageService_PreviewAdsAccount0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/remove_ads_account", _AdsAccountManageService_RemoveAdsAccount0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/account_by_auth", _AdsAccountManageService_GetAccountByAuth0_HTTP_Handler(srv))
	r.GET("/open_api/v1/customer_project/ads_account_list", _AdsAccountManageService_OpenAPIGetEnableAdsAccountList0_HTTP_Handler(srv))
}

func _AdsAccountManageService_GetAdsAccountList0_HTTP_Handler(srv AdsAccountManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAdsAccountListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdsAccountManageServiceGetAdsAccountList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAdsAccountList(ctx, req.(*GetAdsAccountListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAdsAccountListResponse)
		return ctx.Result(200, reply)
	}
}

func _AdsAccountManageService_AddAdsAccount0_HTTP_Handler(srv AdsAccountManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddAdsAccountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdsAccountManageServiceAddAdsAccount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddAdsAccount(ctx, req.(*AddAdsAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _AdsAccountManageService_EnOrDisableAdsAccount0_HTTP_Handler(srv AdsAccountManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EnOrDisableAdsAccountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdsAccountManageServiceEnOrDisableAdsAccount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EnOrDisableAdsAccount(ctx, req.(*EnOrDisableAdsAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _AdsAccountManageService_PreviewAdsAccount0_HTTP_Handler(srv AdsAccountManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PreviewAdsAccountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdsAccountManageServicePreviewAdsAccount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PreviewAdsAccount(ctx, req.(*PreviewAdsAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PreviewAdsAccountResponse)
		return ctx.Result(200, reply)
	}
}

func _AdsAccountManageService_RemoveAdsAccount0_HTTP_Handler(srv AdsAccountManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoveAdsAccountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdsAccountManageServiceRemoveAdsAccount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveAdsAccount(ctx, req.(*RemoveAdsAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _AdsAccountManageService_GetAccountByAuth0_HTTP_Handler(srv AdsAccountManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAccountByAuthRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdsAccountManageServiceGetAccountByAuth)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAccountByAuth(ctx, req.(*GetAccountByAuthRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAccountByAuthResponse)
		return ctx.Result(200, reply)
	}
}

func _AdsAccountManageService_OpenAPIGetEnableAdsAccountList0_HTTP_Handler(srv AdsAccountManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OpenApiGetAdsAccountListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdsAccountManageServiceOpenAPIGetEnableAdsAccountList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OpenAPIGetEnableAdsAccountList(ctx, req.(*OpenApiGetAdsAccountListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OpenApiGetAdsAccountListResponse)
		return ctx.Result(200, reply)
	}
}

type AdsAccountManageServiceHTTPClient interface {
	AddAdsAccount(ctx context.Context, req *AddAdsAccountRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	EnOrDisableAdsAccount(ctx context.Context, req *EnOrDisableAdsAccountRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	GetAccountByAuth(ctx context.Context, req *GetAccountByAuthRequest, opts ...http.CallOption) (rsp *GetAccountByAuthResponse, err error)
	GetAdsAccountList(ctx context.Context, req *GetAdsAccountListRequest, opts ...http.CallOption) (rsp *GetAdsAccountListResponse, err error)
	OpenAPIGetEnableAdsAccountList(ctx context.Context, req *OpenApiGetAdsAccountListRequest, opts ...http.CallOption) (rsp *OpenApiGetAdsAccountListResponse, err error)
	PreviewAdsAccount(ctx context.Context, req *PreviewAdsAccountRequest, opts ...http.CallOption) (rsp *PreviewAdsAccountResponse, err error)
	RemoveAdsAccount(ctx context.Context, req *RemoveAdsAccountRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
}

type AdsAccountManageServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAdsAccountManageServiceHTTPClient(client *http.Client) AdsAccountManageServiceHTTPClient {
	return &AdsAccountManageServiceHTTPClientImpl{client}
}

func (c *AdsAccountManageServiceHTTPClientImpl) AddAdsAccount(ctx context.Context, in *AddAdsAccountRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/customer_project/add_ads_account"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdsAccountManageServiceAddAdsAccount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdsAccountManageServiceHTTPClientImpl) EnOrDisableAdsAccount(ctx context.Context, in *EnOrDisableAdsAccountRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/customer_project/able_ads_account"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdsAccountManageServiceEnOrDisableAdsAccount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdsAccountManageServiceHTTPClientImpl) GetAccountByAuth(ctx context.Context, in *GetAccountByAuthRequest, opts ...http.CallOption) (*GetAccountByAuthResponse, error) {
	var out GetAccountByAuthResponse
	pattern := "/v1/customer_project/account_by_auth"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdsAccountManageServiceGetAccountByAuth))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdsAccountManageServiceHTTPClientImpl) GetAdsAccountList(ctx context.Context, in *GetAdsAccountListRequest, opts ...http.CallOption) (*GetAdsAccountListResponse, error) {
	var out GetAdsAccountListResponse
	pattern := "/v1/customer_project/ads_account_list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdsAccountManageServiceGetAdsAccountList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdsAccountManageServiceHTTPClientImpl) OpenAPIGetEnableAdsAccountList(ctx context.Context, in *OpenApiGetAdsAccountListRequest, opts ...http.CallOption) (*OpenApiGetAdsAccountListResponse, error) {
	var out OpenApiGetAdsAccountListResponse
	pattern := "/open_api/v1/customer_project/ads_account_list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdsAccountManageServiceOpenAPIGetEnableAdsAccountList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdsAccountManageServiceHTTPClientImpl) PreviewAdsAccount(ctx context.Context, in *PreviewAdsAccountRequest, opts ...http.CallOption) (*PreviewAdsAccountResponse, error) {
	var out PreviewAdsAccountResponse
	pattern := "/v1/customer_project/preview_ads_account"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdsAccountManageServicePreviewAdsAccount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AdsAccountManageServiceHTTPClientImpl) RemoveAdsAccount(ctx context.Context, in *RemoveAdsAccountRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/customer_project/remove_ads_account"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAdsAccountManageServiceRemoveAdsAccount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
