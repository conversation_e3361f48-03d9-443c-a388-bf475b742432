// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/customer_project_space/v1/material_manage.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ============== 服务定义END ===========================
// 空请求结构
type GetMaterialTagsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectId int32 `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
}

func (x *GetMaterialTagsRequest) Reset() {
	*x = GetMaterialTagsRequest{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaterialTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaterialTagsRequest) ProtoMessage() {}

func (x *GetMaterialTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaterialTagsRequest.ProtoReflect.Descriptor instead.
func (*GetMaterialTagsRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{0}
}

func (x *GetMaterialTagsRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

type CommonIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CustomerProjectId int32 `protobuf:"varint,2,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
}

func (x *CommonIdRequest) Reset() {
	*x = CommonIdRequest{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonIdRequest) ProtoMessage() {}

func (x *CommonIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonIdRequest.ProtoReflect.Descriptor instead.
func (*CommonIdRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{1}
}

func (x *CommonIdRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CommonIdRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

// ================ 请求定义 START ======================
type GetMaterialListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize          int32    `protobuf:"varint,1,opt,name=page_size,proto3" json:"page_size,omitempty"`
	PageNum           int32    `protobuf:"varint,2,opt,name=page_num,proto3" json:"page_num,omitempty"`
	PxList            []string `protobuf:"bytes,3,rep,name=px_list,proto3" json:"px_list,omitempty"`
	MaterialName      string   `protobuf:"bytes,4,opt,name=material_name,proto3" json:"material_name,omitempty"`
	MaterialTags      []string `protobuf:"bytes,5,rep,name=material_tags,proto3" json:"material_tags,omitempty"`
	UploadStartTime   string   `protobuf:"bytes,6,opt,name=upload_start_time,proto3" json:"upload_start_time,omitempty"`
	UploadEndTime     string   `protobuf:"bytes,7,opt,name=upload_end_time,proto3" json:"upload_end_time,omitempty"`
	CustomerProjectId int32    `protobuf:"varint,8,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
	MaterialType      string   `protobuf:"bytes,9,opt,name=material_type,proto3" json:"material_type,omitempty"`
}

func (x *GetMaterialListRequest) Reset() {
	*x = GetMaterialListRequest{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaterialListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaterialListRequest) ProtoMessage() {}

func (x *GetMaterialListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaterialListRequest.ProtoReflect.Descriptor instead.
func (*GetMaterialListRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{2}
}

func (x *GetMaterialListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetMaterialListRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetMaterialListRequest) GetPxList() []string {
	if x != nil {
		return x.PxList
	}
	return nil
}

func (x *GetMaterialListRequest) GetMaterialName() string {
	if x != nil {
		return x.MaterialName
	}
	return ""
}

func (x *GetMaterialListRequest) GetMaterialTags() []string {
	if x != nil {
		return x.MaterialTags
	}
	return nil
}

func (x *GetMaterialListRequest) GetUploadStartTime() string {
	if x != nil {
		return x.UploadStartTime
	}
	return ""
}

func (x *GetMaterialListRequest) GetUploadEndTime() string {
	if x != nil {
		return x.UploadEndTime
	}
	return ""
}

func (x *GetMaterialListRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *GetMaterialListRequest) GetMaterialType() string {
	if x != nil {
		return x.MaterialType
	}
	return ""
}

type MaterialListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MaterialName     string `protobuf:"bytes,2,opt,name=material_name,proto3" json:"material_name,omitempty"`           //  素材名称
	MaterialPx       string `protobuf:"bytes,3,opt,name=material_px,proto3" json:"material_px,omitempty"`               // 素材尺寸
	MaterialTime     string `protobuf:"bytes,4,opt,name=material_time,proto3" json:"material_time,omitempty"`           //  素材时长
	MaterialCoverUrl string `protobuf:"bytes,5,opt,name=material_cover_url,proto3" json:"material_cover_url,omitempty"` // 素材封面地址
	MaterialUrl      string `protobuf:"bytes,6,opt,name=material_url,proto3" json:"material_url,omitempty"`             // 素材地址
}

func (x *MaterialListItem) Reset() {
	*x = MaterialListItem{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaterialListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialListItem) ProtoMessage() {}

func (x *MaterialListItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialListItem.ProtoReflect.Descriptor instead.
func (*MaterialListItem) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{3}
}

func (x *MaterialListItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MaterialListItem) GetMaterialName() string {
	if x != nil {
		return x.MaterialName
	}
	return ""
}

func (x *MaterialListItem) GetMaterialPx() string {
	if x != nil {
		return x.MaterialPx
	}
	return ""
}

func (x *MaterialListItem) GetMaterialTime() string {
	if x != nil {
		return x.MaterialTime
	}
	return ""
}

func (x *MaterialListItem) GetMaterialCoverUrl() string {
	if x != nil {
		return x.MaterialCoverUrl
	}
	return ""
}

func (x *MaterialListItem) GetMaterialUrl() string {
	if x != nil {
		return x.MaterialUrl
	}
	return ""
}

type MaterialData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination         `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*MaterialListItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *MaterialData) Reset() {
	*x = MaterialData{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaterialData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialData) ProtoMessage() {}

func (x *MaterialData) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialData.ProtoReflect.Descriptor instead.
func (*MaterialData) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{4}
}

func (x *MaterialData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MaterialData) GetList() []*MaterialListItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetMaterialListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32         `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string        `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string        `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *MaterialData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMaterialListResponse) Reset() {
	*x = GetMaterialListResponse{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaterialListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaterialListResponse) ProtoMessage() {}

func (x *GetMaterialListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaterialListResponse.ProtoReflect.Descriptor instead.
func (*GetMaterialListResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{5}
}

func (x *GetMaterialListResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetMaterialListResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetMaterialListResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetMaterialListResponse) GetData() *MaterialData {
	if x != nil {
		return x.Data
	}
	return nil
}

type Material struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MaterialName     string   `protobuf:"bytes,2,opt,name=material_name,proto3" json:"material_name,omitempty"`            // 素材名称
	Language         string   `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`                      // 素材语言
	MaterialTags     []string `protobuf:"bytes,4,rep,name=material_tags,proto3" json:"material_tags,omitempty"`            // 素材标签
	MaterialPx       string   `protobuf:"bytes,5,opt,name=material_px,proto3" json:"material_px,omitempty"`                // 素材尺寸
	Size             string   `protobuf:"bytes,6,opt,name=size,proto3" json:"size,omitempty"`                              //  素材大小
	CreateTime       string   `protobuf:"bytes,7,opt,name=create_time,proto3" json:"create_time,omitempty"`                // 素材上传时间
	CreateUser       string   `protobuf:"bytes,8,opt,name=create_user,proto3" json:"create_user,omitempty"`                // 素材上传人
	MaterialUrl      string   `protobuf:"bytes,9,opt,name=material_url,proto3" json:"material_url,omitempty"`              //  素材地址
	MaterialTime     string   `protobuf:"bytes,10,opt,name=material_time,proto3" json:"material_time,omitempty"`           //  素材时长
	MaterialCoverUrl string   `protobuf:"bytes,11,opt,name=material_cover_url,proto3" json:"material_cover_url,omitempty"` // 素材封面地址
}

func (x *Material) Reset() {
	*x = Material{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Material) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Material) ProtoMessage() {}

func (x *Material) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Material.ProtoReflect.Descriptor instead.
func (*Material) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{6}
}

func (x *Material) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Material) GetMaterialName() string {
	if x != nil {
		return x.MaterialName
	}
	return ""
}

func (x *Material) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *Material) GetMaterialTags() []string {
	if x != nil {
		return x.MaterialTags
	}
	return nil
}

func (x *Material) GetMaterialPx() string {
	if x != nil {
		return x.MaterialPx
	}
	return ""
}

func (x *Material) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *Material) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Material) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *Material) GetMaterialUrl() string {
	if x != nil {
		return x.MaterialUrl
	}
	return ""
}

func (x *Material) GetMaterialTime() string {
	if x != nil {
		return x.MaterialTime
	}
	return ""
}

func (x *Material) GetMaterialCoverUrl() string {
	if x != nil {
		return x.MaterialCoverUrl
	}
	return ""
}

type GetMaterialDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32     `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string    `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string    `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *Material `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMaterialDetailResponse) Reset() {
	*x = GetMaterialDetailResponse{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaterialDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaterialDetailResponse) ProtoMessage() {}

func (x *GetMaterialDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaterialDetailResponse.ProtoReflect.Descriptor instead.
func (*GetMaterialDetailResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{7}
}

func (x *GetMaterialDetailResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetMaterialDetailResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetMaterialDetailResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetMaterialDetailResponse) GetData() *Material {
	if x != nil {
		return x.Data
	}
	return nil
}

type DelMaterialIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids               []int32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	CustomerProjectId int32   `protobuf:"varint,2,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
}

func (x *DelMaterialIdRequest) Reset() {
	*x = DelMaterialIdRequest{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelMaterialIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelMaterialIdRequest) ProtoMessage() {}

func (x *DelMaterialIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelMaterialIdRequest.ProtoReflect.Descriptor instead.
func (*DelMaterialIdRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{8}
}

func (x *DelMaterialIdRequest) GetIds() []int32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DelMaterialIdRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

type UploadMaterialBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaterialUri       []string `protobuf:"bytes,1,rep,name=material_uri,proto3" json:"material_uri,omitempty"`
	MaterialTagsUri   string   `protobuf:"bytes,2,opt,name=material_tags_uri,proto3" json:"material_tags_uri,omitempty"`
	CustomerProjectId int32    `protobuf:"varint,3,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
}

func (x *UploadMaterialBatchRequest) Reset() {
	*x = UploadMaterialBatchRequest{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadMaterialBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMaterialBatchRequest) ProtoMessage() {}

func (x *UploadMaterialBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMaterialBatchRequest.ProtoReflect.Descriptor instead.
func (*UploadMaterialBatchRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{9}
}

func (x *UploadMaterialBatchRequest) GetMaterialUri() []string {
	if x != nil {
		return x.MaterialUri
	}
	return nil
}

func (x *UploadMaterialBatchRequest) GetMaterialTagsUri() string {
	if x != nil {
		return x.MaterialTagsUri
	}
	return ""
}

func (x *UploadMaterialBatchRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

type UploadMaterialBatchCheckItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string   `protobuf:"bytes,1,opt,name=file_name,proto3" json:"file_name,omitempty"`
	Tags     []string `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	Language string   `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	Status   bool     `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	ErrMsg   string   `protobuf:"bytes,5,opt,name=err_msg,proto3" json:"err_msg,omitempty"`
}

func (x *UploadMaterialBatchCheckItem) Reset() {
	*x = UploadMaterialBatchCheckItem{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadMaterialBatchCheckItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMaterialBatchCheckItem) ProtoMessage() {}

func (x *UploadMaterialBatchCheckItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMaterialBatchCheckItem.ProtoReflect.Descriptor instead.
func (*UploadMaterialBatchCheckItem) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{10}
}

func (x *UploadMaterialBatchCheckItem) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadMaterialBatchCheckItem) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *UploadMaterialBatchCheckItem) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UploadMaterialBatchCheckItem) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *UploadMaterialBatchCheckItem) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

type UploadMaterialBatchCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                           `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                          `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                          `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    []*UploadMaterialBatchCheckItem `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *UploadMaterialBatchCheckResponse) Reset() {
	*x = UploadMaterialBatchCheckResponse{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadMaterialBatchCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMaterialBatchCheckResponse) ProtoMessage() {}

func (x *UploadMaterialBatchCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMaterialBatchCheckResponse.ProtoReflect.Descriptor instead.
func (*UploadMaterialBatchCheckResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{11}
}

func (x *UploadMaterialBatchCheckResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *UploadMaterialBatchCheckResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *UploadMaterialBatchCheckResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *UploadMaterialBatchCheckResponse) GetData() []*UploadMaterialBatchCheckItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetMaterialTagsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32    `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string   `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string   `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    []string `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMaterialTagsResponse) Reset() {
	*x = GetMaterialTagsResponse{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaterialTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaterialTagsResponse) ProtoMessage() {}

func (x *GetMaterialTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaterialTagsResponse.ProtoReflect.Descriptor instead.
func (*GetMaterialTagsResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{12}
}

func (x *GetMaterialTagsResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetMaterialTagsResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetMaterialTagsResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetMaterialTagsResponse) GetData() []string {
	if x != nil {
		return x.Data
	}
	return nil
}

type UploadMaterialToMediumRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaterialType      string   `protobuf:"bytes,1,opt,name=material_type,proto3" json:"material_type,omitempty"`                            // 素材类型
	MaterialIds       []int32  `protobuf:"varint,2,rep,packed,name=material_ids,proto3" json:"material_ids,omitempty"`                      // 素材id
	Medium            string   `protobuf:"bytes,3,opt,name=medium,proto3" json:"medium,omitempty"`                                          // 媒体
	AdsAccountList    []string `protobuf:"bytes,4,rep,name=ads_account_list,json=ad_account_list,proto3" json:"ads_account_list,omitempty"` // 广告账户id列表
	CustomerProjectId int32    `protobuf:"varint,5,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
}

func (x *UploadMaterialToMediumRequest) Reset() {
	*x = UploadMaterialToMediumRequest{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadMaterialToMediumRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMaterialToMediumRequest) ProtoMessage() {}

func (x *UploadMaterialToMediumRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMaterialToMediumRequest.ProtoReflect.Descriptor instead.
func (*UploadMaterialToMediumRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{13}
}

func (x *UploadMaterialToMediumRequest) GetMaterialType() string {
	if x != nil {
		return x.MaterialType
	}
	return ""
}

func (x *UploadMaterialToMediumRequest) GetMaterialIds() []int32 {
	if x != nil {
		return x.MaterialIds
	}
	return nil
}

func (x *UploadMaterialToMediumRequest) GetMedium() string {
	if x != nil {
		return x.Medium
	}
	return ""
}

func (x *UploadMaterialToMediumRequest) GetAdsAccountList() []string {
	if x != nil {
		return x.AdsAccountList
	}
	return nil
}

func (x *UploadMaterialToMediumRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

type UploadMaterialToMediumLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize          int32  `protobuf:"varint,1,opt,name=page_size,proto3" json:"page_size,omitempty"`
	PageNum           int32  `protobuf:"varint,2,opt,name=page_num,proto3" json:"page_num,omitempty"`
	CustomerProjectId int32  `protobuf:"varint,3,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
	MaterialType      string `protobuf:"bytes,4,opt,name=material_type,proto3" json:"material_type,omitempty"` // 素材类型
	TaskId            int32  `protobuf:"varint,5,opt,name=task_id,proto3" json:"task_id,omitempty"`            // 任务id
	TaskStatus        string `protobuf:"bytes,6,opt,name=task_status,proto3" json:"task_status,omitempty"`     // 任务状态
}

func (x *UploadMaterialToMediumLogRequest) Reset() {
	*x = UploadMaterialToMediumLogRequest{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadMaterialToMediumLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMaterialToMediumLogRequest) ProtoMessage() {}

func (x *UploadMaterialToMediumLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMaterialToMediumLogRequest.ProtoReflect.Descriptor instead.
func (*UploadMaterialToMediumLogRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{14}
}

func (x *UploadMaterialToMediumLogRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *UploadMaterialToMediumLogRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *UploadMaterialToMediumLogRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *UploadMaterialToMediumLogRequest) GetMaterialType() string {
	if x != nil {
		return x.MaterialType
	}
	return ""
}

func (x *UploadMaterialToMediumLogRequest) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UploadMaterialToMediumLogRequest) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

type UploadMaterialToMediumLogItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId       int32  `protobuf:"varint,1,opt,name=task_id,proto3" json:"task_id,omitempty"`              // 任务id
	TaskStatus   string `protobuf:"bytes,2,opt,name=task_status,proto3" json:"task_status,omitempty"`       // 任务状态
	MaterialName string `protobuf:"bytes,3,opt,name=material_name,proto3" json:"material_name,omitempty"`   // 素材名称
	AdsAccountId string `protobuf:"bytes,4,opt,name=ads_account_id,proto3" json:"ads_account_id,omitempty"` // 广告账户id
	UploadError  string `protobuf:"bytes,5,opt,name=upload_error,proto3" json:"upload_error,omitempty"`     // 错误信息
	Medium       string `protobuf:"bytes,6,opt,name=medium,proto3" json:"medium,omitempty"`                 // 媒体
	UploadTime   string `protobuf:"bytes,7,opt,name=upload_time,proto3" json:"upload_time,omitempty"`       // 上传时间
	MaterialId   int32  `protobuf:"varint,8,opt,name=material_id,proto3" json:"material_id,omitempty"`
}

func (x *UploadMaterialToMediumLogItem) Reset() {
	*x = UploadMaterialToMediumLogItem{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadMaterialToMediumLogItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMaterialToMediumLogItem) ProtoMessage() {}

func (x *UploadMaterialToMediumLogItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMaterialToMediumLogItem.ProtoReflect.Descriptor instead.
func (*UploadMaterialToMediumLogItem) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{15}
}

func (x *UploadMaterialToMediumLogItem) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UploadMaterialToMediumLogItem) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

func (x *UploadMaterialToMediumLogItem) GetMaterialName() string {
	if x != nil {
		return x.MaterialName
	}
	return ""
}

func (x *UploadMaterialToMediumLogItem) GetAdsAccountId() string {
	if x != nil {
		return x.AdsAccountId
	}
	return ""
}

func (x *UploadMaterialToMediumLogItem) GetUploadError() string {
	if x != nil {
		return x.UploadError
	}
	return ""
}

func (x *UploadMaterialToMediumLogItem) GetMedium() string {
	if x != nil {
		return x.Medium
	}
	return ""
}

func (x *UploadMaterialToMediumLogItem) GetUploadTime() string {
	if x != nil {
		return x.UploadTime
	}
	return ""
}

func (x *UploadMaterialToMediumLogItem) GetMaterialId() int32 {
	if x != nil {
		return x.MaterialId
	}
	return 0
}

type UploadMaterialToMediumLogData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination                      `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*UploadMaterialToMediumLogItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *UploadMaterialToMediumLogData) Reset() {
	*x = UploadMaterialToMediumLogData{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadMaterialToMediumLogData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMaterialToMediumLogData) ProtoMessage() {}

func (x *UploadMaterialToMediumLogData) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMaterialToMediumLogData.ProtoReflect.Descriptor instead.
func (*UploadMaterialToMediumLogData) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{16}
}

func (x *UploadMaterialToMediumLogData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *UploadMaterialToMediumLogData) GetList() []*UploadMaterialToMediumLogItem {
	if x != nil {
		return x.List
	}
	return nil
}

type UploadMaterialToMediumLogResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                          `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                         `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                         `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *UploadMaterialToMediumLogData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UploadMaterialToMediumLogResponse) Reset() {
	*x = UploadMaterialToMediumLogResponse{}
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadMaterialToMediumLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadMaterialToMediumLogResponse) ProtoMessage() {}

func (x *UploadMaterialToMediumLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_material_manage_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadMaterialToMediumLogResponse.ProtoReflect.Descriptor instead.
func (*UploadMaterialToMediumLogResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP(), []int{17}
}

func (x *UploadMaterialToMediumLogResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *UploadMaterialToMediumLogResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *UploadMaterialToMediumLogResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *UploadMaterialToMediumLogResponse) GetData() *UploadMaterialToMediumLogData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_api_customer_project_space_v1_material_manage_proto protoreflect.FileDescriptor

var file_api_customer_project_space_v1_material_manage_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x4a, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x22,
	0x53, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x22, 0xe8, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x78, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x78, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x12,
	0x2c, 0x0a, 0x11, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22,
	0xe4, 0x01, 0x0a, 0x10, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x70, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x70, 0x78, 0x12, 0x24, 0x0a, 0x0d,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75,
	0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x96, 0x01, 0x0a, 0x0c, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x9e, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65,
	0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72,
	0x4d, 0x73, 0x67, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xf6, 0x02, 0x0a, 0x08, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a,
	0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x74, 0x61, 0x67, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x70, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x70, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x22, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x75, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x9c, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12,
	0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x5a, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x22, 0xb6, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x75, 0x72, 0x69, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01,
	0x05, 0x08, 0x01, 0x10, 0xc8, 0x01, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x75, 0x72, 0x69, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x74, 0x61, 0x67, 0x73, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x5f, 0x75,
	0x72, 0x69, 0x12, 0x39, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x22, 0x9e, 0x01,
	0x0a, 0x1c, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1c,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x22, 0xb7,
	0x01, 0x0a, 0x20, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x4b, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x75, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xde, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x64,
	0x69, 0x75, 0x6d, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x22, 0xf0, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x4c, 0x6f, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12,
	0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xa9, 0x02, 0x0a, 0x1d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x4c, 0x6f,
	0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x64, 0x73, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12,
	0x22, 0x0a, 0x0c, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x22,
	0xb4, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x4c, 0x6f, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x45, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x4c, 0x6f, 0x67, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb9, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75,
	0x6d, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72,
	0x72, 0x4d, 0x73, 0x67, 0x12, 0x4c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x6f, 0x4d,
	0x65, 0x64, 0x69, 0x75, 0x6d, 0x4c, 0x6f, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x32, 0xa5, 0x0b, 0x0a, 0x15, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa7, 0x01, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x31, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a,
	0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xa3, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2a, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x97, 0x01, 0x0a,
	0x0b, 0x44, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x2f, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x12, 0xa8, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x35,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0xc5, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x35,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x3a, 0x01, 0x2a, 0x22, 0x2a, 0x2f,
	0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0xa4, 0x01, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x61, 0x67, 0x73, 0x12, 0x31, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x32, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x73,
	0x12, 0xb5, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x12, 0x38, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x3a, 0x01, 0x2a, 0x22, 0x2b, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x75,
	0x6d, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0xcf, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64,
	0x69, 0x75, 0x6d, 0x4c, 0x6f, 0x67, 0x12, 0x3b, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x54, 0x6f, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x6f,
	0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x12, 0x2f, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x5f,
	0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x6f, 0x67, 0x42, 0x1f, 0x5a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_customer_project_space_v1_material_manage_proto_rawDescOnce sync.Once
	file_api_customer_project_space_v1_material_manage_proto_rawDescData = file_api_customer_project_space_v1_material_manage_proto_rawDesc
)

func file_api_customer_project_space_v1_material_manage_proto_rawDescGZIP() []byte {
	file_api_customer_project_space_v1_material_manage_proto_rawDescOnce.Do(func() {
		file_api_customer_project_space_v1_material_manage_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_customer_project_space_v1_material_manage_proto_rawDescData)
	})
	return file_api_customer_project_space_v1_material_manage_proto_rawDescData
}

var file_api_customer_project_space_v1_material_manage_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_api_customer_project_space_v1_material_manage_proto_goTypes = []any{
	(*GetMaterialTagsRequest)(nil),            // 0: customer_project_space.v1.GetMaterialTagsRequest
	(*CommonIdRequest)(nil),                   // 1: customer_project_space.v1.CommonIdRequest
	(*GetMaterialListRequest)(nil),            // 2: customer_project_space.v1.GetMaterialListRequest
	(*MaterialListItem)(nil),                  // 3: customer_project_space.v1.MaterialListItem
	(*MaterialData)(nil),                      // 4: customer_project_space.v1.MaterialData
	(*GetMaterialListResponse)(nil),           // 5: customer_project_space.v1.GetMaterialListResponse
	(*Material)(nil),                          // 6: customer_project_space.v1.Material
	(*GetMaterialDetailResponse)(nil),         // 7: customer_project_space.v1.GetMaterialDetailResponse
	(*DelMaterialIdRequest)(nil),              // 8: customer_project_space.v1.DelMaterialIdRequest
	(*UploadMaterialBatchRequest)(nil),        // 9: customer_project_space.v1.UploadMaterialBatchRequest
	(*UploadMaterialBatchCheckItem)(nil),      // 10: customer_project_space.v1.UploadMaterialBatchCheckItem
	(*UploadMaterialBatchCheckResponse)(nil),  // 11: customer_project_space.v1.UploadMaterialBatchCheckResponse
	(*GetMaterialTagsResponse)(nil),           // 12: customer_project_space.v1.GetMaterialTagsResponse
	(*UploadMaterialToMediumRequest)(nil),     // 13: customer_project_space.v1.UploadMaterialToMediumRequest
	(*UploadMaterialToMediumLogRequest)(nil),  // 14: customer_project_space.v1.UploadMaterialToMediumLogRequest
	(*UploadMaterialToMediumLogItem)(nil),     // 15: customer_project_space.v1.UploadMaterialToMediumLogItem
	(*UploadMaterialToMediumLogData)(nil),     // 16: customer_project_space.v1.UploadMaterialToMediumLogData
	(*UploadMaterialToMediumLogResponse)(nil), // 17: customer_project_space.v1.UploadMaterialToMediumLogResponse
	(*Pagination)(nil),                        // 18: customer_project_space.v1.Pagination
	(*CommonResponse)(nil),                    // 19: customer_project_space.v1.CommonResponse
}
var file_api_customer_project_space_v1_material_manage_proto_depIdxs = []int32{
	18, // 0: customer_project_space.v1.MaterialData.pagination:type_name -> customer_project_space.v1.Pagination
	3,  // 1: customer_project_space.v1.MaterialData.list:type_name -> customer_project_space.v1.MaterialListItem
	4,  // 2: customer_project_space.v1.GetMaterialListResponse.data:type_name -> customer_project_space.v1.MaterialData
	6,  // 3: customer_project_space.v1.GetMaterialDetailResponse.data:type_name -> customer_project_space.v1.Material
	10, // 4: customer_project_space.v1.UploadMaterialBatchCheckResponse.data:type_name -> customer_project_space.v1.UploadMaterialBatchCheckItem
	18, // 5: customer_project_space.v1.UploadMaterialToMediumLogData.pagination:type_name -> customer_project_space.v1.Pagination
	15, // 6: customer_project_space.v1.UploadMaterialToMediumLogData.list:type_name -> customer_project_space.v1.UploadMaterialToMediumLogItem
	16, // 7: customer_project_space.v1.UploadMaterialToMediumLogResponse.data:type_name -> customer_project_space.v1.UploadMaterialToMediumLogData
	2,  // 8: customer_project_space.v1.MaterialManageService.GetMaterialList:input_type -> customer_project_space.v1.GetMaterialListRequest
	1,  // 9: customer_project_space.v1.MaterialManageService.GetMaterialDetail:input_type -> customer_project_space.v1.CommonIdRequest
	8,  // 10: customer_project_space.v1.MaterialManageService.DelMaterial:input_type -> customer_project_space.v1.DelMaterialIdRequest
	9,  // 11: customer_project_space.v1.MaterialManageService.UploadMaterialBatch:input_type -> customer_project_space.v1.UploadMaterialBatchRequest
	9,  // 12: customer_project_space.v1.MaterialManageService.UploadMaterialBatchCheck:input_type -> customer_project_space.v1.UploadMaterialBatchRequest
	0,  // 13: customer_project_space.v1.MaterialManageService.GetMaterialTags:input_type -> customer_project_space.v1.GetMaterialTagsRequest
	13, // 14: customer_project_space.v1.MaterialManageService.UploadMaterialToMedium:input_type -> customer_project_space.v1.UploadMaterialToMediumRequest
	14, // 15: customer_project_space.v1.MaterialManageService.UploadMaterialToMediumLog:input_type -> customer_project_space.v1.UploadMaterialToMediumLogRequest
	5,  // 16: customer_project_space.v1.MaterialManageService.GetMaterialList:output_type -> customer_project_space.v1.GetMaterialListResponse
	7,  // 17: customer_project_space.v1.MaterialManageService.GetMaterialDetail:output_type -> customer_project_space.v1.GetMaterialDetailResponse
	19, // 18: customer_project_space.v1.MaterialManageService.DelMaterial:output_type -> customer_project_space.v1.CommonResponse
	19, // 19: customer_project_space.v1.MaterialManageService.UploadMaterialBatch:output_type -> customer_project_space.v1.CommonResponse
	11, // 20: customer_project_space.v1.MaterialManageService.UploadMaterialBatchCheck:output_type -> customer_project_space.v1.UploadMaterialBatchCheckResponse
	12, // 21: customer_project_space.v1.MaterialManageService.GetMaterialTags:output_type -> customer_project_space.v1.GetMaterialTagsResponse
	19, // 22: customer_project_space.v1.MaterialManageService.UploadMaterialToMedium:output_type -> customer_project_space.v1.CommonResponse
	17, // 23: customer_project_space.v1.MaterialManageService.UploadMaterialToMediumLog:output_type -> customer_project_space.v1.UploadMaterialToMediumLogResponse
	16, // [16:24] is the sub-list for method output_type
	8,  // [8:16] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_customer_project_space_v1_material_manage_proto_init() }
func file_api_customer_project_space_v1_material_manage_proto_init() {
	if File_api_customer_project_space_v1_material_manage_proto != nil {
		return
	}
	file_api_customer_project_space_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_customer_project_space_v1_material_manage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_customer_project_space_v1_material_manage_proto_goTypes,
		DependencyIndexes: file_api_customer_project_space_v1_material_manage_proto_depIdxs,
		MessageInfos:      file_api_customer_project_space_v1_material_manage_proto_msgTypes,
	}.Build()
	File_api_customer_project_space_v1_material_manage_proto = out.File
	file_api_customer_project_space_v1_material_manage_proto_rawDesc = nil
	file_api_customer_project_space_v1_material_manage_proto_goTypes = nil
	file_api_customer_project_space_v1_material_manage_proto_depIdxs = nil
}
