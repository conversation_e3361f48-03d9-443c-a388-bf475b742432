// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/customer_project_space/v1/config.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CustomerProConfigService_GetCustomerProConfig_FullMethodName  = "/customer_project_space.v1.CustomerProConfigService/GetCustomerProConfig"
	CustomerProConfigService_SaveCustomerProConfig_FullMethodName = "/customer_project_space.v1.CustomerProConfigService/SaveCustomerProConfig"
)

// CustomerProConfigServiceClient is the client API for CustomerProConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CustomerProConfigServiceClient interface {
	// 获取项目设置
	GetCustomerProConfig(ctx context.Context, in *GetCustomerProConfigRequest, opts ...grpc.CallOption) (*GetCustomerProConfigResponse, error)
	// 新增项目设置
	SaveCustomerProConfig(ctx context.Context, in *AddCustomerProConfigRequest, opts ...grpc.CallOption) (*CommonResponse, error)
}

type customerProConfigServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomerProConfigServiceClient(cc grpc.ClientConnInterface) CustomerProConfigServiceClient {
	return &customerProConfigServiceClient{cc}
}

func (c *customerProConfigServiceClient) GetCustomerProConfig(ctx context.Context, in *GetCustomerProConfigRequest, opts ...grpc.CallOption) (*GetCustomerProConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomerProConfigResponse)
	err := c.cc.Invoke(ctx, CustomerProConfigService_GetCustomerProConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerProConfigServiceClient) SaveCustomerProConfig(ctx context.Context, in *AddCustomerProConfigRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, CustomerProConfigService_SaveCustomerProConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerProConfigServiceServer is the server API for CustomerProConfigService service.
// All implementations must embed UnimplementedCustomerProConfigServiceServer
// for forward compatibility.
type CustomerProConfigServiceServer interface {
	// 获取项目设置
	GetCustomerProConfig(context.Context, *GetCustomerProConfigRequest) (*GetCustomerProConfigResponse, error)
	// 新增项目设置
	SaveCustomerProConfig(context.Context, *AddCustomerProConfigRequest) (*CommonResponse, error)
	mustEmbedUnimplementedCustomerProConfigServiceServer()
}

// UnimplementedCustomerProConfigServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCustomerProConfigServiceServer struct{}

func (UnimplementedCustomerProConfigServiceServer) GetCustomerProConfig(context.Context, *GetCustomerProConfigRequest) (*GetCustomerProConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerProConfig not implemented")
}
func (UnimplementedCustomerProConfigServiceServer) SaveCustomerProConfig(context.Context, *AddCustomerProConfigRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCustomerProConfig not implemented")
}
func (UnimplementedCustomerProConfigServiceServer) mustEmbedUnimplementedCustomerProConfigServiceServer() {
}
func (UnimplementedCustomerProConfigServiceServer) testEmbeddedByValue() {}

// UnsafeCustomerProConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomerProConfigServiceServer will
// result in compilation errors.
type UnsafeCustomerProConfigServiceServer interface {
	mustEmbedUnimplementedCustomerProConfigServiceServer()
}

func RegisterCustomerProConfigServiceServer(s grpc.ServiceRegistrar, srv CustomerProConfigServiceServer) {
	// If the following call pancis, it indicates UnimplementedCustomerProConfigServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CustomerProConfigService_ServiceDesc, srv)
}

func _CustomerProConfigService_GetCustomerProConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerProConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerProConfigServiceServer).GetCustomerProConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerProConfigService_GetCustomerProConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerProConfigServiceServer).GetCustomerProConfig(ctx, req.(*GetCustomerProConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerProConfigService_SaveCustomerProConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCustomerProConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerProConfigServiceServer).SaveCustomerProConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerProConfigService_SaveCustomerProConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerProConfigServiceServer).SaveCustomerProConfig(ctx, req.(*AddCustomerProConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomerProConfigService_ServiceDesc is the grpc.ServiceDesc for CustomerProConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomerProConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "customer_project_space.v1.CustomerProConfigService",
	HandlerType: (*CustomerProConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCustomerProConfig",
			Handler:    _CustomerProConfigService_GetCustomerProConfig_Handler,
		},
		{
			MethodName: "SaveCustomerProConfig",
			Handler:    _CustomerProConfigService_SaveCustomerProConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/customer_project_space/v1/config.proto",
}
