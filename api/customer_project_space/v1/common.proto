syntax = "proto3";

package customer_project_space.v1;

option go_package = "api/customer_project_space/v1";

// ============== 服务通用message ===========================

// 空请求结构
message Empty {}

// 通用返回
message CommonResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
}

// 通用用户信息
message CommonUserInfo {
  int32 user_id = 1 [json_name = "user_id"];
  string user_name = 2 [json_name = "user_name"];
  string user_email = 3 [json_name = "user_email"];
}

// 分页参数
message Pagination {
  int32 total = 1;
  int32 page_size = 2 [json_name = "page_size"];
  int32 page_num = 3 [json_name = "page_num"];
}

// ================ 服务通用message ======================

// ================ 业务通用message ======================
// 签约信息
message SignInfo {
  string ae_email = 1 [json_name = "ae_email"];
  int32 ae_id = 2 [json_name = "ae_id"];
  string ae_name = 3 [json_name = "ae_name"];
  int32 company_id = 4 [json_name = "company_id"];
  string our_side_entity = 5 [json_name = "our_side_entity"];
  string sale_email = 6 [json_name = "sale_email"];
  int32 sale_id = 7 [json_name = "sale_id"];
  string sale_name = 8 [json_name = "sale_name"];
  string settlement = 9;
  int32 settlement_id = 10 [json_name = "settlement_id"];
  int32 sign_id = 11 [json_name = "sign_id"];
  string sign_sale_email = 12 [json_name = "sign_sale_email"];
  int32 sign_sale_id = 13 [json_name = "sign_sale_id"];
  string sign_sale_name = 14 [json_name = "sign_sale_name"];
  string our_side_entity_name = 15 [json_name = "our_side_entity_name"];
  string company_name = 16 [json_name = "company_name"];
}

// Impact Campaign
message ImpactCampaign{
  string CampaignID = 1 [json_name = "campaign_id"];
  string CampaignName = 2 [json_name = "campaign_name"];
}
// ================ 业务通用message ======================