syntax = "proto3";

package customer_project_space.v1;

option go_package = "api/customer_project_space/v1";

import "google/api/annotations.proto";
import "api/customer_project_space/v1/common.proto";

// ============== 服务定义START ========================

service CustomerProConfigService {
  // 获取项目设置
  rpc GetCustomerProConfig(GetCustomerProConfigRequest) returns (GetCustomerProConfigResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/config_list"
      body: "*"
    };
  }
  // 新增项目设置
  rpc SaveCustomerProConfig(AddCustomerProConfigRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/save_config"
      body: "*"
    };
  }
}
// ============== 服务定义END ===========================

message GetCustomerProConfigRequest{
  int32 customer_project_id = 1 [json_name = "customer_project_id"];  // 项目id
  int32 config_key = 2 [json_name = "config_key"];  // 项目设置类型
  string config_status = 3 [json_name = "config_status"];  // 项目设置状态
}

message CustomerProConfigData{
  int32 id = 1;
  string config_key = 2 [json_name = "config_key"];  // 项目设置类型
  string config_status = 3 [json_name = "config_status"];  // 项目设置状态
  string config_display_name = 4 [json_name = "config_display_name"];  // 项目设置名称
  string config_value = 5 [json_name = "config_value"];  // 项目设置值
}

message GetCustomerProConfigResponse{
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  repeated CustomerProConfigData data = 4;
}

message AddCustomerProConfigRequest{
  int32 customer_project_id = 1 [json_name = "customer_project_id"];  // 项目id
  string config_key = 2 [json_name = "config_key"];  // 项目设置类型
  string config_value = 3 [json_name = "config_value"];  // 项目设置值
}