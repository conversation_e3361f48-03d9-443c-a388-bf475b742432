// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/customer_project_space/v1/ads_account.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AdsAccountManageService_GetAdsAccountList_FullMethodName              = "/customer_project_space.v1.AdsAccountManageService/GetAdsAccountList"
	AdsAccountManageService_AddAdsAccount_FullMethodName                  = "/customer_project_space.v1.AdsAccountManageService/AddAdsAccount"
	AdsAccountManageService_EnOrDisableAdsAccount_FullMethodName          = "/customer_project_space.v1.AdsAccountManageService/EnOrDisableAdsAccount"
	AdsAccountManageService_PreviewAdsAccount_FullMethodName              = "/customer_project_space.v1.AdsAccountManageService/PreviewAdsAccount"
	AdsAccountManageService_RemoveAdsAccount_FullMethodName               = "/customer_project_space.v1.AdsAccountManageService/RemoveAdsAccount"
	AdsAccountManageService_GetAccountByAuth_FullMethodName               = "/customer_project_space.v1.AdsAccountManageService/GetAccountByAuth"
	AdsAccountManageService_OpenAPIGetEnableAdsAccountList_FullMethodName = "/customer_project_space.v1.AdsAccountManageService/OpenAPIGetEnableAdsAccountList"
)

// AdsAccountManageServiceClient is the client API for AdsAccountManageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdsAccountManageServiceClient interface {
	// 获取项目已关联的账号列表
	GetAdsAccountList(ctx context.Context, in *GetAdsAccountListRequest, opts ...grpc.CallOption) (*GetAdsAccountListResponse, error)
	// 新增关联账号
	AddAdsAccount(ctx context.Context, in *AddAdsAccountRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 启用禁用
	EnOrDisableAdsAccount(ctx context.Context, in *EnOrDisableAdsAccountRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 预览广告账户的签约信息
	PreviewAdsAccount(ctx context.Context, in *PreviewAdsAccountRequest, opts ...grpc.CallOption) (*PreviewAdsAccountResponse, error)
	// 移除账号
	RemoveAdsAccount(ctx context.Context, in *RemoveAdsAccountRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 获取授权下的账号
	GetAccountByAuth(ctx context.Context, in *GetAccountByAuthRequest, opts ...grpc.CallOption) (*GetAccountByAuthResponse, error)
	// todo 切换成open api 获取所有启用的广告账号
	OpenAPIGetEnableAdsAccountList(ctx context.Context, in *OpenApiGetAdsAccountListRequest, opts ...grpc.CallOption) (*OpenApiGetAdsAccountListResponse, error)
}

type adsAccountManageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAdsAccountManageServiceClient(cc grpc.ClientConnInterface) AdsAccountManageServiceClient {
	return &adsAccountManageServiceClient{cc}
}

func (c *adsAccountManageServiceClient) GetAdsAccountList(ctx context.Context, in *GetAdsAccountListRequest, opts ...grpc.CallOption) (*GetAdsAccountListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAdsAccountListResponse)
	err := c.cc.Invoke(ctx, AdsAccountManageService_GetAdsAccountList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adsAccountManageServiceClient) AddAdsAccount(ctx context.Context, in *AddAdsAccountRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AdsAccountManageService_AddAdsAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adsAccountManageServiceClient) EnOrDisableAdsAccount(ctx context.Context, in *EnOrDisableAdsAccountRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AdsAccountManageService_EnOrDisableAdsAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adsAccountManageServiceClient) PreviewAdsAccount(ctx context.Context, in *PreviewAdsAccountRequest, opts ...grpc.CallOption) (*PreviewAdsAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PreviewAdsAccountResponse)
	err := c.cc.Invoke(ctx, AdsAccountManageService_PreviewAdsAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adsAccountManageServiceClient) RemoveAdsAccount(ctx context.Context, in *RemoveAdsAccountRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AdsAccountManageService_RemoveAdsAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adsAccountManageServiceClient) GetAccountByAuth(ctx context.Context, in *GetAccountByAuthRequest, opts ...grpc.CallOption) (*GetAccountByAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountByAuthResponse)
	err := c.cc.Invoke(ctx, AdsAccountManageService_GetAccountByAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adsAccountManageServiceClient) OpenAPIGetEnableAdsAccountList(ctx context.Context, in *OpenApiGetAdsAccountListRequest, opts ...grpc.CallOption) (*OpenApiGetAdsAccountListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OpenApiGetAdsAccountListResponse)
	err := c.cc.Invoke(ctx, AdsAccountManageService_OpenAPIGetEnableAdsAccountList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdsAccountManageServiceServer is the server API for AdsAccountManageService service.
// All implementations must embed UnimplementedAdsAccountManageServiceServer
// for forward compatibility.
type AdsAccountManageServiceServer interface {
	// 获取项目已关联的账号列表
	GetAdsAccountList(context.Context, *GetAdsAccountListRequest) (*GetAdsAccountListResponse, error)
	// 新增关联账号
	AddAdsAccount(context.Context, *AddAdsAccountRequest) (*CommonResponse, error)
	// 启用禁用
	EnOrDisableAdsAccount(context.Context, *EnOrDisableAdsAccountRequest) (*CommonResponse, error)
	// 预览广告账户的签约信息
	PreviewAdsAccount(context.Context, *PreviewAdsAccountRequest) (*PreviewAdsAccountResponse, error)
	// 移除账号
	RemoveAdsAccount(context.Context, *RemoveAdsAccountRequest) (*CommonResponse, error)
	// 获取授权下的账号
	GetAccountByAuth(context.Context, *GetAccountByAuthRequest) (*GetAccountByAuthResponse, error)
	// todo 切换成open api 获取所有启用的广告账号
	OpenAPIGetEnableAdsAccountList(context.Context, *OpenApiGetAdsAccountListRequest) (*OpenApiGetAdsAccountListResponse, error)
	mustEmbedUnimplementedAdsAccountManageServiceServer()
}

// UnimplementedAdsAccountManageServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAdsAccountManageServiceServer struct{}

func (UnimplementedAdsAccountManageServiceServer) GetAdsAccountList(context.Context, *GetAdsAccountListRequest) (*GetAdsAccountListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAdsAccountList not implemented")
}
func (UnimplementedAdsAccountManageServiceServer) AddAdsAccount(context.Context, *AddAdsAccountRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAdsAccount not implemented")
}
func (UnimplementedAdsAccountManageServiceServer) EnOrDisableAdsAccount(context.Context, *EnOrDisableAdsAccountRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnOrDisableAdsAccount not implemented")
}
func (UnimplementedAdsAccountManageServiceServer) PreviewAdsAccount(context.Context, *PreviewAdsAccountRequest) (*PreviewAdsAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewAdsAccount not implemented")
}
func (UnimplementedAdsAccountManageServiceServer) RemoveAdsAccount(context.Context, *RemoveAdsAccountRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAdsAccount not implemented")
}
func (UnimplementedAdsAccountManageServiceServer) GetAccountByAuth(context.Context, *GetAccountByAuthRequest) (*GetAccountByAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountByAuth not implemented")
}
func (UnimplementedAdsAccountManageServiceServer) OpenAPIGetEnableAdsAccountList(context.Context, *OpenApiGetAdsAccountListRequest) (*OpenApiGetAdsAccountListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OpenAPIGetEnableAdsAccountList not implemented")
}
func (UnimplementedAdsAccountManageServiceServer) mustEmbedUnimplementedAdsAccountManageServiceServer() {
}
func (UnimplementedAdsAccountManageServiceServer) testEmbeddedByValue() {}

// UnsafeAdsAccountManageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdsAccountManageServiceServer will
// result in compilation errors.
type UnsafeAdsAccountManageServiceServer interface {
	mustEmbedUnimplementedAdsAccountManageServiceServer()
}

func RegisterAdsAccountManageServiceServer(s grpc.ServiceRegistrar, srv AdsAccountManageServiceServer) {
	// If the following call pancis, it indicates UnimplementedAdsAccountManageServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AdsAccountManageService_ServiceDesc, srv)
}

func _AdsAccountManageService_GetAdsAccountList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdsAccountListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdsAccountManageServiceServer).GetAdsAccountList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdsAccountManageService_GetAdsAccountList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdsAccountManageServiceServer).GetAdsAccountList(ctx, req.(*GetAdsAccountListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdsAccountManageService_AddAdsAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAdsAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdsAccountManageServiceServer).AddAdsAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdsAccountManageService_AddAdsAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdsAccountManageServiceServer).AddAdsAccount(ctx, req.(*AddAdsAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdsAccountManageService_EnOrDisableAdsAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnOrDisableAdsAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdsAccountManageServiceServer).EnOrDisableAdsAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdsAccountManageService_EnOrDisableAdsAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdsAccountManageServiceServer).EnOrDisableAdsAccount(ctx, req.(*EnOrDisableAdsAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdsAccountManageService_PreviewAdsAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewAdsAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdsAccountManageServiceServer).PreviewAdsAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdsAccountManageService_PreviewAdsAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdsAccountManageServiceServer).PreviewAdsAccount(ctx, req.(*PreviewAdsAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdsAccountManageService_RemoveAdsAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveAdsAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdsAccountManageServiceServer).RemoveAdsAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdsAccountManageService_RemoveAdsAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdsAccountManageServiceServer).RemoveAdsAccount(ctx, req.(*RemoveAdsAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdsAccountManageService_GetAccountByAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountByAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdsAccountManageServiceServer).GetAccountByAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdsAccountManageService_GetAccountByAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdsAccountManageServiceServer).GetAccountByAuth(ctx, req.(*GetAccountByAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdsAccountManageService_OpenAPIGetEnableAdsAccountList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenApiGetAdsAccountListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdsAccountManageServiceServer).OpenAPIGetEnableAdsAccountList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdsAccountManageService_OpenAPIGetEnableAdsAccountList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdsAccountManageServiceServer).OpenAPIGetEnableAdsAccountList(ctx, req.(*OpenApiGetAdsAccountListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AdsAccountManageService_ServiceDesc is the grpc.ServiceDesc for AdsAccountManageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdsAccountManageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "customer_project_space.v1.AdsAccountManageService",
	HandlerType: (*AdsAccountManageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAdsAccountList",
			Handler:    _AdsAccountManageService_GetAdsAccountList_Handler,
		},
		{
			MethodName: "AddAdsAccount",
			Handler:    _AdsAccountManageService_AddAdsAccount_Handler,
		},
		{
			MethodName: "EnOrDisableAdsAccount",
			Handler:    _AdsAccountManageService_EnOrDisableAdsAccount_Handler,
		},
		{
			MethodName: "PreviewAdsAccount",
			Handler:    _AdsAccountManageService_PreviewAdsAccount_Handler,
		},
		{
			MethodName: "RemoveAdsAccount",
			Handler:    _AdsAccountManageService_RemoveAdsAccount_Handler,
		},
		{
			MethodName: "GetAccountByAuth",
			Handler:    _AdsAccountManageService_GetAccountByAuth_Handler,
		},
		{
			MethodName: "OpenAPIGetEnableAdsAccountList",
			Handler:    _AdsAccountManageService_OpenAPIGetEnableAdsAccountList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/customer_project_space/v1/ads_account.proto",
}
