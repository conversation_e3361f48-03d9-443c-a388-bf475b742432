// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/customer_project_space/v1/ads_account.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AddAdsAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddAdsAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddAdsAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddAdsAccountRequestMultiError, or nil if none found.
func (m *AddAdsAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddAdsAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	// no validation rules for Platform

	if len(errors) > 0 {
		return AddAdsAccountRequestMultiError(errors)
	}

	return nil
}

// AddAdsAccountRequestMultiError is an error wrapping multiple validation
// errors returned by AddAdsAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type AddAdsAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddAdsAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddAdsAccountRequestMultiError) AllErrors() []error { return m }

// AddAdsAccountRequestValidationError is the validation error returned by
// AddAdsAccountRequest.Validate if the designated constraints aren't met.
type AddAdsAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddAdsAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddAdsAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddAdsAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddAdsAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddAdsAccountRequestValidationError) ErrorName() string {
	return "AddAdsAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddAdsAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddAdsAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddAdsAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddAdsAccountRequestValidationError{}

// Validate checks the field values on GetAdsAccountListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAdsAccountListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAdsAccountListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAdsAccountListRequestMultiError, or nil if none found.
func (m *GetAdsAccountListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAdsAccountListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	// no validation rules for AdsAccountId

	// no validation rules for Status

	// no validation rules for PageSize

	// no validation rules for PageNum

	if len(errors) > 0 {
		return GetAdsAccountListRequestMultiError(errors)
	}

	return nil
}

// GetAdsAccountListRequestMultiError is an error wrapping multiple validation
// errors returned by GetAdsAccountListRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAdsAccountListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAdsAccountListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAdsAccountListRequestMultiError) AllErrors() []error { return m }

// GetAdsAccountListRequestValidationError is the validation error returned by
// GetAdsAccountListRequest.Validate if the designated constraints aren't met.
type GetAdsAccountListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAdsAccountListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAdsAccountListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAdsAccountListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAdsAccountListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAdsAccountListRequestValidationError) ErrorName() string {
	return "GetAdsAccountListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAdsAccountListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAdsAccountListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAdsAccountListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAdsAccountListRequestValidationError{}

// Validate checks the field values on AdsAccountListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdsAccountListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdsAccountListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdsAccountListItemMultiError, or nil if none found.
func (m *AdsAccountListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *AdsAccountListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Medium

	// no validation rules for AdsAccountId

	// no validation rules for Status

	if m.SignInfo != nil {

		if all {
			switch v := interface{}(m.GetSignInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdsAccountListItemValidationError{
						field:  "SignInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdsAccountListItemValidationError{
						field:  "SignInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSignInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdsAccountListItemValidationError{
					field:  "SignInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AdsAccountListItemMultiError(errors)
	}

	return nil
}

// AdsAccountListItemMultiError is an error wrapping multiple validation errors
// returned by AdsAccountListItem.ValidateAll() if the designated constraints
// aren't met.
type AdsAccountListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdsAccountListItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdsAccountListItemMultiError) AllErrors() []error { return m }

// AdsAccountListItemValidationError is the validation error returned by
// AdsAccountListItem.Validate if the designated constraints aren't met.
type AdsAccountListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdsAccountListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdsAccountListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdsAccountListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdsAccountListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdsAccountListItemValidationError) ErrorName() string {
	return "AdsAccountListItemValidationError"
}

// Error satisfies the builtin error interface
func (e AdsAccountListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdsAccountListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdsAccountListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdsAccountListItemValidationError{}

// Validate checks the field values on AdsAccountData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AdsAccountData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdsAccountData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AdsAccountDataMultiError,
// or nil if none found.
func (m *AdsAccountData) ValidateAll() error {
	return m.validate(true)
}

func (m *AdsAccountData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AdsAccountDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AdsAccountDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AdsAccountDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdsAccountDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdsAccountDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdsAccountDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AdsAccountDataMultiError(errors)
	}

	return nil
}

// AdsAccountDataMultiError is an error wrapping multiple validation errors
// returned by AdsAccountData.ValidateAll() if the designated constraints
// aren't met.
type AdsAccountDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdsAccountDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdsAccountDataMultiError) AllErrors() []error { return m }

// AdsAccountDataValidationError is the validation error returned by
// AdsAccountData.Validate if the designated constraints aren't met.
type AdsAccountDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdsAccountDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdsAccountDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdsAccountDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdsAccountDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdsAccountDataValidationError) ErrorName() string { return "AdsAccountDataValidationError" }

// Error satisfies the builtin error interface
func (e AdsAccountDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdsAccountData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdsAccountDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdsAccountDataValidationError{}

// Validate checks the field values on GetAdsAccountListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAdsAccountListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAdsAccountListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAdsAccountListResponseMultiError, or nil if none found.
func (m *GetAdsAccountListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAdsAccountListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAdsAccountListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAdsAccountListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAdsAccountListResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAdsAccountListResponseMultiError(errors)
	}

	return nil
}

// GetAdsAccountListResponseMultiError is an error wrapping multiple validation
// errors returned by GetAdsAccountListResponse.ValidateAll() if the
// designated constraints aren't met.
type GetAdsAccountListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAdsAccountListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAdsAccountListResponseMultiError) AllErrors() []error { return m }

// GetAdsAccountListResponseValidationError is the validation error returned by
// GetAdsAccountListResponse.Validate if the designated constraints aren't met.
type GetAdsAccountListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAdsAccountListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAdsAccountListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAdsAccountListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAdsAccountListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAdsAccountListResponseValidationError) ErrorName() string {
	return "GetAdsAccountListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAdsAccountListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAdsAccountListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAdsAccountListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAdsAccountListResponseValidationError{}

// Validate checks the field values on EnOrDisableAdsAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnOrDisableAdsAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnOrDisableAdsAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnOrDisableAdsAccountRequestMultiError, or nil if none found.
func (m *EnOrDisableAdsAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnOrDisableAdsAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	// no validation rules for Status

	if len(errors) > 0 {
		return EnOrDisableAdsAccountRequestMultiError(errors)
	}

	return nil
}

// EnOrDisableAdsAccountRequestMultiError is an error wrapping multiple
// validation errors returned by EnOrDisableAdsAccountRequest.ValidateAll() if
// the designated constraints aren't met.
type EnOrDisableAdsAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnOrDisableAdsAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnOrDisableAdsAccountRequestMultiError) AllErrors() []error { return m }

// EnOrDisableAdsAccountRequestValidationError is the validation error returned
// by EnOrDisableAdsAccountRequest.Validate if the designated constraints
// aren't met.
type EnOrDisableAdsAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnOrDisableAdsAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnOrDisableAdsAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnOrDisableAdsAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnOrDisableAdsAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnOrDisableAdsAccountRequestValidationError) ErrorName() string {
	return "EnOrDisableAdsAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnOrDisableAdsAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnOrDisableAdsAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnOrDisableAdsAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnOrDisableAdsAccountRequestValidationError{}

// Validate checks the field values on PreviewAdsAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PreviewAdsAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreviewAdsAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreviewAdsAccountRequestMultiError, or nil if none found.
func (m *PreviewAdsAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PreviewAdsAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PreviewAdsAccountRequestMultiError(errors)
	}

	return nil
}

// PreviewAdsAccountRequestMultiError is an error wrapping multiple validation
// errors returned by PreviewAdsAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type PreviewAdsAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreviewAdsAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreviewAdsAccountRequestMultiError) AllErrors() []error { return m }

// PreviewAdsAccountRequestValidationError is the validation error returned by
// PreviewAdsAccountRequest.Validate if the designated constraints aren't met.
type PreviewAdsAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreviewAdsAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreviewAdsAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreviewAdsAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreviewAdsAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreviewAdsAccountRequestValidationError) ErrorName() string {
	return "PreviewAdsAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PreviewAdsAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreviewAdsAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreviewAdsAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreviewAdsAccountRequestValidationError{}

// Validate checks the field values on AdsAccountSignData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdsAccountSignData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdsAccountSignData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdsAccountSignDataMultiError, or nil if none found.
func (m *AdsAccountSignData) ValidateAll() error {
	return m.validate(true)
}

func (m *AdsAccountSignData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdsAccountSignDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdsAccountSignDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdsAccountSignDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AdsAccountSignDataMultiError(errors)
	}

	return nil
}

// AdsAccountSignDataMultiError is an error wrapping multiple validation errors
// returned by AdsAccountSignData.ValidateAll() if the designated constraints
// aren't met.
type AdsAccountSignDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdsAccountSignDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdsAccountSignDataMultiError) AllErrors() []error { return m }

// AdsAccountSignDataValidationError is the validation error returned by
// AdsAccountSignData.Validate if the designated constraints aren't met.
type AdsAccountSignDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdsAccountSignDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdsAccountSignDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdsAccountSignDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdsAccountSignDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdsAccountSignDataValidationError) ErrorName() string {
	return "AdsAccountSignDataValidationError"
}

// Error satisfies the builtin error interface
func (e AdsAccountSignDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdsAccountSignData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdsAccountSignDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdsAccountSignDataValidationError{}

// Validate checks the field values on PreviewAdsAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PreviewAdsAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreviewAdsAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreviewAdsAccountResponseMultiError, or nil if none found.
func (m *PreviewAdsAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PreviewAdsAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreviewAdsAccountResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreviewAdsAccountResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreviewAdsAccountResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PreviewAdsAccountResponseMultiError(errors)
	}

	return nil
}

// PreviewAdsAccountResponseMultiError is an error wrapping multiple validation
// errors returned by PreviewAdsAccountResponse.ValidateAll() if the
// designated constraints aren't met.
type PreviewAdsAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreviewAdsAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreviewAdsAccountResponseMultiError) AllErrors() []error { return m }

// PreviewAdsAccountResponseValidationError is the validation error returned by
// PreviewAdsAccountResponse.Validate if the designated constraints aren't met.
type PreviewAdsAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreviewAdsAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreviewAdsAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreviewAdsAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreviewAdsAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreviewAdsAccountResponseValidationError) ErrorName() string {
	return "PreviewAdsAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PreviewAdsAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreviewAdsAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreviewAdsAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreviewAdsAccountResponseValidationError{}

// Validate checks the field values on OpenApiGetAdsAccountListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OpenApiGetAdsAccountListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OpenApiGetAdsAccountListRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// OpenApiGetAdsAccountListRequestMultiError, or nil if none found.
func (m *OpenApiGetAdsAccountListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OpenApiGetAdsAccountListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectNumber

	if len(errors) > 0 {
		return OpenApiGetAdsAccountListRequestMultiError(errors)
	}

	return nil
}

// OpenApiGetAdsAccountListRequestMultiError is an error wrapping multiple
// validation errors returned by OpenApiGetAdsAccountListRequest.ValidateAll()
// if the designated constraints aren't met.
type OpenApiGetAdsAccountListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OpenApiGetAdsAccountListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OpenApiGetAdsAccountListRequestMultiError) AllErrors() []error { return m }

// OpenApiGetAdsAccountListRequestValidationError is the validation error
// returned by OpenApiGetAdsAccountListRequest.Validate if the designated
// constraints aren't met.
type OpenApiGetAdsAccountListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OpenApiGetAdsAccountListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OpenApiGetAdsAccountListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OpenApiGetAdsAccountListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OpenApiGetAdsAccountListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OpenApiGetAdsAccountListRequestValidationError) ErrorName() string {
	return "OpenApiGetAdsAccountListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OpenApiGetAdsAccountListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOpenApiGetAdsAccountListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OpenApiGetAdsAccountListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OpenApiGetAdsAccountListRequestValidationError{}

// Validate checks the field values on OpenApiGetAdsAccountListData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OpenApiGetAdsAccountListData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OpenApiGetAdsAccountListData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OpenApiGetAdsAccountListDataMultiError, or nil if none found.
func (m *OpenApiGetAdsAccountListData) ValidateAll() error {
	return m.validate(true)
}

func (m *OpenApiGetAdsAccountListData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Medium

	if len(errors) > 0 {
		return OpenApiGetAdsAccountListDataMultiError(errors)
	}

	return nil
}

// OpenApiGetAdsAccountListDataMultiError is an error wrapping multiple
// validation errors returned by OpenApiGetAdsAccountListData.ValidateAll() if
// the designated constraints aren't met.
type OpenApiGetAdsAccountListDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OpenApiGetAdsAccountListDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OpenApiGetAdsAccountListDataMultiError) AllErrors() []error { return m }

// OpenApiGetAdsAccountListDataValidationError is the validation error returned
// by OpenApiGetAdsAccountListData.Validate if the designated constraints
// aren't met.
type OpenApiGetAdsAccountListDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OpenApiGetAdsAccountListDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OpenApiGetAdsAccountListDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OpenApiGetAdsAccountListDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OpenApiGetAdsAccountListDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OpenApiGetAdsAccountListDataValidationError) ErrorName() string {
	return "OpenApiGetAdsAccountListDataValidationError"
}

// Error satisfies the builtin error interface
func (e OpenApiGetAdsAccountListDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOpenApiGetAdsAccountListData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OpenApiGetAdsAccountListDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OpenApiGetAdsAccountListDataValidationError{}

// Validate checks the field values on OpenApiGetAdsAccountListResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *OpenApiGetAdsAccountListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OpenApiGetAdsAccountListResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// OpenApiGetAdsAccountListResponseMultiError, or nil if none found.
func (m *OpenApiGetAdsAccountListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OpenApiGetAdsAccountListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OpenApiGetAdsAccountListResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OpenApiGetAdsAccountListResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OpenApiGetAdsAccountListResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OpenApiGetAdsAccountListResponseMultiError(errors)
	}

	return nil
}

// OpenApiGetAdsAccountListResponseMultiError is an error wrapping multiple
// validation errors returned by
// OpenApiGetAdsAccountListResponse.ValidateAll() if the designated
// constraints aren't met.
type OpenApiGetAdsAccountListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OpenApiGetAdsAccountListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OpenApiGetAdsAccountListResponseMultiError) AllErrors() []error { return m }

// OpenApiGetAdsAccountListResponseValidationError is the validation error
// returned by OpenApiGetAdsAccountListResponse.Validate if the designated
// constraints aren't met.
type OpenApiGetAdsAccountListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OpenApiGetAdsAccountListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OpenApiGetAdsAccountListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OpenApiGetAdsAccountListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OpenApiGetAdsAccountListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OpenApiGetAdsAccountListResponseValidationError) ErrorName() string {
	return "OpenApiGetAdsAccountListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OpenApiGetAdsAccountListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOpenApiGetAdsAccountListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OpenApiGetAdsAccountListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OpenApiGetAdsAccountListResponseValidationError{}

// Validate checks the field values on AccountByAuthData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountByAuthData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountByAuthData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountByAuthDataMultiError, or nil if none found.
func (m *AccountByAuthData) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountByAuthData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for AccountName

	if len(errors) > 0 {
		return AccountByAuthDataMultiError(errors)
	}

	return nil
}

// AccountByAuthDataMultiError is an error wrapping multiple validation errors
// returned by AccountByAuthData.ValidateAll() if the designated constraints
// aren't met.
type AccountByAuthDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountByAuthDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountByAuthDataMultiError) AllErrors() []error { return m }

// AccountByAuthDataValidationError is the validation error returned by
// AccountByAuthData.Validate if the designated constraints aren't met.
type AccountByAuthDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountByAuthDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountByAuthDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountByAuthDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountByAuthDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountByAuthDataValidationError) ErrorName() string {
	return "AccountByAuthDataValidationError"
}

// Error satisfies the builtin error interface
func (e AccountByAuthDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountByAuthData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountByAuthDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountByAuthDataValidationError{}

// Validate checks the field values on GetAccountByAuthRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountByAuthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountByAuthRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountByAuthRequestMultiError, or nil if none found.
func (m *GetAccountByAuthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountByAuthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	// no validation rules for ConfigKey

	// no validation rules for ConfigValue

	if len(errors) > 0 {
		return GetAccountByAuthRequestMultiError(errors)
	}

	return nil
}

// GetAccountByAuthRequestMultiError is an error wrapping multiple validation
// errors returned by GetAccountByAuthRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccountByAuthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountByAuthRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountByAuthRequestMultiError) AllErrors() []error { return m }

// GetAccountByAuthRequestValidationError is the validation error returned by
// GetAccountByAuthRequest.Validate if the designated constraints aren't met.
type GetAccountByAuthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountByAuthRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountByAuthRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountByAuthRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountByAuthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountByAuthRequestValidationError) ErrorName() string {
	return "GetAccountByAuthRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountByAuthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountByAuthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountByAuthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountByAuthRequestValidationError{}

// Validate checks the field values on GetAccountByAuthResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountByAuthResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountByAuthResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountByAuthResponseMultiError, or nil if none found.
func (m *GetAccountByAuthResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountByAuthResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountByAuthResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountByAuthResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountByAuthResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAccountByAuthResponseMultiError(errors)
	}

	return nil
}

// GetAccountByAuthResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccountByAuthResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAccountByAuthResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountByAuthResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountByAuthResponseMultiError) AllErrors() []error { return m }

// GetAccountByAuthResponseValidationError is the validation error returned by
// GetAccountByAuthResponse.Validate if the designated constraints aren't met.
type GetAccountByAuthResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountByAuthResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountByAuthResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountByAuthResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountByAuthResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountByAuthResponseValidationError) ErrorName() string {
	return "GetAccountByAuthResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountByAuthResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountByAuthResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountByAuthResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountByAuthResponseValidationError{}

// Validate checks the field values on RemoveAdsAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveAdsAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveAdsAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveAdsAccountRequestMultiError, or nil if none found.
func (m *RemoveAdsAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveAdsAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	// no validation rules for Platform

	if len(errors) > 0 {
		return RemoveAdsAccountRequestMultiError(errors)
	}

	return nil
}

// RemoveAdsAccountRequestMultiError is an error wrapping multiple validation
// errors returned by RemoveAdsAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type RemoveAdsAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveAdsAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveAdsAccountRequestMultiError) AllErrors() []error { return m }

// RemoveAdsAccountRequestValidationError is the validation error returned by
// RemoveAdsAccountRequest.Validate if the designated constraints aren't met.
type RemoveAdsAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveAdsAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveAdsAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveAdsAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveAdsAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveAdsAccountRequestValidationError) ErrorName() string {
	return "RemoveAdsAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveAdsAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveAdsAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveAdsAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveAdsAccountRequestValidationError{}

// Validate checks the field values on GetCustomerAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerAccountRequestMultiError, or nil if none found.
func (m *GetCustomerAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	// no validation rules for AccountId

	// no validation rules for Status

	// no validation rules for PageSize

	// no validation rules for PageNum

	// no validation rules for Platform

	if len(errors) > 0 {
		return GetCustomerAccountRequestMultiError(errors)
	}

	return nil
}

// GetCustomerAccountRequestMultiError is an error wrapping multiple validation
// errors returned by GetCustomerAccountRequest.ValidateAll() if the
// designated constraints aren't met.
type GetCustomerAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerAccountRequestMultiError) AllErrors() []error { return m }

// GetCustomerAccountRequestValidationError is the validation error returned by
// GetCustomerAccountRequest.Validate if the designated constraints aren't met.
type GetCustomerAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerAccountRequestValidationError) ErrorName() string {
	return "GetCustomerAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerAccountRequestValidationError{}

// Validate checks the field values on AccountListItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountListItemMultiError, or nil if none found.
func (m *AccountListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Medium

	// no validation rules for AccountId

	// no validation rules for AccountName

	// no validation rules for Status

	if m.AccountExtraInfo != nil {

		if all {
			switch v := interface{}(m.GetAccountExtraInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountListItemValidationError{
						field:  "AccountExtraInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountListItemValidationError{
						field:  "AccountExtraInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAccountExtraInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountListItemValidationError{
					field:  "AccountExtraInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountListItemMultiError(errors)
	}

	return nil
}

// AccountListItemMultiError is an error wrapping multiple validation errors
// returned by AccountListItem.ValidateAll() if the designated constraints
// aren't met.
type AccountListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountListItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountListItemMultiError) AllErrors() []error { return m }

// AccountListItemValidationError is the validation error returned by
// AccountListItem.Validate if the designated constraints aren't met.
type AccountListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountListItemValidationError) ErrorName() string { return "AccountListItemValidationError" }

// Error satisfies the builtin error interface
func (e AccountListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountListItemValidationError{}

// Validate checks the field values on AccountData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountDataMultiError, or
// nil if none found.
func (m *AccountData) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountDataMultiError(errors)
	}

	return nil
}

// AccountDataMultiError is an error wrapping multiple validation errors
// returned by AccountData.ValidateAll() if the designated constraints aren't met.
type AccountDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountDataMultiError) AllErrors() []error { return m }

// AccountDataValidationError is the validation error returned by
// AccountData.Validate if the designated constraints aren't met.
type AccountDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountDataValidationError) ErrorName() string { return "AccountDataValidationError" }

// Error satisfies the builtin error interface
func (e AccountDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountDataValidationError{}

// Validate checks the field values on GetAccountListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountListResponseMultiError, or nil if none found.
func (m *GetAccountListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountListResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountListResponseMultiError(errors)
	}

	return nil
}

// GetAccountListResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccountListResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAccountListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountListResponseMultiError) AllErrors() []error { return m }

// GetAccountListResponseValidationError is the validation error returned by
// GetAccountListResponse.Validate if the designated constraints aren't met.
type GetAccountListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountListResponseValidationError) ErrorName() string {
	return "GetAccountListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountListResponseValidationError{}
