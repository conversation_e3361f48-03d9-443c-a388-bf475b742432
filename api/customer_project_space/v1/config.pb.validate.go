// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/customer_project_space/v1/config.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetCustomerProConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerProConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerProConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerProConfigRequestMultiError, or nil if none found.
func (m *GetCustomerProConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerProConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	// no validation rules for ConfigKey

	// no validation rules for ConfigStatus

	if len(errors) > 0 {
		return GetCustomerProConfigRequestMultiError(errors)
	}

	return nil
}

// GetCustomerProConfigRequestMultiError is an error wrapping multiple
// validation errors returned by GetCustomerProConfigRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCustomerProConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerProConfigRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerProConfigRequestMultiError) AllErrors() []error { return m }

// GetCustomerProConfigRequestValidationError is the validation error returned
// by GetCustomerProConfigRequest.Validate if the designated constraints
// aren't met.
type GetCustomerProConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerProConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerProConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerProConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerProConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerProConfigRequestValidationError) ErrorName() string {
	return "GetCustomerProConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerProConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerProConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerProConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerProConfigRequestValidationError{}

// Validate checks the field values on CustomerProConfigData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerProConfigData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerProConfigData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerProConfigDataMultiError, or nil if none found.
func (m *CustomerProConfigData) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerProConfigData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ConfigKey

	// no validation rules for ConfigStatus

	// no validation rules for ConfigDisplayName

	// no validation rules for ConfigValue

	if len(errors) > 0 {
		return CustomerProConfigDataMultiError(errors)
	}

	return nil
}

// CustomerProConfigDataMultiError is an error wrapping multiple validation
// errors returned by CustomerProConfigData.ValidateAll() if the designated
// constraints aren't met.
type CustomerProConfigDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerProConfigDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerProConfigDataMultiError) AllErrors() []error { return m }

// CustomerProConfigDataValidationError is the validation error returned by
// CustomerProConfigData.Validate if the designated constraints aren't met.
type CustomerProConfigDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerProConfigDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerProConfigDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerProConfigDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerProConfigDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerProConfigDataValidationError) ErrorName() string {
	return "CustomerProConfigDataValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerProConfigDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerProConfigData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerProConfigDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerProConfigDataValidationError{}

// Validate checks the field values on GetCustomerProConfigResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerProConfigResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerProConfigResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerProConfigResponseMultiError, or nil if none found.
func (m *GetCustomerProConfigResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerProConfigResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCustomerProConfigResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCustomerProConfigResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCustomerProConfigResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCustomerProConfigResponseMultiError(errors)
	}

	return nil
}

// GetCustomerProConfigResponseMultiError is an error wrapping multiple
// validation errors returned by GetCustomerProConfigResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCustomerProConfigResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerProConfigResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerProConfigResponseMultiError) AllErrors() []error { return m }

// GetCustomerProConfigResponseValidationError is the validation error returned
// by GetCustomerProConfigResponse.Validate if the designated constraints
// aren't met.
type GetCustomerProConfigResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerProConfigResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerProConfigResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerProConfigResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerProConfigResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerProConfigResponseValidationError) ErrorName() string {
	return "GetCustomerProConfigResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerProConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerProConfigResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerProConfigResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerProConfigResponseValidationError{}

// Validate checks the field values on AddCustomerProConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddCustomerProConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddCustomerProConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddCustomerProConfigRequestMultiError, or nil if none found.
func (m *AddCustomerProConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCustomerProConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	// no validation rules for ConfigKey

	// no validation rules for ConfigValue

	if len(errors) > 0 {
		return AddCustomerProConfigRequestMultiError(errors)
	}

	return nil
}

// AddCustomerProConfigRequestMultiError is an error wrapping multiple
// validation errors returned by AddCustomerProConfigRequest.ValidateAll() if
// the designated constraints aren't met.
type AddCustomerProConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCustomerProConfigRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCustomerProConfigRequestMultiError) AllErrors() []error { return m }

// AddCustomerProConfigRequestValidationError is the validation error returned
// by AddCustomerProConfigRequest.Validate if the designated constraints
// aren't met.
type AddCustomerProConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCustomerProConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddCustomerProConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddCustomerProConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddCustomerProConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddCustomerProConfigRequestValidationError) ErrorName() string {
	return "AddCustomerProConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddCustomerProConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCustomerProConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCustomerProConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCustomerProConfigRequestValidationError{}
