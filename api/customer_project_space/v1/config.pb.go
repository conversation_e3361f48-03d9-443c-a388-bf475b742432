// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/customer_project_space/v1/config.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetCustomerProConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectId int32  `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"` // 项目id
	ConfigType        int32  `protobuf:"varint,2,opt,name=config_type,proto3" json:"config_type,omitempty"`                 // 项目设置类型
	ConfigStatus      string `protobuf:"bytes,3,opt,name=config_status,proto3" json:"config_status,omitempty"`              // 项目设置状态
}

func (x *GetCustomerProConfigRequest) Reset() {
	*x = GetCustomerProConfigRequest{}
	mi := &file_api_customer_project_space_v1_config_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerProConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProConfigRequest) ProtoMessage() {}

func (x *GetCustomerProConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_config_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProConfigRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerProConfigRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_config_proto_rawDescGZIP(), []int{0}
}

func (x *GetCustomerProConfigRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *GetCustomerProConfigRequest) GetConfigType() int32 {
	if x != nil {
		return x.ConfigType
	}
	return 0
}

func (x *GetCustomerProConfigRequest) GetConfigStatus() string {
	if x != nil {
		return x.ConfigStatus
	}
	return ""
}

type CustomerProConfigData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ConfigType   string `protobuf:"bytes,2,opt,name=config_type,proto3" json:"config_type,omitempty"`     // 项目设置类型
	ConfigStatus string `protobuf:"bytes,3,opt,name=config_status,proto3" json:"config_status,omitempty"` // 项目设置状态
	ConfigName   string `protobuf:"bytes,4,opt,name=config_name,proto3" json:"config_name,omitempty"`     // 项目设置名称
	ConfigValue  string `protobuf:"bytes,5,opt,name=config_value,proto3" json:"config_value,omitempty"`   // 项目设置值
}

func (x *CustomerProConfigData) Reset() {
	*x = CustomerProConfigData{}
	mi := &file_api_customer_project_space_v1_config_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProConfigData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProConfigData) ProtoMessage() {}

func (x *CustomerProConfigData) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_config_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProConfigData.ProtoReflect.Descriptor instead.
func (*CustomerProConfigData) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_config_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerProConfigData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomerProConfigData) GetConfigType() string {
	if x != nil {
		return x.ConfigType
	}
	return ""
}

func (x *CustomerProConfigData) GetConfigStatus() string {
	if x != nil {
		return x.ConfigStatus
	}
	return ""
}

func (x *CustomerProConfigData) GetConfigName() string {
	if x != nil {
		return x.ConfigName
	}
	return ""
}

func (x *CustomerProConfigData) GetConfigValue() string {
	if x != nil {
		return x.ConfigValue
	}
	return ""
}

type GetCustomerProConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                    `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                   `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                   `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    []*CustomerProConfigData `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetCustomerProConfigResponse) Reset() {
	*x = GetCustomerProConfigResponse{}
	mi := &file_api_customer_project_space_v1_config_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerProConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProConfigResponse) ProtoMessage() {}

func (x *GetCustomerProConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_config_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProConfigResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerProConfigResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_config_proto_rawDescGZIP(), []int{2}
}

func (x *GetCustomerProConfigResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetCustomerProConfigResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetCustomerProConfigResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetCustomerProConfigResponse) GetData() []*CustomerProConfigData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AddCustomerProConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectId int32  `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"` // 项目id
	ConfigType        string `protobuf:"bytes,2,opt,name=config_type,proto3" json:"config_type,omitempty"`                  // 项目设置类型
	ConfigValue       string `protobuf:"bytes,3,opt,name=config_value,proto3" json:"config_value,omitempty"`                // 项目设置值
}

func (x *AddCustomerProConfigRequest) Reset() {
	*x = AddCustomerProConfigRequest{}
	mi := &file_api_customer_project_space_v1_config_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCustomerProConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCustomerProConfigRequest) ProtoMessage() {}

func (x *AddCustomerProConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_config_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCustomerProConfigRequest.ProtoReflect.Descriptor instead.
func (*AddCustomerProConfigRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_config_proto_rawDescGZIP(), []int{3}
}

func (x *AddCustomerProConfigRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *AddCustomerProConfigRequest) GetConfigType() string {
	if x != nil {
		return x.ConfigType
	}
	return ""
}

func (x *AddCustomerProConfigRequest) GetConfigValue() string {
	if x != nil {
		return x.ConfigValue
	}
	return ""
}

var File_api_customer_project_space_v1_config_proto protoreflect.FileDescriptor

var file_api_customer_project_space_v1_config_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x97, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xb5, 0x01, 0x0a, 0x15,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0xac, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x44, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x95, 0x01, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xf9, 0x02, 0x0a, 0x18, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb4, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x36, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xa5,
	0x01, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x29, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x61, 0x64, 0x64, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x1f, 0x5a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_customer_project_space_v1_config_proto_rawDescOnce sync.Once
	file_api_customer_project_space_v1_config_proto_rawDescData = file_api_customer_project_space_v1_config_proto_rawDesc
)

func file_api_customer_project_space_v1_config_proto_rawDescGZIP() []byte {
	file_api_customer_project_space_v1_config_proto_rawDescOnce.Do(func() {
		file_api_customer_project_space_v1_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_customer_project_space_v1_config_proto_rawDescData)
	})
	return file_api_customer_project_space_v1_config_proto_rawDescData
}

var file_api_customer_project_space_v1_config_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_customer_project_space_v1_config_proto_goTypes = []any{
	(*GetCustomerProConfigRequest)(nil),  // 0: customer_project_space.v1.GetCustomerProConfigRequest
	(*CustomerProConfigData)(nil),        // 1: customer_project_space.v1.CustomerProConfigData
	(*GetCustomerProConfigResponse)(nil), // 2: customer_project_space.v1.GetCustomerProConfigResponse
	(*AddCustomerProConfigRequest)(nil),  // 3: customer_project_space.v1.AddCustomerProConfigRequest
	(*CommonResponse)(nil),               // 4: customer_project_space.v1.CommonResponse
}
var file_api_customer_project_space_v1_config_proto_depIdxs = []int32{
	1, // 0: customer_project_space.v1.GetCustomerProConfigResponse.data:type_name -> customer_project_space.v1.CustomerProConfigData
	0, // 1: customer_project_space.v1.CustomerProConfigService.GetCustomerProConfig:input_type -> customer_project_space.v1.GetCustomerProConfigRequest
	3, // 2: customer_project_space.v1.CustomerProConfigService.AddCustomerProConfig:input_type -> customer_project_space.v1.AddCustomerProConfigRequest
	2, // 3: customer_project_space.v1.CustomerProConfigService.GetCustomerProConfig:output_type -> customer_project_space.v1.GetCustomerProConfigResponse
	4, // 4: customer_project_space.v1.CustomerProConfigService.AddCustomerProConfig:output_type -> customer_project_space.v1.CommonResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_customer_project_space_v1_config_proto_init() }
func file_api_customer_project_space_v1_config_proto_init() {
	if File_api_customer_project_space_v1_config_proto != nil {
		return
	}
	file_api_customer_project_space_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_customer_project_space_v1_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_customer_project_space_v1_config_proto_goTypes,
		DependencyIndexes: file_api_customer_project_space_v1_config_proto_depIdxs,
		MessageInfos:      file_api_customer_project_space_v1_config_proto_msgTypes,
	}.Build()
	File_api_customer_project_space_v1_config_proto = out.File
	file_api_customer_project_space_v1_config_proto_rawDesc = nil
	file_api_customer_project_space_v1_config_proto_goTypes = nil
	file_api_customer_project_space_v1_config_proto_depIdxs = nil
}
