// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/customer_project_space/v1/resource.proto

package v1

import (
	v1 "api/resource_manager/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddOrRemoveCustomerProResourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 项目ID
	CustomerProjectId int32 `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
	// 资源类型
	ResourceType string `protobuf:"bytes,2,opt,name=resource_type,proto3" json:"resource_type,omitempty"`
	// 资源ID
	ResourceIdList []string `protobuf:"bytes,3,rep,name=resource_id_list,proto3" json:"resource_id_list,omitempty"`
}

func (x *AddOrRemoveCustomerProResourceRequest) Reset() {
	*x = AddOrRemoveCustomerProResourceRequest{}
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOrRemoveCustomerProResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOrRemoveCustomerProResourceRequest) ProtoMessage() {}

func (x *AddOrRemoveCustomerProResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOrRemoveCustomerProResourceRequest.ProtoReflect.Descriptor instead.
func (*AddOrRemoveCustomerProResourceRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_resource_proto_rawDescGZIP(), []int{0}
}

func (x *AddOrRemoveCustomerProResourceRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *AddOrRemoveCustomerProResourceRequest) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *AddOrRemoveCustomerProResourceRequest) GetResourceIdList() []string {
	if x != nil {
		return x.ResourceIdList
	}
	return nil
}

type ExportCustomerProResourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 项目ID
	CustomerProjectId int32 `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
	// 资源类型
	ResourceType string `protobuf:"bytes,2,opt,name=resource_type,proto3" json:"resource_type,omitempty"`
	// 任务名称
	TaskName string `protobuf:"bytes,3,opt,name=task_name,proto3" json:"task_name,omitempty"`
	// 名称
	ResourceName string `protobuf:"bytes,4,opt,name=resource_name,proto3" json:"resource_name,omitempty"`
}

func (x *ExportCustomerProResourceRequest) Reset() {
	*x = ExportCustomerProResourceRequest{}
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportCustomerProResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportCustomerProResourceRequest) ProtoMessage() {}

func (x *ExportCustomerProResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportCustomerProResourceRequest.ProtoReflect.Descriptor instead.
func (*ExportCustomerProResourceRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_resource_proto_rawDescGZIP(), []int{1}
}

func (x *ExportCustomerProResourceRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *ExportCustomerProResourceRequest) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ExportCustomerProResourceRequest) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *ExportCustomerProResourceRequest) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

type GetCustomerProResourceListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 项目ID
	CustomerProjectId int32 `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
	// 资源类型
	ResourceType string `protobuf:"bytes,2,opt,name=resource_type,proto3" json:"resource_type,omitempty"`
	// 分页参数
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,proto3" json:"page_size,omitempty"`
	// 页码
	PageNum int32 `protobuf:"varint,4,opt,name=page_num,proto3" json:"page_num,omitempty"`
	// 名称
	ResourceName string `protobuf:"bytes,5,opt,name=resource_name,proto3" json:"resource_name,omitempty"`
}

func (x *GetCustomerProResourceListRequest) Reset() {
	*x = GetCustomerProResourceListRequest{}
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerProResourceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProResourceListRequest) ProtoMessage() {}

func (x *GetCustomerProResourceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProResourceListRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerProResourceListRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_resource_proto_rawDescGZIP(), []int{2}
}

func (x *GetCustomerProResourceListRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *GetCustomerProResourceListRequest) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *GetCustomerProResourceListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetCustomerProResourceListRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetCustomerProResourceListRequest) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

type GetCustomerProResourceListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpResp            *v1.IpResourceListResponse       `protobuf:"bytes,1,opt,name=ip_resp,proto3" json:"ip_resp,omitempty"`
	PrResp            *v1.PrResourceListResponse       `protobuf:"bytes,2,opt,name=pr_resp,proto3" json:"pr_resp,omitempty"`
	OutdoorScreenResp *v1.ListOutdoorScreenReply       `protobuf:"bytes,3,opt,name=outdoor_screen_resp,proto3" json:"outdoor_screen_resp,omitempty"`
	SupplierResp      *v1.ListSupplierReply            `protobuf:"bytes,4,opt,name=supplier_resp,proto3" json:"supplier_resp,omitempty"`
	ReporterResp      *v1.ReporterResourceListResponse `protobuf:"bytes,5,opt,name=reporter_resp,proto3" json:"reporter_resp,omitempty"`
	PublisherResp     *v1.ListPublisherReply           `protobuf:"bytes,6,opt,name=publisher_resp,proto3" json:"publisher_resp,omitempty"`
}

func (x *GetCustomerProResourceListData) Reset() {
	*x = GetCustomerProResourceListData{}
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerProResourceListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProResourceListData) ProtoMessage() {}

func (x *GetCustomerProResourceListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProResourceListData.ProtoReflect.Descriptor instead.
func (*GetCustomerProResourceListData) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_resource_proto_rawDescGZIP(), []int{3}
}

func (x *GetCustomerProResourceListData) GetIpResp() *v1.IpResourceListResponse {
	if x != nil {
		return x.IpResp
	}
	return nil
}

func (x *GetCustomerProResourceListData) GetPrResp() *v1.PrResourceListResponse {
	if x != nil {
		return x.PrResp
	}
	return nil
}

func (x *GetCustomerProResourceListData) GetOutdoorScreenResp() *v1.ListOutdoorScreenReply {
	if x != nil {
		return x.OutdoorScreenResp
	}
	return nil
}

func (x *GetCustomerProResourceListData) GetSupplierResp() *v1.ListSupplierReply {
	if x != nil {
		return x.SupplierResp
	}
	return nil
}

func (x *GetCustomerProResourceListData) GetReporterResp() *v1.ReporterResourceListResponse {
	if x != nil {
		return x.ReporterResp
	}
	return nil
}

func (x *GetCustomerProResourceListData) GetPublisherResp() *v1.ListPublisherReply {
	if x != nil {
		return x.PublisherResp
	}
	return nil
}

type GetCustomerProResourceListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                           `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                          `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                          `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *GetCustomerProResourceListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetCustomerProResourceListResponse) Reset() {
	*x = GetCustomerProResourceListResponse{}
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerProResourceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProResourceListResponse) ProtoMessage() {}

func (x *GetCustomerProResourceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_resource_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProResourceListResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerProResourceListResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_resource_proto_rawDescGZIP(), []int{4}
}

func (x *GetCustomerProResourceListResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetCustomerProResourceListResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetCustomerProResourceListResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetCustomerProResourceListResponse) GetData() *GetCustomerProResourceListData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_api_customer_project_space_v1_resource_proto protoreflect.FileDescriptor

var file_api_customer_project_space_v1_resource_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x27, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x6f, 0x75, 0x74, 0x64, 0x6f, 0x6f, 0x72, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xab,
	0x01, 0x0a, 0x25, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xbe, 0x01, 0x0a,
	0x20, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xdb, 0x01,
	0x0a, 0x21, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x85, 0x04, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x45,
	0x0a, 0x07, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x07, 0x69, 0x70,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x07, 0x70, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x07, 0x70, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x12, 0x5d, 0x0a, 0x13,
	0x6f, 0x75, 0x74, 0x64, 0x6f, 0x6f, 0x72, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x64, 0x6f, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x13, 0x6f, 0x75, 0x74, 0x64, 0x6f, 0x6f, 0x72, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0d, 0x73,
	0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x70,
	0x70, 0x6c, 0x69, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x0d, 0x73, 0x75, 0x70, 0x70,
	0x6c, 0x69, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x0d, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x12, 0x4f, 0x0a, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x52, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x22, 0xbb, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72,
	0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d,
	0x73, 0x67, 0x12, 0x4d, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x39, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x32, 0x90, 0x06, 0x0a, 0x1a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0xb3, 0x01, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x40, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x61, 0x64, 0x64, 0x5f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0xb9, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x40, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x2f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0xc8, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x3c, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3d, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xb4, 0x01,
	0x0a, 0x19, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3b, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x65, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x42, 0x1f, 0x5a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_customer_project_space_v1_resource_proto_rawDescOnce sync.Once
	file_api_customer_project_space_v1_resource_proto_rawDescData = file_api_customer_project_space_v1_resource_proto_rawDesc
)

func file_api_customer_project_space_v1_resource_proto_rawDescGZIP() []byte {
	file_api_customer_project_space_v1_resource_proto_rawDescOnce.Do(func() {
		file_api_customer_project_space_v1_resource_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_customer_project_space_v1_resource_proto_rawDescData)
	})
	return file_api_customer_project_space_v1_resource_proto_rawDescData
}

var file_api_customer_project_space_v1_resource_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_customer_project_space_v1_resource_proto_goTypes = []any{
	(*AddOrRemoveCustomerProResourceRequest)(nil), // 0: customer_project_space.v1.AddOrRemoveCustomerProResourceRequest
	(*ExportCustomerProResourceRequest)(nil),      // 1: customer_project_space.v1.ExportCustomerProResourceRequest
	(*GetCustomerProResourceListRequest)(nil),     // 2: customer_project_space.v1.GetCustomerProResourceListRequest
	(*GetCustomerProResourceListData)(nil),        // 3: customer_project_space.v1.GetCustomerProResourceListData
	(*GetCustomerProResourceListResponse)(nil),    // 4: customer_project_space.v1.GetCustomerProResourceListResponse
	(*v1.IpResourceListResponse)(nil),             // 5: resource_manager.v1.IpResourceListResponse
	(*v1.PrResourceListResponse)(nil),             // 6: resource_manager.v1.PrResourceListResponse
	(*v1.ListOutdoorScreenReply)(nil),             // 7: resource_manager.v1.ListOutdoorScreenReply
	(*v1.ListSupplierReply)(nil),                  // 8: resource_manager.v1.ListSupplierReply
	(*v1.ReporterResourceListResponse)(nil),       // 9: resource_manager.v1.ReporterResourceListResponse
	(*v1.ListPublisherReply)(nil),                 // 10: resource_manager.v1.ListPublisherReply
	(*CommonResponse)(nil),                        // 11: customer_project_space.v1.CommonResponse
}
var file_api_customer_project_space_v1_resource_proto_depIdxs = []int32{
	5,  // 0: customer_project_space.v1.GetCustomerProResourceListData.ip_resp:type_name -> resource_manager.v1.IpResourceListResponse
	6,  // 1: customer_project_space.v1.GetCustomerProResourceListData.pr_resp:type_name -> resource_manager.v1.PrResourceListResponse
	7,  // 2: customer_project_space.v1.GetCustomerProResourceListData.outdoor_screen_resp:type_name -> resource_manager.v1.ListOutdoorScreenReply
	8,  // 3: customer_project_space.v1.GetCustomerProResourceListData.supplier_resp:type_name -> resource_manager.v1.ListSupplierReply
	9,  // 4: customer_project_space.v1.GetCustomerProResourceListData.reporter_resp:type_name -> resource_manager.v1.ReporterResourceListResponse
	10, // 5: customer_project_space.v1.GetCustomerProResourceListData.publisher_resp:type_name -> resource_manager.v1.ListPublisherReply
	3,  // 6: customer_project_space.v1.GetCustomerProResourceListResponse.data:type_name -> customer_project_space.v1.GetCustomerProResourceListData
	0,  // 7: customer_project_space.v1.CustomerProResourceService.AddCustomerProResource:input_type -> customer_project_space.v1.AddOrRemoveCustomerProResourceRequest
	0,  // 8: customer_project_space.v1.CustomerProResourceService.RemoveCustomerProResource:input_type -> customer_project_space.v1.AddOrRemoveCustomerProResourceRequest
	2,  // 9: customer_project_space.v1.CustomerProResourceService.GetCustomerProResourceList:input_type -> customer_project_space.v1.GetCustomerProResourceListRequest
	1,  // 10: customer_project_space.v1.CustomerProResourceService.ExportCustomerProResource:input_type -> customer_project_space.v1.ExportCustomerProResourceRequest
	11, // 11: customer_project_space.v1.CustomerProResourceService.AddCustomerProResource:output_type -> customer_project_space.v1.CommonResponse
	11, // 12: customer_project_space.v1.CustomerProResourceService.RemoveCustomerProResource:output_type -> customer_project_space.v1.CommonResponse
	4,  // 13: customer_project_space.v1.CustomerProResourceService.GetCustomerProResourceList:output_type -> customer_project_space.v1.GetCustomerProResourceListResponse
	11, // 14: customer_project_space.v1.CustomerProResourceService.ExportCustomerProResource:output_type -> customer_project_space.v1.CommonResponse
	11, // [11:15] is the sub-list for method output_type
	7,  // [7:11] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_customer_project_space_v1_resource_proto_init() }
func file_api_customer_project_space_v1_resource_proto_init() {
	if File_api_customer_project_space_v1_resource_proto != nil {
		return
	}
	file_api_customer_project_space_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_customer_project_space_v1_resource_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_customer_project_space_v1_resource_proto_goTypes,
		DependencyIndexes: file_api_customer_project_space_v1_resource_proto_depIdxs,
		MessageInfos:      file_api_customer_project_space_v1_resource_proto_msgTypes,
	}.Build()
	File_api_customer_project_space_v1_resource_proto = out.File
	file_api_customer_project_space_v1_resource_proto_rawDesc = nil
	file_api_customer_project_space_v1_resource_proto_goTypes = nil
	file_api_customer_project_space_v1_resource_proto_depIdxs = nil
}
