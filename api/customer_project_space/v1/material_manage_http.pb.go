// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.29.3
// source: api/customer_project_space/v1/material_manage.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationMaterialManageServiceDelMaterial = "/customer_project_space.v1.MaterialManageService/DelMaterial"
const OperationMaterialManageServiceGetMaterialDetail = "/customer_project_space.v1.MaterialManageService/GetMaterialDetail"
const OperationMaterialManageServiceGetMaterialList = "/customer_project_space.v1.MaterialManageService/GetMaterialList"
const OperationMaterialManageServiceGetMaterialTags = "/customer_project_space.v1.MaterialManageService/GetMaterialTags"
const OperationMaterialManageServiceUploadMaterialBatch = "/customer_project_space.v1.MaterialManageService/UploadMaterialBatch"
const OperationMaterialManageServiceUploadMaterialBatchCheck = "/customer_project_space.v1.MaterialManageService/UploadMaterialBatchCheck"
const OperationMaterialManageServiceUploadMaterialToMedium = "/customer_project_space.v1.MaterialManageService/UploadMaterialToMedium"
const OperationMaterialManageServiceUploadMaterialToMediumLog = "/customer_project_space.v1.MaterialManageService/UploadMaterialToMediumLog"

type MaterialManageServiceHTTPServer interface {
	DelMaterial(context.Context, *DelMaterialIdRequest) (*CommonResponse, error)
	GetMaterialDetail(context.Context, *CommonIdRequest) (*GetMaterialDetailResponse, error)
	GetMaterialList(context.Context, *GetMaterialListRequest) (*GetMaterialListResponse, error)
	GetMaterialTags(context.Context, *GetMaterialTagsRequest) (*GetMaterialTagsResponse, error)
	UploadMaterialBatch(context.Context, *UploadMaterialBatchRequest) (*CommonResponse, error)
	UploadMaterialBatchCheck(context.Context, *UploadMaterialBatchRequest) (*UploadMaterialBatchCheckResponse, error)
	UploadMaterialToMedium(context.Context, *UploadMaterialToMediumRequest) (*CommonResponse, error)
	UploadMaterialToMediumLog(context.Context, *UploadMaterialToMediumLogRequest) (*UploadMaterialToMediumLogResponse, error)
}

func RegisterMaterialManageServiceHTTPServer(s *http.Server, srv MaterialManageServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/customer_project/material_list", _MaterialManageService_GetMaterialList0_HTTP_Handler(srv))
	r.GET("/v1/customer_project/material_detail", _MaterialManageService_GetMaterialDetail0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/material_del", _MaterialManageService_DelMaterial0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/material_upload", _MaterialManageService_UploadMaterialBatch0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/material_upload_check", _MaterialManageService_UploadMaterialBatchCheck0_HTTP_Handler(srv))
	r.GET("/v1/customer_project/material_tags", _MaterialManageService_GetMaterialTags0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/material_medium_upload", _MaterialManageService_UploadMaterialToMedium0_HTTP_Handler(srv))
	r.GET("/v1/customer_project/material_medium_upload_log", _MaterialManageService_UploadMaterialToMediumLog0_HTTP_Handler(srv))
}

func _MaterialManageService_GetMaterialList0_HTTP_Handler(srv MaterialManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetMaterialListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMaterialManageServiceGetMaterialList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMaterialList(ctx, req.(*GetMaterialListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMaterialListResponse)
		return ctx.Result(200, reply)
	}
}

func _MaterialManageService_GetMaterialDetail0_HTTP_Handler(srv MaterialManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonIdRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMaterialManageServiceGetMaterialDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMaterialDetail(ctx, req.(*CommonIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMaterialDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _MaterialManageService_DelMaterial0_HTTP_Handler(srv MaterialManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DelMaterialIdRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMaterialManageServiceDelMaterial)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DelMaterial(ctx, req.(*DelMaterialIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _MaterialManageService_UploadMaterialBatch0_HTTP_Handler(srv MaterialManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadMaterialBatchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMaterialManageServiceUploadMaterialBatch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadMaterialBatch(ctx, req.(*UploadMaterialBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _MaterialManageService_UploadMaterialBatchCheck0_HTTP_Handler(srv MaterialManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadMaterialBatchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMaterialManageServiceUploadMaterialBatchCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadMaterialBatchCheck(ctx, req.(*UploadMaterialBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UploadMaterialBatchCheckResponse)
		return ctx.Result(200, reply)
	}
}

func _MaterialManageService_GetMaterialTags0_HTTP_Handler(srv MaterialManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetMaterialTagsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMaterialManageServiceGetMaterialTags)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMaterialTags(ctx, req.(*GetMaterialTagsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMaterialTagsResponse)
		return ctx.Result(200, reply)
	}
}

func _MaterialManageService_UploadMaterialToMedium0_HTTP_Handler(srv MaterialManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadMaterialToMediumRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMaterialManageServiceUploadMaterialToMedium)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadMaterialToMedium(ctx, req.(*UploadMaterialToMediumRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _MaterialManageService_UploadMaterialToMediumLog0_HTTP_Handler(srv MaterialManageServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadMaterialToMediumLogRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMaterialManageServiceUploadMaterialToMediumLog)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadMaterialToMediumLog(ctx, req.(*UploadMaterialToMediumLogRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UploadMaterialToMediumLogResponse)
		return ctx.Result(200, reply)
	}
}

type MaterialManageServiceHTTPClient interface {
	DelMaterial(ctx context.Context, req *DelMaterialIdRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	GetMaterialDetail(ctx context.Context, req *CommonIdRequest, opts ...http.CallOption) (rsp *GetMaterialDetailResponse, err error)
	GetMaterialList(ctx context.Context, req *GetMaterialListRequest, opts ...http.CallOption) (rsp *GetMaterialListResponse, err error)
	GetMaterialTags(ctx context.Context, req *GetMaterialTagsRequest, opts ...http.CallOption) (rsp *GetMaterialTagsResponse, err error)
	UploadMaterialBatch(ctx context.Context, req *UploadMaterialBatchRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	UploadMaterialBatchCheck(ctx context.Context, req *UploadMaterialBatchRequest, opts ...http.CallOption) (rsp *UploadMaterialBatchCheckResponse, err error)
	UploadMaterialToMedium(ctx context.Context, req *UploadMaterialToMediumRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	UploadMaterialToMediumLog(ctx context.Context, req *UploadMaterialToMediumLogRequest, opts ...http.CallOption) (rsp *UploadMaterialToMediumLogResponse, err error)
}

type MaterialManageServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewMaterialManageServiceHTTPClient(client *http.Client) MaterialManageServiceHTTPClient {
	return &MaterialManageServiceHTTPClientImpl{client}
}

func (c *MaterialManageServiceHTTPClientImpl) DelMaterial(ctx context.Context, in *DelMaterialIdRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/customer_project/material_del"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMaterialManageServiceDelMaterial))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MaterialManageServiceHTTPClientImpl) GetMaterialDetail(ctx context.Context, in *CommonIdRequest, opts ...http.CallOption) (*GetMaterialDetailResponse, error) {
	var out GetMaterialDetailResponse
	pattern := "/v1/customer_project/material_detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMaterialManageServiceGetMaterialDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MaterialManageServiceHTTPClientImpl) GetMaterialList(ctx context.Context, in *GetMaterialListRequest, opts ...http.CallOption) (*GetMaterialListResponse, error) {
	var out GetMaterialListResponse
	pattern := "/v1/customer_project/material_list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMaterialManageServiceGetMaterialList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MaterialManageServiceHTTPClientImpl) GetMaterialTags(ctx context.Context, in *GetMaterialTagsRequest, opts ...http.CallOption) (*GetMaterialTagsResponse, error) {
	var out GetMaterialTagsResponse
	pattern := "/v1/customer_project/material_tags"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMaterialManageServiceGetMaterialTags))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MaterialManageServiceHTTPClientImpl) UploadMaterialBatch(ctx context.Context, in *UploadMaterialBatchRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/customer_project/material_upload"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMaterialManageServiceUploadMaterialBatch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MaterialManageServiceHTTPClientImpl) UploadMaterialBatchCheck(ctx context.Context, in *UploadMaterialBatchRequest, opts ...http.CallOption) (*UploadMaterialBatchCheckResponse, error) {
	var out UploadMaterialBatchCheckResponse
	pattern := "/v1/customer_project/material_upload_check"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMaterialManageServiceUploadMaterialBatchCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MaterialManageServiceHTTPClientImpl) UploadMaterialToMedium(ctx context.Context, in *UploadMaterialToMediumRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/customer_project/material_medium_upload"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMaterialManageServiceUploadMaterialToMedium))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MaterialManageServiceHTTPClientImpl) UploadMaterialToMediumLog(ctx context.Context, in *UploadMaterialToMediumLogRequest, opts ...http.CallOption) (*UploadMaterialToMediumLogResponse, error) {
	var out UploadMaterialToMediumLogResponse
	pattern := "/v1/customer_project/material_medium_upload_log"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMaterialManageServiceUploadMaterialToMediumLog))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
