// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/customer_project_space/v1/material_manage.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetMaterialTagsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMaterialTagsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMaterialTagsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMaterialTagsRequestMultiError, or nil if none found.
func (m *GetMaterialTagsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaterialTagsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return GetMaterialTagsRequestMultiError(errors)
	}

	return nil
}

// GetMaterialTagsRequestMultiError is an error wrapping multiple validation
// errors returned by GetMaterialTagsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMaterialTagsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaterialTagsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaterialTagsRequestMultiError) AllErrors() []error { return m }

// GetMaterialTagsRequestValidationError is the validation error returned by
// GetMaterialTagsRequest.Validate if the designated constraints aren't met.
type GetMaterialTagsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaterialTagsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMaterialTagsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMaterialTagsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMaterialTagsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaterialTagsRequestValidationError) ErrorName() string {
	return "GetMaterialTagsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaterialTagsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaterialTagsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaterialTagsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaterialTagsRequestValidationError{}

// Validate checks the field values on CommonIdRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CommonIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CommonIdRequestMultiError, or nil if none found.
func (m *CommonIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return CommonIdRequestMultiError(errors)
	}

	return nil
}

// CommonIdRequestMultiError is an error wrapping multiple validation errors
// returned by CommonIdRequest.ValidateAll() if the designated constraints
// aren't met.
type CommonIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonIdRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonIdRequestMultiError) AllErrors() []error { return m }

// CommonIdRequestValidationError is the validation error returned by
// CommonIdRequest.Validate if the designated constraints aren't met.
type CommonIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonIdRequestValidationError) ErrorName() string { return "CommonIdRequestValidationError" }

// Error satisfies the builtin error interface
func (e CommonIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonIdRequestValidationError{}

// Validate checks the field values on GetMaterialListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMaterialListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMaterialListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMaterialListRequestMultiError, or nil if none found.
func (m *GetMaterialListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaterialListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageSize

	// no validation rules for PageNum

	// no validation rules for MaterialName

	// no validation rules for UploadStartTime

	// no validation rules for UploadEndTime

	// no validation rules for CustomerProjectId

	// no validation rules for MaterialType

	if len(errors) > 0 {
		return GetMaterialListRequestMultiError(errors)
	}

	return nil
}

// GetMaterialListRequestMultiError is an error wrapping multiple validation
// errors returned by GetMaterialListRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMaterialListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaterialListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaterialListRequestMultiError) AllErrors() []error { return m }

// GetMaterialListRequestValidationError is the validation error returned by
// GetMaterialListRequest.Validate if the designated constraints aren't met.
type GetMaterialListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaterialListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMaterialListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMaterialListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMaterialListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaterialListRequestValidationError) ErrorName() string {
	return "GetMaterialListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaterialListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaterialListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaterialListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaterialListRequestValidationError{}

// Validate checks the field values on MaterialListItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MaterialListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MaterialListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MaterialListItemMultiError, or nil if none found.
func (m *MaterialListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *MaterialListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MaterialName

	// no validation rules for MaterialPx

	// no validation rules for MaterialTime

	// no validation rules for MaterialCoverUrl

	// no validation rules for MaterialUrl

	if len(errors) > 0 {
		return MaterialListItemMultiError(errors)
	}

	return nil
}

// MaterialListItemMultiError is an error wrapping multiple validation errors
// returned by MaterialListItem.ValidateAll() if the designated constraints
// aren't met.
type MaterialListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MaterialListItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MaterialListItemMultiError) AllErrors() []error { return m }

// MaterialListItemValidationError is the validation error returned by
// MaterialListItem.Validate if the designated constraints aren't met.
type MaterialListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MaterialListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MaterialListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MaterialListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MaterialListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MaterialListItemValidationError) ErrorName() string { return "MaterialListItemValidationError" }

// Error satisfies the builtin error interface
func (e MaterialListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMaterialListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MaterialListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MaterialListItemValidationError{}

// Validate checks the field values on MaterialData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MaterialData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MaterialData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MaterialDataMultiError, or
// nil if none found.
func (m *MaterialData) ValidateAll() error {
	return m.validate(true)
}

func (m *MaterialData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MaterialDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MaterialDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MaterialDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MaterialDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MaterialDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MaterialDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MaterialDataMultiError(errors)
	}

	return nil
}

// MaterialDataMultiError is an error wrapping multiple validation errors
// returned by MaterialData.ValidateAll() if the designated constraints aren't met.
type MaterialDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MaterialDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MaterialDataMultiError) AllErrors() []error { return m }

// MaterialDataValidationError is the validation error returned by
// MaterialData.Validate if the designated constraints aren't met.
type MaterialDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MaterialDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MaterialDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MaterialDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MaterialDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MaterialDataValidationError) ErrorName() string { return "MaterialDataValidationError" }

// Error satisfies the builtin error interface
func (e MaterialDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMaterialData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MaterialDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MaterialDataValidationError{}

// Validate checks the field values on GetMaterialListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMaterialListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMaterialListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMaterialListResponseMultiError, or nil if none found.
func (m *GetMaterialListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaterialListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMaterialListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMaterialListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMaterialListResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMaterialListResponseMultiError(errors)
	}

	return nil
}

// GetMaterialListResponseMultiError is an error wrapping multiple validation
// errors returned by GetMaterialListResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMaterialListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaterialListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaterialListResponseMultiError) AllErrors() []error { return m }

// GetMaterialListResponseValidationError is the validation error returned by
// GetMaterialListResponse.Validate if the designated constraints aren't met.
type GetMaterialListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaterialListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMaterialListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMaterialListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMaterialListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaterialListResponseValidationError) ErrorName() string {
	return "GetMaterialListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaterialListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaterialListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaterialListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaterialListResponseValidationError{}

// Validate checks the field values on Material with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Material) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Material with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MaterialMultiError, or nil
// if none found.
func (m *Material) ValidateAll() error {
	return m.validate(true)
}

func (m *Material) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MaterialName

	// no validation rules for Language

	// no validation rules for MaterialPx

	// no validation rules for Size

	// no validation rules for CreateTime

	// no validation rules for CreateUser

	// no validation rules for MaterialUrl

	// no validation rules for MaterialTime

	// no validation rules for MaterialCoverUrl

	if len(errors) > 0 {
		return MaterialMultiError(errors)
	}

	return nil
}

// MaterialMultiError is an error wrapping multiple validation errors returned
// by Material.ValidateAll() if the designated constraints aren't met.
type MaterialMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MaterialMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MaterialMultiError) AllErrors() []error { return m }

// MaterialValidationError is the validation error returned by
// Material.Validate if the designated constraints aren't met.
type MaterialValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MaterialValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MaterialValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MaterialValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MaterialValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MaterialValidationError) ErrorName() string { return "MaterialValidationError" }

// Error satisfies the builtin error interface
func (e MaterialValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMaterial.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MaterialValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MaterialValidationError{}

// Validate checks the field values on GetMaterialDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMaterialDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMaterialDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMaterialDetailResponseMultiError, or nil if none found.
func (m *GetMaterialDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaterialDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMaterialDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMaterialDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMaterialDetailResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMaterialDetailResponseMultiError(errors)
	}

	return nil
}

// GetMaterialDetailResponseMultiError is an error wrapping multiple validation
// errors returned by GetMaterialDetailResponse.ValidateAll() if the
// designated constraints aren't met.
type GetMaterialDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaterialDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaterialDetailResponseMultiError) AllErrors() []error { return m }

// GetMaterialDetailResponseValidationError is the validation error returned by
// GetMaterialDetailResponse.Validate if the designated constraints aren't met.
type GetMaterialDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaterialDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMaterialDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMaterialDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMaterialDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaterialDetailResponseValidationError) ErrorName() string {
	return "GetMaterialDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaterialDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaterialDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaterialDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaterialDetailResponseValidationError{}

// Validate checks the field values on DelMaterialIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DelMaterialIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelMaterialIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DelMaterialIdRequestMultiError, or nil if none found.
func (m *DelMaterialIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DelMaterialIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return DelMaterialIdRequestMultiError(errors)
	}

	return nil
}

// DelMaterialIdRequestMultiError is an error wrapping multiple validation
// errors returned by DelMaterialIdRequest.ValidateAll() if the designated
// constraints aren't met.
type DelMaterialIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelMaterialIdRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelMaterialIdRequestMultiError) AllErrors() []error { return m }

// DelMaterialIdRequestValidationError is the validation error returned by
// DelMaterialIdRequest.Validate if the designated constraints aren't met.
type DelMaterialIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelMaterialIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelMaterialIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelMaterialIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelMaterialIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelMaterialIdRequestValidationError) ErrorName() string {
	return "DelMaterialIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DelMaterialIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelMaterialIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelMaterialIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelMaterialIdRequestValidationError{}

// Validate checks the field values on UploadMaterialBatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadMaterialBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadMaterialBatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadMaterialBatchRequestMultiError, or nil if none found.
func (m *UploadMaterialBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadMaterialBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetMaterialUri()); l < 1 || l > 200 {
		err := UploadMaterialBatchRequestValidationError{
			field:  "MaterialUri",
			reason: "value must contain between 1 and 200 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for MaterialTagsUri

	if m.GetCustomerProjectId() <= 0 {
		err := UploadMaterialBatchRequestValidationError{
			field:  "CustomerProjectId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UploadMaterialBatchRequestMultiError(errors)
	}

	return nil
}

// UploadMaterialBatchRequestMultiError is an error wrapping multiple
// validation errors returned by UploadMaterialBatchRequest.ValidateAll() if
// the designated constraints aren't met.
type UploadMaterialBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadMaterialBatchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadMaterialBatchRequestMultiError) AllErrors() []error { return m }

// UploadMaterialBatchRequestValidationError is the validation error returned
// by UploadMaterialBatchRequest.Validate if the designated constraints aren't met.
type UploadMaterialBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadMaterialBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadMaterialBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadMaterialBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadMaterialBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadMaterialBatchRequestValidationError) ErrorName() string {
	return "UploadMaterialBatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadMaterialBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadMaterialBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadMaterialBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadMaterialBatchRequestValidationError{}

// Validate checks the field values on UploadMaterialBatchCheckItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadMaterialBatchCheckItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadMaterialBatchCheckItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadMaterialBatchCheckItemMultiError, or nil if none found.
func (m *UploadMaterialBatchCheckItem) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadMaterialBatchCheckItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	// no validation rules for Language

	// no validation rules for Status

	// no validation rules for ErrMsg

	if len(errors) > 0 {
		return UploadMaterialBatchCheckItemMultiError(errors)
	}

	return nil
}

// UploadMaterialBatchCheckItemMultiError is an error wrapping multiple
// validation errors returned by UploadMaterialBatchCheckItem.ValidateAll() if
// the designated constraints aren't met.
type UploadMaterialBatchCheckItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadMaterialBatchCheckItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadMaterialBatchCheckItemMultiError) AllErrors() []error { return m }

// UploadMaterialBatchCheckItemValidationError is the validation error returned
// by UploadMaterialBatchCheckItem.Validate if the designated constraints
// aren't met.
type UploadMaterialBatchCheckItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadMaterialBatchCheckItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadMaterialBatchCheckItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadMaterialBatchCheckItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadMaterialBatchCheckItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadMaterialBatchCheckItemValidationError) ErrorName() string {
	return "UploadMaterialBatchCheckItemValidationError"
}

// Error satisfies the builtin error interface
func (e UploadMaterialBatchCheckItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadMaterialBatchCheckItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadMaterialBatchCheckItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadMaterialBatchCheckItemValidationError{}

// Validate checks the field values on UploadMaterialBatchCheckResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UploadMaterialBatchCheckResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadMaterialBatchCheckResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadMaterialBatchCheckResponseMultiError, or nil if none found.
func (m *UploadMaterialBatchCheckResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadMaterialBatchCheckResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UploadMaterialBatchCheckResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UploadMaterialBatchCheckResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UploadMaterialBatchCheckResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UploadMaterialBatchCheckResponseMultiError(errors)
	}

	return nil
}

// UploadMaterialBatchCheckResponseMultiError is an error wrapping multiple
// validation errors returned by
// UploadMaterialBatchCheckResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadMaterialBatchCheckResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadMaterialBatchCheckResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadMaterialBatchCheckResponseMultiError) AllErrors() []error { return m }

// UploadMaterialBatchCheckResponseValidationError is the validation error
// returned by UploadMaterialBatchCheckResponse.Validate if the designated
// constraints aren't met.
type UploadMaterialBatchCheckResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadMaterialBatchCheckResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadMaterialBatchCheckResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadMaterialBatchCheckResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadMaterialBatchCheckResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadMaterialBatchCheckResponseValidationError) ErrorName() string {
	return "UploadMaterialBatchCheckResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadMaterialBatchCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadMaterialBatchCheckResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadMaterialBatchCheckResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadMaterialBatchCheckResponseValidationError{}

// Validate checks the field values on GetMaterialTagsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMaterialTagsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMaterialTagsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMaterialTagsResponseMultiError, or nil if none found.
func (m *GetMaterialTagsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaterialTagsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if len(errors) > 0 {
		return GetMaterialTagsResponseMultiError(errors)
	}

	return nil
}

// GetMaterialTagsResponseMultiError is an error wrapping multiple validation
// errors returned by GetMaterialTagsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMaterialTagsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaterialTagsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaterialTagsResponseMultiError) AllErrors() []error { return m }

// GetMaterialTagsResponseValidationError is the validation error returned by
// GetMaterialTagsResponse.Validate if the designated constraints aren't met.
type GetMaterialTagsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaterialTagsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMaterialTagsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMaterialTagsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMaterialTagsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaterialTagsResponseValidationError) ErrorName() string {
	return "GetMaterialTagsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaterialTagsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaterialTagsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaterialTagsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaterialTagsResponseValidationError{}

// Validate checks the field values on UploadMaterialToMediumRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadMaterialToMediumRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadMaterialToMediumRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadMaterialToMediumRequestMultiError, or nil if none found.
func (m *UploadMaterialToMediumRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadMaterialToMediumRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaterialType

	// no validation rules for Medium

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return UploadMaterialToMediumRequestMultiError(errors)
	}

	return nil
}

// UploadMaterialToMediumRequestMultiError is an error wrapping multiple
// validation errors returned by UploadMaterialToMediumRequest.ValidateAll()
// if the designated constraints aren't met.
type UploadMaterialToMediumRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadMaterialToMediumRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadMaterialToMediumRequestMultiError) AllErrors() []error { return m }

// UploadMaterialToMediumRequestValidationError is the validation error
// returned by UploadMaterialToMediumRequest.Validate if the designated
// constraints aren't met.
type UploadMaterialToMediumRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadMaterialToMediumRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadMaterialToMediumRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadMaterialToMediumRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadMaterialToMediumRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadMaterialToMediumRequestValidationError) ErrorName() string {
	return "UploadMaterialToMediumRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadMaterialToMediumRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadMaterialToMediumRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadMaterialToMediumRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadMaterialToMediumRequestValidationError{}

// Validate checks the field values on UploadMaterialToMediumLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UploadMaterialToMediumLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadMaterialToMediumLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadMaterialToMediumLogRequestMultiError, or nil if none found.
func (m *UploadMaterialToMediumLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadMaterialToMediumLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageSize

	// no validation rules for PageNum

	// no validation rules for CustomerProjectId

	// no validation rules for MaterialType

	// no validation rules for TaskId

	// no validation rules for TaskStatus

	if len(errors) > 0 {
		return UploadMaterialToMediumLogRequestMultiError(errors)
	}

	return nil
}

// UploadMaterialToMediumLogRequestMultiError is an error wrapping multiple
// validation errors returned by
// UploadMaterialToMediumLogRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadMaterialToMediumLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadMaterialToMediumLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadMaterialToMediumLogRequestMultiError) AllErrors() []error { return m }

// UploadMaterialToMediumLogRequestValidationError is the validation error
// returned by UploadMaterialToMediumLogRequest.Validate if the designated
// constraints aren't met.
type UploadMaterialToMediumLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadMaterialToMediumLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadMaterialToMediumLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadMaterialToMediumLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadMaterialToMediumLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadMaterialToMediumLogRequestValidationError) ErrorName() string {
	return "UploadMaterialToMediumLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadMaterialToMediumLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadMaterialToMediumLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadMaterialToMediumLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadMaterialToMediumLogRequestValidationError{}

// Validate checks the field values on UploadMaterialToMediumLogItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadMaterialToMediumLogItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadMaterialToMediumLogItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadMaterialToMediumLogItemMultiError, or nil if none found.
func (m *UploadMaterialToMediumLogItem) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadMaterialToMediumLogItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for TaskStatus

	// no validation rules for MaterialName

	// no validation rules for AdsAccountId

	// no validation rules for UploadError

	// no validation rules for Medium

	// no validation rules for UploadTime

	// no validation rules for MaterialId

	if len(errors) > 0 {
		return UploadMaterialToMediumLogItemMultiError(errors)
	}

	return nil
}

// UploadMaterialToMediumLogItemMultiError is an error wrapping multiple
// validation errors returned by UploadMaterialToMediumLogItem.ValidateAll()
// if the designated constraints aren't met.
type UploadMaterialToMediumLogItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadMaterialToMediumLogItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadMaterialToMediumLogItemMultiError) AllErrors() []error { return m }

// UploadMaterialToMediumLogItemValidationError is the validation error
// returned by UploadMaterialToMediumLogItem.Validate if the designated
// constraints aren't met.
type UploadMaterialToMediumLogItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadMaterialToMediumLogItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadMaterialToMediumLogItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadMaterialToMediumLogItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadMaterialToMediumLogItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadMaterialToMediumLogItemValidationError) ErrorName() string {
	return "UploadMaterialToMediumLogItemValidationError"
}

// Error satisfies the builtin error interface
func (e UploadMaterialToMediumLogItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadMaterialToMediumLogItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadMaterialToMediumLogItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadMaterialToMediumLogItemValidationError{}

// Validate checks the field values on UploadMaterialToMediumLogData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadMaterialToMediumLogData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadMaterialToMediumLogData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadMaterialToMediumLogDataMultiError, or nil if none found.
func (m *UploadMaterialToMediumLogData) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadMaterialToMediumLogData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadMaterialToMediumLogDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadMaterialToMediumLogDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadMaterialToMediumLogDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UploadMaterialToMediumLogDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UploadMaterialToMediumLogDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UploadMaterialToMediumLogDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UploadMaterialToMediumLogDataMultiError(errors)
	}

	return nil
}

// UploadMaterialToMediumLogDataMultiError is an error wrapping multiple
// validation errors returned by UploadMaterialToMediumLogData.ValidateAll()
// if the designated constraints aren't met.
type UploadMaterialToMediumLogDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadMaterialToMediumLogDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadMaterialToMediumLogDataMultiError) AllErrors() []error { return m }

// UploadMaterialToMediumLogDataValidationError is the validation error
// returned by UploadMaterialToMediumLogData.Validate if the designated
// constraints aren't met.
type UploadMaterialToMediumLogDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadMaterialToMediumLogDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadMaterialToMediumLogDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadMaterialToMediumLogDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadMaterialToMediumLogDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadMaterialToMediumLogDataValidationError) ErrorName() string {
	return "UploadMaterialToMediumLogDataValidationError"
}

// Error satisfies the builtin error interface
func (e UploadMaterialToMediumLogDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadMaterialToMediumLogData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadMaterialToMediumLogDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadMaterialToMediumLogDataValidationError{}

// Validate checks the field values on UploadMaterialToMediumLogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UploadMaterialToMediumLogResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadMaterialToMediumLogResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UploadMaterialToMediumLogResponseMultiError, or nil if none found.
func (m *UploadMaterialToMediumLogResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadMaterialToMediumLogResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadMaterialToMediumLogResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadMaterialToMediumLogResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadMaterialToMediumLogResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadMaterialToMediumLogResponseMultiError(errors)
	}

	return nil
}

// UploadMaterialToMediumLogResponseMultiError is an error wrapping multiple
// validation errors returned by
// UploadMaterialToMediumLogResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadMaterialToMediumLogResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadMaterialToMediumLogResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadMaterialToMediumLogResponseMultiError) AllErrors() []error { return m }

// UploadMaterialToMediumLogResponseValidationError is the validation error
// returned by UploadMaterialToMediumLogResponse.Validate if the designated
// constraints aren't met.
type UploadMaterialToMediumLogResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadMaterialToMediumLogResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadMaterialToMediumLogResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadMaterialToMediumLogResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadMaterialToMediumLogResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadMaterialToMediumLogResponseValidationError) ErrorName() string {
	return "UploadMaterialToMediumLogResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadMaterialToMediumLogResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadMaterialToMediumLogResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadMaterialToMediumLogResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadMaterialToMediumLogResponseValidationError{}
