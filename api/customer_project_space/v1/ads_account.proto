syntax = "proto3";

package customer_project_space.v1;

option go_package = "api/customer_project_space/v1";

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "api/customer_project_space/v1/common.proto";

// ============== 服务定义START ========================

service AdsAccountManageService {
  // 获取项目已关联的账号列表
  rpc GetAdsAccountList(GetAdsAccountListRequest) returns (GetAdsAccountListResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/ads_account_list"
      body: "*"
    };
  }
  // 新增关联账号
  rpc AddAdsAccount(AddAdsAccountRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/add_ads_account"
      body: "*"
    };
  }
  // 启用禁用
  rpc EnOrDisableAdsAccount(EnOrDisableAdsAccountRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/able_ads_account"
      body: "*"
    };
  }
  // 预览广告账户的签约信息
  rpc PreviewAdsAccount(PreviewAdsAccountRequest) returns (PreviewAdsAccountResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/preview_ads_account"
      body: "*"
    };
  }
  // 移除账号
  rpc RemoveAdsAccount(RemoveAdsAccountRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/remove_ads_account"
      body: "*"
    };
  }
  // 获取授权下的账号
  rpc GetAccountByAuth(GetAccountByAuthRequest) returns (GetAccountByAuthResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/account_by_auth"
      body: "*"
    };
  }

  // 获取关联账号(通用版本)
  rpc GetCustomerAccount(GetCustomerAccountRequest) returns (GetAccountListResponse) {
    option (google.api.http) = {
      post: "/v1/customer_project/account"
      body: "*"
    };
  }

  // todo 切换成open api 获取所有启用的广告账号
  rpc OpenAPIGetEnableAdsAccountList(OpenApiGetAdsAccountListRequest) returns (OpenApiGetAdsAccountListResponse) {
    option (google.api.http) = {
      get: "/open_api/v1/customer_project/ads_account_list"
    };
  }
}

// ============== 服务定义END ===========================

message AddAdsAccountRequest{
  int32 customer_project_id = 1 [json_name = "customer_project_id"];  // 项目id
  repeated string ads_account_list = 2 [json_name = "ad_account_list"];  // 广告账户id列表
  string platform = 3 [json_name = "platform"];  // 平台
}

message GetAdsAccountListRequest{
  int32 customer_project_id = 1 [json_name = "customer_project_id"];  // 项目id
  string ads_account_id = 2 [json_name = "ads_account_id"]; // 模糊搜索广告账户id
  repeated string medium = 3 [json_name = "medium"]; // 媒体枚举值
  string status = 4 [json_name = "status"]; // 状态枚举值,enable-启用,disable-禁用
  int32 page_size = 5 [json_name = "page_size"]; // 分页大小
  int32 page_num = 6 [json_name = "page_num"]; // 当前页
}
message AdsAccountListItem{
  string medium = 1 [json_name = "medium"]; // 媒体枚举值
  string ads_account_id = 2 [json_name = "ads_account_id"]; // 广告账户id
  string status = 3 [json_name = "status"]; // 状态枚举值,enable-启用,disable-禁用
  optional SignInfo sign_info = 4 [json_name = "sign_info"];
}
message AdsAccountData{
  Pagination pagination = 1;
  repeated AdsAccountListItem list = 2;
}
message GetAdsAccountListResponse{
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  AdsAccountData data = 4;
}

message EnOrDisableAdsAccountRequest{
  int32 customer_project_id = 1 [json_name = "customer_project_id"];  // 项目id
  repeated string ads_account_list = 2 [json_name = "ads_account_list"];  // 广告账户id列表
  string status = 3 [json_name = "status"]; // 状态枚举值,enable-启用,disable-禁用
}

message PreviewAdsAccountRequest{
  repeated string ads_account_list = 1 [json_name = "ads_account_list"];  // 广告账户id列表
}

message AdsAccountSignData{
  repeated AdsAccountListItem list = 1;
}

message PreviewAdsAccountResponse{
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  AdsAccountSignData data = 4;
}

message OpenApiGetAdsAccountListRequest{
  string customer_project_number = 1 [json_name = "customer_project_number"];  // 项目编号
}

message OpenApiGetAdsAccountListData{
  string medium = 1;
  repeated string ads_account_ids = 2 [json_name = "ads_account_ids"];
}
message OpenApiGetAdsAccountListResponse{
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  repeated OpenApiGetAdsAccountListData data = 4;
}

message AccountByAuthData{
  string account_id = 1 [json_name = "account_id"];  // 广告账户id
  string account_name = 2 [json_name = "account_name"];  // 广告账户名称
}

message GetAccountByAuthRequest{
  int32 customer_project_id = 1 [json_name = "customer_project_id"];  // 项目id
  string config_key = 2 [json_name = "config_key"];  // 项目设置类型
  string config_value = 3 [json_name = "config_value"];  // 项目设置值
}

message GetAccountByAuthResponse{
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  repeated AccountByAuthData data = 4;
}

message RemoveAdsAccountRequest{
  int32 customer_project_id = 1 [json_name = "customer_project_id"];  // 项目id
  repeated string ads_account_list = 2 [json_name = "ads_account_list"];  // 广告账户id列表
  string platform = 3 [json_name = "platform"];  // 平台
}

message GetCustomerAccountRequest{
  int32 customer_project_id = 1 [json_name = "customer_project_id"];  // 项目id
  string account_id = 2 [json_name = "account_id"]; // 模糊搜索广告账户id
  repeated string medium = 3 [json_name = "medium"]; // 媒体枚举值
  string status = 4 [json_name = "status"]; // 状态枚举值,enable-启用,disable-禁用
  int32 page_size = 5 [json_name = "page_size"]; // 分页大小
  int32 page_num = 6 [json_name = "page_num"]; // 当前页
  string platform = 7 [json_name = "platform"];  // 平台
}
message AccountListItem{
  string medium = 1 [json_name = "medium"]; // 媒体枚举值
  string account_id = 2 [json_name = "account_id"]; // 广告账户id
  string account_name = 3 [json_name = "account_name"]; // 广告账户id
  string status = 4 [json_name = "status"]; // 状态枚举值,enable-启用,disable-禁用
  optional google.protobuf.Struct account_extra_info = 5 [json_name = "account_extra_info"];
}
message AccountData{
  Pagination pagination = 1;
  repeated AccountListItem list = 2;
}
message GetAccountListResponse{
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  AccountData data = 4;
}