// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.29.3
// source: api/customer_project_space/v1/config.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationCustomerProConfigServiceAddCustomerProConfig = "/customer_project_space.v1.CustomerProConfigService/AddCustomerProConfig"
const OperationCustomerProConfigServiceGetCustomerProConfig = "/customer_project_space.v1.CustomerProConfigService/GetCustomerProConfig"

type CustomerProConfigServiceHTTPServer interface {
	// AddCustomerProConfig 新增项目设置
	AddCustomerProConfig(context.Context, *AddCustomerProConfigRequest) (*CommonResponse, error)
	// GetCustomerProConfig 获取项目设置
	GetCustomerProConfig(context.Context, *GetCustomerProConfigRequest) (*GetCustomerProConfigResponse, error)
}

func RegisterCustomerProConfigServiceHTTPServer(s *http.Server, srv CustomerProConfigServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/customer_project/config_list", _CustomerProConfigService_GetCustomerProConfig0_HTTP_Handler(srv))
	r.POST("/v1/customer_project/add_config", _CustomerProConfigService_AddCustomerProConfig0_HTTP_Handler(srv))
}

func _CustomerProConfigService_GetCustomerProConfig0_HTTP_Handler(srv CustomerProConfigServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCustomerProConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCustomerProConfigServiceGetCustomerProConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCustomerProConfig(ctx, req.(*GetCustomerProConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCustomerProConfigResponse)
		return ctx.Result(200, reply)
	}
}

func _CustomerProConfigService_AddCustomerProConfig0_HTTP_Handler(srv CustomerProConfigServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddCustomerProConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCustomerProConfigServiceAddCustomerProConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddCustomerProConfig(ctx, req.(*AddCustomerProConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

type CustomerProConfigServiceHTTPClient interface {
	AddCustomerProConfig(ctx context.Context, req *AddCustomerProConfigRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	GetCustomerProConfig(ctx context.Context, req *GetCustomerProConfigRequest, opts ...http.CallOption) (rsp *GetCustomerProConfigResponse, err error)
}

type CustomerProConfigServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewCustomerProConfigServiceHTTPClient(client *http.Client) CustomerProConfigServiceHTTPClient {
	return &CustomerProConfigServiceHTTPClientImpl{client}
}

func (c *CustomerProConfigServiceHTTPClientImpl) AddCustomerProConfig(ctx context.Context, in *AddCustomerProConfigRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/customer_project/add_config"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCustomerProConfigServiceAddCustomerProConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CustomerProConfigServiceHTTPClientImpl) GetCustomerProConfig(ctx context.Context, in *GetCustomerProConfigRequest, opts ...http.CallOption) (*GetCustomerProConfigResponse, error) {
	var out GetCustomerProConfigResponse
	pattern := "/v1/customer_project/config_list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCustomerProConfigServiceGetCustomerProConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
