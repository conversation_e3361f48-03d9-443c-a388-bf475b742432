// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/customer_project_space/v1/ads_account.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddAdsAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectId int32    `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`               // 项目id
	AdsAccountList    []string `protobuf:"bytes,2,rep,name=ads_account_list,json=ad_account_list,proto3" json:"ads_account_list,omitempty"` // 广告账户id列表
	Platform          string   `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"`                                      // 平台
}

func (x *AddAdsAccountRequest) Reset() {
	*x = AddAdsAccountRequest{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAdsAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAdsAccountRequest) ProtoMessage() {}

func (x *AddAdsAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAdsAccountRequest.ProtoReflect.Descriptor instead.
func (*AddAdsAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{0}
}

func (x *AddAdsAccountRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *AddAdsAccountRequest) GetAdsAccountList() []string {
	if x != nil {
		return x.AdsAccountList
	}
	return nil
}

func (x *AddAdsAccountRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type GetAdsAccountListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectId int32    `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"` // 项目id
	AdsAccountId      string   `protobuf:"bytes,2,opt,name=ads_account_id,proto3" json:"ads_account_id,omitempty"`            // 模糊搜索广告账户id
	Medium            []string `protobuf:"bytes,3,rep,name=medium,proto3" json:"medium,omitempty"`                            // 媒体枚举值
	Status            string   `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`                            // 状态枚举值,enable-启用,disable-禁用
	PageSize          int32    `protobuf:"varint,5,opt,name=page_size,proto3" json:"page_size,omitempty"`                     // 分页大小
	PageNum           int32    `protobuf:"varint,6,opt,name=page_num,proto3" json:"page_num,omitempty"`                       // 当前页
	Platform          string   `protobuf:"bytes,7,opt,name=platform,proto3" json:"platform,omitempty"`                        // 平台
}

func (x *GetAdsAccountListRequest) Reset() {
	*x = GetAdsAccountListRequest{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAdsAccountListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdsAccountListRequest) ProtoMessage() {}

func (x *GetAdsAccountListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdsAccountListRequest.ProtoReflect.Descriptor instead.
func (*GetAdsAccountListRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{1}
}

func (x *GetAdsAccountListRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *GetAdsAccountListRequest) GetAdsAccountId() string {
	if x != nil {
		return x.AdsAccountId
	}
	return ""
}

func (x *GetAdsAccountListRequest) GetMedium() []string {
	if x != nil {
		return x.Medium
	}
	return nil
}

func (x *GetAdsAccountListRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetAdsAccountListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAdsAccountListRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetAdsAccountListRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type AdsAccountListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Medium         string          `protobuf:"bytes,1,opt,name=medium,proto3" json:"medium,omitempty"`                 // 媒体枚举值
	AdsAccountId   string          `protobuf:"bytes,2,opt,name=ads_account_id,proto3" json:"ads_account_id,omitempty"` // 广告账户id
	Status         string          `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`                 // 状态枚举值,enable-启用,disable-禁用
	SignInfo       *SignInfo       `protobuf:"bytes,4,opt,name=sign_info,proto3,oneof" json:"sign_info,omitempty"`
	ImpactCampaign *ImpactCampaign `protobuf:"bytes,5,opt,name=impact_campaign,proto3,oneof" json:"impact_campaign,omitempty"` // impact campaign
}

func (x *AdsAccountListItem) Reset() {
	*x = AdsAccountListItem{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdsAccountListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdsAccountListItem) ProtoMessage() {}

func (x *AdsAccountListItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdsAccountListItem.ProtoReflect.Descriptor instead.
func (*AdsAccountListItem) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{2}
}

func (x *AdsAccountListItem) GetMedium() string {
	if x != nil {
		return x.Medium
	}
	return ""
}

func (x *AdsAccountListItem) GetAdsAccountId() string {
	if x != nil {
		return x.AdsAccountId
	}
	return ""
}

func (x *AdsAccountListItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AdsAccountListItem) GetSignInfo() *SignInfo {
	if x != nil {
		return x.SignInfo
	}
	return nil
}

func (x *AdsAccountListItem) GetImpactCampaign() *ImpactCampaign {
	if x != nil {
		return x.ImpactCampaign
	}
	return nil
}

type AdsAccountData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination           `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*AdsAccountListItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AdsAccountData) Reset() {
	*x = AdsAccountData{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdsAccountData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdsAccountData) ProtoMessage() {}

func (x *AdsAccountData) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdsAccountData.ProtoReflect.Descriptor instead.
func (*AdsAccountData) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{3}
}

func (x *AdsAccountData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *AdsAccountData) GetList() []*AdsAccountListItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetAdsAccountListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32           `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string          `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string          `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *AdsAccountData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetAdsAccountListResponse) Reset() {
	*x = GetAdsAccountListResponse{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAdsAccountListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdsAccountListResponse) ProtoMessage() {}

func (x *GetAdsAccountListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdsAccountListResponse.ProtoReflect.Descriptor instead.
func (*GetAdsAccountListResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{4}
}

func (x *GetAdsAccountListResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetAdsAccountListResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetAdsAccountListResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetAdsAccountListResponse) GetData() *AdsAccountData {
	if x != nil {
		return x.Data
	}
	return nil
}

type EnOrDisableAdsAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectId int32    `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"` // 项目id
	AdsAccountList    []string `protobuf:"bytes,2,rep,name=ads_account_list,proto3" json:"ads_account_list,omitempty"`        // 广告账户id列表
	Status            string   `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`                            // 状态枚举值,enable-启用,disable-禁用
}

func (x *EnOrDisableAdsAccountRequest) Reset() {
	*x = EnOrDisableAdsAccountRequest{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnOrDisableAdsAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnOrDisableAdsAccountRequest) ProtoMessage() {}

func (x *EnOrDisableAdsAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnOrDisableAdsAccountRequest.ProtoReflect.Descriptor instead.
func (*EnOrDisableAdsAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{5}
}

func (x *EnOrDisableAdsAccountRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *EnOrDisableAdsAccountRequest) GetAdsAccountList() []string {
	if x != nil {
		return x.AdsAccountList
	}
	return nil
}

func (x *EnOrDisableAdsAccountRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type PreviewAdsAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdsAccountList []string `protobuf:"bytes,1,rep,name=ads_account_list,proto3" json:"ads_account_list,omitempty"` // 广告账户id列表
}

func (x *PreviewAdsAccountRequest) Reset() {
	*x = PreviewAdsAccountRequest{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PreviewAdsAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewAdsAccountRequest) ProtoMessage() {}

func (x *PreviewAdsAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewAdsAccountRequest.ProtoReflect.Descriptor instead.
func (*PreviewAdsAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{6}
}

func (x *PreviewAdsAccountRequest) GetAdsAccountList() []string {
	if x != nil {
		return x.AdsAccountList
	}
	return nil
}

type AdsAccountSignData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*AdsAccountListItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AdsAccountSignData) Reset() {
	*x = AdsAccountSignData{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdsAccountSignData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdsAccountSignData) ProtoMessage() {}

func (x *AdsAccountSignData) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdsAccountSignData.ProtoReflect.Descriptor instead.
func (*AdsAccountSignData) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{7}
}

func (x *AdsAccountSignData) GetList() []*AdsAccountListItem {
	if x != nil {
		return x.List
	}
	return nil
}

type PreviewAdsAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32               `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string              `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string              `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *AdsAccountSignData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *PreviewAdsAccountResponse) Reset() {
	*x = PreviewAdsAccountResponse{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PreviewAdsAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewAdsAccountResponse) ProtoMessage() {}

func (x *PreviewAdsAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewAdsAccountResponse.ProtoReflect.Descriptor instead.
func (*PreviewAdsAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{8}
}

func (x *PreviewAdsAccountResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *PreviewAdsAccountResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *PreviewAdsAccountResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *PreviewAdsAccountResponse) GetData() *AdsAccountSignData {
	if x != nil {
		return x.Data
	}
	return nil
}

type OpenApiGetAdsAccountListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectNumber string `protobuf:"bytes,1,opt,name=customer_project_number,proto3" json:"customer_project_number,omitempty"` // 项目编号
}

func (x *OpenApiGetAdsAccountListRequest) Reset() {
	*x = OpenApiGetAdsAccountListRequest{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpenApiGetAdsAccountListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenApiGetAdsAccountListRequest) ProtoMessage() {}

func (x *OpenApiGetAdsAccountListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenApiGetAdsAccountListRequest.ProtoReflect.Descriptor instead.
func (*OpenApiGetAdsAccountListRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{9}
}

func (x *OpenApiGetAdsAccountListRequest) GetCustomerProjectNumber() string {
	if x != nil {
		return x.CustomerProjectNumber
	}
	return ""
}

type OpenApiGetAdsAccountListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Medium        string   `protobuf:"bytes,1,opt,name=medium,proto3" json:"medium,omitempty"`
	AdsAccountIds []string `protobuf:"bytes,2,rep,name=ads_account_ids,proto3" json:"ads_account_ids,omitempty"`
}

func (x *OpenApiGetAdsAccountListData) Reset() {
	*x = OpenApiGetAdsAccountListData{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpenApiGetAdsAccountListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenApiGetAdsAccountListData) ProtoMessage() {}

func (x *OpenApiGetAdsAccountListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenApiGetAdsAccountListData.ProtoReflect.Descriptor instead.
func (*OpenApiGetAdsAccountListData) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{10}
}

func (x *OpenApiGetAdsAccountListData) GetMedium() string {
	if x != nil {
		return x.Medium
	}
	return ""
}

func (x *OpenApiGetAdsAccountListData) GetAdsAccountIds() []string {
	if x != nil {
		return x.AdsAccountIds
	}
	return nil
}

type OpenApiGetAdsAccountListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                           `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string                          `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string                          `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    []*OpenApiGetAdsAccountListData `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *OpenApiGetAdsAccountListResponse) Reset() {
	*x = OpenApiGetAdsAccountListResponse{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpenApiGetAdsAccountListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenApiGetAdsAccountListResponse) ProtoMessage() {}

func (x *OpenApiGetAdsAccountListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenApiGetAdsAccountListResponse.ProtoReflect.Descriptor instead.
func (*OpenApiGetAdsAccountListResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{11}
}

func (x *OpenApiGetAdsAccountListResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *OpenApiGetAdsAccountListResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *OpenApiGetAdsAccountListResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *OpenApiGetAdsAccountListResponse) GetData() []*OpenApiGetAdsAccountListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccountByAuthData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId   string `protobuf:"bytes,1,opt,name=account_id,proto3" json:"account_id,omitempty"`     // 广告账户id
	AccountName string `protobuf:"bytes,2,opt,name=account_name,proto3" json:"account_name,omitempty"` // 广告账户名称
}

func (x *AccountByAuthData) Reset() {
	*x = AccountByAuthData{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountByAuthData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountByAuthData) ProtoMessage() {}

func (x *AccountByAuthData) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountByAuthData.ProtoReflect.Descriptor instead.
func (*AccountByAuthData) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{12}
}

func (x *AccountByAuthData) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AccountByAuthData) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

type GetAccountByAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectId int32  `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"` // 项目id
	ConfigType        string `protobuf:"bytes,2,opt,name=config_type,proto3" json:"config_type,omitempty"`                  // 项目设置类型
	ConfigValue       string `protobuf:"bytes,3,opt,name=config_value,proto3" json:"config_value,omitempty"`                // 项目设置值
}

func (x *GetAccountByAuthRequest) Reset() {
	*x = GetAccountByAuthRequest{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountByAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountByAuthRequest) ProtoMessage() {}

func (x *GetAccountByAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountByAuthRequest.ProtoReflect.Descriptor instead.
func (*GetAccountByAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{13}
}

func (x *GetAccountByAuthRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *GetAccountByAuthRequest) GetConfigType() string {
	if x != nil {
		return x.ConfigType
	}
	return ""
}

func (x *GetAccountByAuthRequest) GetConfigValue() string {
	if x != nil {
		return x.ConfigValue
	}
	return ""
}

type GetAccountByAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string               `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string               `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    []*AccountByAuthData `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetAccountByAuthResponse) Reset() {
	*x = GetAccountByAuthResponse{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountByAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountByAuthResponse) ProtoMessage() {}

func (x *GetAccountByAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountByAuthResponse.ProtoReflect.Descriptor instead.
func (*GetAccountByAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{14}
}

func (x *GetAccountByAuthResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetAccountByAuthResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetAccountByAuthResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetAccountByAuthResponse) GetData() []*AccountByAuthData {
	if x != nil {
		return x.Data
	}
	return nil
}

type RemoveAdsAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerProjectId int32    `protobuf:"varint,1,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"` // 项目id
	AdsAccountList    []string `protobuf:"bytes,2,rep,name=ads_account_list,proto3" json:"ads_account_list,omitempty"`        // 广告账户id列表
	Platform          string   `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"`                        // 平台
}

func (x *RemoveAdsAccountRequest) Reset() {
	*x = RemoveAdsAccountRequest{}
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveAdsAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveAdsAccountRequest) ProtoMessage() {}

func (x *RemoveAdsAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_customer_project_space_v1_ads_account_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveAdsAccountRequest.ProtoReflect.Descriptor instead.
func (*RemoveAdsAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP(), []int{15}
}

func (x *RemoveAdsAccountRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

func (x *RemoveAdsAccountRequest) GetAdsAccountList() []string {
	if x != nil {
		return x.AdsAccountList
	}
	return nil
}

func (x *RemoveAdsAccountRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

var File_api_customer_project_space_v1_ads_account_proto protoreflect.FileDescriptor

var file_api_customer_project_space_v1_ads_account_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x19, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8f, 0x01, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x41, 0x64,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0xfa, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x64, 0x73, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0xb0, 0x02, 0x0a, 0x12, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65,
	0x64, 0x69, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x64,
	0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x58, 0x0a, 0x0f,
	0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x48, 0x01, 0x52, 0x0f, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x5f,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x22, 0x9a, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xa2, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x64, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x3d, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x94, 0x01, 0x0a, 0x1c, 0x45,
	0x6e, 0x4f, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x2a, 0x0a,
	0x10, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x46, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x64, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a,
	0x10, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x57, 0x0a, 0x12, 0x41, 0x64, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x41, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0xa6, 0x01, 0x0a, 0x19, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x64,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x41, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x5b, 0x0a, 0x1f, 0x4f,
	0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x47, 0x65, 0x74, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38,
	0x0a, 0x17, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x17, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x60, 0x0a, 0x1c, 0x4f, 0x70, 0x65, 0x6e,
	0x41, 0x70, 0x69, 0x47, 0x65, 0x74, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x64, 0x69,
	0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d,
	0x12, 0x28, 0x0a, 0x0f, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x64, 0x73, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x20, 0x4f,
	0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x47, 0x65, 0x74, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x4b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x47, 0x65, 0x74, 0x41, 0x64, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x57, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x79, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x91, 0x01,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0xa4, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x40, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x93, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x10, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x32, 0xfc,
	0x09, 0x0a, 0x17, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb0, 0x01, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x33, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x61, 0x64, 0x73,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x9c, 0x01,
	0x0a, 0x0d, 0x41, 0x64, 0x64, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2f, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x41,
	0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2f, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x61, 0x64, 0x64,
	0x5f, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xad, 0x01, 0x0a,
	0x15, 0x45, 0x6e, 0x4f, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x64, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x6e, 0x4f, 0x72, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x64,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x29, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xb3, 0x01, 0x0a,
	0x11, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x33, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x64, 0x73, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x61, 0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0xa5, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x64, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01,
	0x2a, 0x22, 0x27, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x61,
	0x64, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xac, 0x01, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x12,
	0x32, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29,
	0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x12, 0xd1, 0x01, 0x0a, 0x1e, 0x4f, 0x70,
	0x65, 0x6e, 0x41, 0x50, 0x49, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x64,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69,
	0x47, 0x65, 0x74, 0x41, 0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x12, 0x2e, 0x2f,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x61, 0x64, 0x73,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x1f, 0x5a,
	0x1d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_customer_project_space_v1_ads_account_proto_rawDescOnce sync.Once
	file_api_customer_project_space_v1_ads_account_proto_rawDescData = file_api_customer_project_space_v1_ads_account_proto_rawDesc
)

func file_api_customer_project_space_v1_ads_account_proto_rawDescGZIP() []byte {
	file_api_customer_project_space_v1_ads_account_proto_rawDescOnce.Do(func() {
		file_api_customer_project_space_v1_ads_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_customer_project_space_v1_ads_account_proto_rawDescData)
	})
	return file_api_customer_project_space_v1_ads_account_proto_rawDescData
}

var file_api_customer_project_space_v1_ads_account_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_api_customer_project_space_v1_ads_account_proto_goTypes = []any{
	(*AddAdsAccountRequest)(nil),             // 0: customer_project_space.v1.AddAdsAccountRequest
	(*GetAdsAccountListRequest)(nil),         // 1: customer_project_space.v1.GetAdsAccountListRequest
	(*AdsAccountListItem)(nil),               // 2: customer_project_space.v1.AdsAccountListItem
	(*AdsAccountData)(nil),                   // 3: customer_project_space.v1.AdsAccountData
	(*GetAdsAccountListResponse)(nil),        // 4: customer_project_space.v1.GetAdsAccountListResponse
	(*EnOrDisableAdsAccountRequest)(nil),     // 5: customer_project_space.v1.EnOrDisableAdsAccountRequest
	(*PreviewAdsAccountRequest)(nil),         // 6: customer_project_space.v1.PreviewAdsAccountRequest
	(*AdsAccountSignData)(nil),               // 7: customer_project_space.v1.AdsAccountSignData
	(*PreviewAdsAccountResponse)(nil),        // 8: customer_project_space.v1.PreviewAdsAccountResponse
	(*OpenApiGetAdsAccountListRequest)(nil),  // 9: customer_project_space.v1.OpenApiGetAdsAccountListRequest
	(*OpenApiGetAdsAccountListData)(nil),     // 10: customer_project_space.v1.OpenApiGetAdsAccountListData
	(*OpenApiGetAdsAccountListResponse)(nil), // 11: customer_project_space.v1.OpenApiGetAdsAccountListResponse
	(*AccountByAuthData)(nil),                // 12: customer_project_space.v1.AccountByAuthData
	(*GetAccountByAuthRequest)(nil),          // 13: customer_project_space.v1.GetAccountByAuthRequest
	(*GetAccountByAuthResponse)(nil),         // 14: customer_project_space.v1.GetAccountByAuthResponse
	(*RemoveAdsAccountRequest)(nil),          // 15: customer_project_space.v1.RemoveAdsAccountRequest
	(*SignInfo)(nil),                         // 16: customer_project_space.v1.SignInfo
	(*ImpactCampaign)(nil),                   // 17: customer_project_space.v1.ImpactCampaign
	(*Pagination)(nil),                       // 18: customer_project_space.v1.Pagination
	(*CommonResponse)(nil),                   // 19: customer_project_space.v1.CommonResponse
}
var file_api_customer_project_space_v1_ads_account_proto_depIdxs = []int32{
	16, // 0: customer_project_space.v1.AdsAccountListItem.sign_info:type_name -> customer_project_space.v1.SignInfo
	17, // 1: customer_project_space.v1.AdsAccountListItem.impact_campaign:type_name -> customer_project_space.v1.ImpactCampaign
	18, // 2: customer_project_space.v1.AdsAccountData.pagination:type_name -> customer_project_space.v1.Pagination
	2,  // 3: customer_project_space.v1.AdsAccountData.list:type_name -> customer_project_space.v1.AdsAccountListItem
	3,  // 4: customer_project_space.v1.GetAdsAccountListResponse.data:type_name -> customer_project_space.v1.AdsAccountData
	2,  // 5: customer_project_space.v1.AdsAccountSignData.list:type_name -> customer_project_space.v1.AdsAccountListItem
	7,  // 6: customer_project_space.v1.PreviewAdsAccountResponse.data:type_name -> customer_project_space.v1.AdsAccountSignData
	10, // 7: customer_project_space.v1.OpenApiGetAdsAccountListResponse.data:type_name -> customer_project_space.v1.OpenApiGetAdsAccountListData
	12, // 8: customer_project_space.v1.GetAccountByAuthResponse.data:type_name -> customer_project_space.v1.AccountByAuthData
	1,  // 9: customer_project_space.v1.AdsAccountManageService.GetAdsAccountList:input_type -> customer_project_space.v1.GetAdsAccountListRequest
	0,  // 10: customer_project_space.v1.AdsAccountManageService.AddAdsAccount:input_type -> customer_project_space.v1.AddAdsAccountRequest
	5,  // 11: customer_project_space.v1.AdsAccountManageService.EnOrDisableAdsAccount:input_type -> customer_project_space.v1.EnOrDisableAdsAccountRequest
	6,  // 12: customer_project_space.v1.AdsAccountManageService.PreviewAdsAccount:input_type -> customer_project_space.v1.PreviewAdsAccountRequest
	15, // 13: customer_project_space.v1.AdsAccountManageService.RemoveAdsAccount:input_type -> customer_project_space.v1.RemoveAdsAccountRequest
	13, // 14: customer_project_space.v1.AdsAccountManageService.GetAccountByAuth:input_type -> customer_project_space.v1.GetAccountByAuthRequest
	9,  // 15: customer_project_space.v1.AdsAccountManageService.OpenAPIGetEnableAdsAccountList:input_type -> customer_project_space.v1.OpenApiGetAdsAccountListRequest
	4,  // 16: customer_project_space.v1.AdsAccountManageService.GetAdsAccountList:output_type -> customer_project_space.v1.GetAdsAccountListResponse
	19, // 17: customer_project_space.v1.AdsAccountManageService.AddAdsAccount:output_type -> customer_project_space.v1.CommonResponse
	19, // 18: customer_project_space.v1.AdsAccountManageService.EnOrDisableAdsAccount:output_type -> customer_project_space.v1.CommonResponse
	8,  // 19: customer_project_space.v1.AdsAccountManageService.PreviewAdsAccount:output_type -> customer_project_space.v1.PreviewAdsAccountResponse
	19, // 20: customer_project_space.v1.AdsAccountManageService.RemoveAdsAccount:output_type -> customer_project_space.v1.CommonResponse
	14, // 21: customer_project_space.v1.AdsAccountManageService.GetAccountByAuth:output_type -> customer_project_space.v1.GetAccountByAuthResponse
	11, // 22: customer_project_space.v1.AdsAccountManageService.OpenAPIGetEnableAdsAccountList:output_type -> customer_project_space.v1.OpenApiGetAdsAccountListResponse
	16, // [16:23] is the sub-list for method output_type
	9,  // [9:16] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_api_customer_project_space_v1_ads_account_proto_init() }
func file_api_customer_project_space_v1_ads_account_proto_init() {
	if File_api_customer_project_space_v1_ads_account_proto != nil {
		return
	}
	file_api_customer_project_space_v1_common_proto_init()
	file_api_customer_project_space_v1_ads_account_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_customer_project_space_v1_ads_account_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_customer_project_space_v1_ads_account_proto_goTypes,
		DependencyIndexes: file_api_customer_project_space_v1_ads_account_proto_depIdxs,
		MessageInfos:      file_api_customer_project_space_v1_ads_account_proto_msgTypes,
	}.Build()
	File_api_customer_project_space_v1_ads_account_proto = out.File
	file_api_customer_project_space_v1_ads_account_proto_rawDesc = nil
	file_api_customer_project_space_v1_ads_account_proto_goTypes = nil
	file_api_customer_project_space_v1_ads_account_proto_depIdxs = nil
}
