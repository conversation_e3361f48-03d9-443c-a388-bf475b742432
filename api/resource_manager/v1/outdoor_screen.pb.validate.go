// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/resource_manager/v1/outdoor_screen.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateOutdoorScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateOutdoorScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOutdoorScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateOutdoorScreenRequestMultiError, or nil if none found.
func (m *CreateOutdoorScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOutdoorScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOutdoorScreenRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOutdoorScreenRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOutdoorScreenRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOutdoorScreenRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOutdoorScreenRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOutdoorScreenRequestValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOutdoorScreenRequestMultiError(errors)
	}

	return nil
}

// CreateOutdoorScreenRequestMultiError is an error wrapping multiple
// validation errors returned by CreateOutdoorScreenRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateOutdoorScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOutdoorScreenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOutdoorScreenRequestMultiError) AllErrors() []error { return m }

// CreateOutdoorScreenRequestValidationError is the validation error returned
// by CreateOutdoorScreenRequest.Validate if the designated constraints aren't met.
type CreateOutdoorScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOutdoorScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOutdoorScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOutdoorScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOutdoorScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOutdoorScreenRequestValidationError) ErrorName() string {
	return "CreateOutdoorScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOutdoorScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOutdoorScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOutdoorScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOutdoorScreenRequestValidationError{}

// Validate checks the field values on UpdateOutdoorScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateOutdoorScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateOutdoorScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateOutdoorScreenRequestMultiError, or nil if none found.
func (m *UpdateOutdoorScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateOutdoorScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOutdoorScreenRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOutdoorScreenRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOutdoorScreenRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateOutdoorScreenRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateOutdoorScreenRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateOutdoorScreenRequestValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateOutdoorScreenRequestMultiError(errors)
	}

	return nil
}

// UpdateOutdoorScreenRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateOutdoorScreenRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateOutdoorScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateOutdoorScreenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateOutdoorScreenRequestMultiError) AllErrors() []error { return m }

// UpdateOutdoorScreenRequestValidationError is the validation error returned
// by UpdateOutdoorScreenRequest.Validate if the designated constraints aren't met.
type UpdateOutdoorScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateOutdoorScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateOutdoorScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateOutdoorScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateOutdoorScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateOutdoorScreenRequestValidationError) ErrorName() string {
	return "UpdateOutdoorScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateOutdoorScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateOutdoorScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateOutdoorScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateOutdoorScreenRequestValidationError{}

// Validate checks the field values on DeleteOutdoorScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteOutdoorScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteOutdoorScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteOutdoorScreenRequestMultiError, or nil if none found.
func (m *DeleteOutdoorScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteOutdoorScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteOutdoorScreenRequestMultiError(errors)
	}

	return nil
}

// DeleteOutdoorScreenRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteOutdoorScreenRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteOutdoorScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteOutdoorScreenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteOutdoorScreenRequestMultiError) AllErrors() []error { return m }

// DeleteOutdoorScreenRequestValidationError is the validation error returned
// by DeleteOutdoorScreenRequest.Validate if the designated constraints aren't met.
type DeleteOutdoorScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteOutdoorScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteOutdoorScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteOutdoorScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteOutdoorScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteOutdoorScreenRequestValidationError) ErrorName() string {
	return "DeleteOutdoorScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteOutdoorScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteOutdoorScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteOutdoorScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteOutdoorScreenRequestValidationError{}

// Validate checks the field values on OutdoorScreenResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OutdoorScreenResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutdoorScreenResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutdoorScreenResourceMultiError, or nil if none found.
func (m *OutdoorScreenResource) ValidateAll() error {
	return m.validate(true)
}

func (m *OutdoorScreenResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	// no validation rules for IntroductionDate

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutdoorScreenResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutdoorScreenResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutdoorScreenResourceValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UpdateTime

	if len(errors) > 0 {
		return OutdoorScreenResourceMultiError(errors)
	}

	return nil
}

// OutdoorScreenResourceMultiError is an error wrapping multiple validation
// errors returned by OutdoorScreenResource.ValidateAll() if the designated
// constraints aren't met.
type OutdoorScreenResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutdoorScreenResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutdoorScreenResourceMultiError) AllErrors() []error { return m }

// OutdoorScreenResourceValidationError is the validation error returned by
// OutdoorScreenResource.Validate if the designated constraints aren't met.
type OutdoorScreenResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutdoorScreenResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutdoorScreenResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutdoorScreenResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutdoorScreenResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutdoorScreenResourceValidationError) ErrorName() string {
	return "OutdoorScreenResourceValidationError"
}

// Error satisfies the builtin error interface
func (e OutdoorScreenResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutdoorScreenResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutdoorScreenResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutdoorScreenResourceValidationError{}

// Validate checks the field values on OutdoorScreenData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutdoorScreenData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutdoorScreenData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutdoorScreenDataMultiError, or nil if none found.
func (m *OutdoorScreenData) ValidateAll() error {
	return m.validate(true)
}

func (m *OutdoorScreenData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutdoorScreenDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutdoorScreenDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutdoorScreenDataValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutdoorScreenDataValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutdoorScreenDataValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutdoorScreenDataValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutdoorScreenDataMultiError(errors)
	}

	return nil
}

// OutdoorScreenDataMultiError is an error wrapping multiple validation errors
// returned by OutdoorScreenData.ValidateAll() if the designated constraints
// aren't met.
type OutdoorScreenDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutdoorScreenDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutdoorScreenDataMultiError) AllErrors() []error { return m }

// OutdoorScreenDataValidationError is the validation error returned by
// OutdoorScreenData.Validate if the designated constraints aren't met.
type OutdoorScreenDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutdoorScreenDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutdoorScreenDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutdoorScreenDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutdoorScreenDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutdoorScreenDataValidationError) ErrorName() string {
	return "OutdoorScreenDataValidationError"
}

// Error satisfies the builtin error interface
func (e OutdoorScreenDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutdoorScreenData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutdoorScreenDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutdoorScreenDataValidationError{}

// Validate checks the field values on GetOutdoorScreenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOutdoorScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOutdoorScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOutdoorScreenRequestMultiError, or nil if none found.
func (m *GetOutdoorScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOutdoorScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetOutdoorScreenRequestMultiError(errors)
	}

	return nil
}

// GetOutdoorScreenRequestMultiError is an error wrapping multiple validation
// errors returned by GetOutdoorScreenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOutdoorScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOutdoorScreenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOutdoorScreenRequestMultiError) AllErrors() []error { return m }

// GetOutdoorScreenRequestValidationError is the validation error returned by
// GetOutdoorScreenRequest.Validate if the designated constraints aren't met.
type GetOutdoorScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOutdoorScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOutdoorScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOutdoorScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOutdoorScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOutdoorScreenRequestValidationError) ErrorName() string {
	return "GetOutdoorScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOutdoorScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOutdoorScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOutdoorScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOutdoorScreenRequestValidationError{}

// Validate checks the field values on GetOutdoorScreenReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOutdoorScreenReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOutdoorScreenReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOutdoorScreenReplyMultiError, or nil if none found.
func (m *GetOutdoorScreenReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOutdoorScreenReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOutdoorScreenReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOutdoorScreenReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOutdoorScreenReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOutdoorScreenReplyMultiError(errors)
	}

	return nil
}

// GetOutdoorScreenReplyMultiError is an error wrapping multiple validation
// errors returned by GetOutdoorScreenReply.ValidateAll() if the designated
// constraints aren't met.
type GetOutdoorScreenReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOutdoorScreenReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOutdoorScreenReplyMultiError) AllErrors() []error { return m }

// GetOutdoorScreenReplyValidationError is the validation error returned by
// GetOutdoorScreenReply.Validate if the designated constraints aren't met.
type GetOutdoorScreenReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOutdoorScreenReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOutdoorScreenReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOutdoorScreenReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOutdoorScreenReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOutdoorScreenReplyValidationError) ErrorName() string {
	return "GetOutdoorScreenReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetOutdoorScreenReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOutdoorScreenReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOutdoorScreenReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOutdoorScreenReplyValidationError{}

// Validate checks the field values on OutdoorScreenList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutdoorScreenList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutdoorScreenList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutdoorScreenListMultiError, or nil if none found.
func (m *OutdoorScreenList) ValidateAll() error {
	return m.validate(true)
}

func (m *OutdoorScreenList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutdoorScreenListValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutdoorScreenListValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutdoorScreenListValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutdoorScreenListValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutdoorScreenListValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutdoorScreenListValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OutdoorScreenListMultiError(errors)
	}

	return nil
}

// OutdoorScreenListMultiError is an error wrapping multiple validation errors
// returned by OutdoorScreenList.ValidateAll() if the designated constraints
// aren't met.
type OutdoorScreenListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutdoorScreenListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutdoorScreenListMultiError) AllErrors() []error { return m }

// OutdoorScreenListValidationError is the validation error returned by
// OutdoorScreenList.Validate if the designated constraints aren't met.
type OutdoorScreenListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutdoorScreenListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutdoorScreenListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutdoorScreenListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutdoorScreenListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutdoorScreenListValidationError) ErrorName() string {
	return "OutdoorScreenListValidationError"
}

// Error satisfies the builtin error interface
func (e OutdoorScreenListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutdoorScreenList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutdoorScreenListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutdoorScreenListValidationError{}

// Validate checks the field values on ListOutdoorScreenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListOutdoorScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOutdoorScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListOutdoorScreenRequestMultiError, or nil if none found.
func (m *ListOutdoorScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOutdoorScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOutdoorScreenRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOutdoorScreenRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOutdoorScreenRequestValidationError{
				field:  "BaseParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOutdoorScreenRequestValidationError{
					field:  "BrandParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOutdoorScreenRequestValidationError{
					field:  "BrandParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOutdoorScreenRequestValidationError{
				field:  "BrandParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return ListOutdoorScreenRequestMultiError(errors)
	}

	return nil
}

// ListOutdoorScreenRequestMultiError is an error wrapping multiple validation
// errors returned by ListOutdoorScreenRequest.ValidateAll() if the designated
// constraints aren't met.
type ListOutdoorScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOutdoorScreenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOutdoorScreenRequestMultiError) AllErrors() []error { return m }

// ListOutdoorScreenRequestValidationError is the validation error returned by
// ListOutdoorScreenRequest.Validate if the designated constraints aren't met.
type ListOutdoorScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOutdoorScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOutdoorScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOutdoorScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOutdoorScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOutdoorScreenRequestValidationError) ErrorName() string {
	return "ListOutdoorScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListOutdoorScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOutdoorScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOutdoorScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOutdoorScreenRequestValidationError{}

// Validate checks the field values on ListOutdoorScreenReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListOutdoorScreenReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOutdoorScreenReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListOutdoorScreenReplyMultiError, or nil if none found.
func (m *ListOutdoorScreenReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOutdoorScreenReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOutdoorScreenReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOutdoorScreenReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOutdoorScreenReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListOutdoorScreenReplyMultiError(errors)
	}

	return nil
}

// ListOutdoorScreenReplyMultiError is an error wrapping multiple validation
// errors returned by ListOutdoorScreenReply.ValidateAll() if the designated
// constraints aren't met.
type ListOutdoorScreenReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOutdoorScreenReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOutdoorScreenReplyMultiError) AllErrors() []error { return m }

// ListOutdoorScreenReplyValidationError is the validation error returned by
// ListOutdoorScreenReply.Validate if the designated constraints aren't met.
type ListOutdoorScreenReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOutdoorScreenReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOutdoorScreenReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOutdoorScreenReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOutdoorScreenReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOutdoorScreenReplyValidationError) ErrorName() string {
	return "ListOutdoorScreenReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListOutdoorScreenReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOutdoorScreenReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOutdoorScreenReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOutdoorScreenReplyValidationError{}
