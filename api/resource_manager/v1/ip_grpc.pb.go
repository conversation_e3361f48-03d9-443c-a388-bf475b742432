// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/resource_manager/v1/ip.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ResourceManagerIpService_GetIpResourceList_FullMethodName   = "/resource_manager.v1.ResourceManagerIpService/GetIpResourceList"
	ResourceManagerIpService_GetIpResourceDetail_FullMethodName = "/resource_manager.v1.ResourceManagerIpService/GetIpResourceDetail"
	ResourceManagerIpService_AddIpResource_FullMethodName       = "/resource_manager.v1.ResourceManagerIpService/AddIpResource"
	ResourceManagerIpService_UpdateIpResource_FullMethodName    = "/resource_manager.v1.ResourceManagerIpService/UpdateIpResource"
	ResourceManagerIpService_DeleteIpResource_FullMethodName    = "/resource_manager.v1.ResourceManagerIpService/DeleteIpResource"
)

// ResourceManagerIpServiceClient is the client API for ResourceManagerIpService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ResourceManagerIpServiceClient interface {
	GetIpResourceList(ctx context.Context, in *IpListRequest, opts ...grpc.CallOption) (*IpResourceListResponse, error)
	GetIpResourceDetail(ctx context.Context, in *CommonIdParam, opts ...grpc.CallOption) (*IpResourceDetailResponse, error)
	AddIpResource(ctx context.Context, in *IpAddRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	UpdateIpResource(ctx context.Context, in *IpUpdateRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	DeleteIpResource(ctx context.Context, in *CommonIdParam, opts ...grpc.CallOption) (*CommonResponse, error)
}

type resourceManagerIpServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewResourceManagerIpServiceClient(cc grpc.ClientConnInterface) ResourceManagerIpServiceClient {
	return &resourceManagerIpServiceClient{cc}
}

func (c *resourceManagerIpServiceClient) GetIpResourceList(ctx context.Context, in *IpListRequest, opts ...grpc.CallOption) (*IpResourceListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IpResourceListResponse)
	err := c.cc.Invoke(ctx, ResourceManagerIpService_GetIpResourceList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerIpServiceClient) GetIpResourceDetail(ctx context.Context, in *CommonIdParam, opts ...grpc.CallOption) (*IpResourceDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IpResourceDetailResponse)
	err := c.cc.Invoke(ctx, ResourceManagerIpService_GetIpResourceDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerIpServiceClient) AddIpResource(ctx context.Context, in *IpAddRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, ResourceManagerIpService_AddIpResource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerIpServiceClient) UpdateIpResource(ctx context.Context, in *IpUpdateRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, ResourceManagerIpService_UpdateIpResource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerIpServiceClient) DeleteIpResource(ctx context.Context, in *CommonIdParam, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, ResourceManagerIpService_DeleteIpResource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ResourceManagerIpServiceServer is the server API for ResourceManagerIpService service.
// All implementations must embed UnimplementedResourceManagerIpServiceServer
// for forward compatibility.
type ResourceManagerIpServiceServer interface {
	GetIpResourceList(context.Context, *IpListRequest) (*IpResourceListResponse, error)
	GetIpResourceDetail(context.Context, *CommonIdParam) (*IpResourceDetailResponse, error)
	AddIpResource(context.Context, *IpAddRequest) (*CommonResponse, error)
	UpdateIpResource(context.Context, *IpUpdateRequest) (*CommonResponse, error)
	DeleteIpResource(context.Context, *CommonIdParam) (*CommonResponse, error)
	mustEmbedUnimplementedResourceManagerIpServiceServer()
}

// UnimplementedResourceManagerIpServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedResourceManagerIpServiceServer struct{}

func (UnimplementedResourceManagerIpServiceServer) GetIpResourceList(context.Context, *IpListRequest) (*IpResourceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIpResourceList not implemented")
}
func (UnimplementedResourceManagerIpServiceServer) GetIpResourceDetail(context.Context, *CommonIdParam) (*IpResourceDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIpResourceDetail not implemented")
}
func (UnimplementedResourceManagerIpServiceServer) AddIpResource(context.Context, *IpAddRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddIpResource not implemented")
}
func (UnimplementedResourceManagerIpServiceServer) UpdateIpResource(context.Context, *IpUpdateRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIpResource not implemented")
}
func (UnimplementedResourceManagerIpServiceServer) DeleteIpResource(context.Context, *CommonIdParam) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIpResource not implemented")
}
func (UnimplementedResourceManagerIpServiceServer) mustEmbedUnimplementedResourceManagerIpServiceServer() {
}
func (UnimplementedResourceManagerIpServiceServer) testEmbeddedByValue() {}

// UnsafeResourceManagerIpServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ResourceManagerIpServiceServer will
// result in compilation errors.
type UnsafeResourceManagerIpServiceServer interface {
	mustEmbedUnimplementedResourceManagerIpServiceServer()
}

func RegisterResourceManagerIpServiceServer(s grpc.ServiceRegistrar, srv ResourceManagerIpServiceServer) {
	// If the following call pancis, it indicates UnimplementedResourceManagerIpServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ResourceManagerIpService_ServiceDesc, srv)
}

func _ResourceManagerIpService_GetIpResourceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IpListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerIpServiceServer).GetIpResourceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceManagerIpService_GetIpResourceList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerIpServiceServer).GetIpResourceList(ctx, req.(*IpListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManagerIpService_GetIpResourceDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonIdParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerIpServiceServer).GetIpResourceDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceManagerIpService_GetIpResourceDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerIpServiceServer).GetIpResourceDetail(ctx, req.(*CommonIdParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManagerIpService_AddIpResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IpAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerIpServiceServer).AddIpResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceManagerIpService_AddIpResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerIpServiceServer).AddIpResource(ctx, req.(*IpAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManagerIpService_UpdateIpResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IpUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerIpServiceServer).UpdateIpResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceManagerIpService_UpdateIpResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerIpServiceServer).UpdateIpResource(ctx, req.(*IpUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManagerIpService_DeleteIpResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonIdParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerIpServiceServer).DeleteIpResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceManagerIpService_DeleteIpResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerIpServiceServer).DeleteIpResource(ctx, req.(*CommonIdParam))
	}
	return interceptor(ctx, in, info, handler)
}

// ResourceManagerIpService_ServiceDesc is the grpc.ServiceDesc for ResourceManagerIpService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ResourceManagerIpService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "resource_manager.v1.ResourceManagerIpService",
	HandlerType: (*ResourceManagerIpServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetIpResourceList",
			Handler:    _ResourceManagerIpService_GetIpResourceList_Handler,
		},
		{
			MethodName: "GetIpResourceDetail",
			Handler:    _ResourceManagerIpService_GetIpResourceDetail_Handler,
		},
		{
			MethodName: "AddIpResource",
			Handler:    _ResourceManagerIpService_AddIpResource_Handler,
		},
		{
			MethodName: "UpdateIpResource",
			Handler:    _ResourceManagerIpService_UpdateIpResource_Handler,
		},
		{
			MethodName: "DeleteIpResource",
			Handler:    _ResourceManagerIpService_DeleteIpResource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/resource_manager/v1/ip.proto",
}
