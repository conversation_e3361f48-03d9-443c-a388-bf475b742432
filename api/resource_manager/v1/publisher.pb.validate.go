// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/resource_manager/v1/publisher.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CrOrUpPublisherRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CrOrUpPublisherRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CrOrUpPublisherRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CrOrUpPublisherRequestMultiError, or nil if none found.
func (m *CrOrUpPublisherRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CrOrUpPublisherRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CrOrUpPublisherRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CrOrUpPublisherRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CrOrUpPublisherRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CrOrUpPublisherRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CrOrUpPublisherRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CrOrUpPublisherRequestValidationError{
				field:  "AffiliateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetContact() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CrOrUpPublisherRequestValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CrOrUpPublisherRequestValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CrOrUpPublisherRequestValidationError{
					field:  fmt.Sprintf("Contact[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CrOrUpPublisherRequestMultiError(errors)
	}

	return nil
}

// CrOrUpPublisherRequestMultiError is an error wrapping multiple validation
// errors returned by CrOrUpPublisherRequest.ValidateAll() if the designated
// constraints aren't met.
type CrOrUpPublisherRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CrOrUpPublisherRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CrOrUpPublisherRequestMultiError) AllErrors() []error { return m }

// CrOrUpPublisherRequestValidationError is the validation error returned by
// CrOrUpPublisherRequest.Validate if the designated constraints aren't met.
type CrOrUpPublisherRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CrOrUpPublisherRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CrOrUpPublisherRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CrOrUpPublisherRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CrOrUpPublisherRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CrOrUpPublisherRequestValidationError) ErrorName() string {
	return "CrOrUpPublisherRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CrOrUpPublisherRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCrOrUpPublisherRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CrOrUpPublisherRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CrOrUpPublisherRequestValidationError{}

// Validate checks the field values on PublisherContactInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PublisherContactInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublisherContactInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublisherContactInfoMultiError, or nil if none found.
func (m *PublisherContactInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PublisherContactInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Value

	// no validation rules for DisplayName

	if len(errors) > 0 {
		return PublisherContactInfoMultiError(errors)
	}

	return nil
}

// PublisherContactInfoMultiError is an error wrapping multiple validation
// errors returned by PublisherContactInfo.ValidateAll() if the designated
// constraints aren't met.
type PublisherContactInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublisherContactInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublisherContactInfoMultiError) AllErrors() []error { return m }

// PublisherContactInfoValidationError is the validation error returned by
// PublisherContactInfo.Validate if the designated constraints aren't met.
type PublisherContactInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublisherContactInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublisherContactInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublisherContactInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublisherContactInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublisherContactInfoValidationError) ErrorName() string {
	return "PublisherContactInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PublisherContactInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublisherContactInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublisherContactInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublisherContactInfoValidationError{}

// Validate checks the field values on DeletePublisherRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePublisherRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePublisherRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePublisherRequestMultiError, or nil if none found.
func (m *DeletePublisherRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePublisherRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeletePublisherRequestMultiError(errors)
	}

	return nil
}

// DeletePublisherRequestMultiError is an error wrapping multiple validation
// errors returned by DeletePublisherRequest.ValidateAll() if the designated
// constraints aren't met.
type DeletePublisherRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePublisherRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePublisherRequestMultiError) AllErrors() []error { return m }

// DeletePublisherRequestValidationError is the validation error returned by
// DeletePublisherRequest.Validate if the designated constraints aren't met.
type DeletePublisherRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePublisherRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePublisherRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePublisherRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePublisherRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePublisherRequestValidationError) ErrorName() string {
	return "DeletePublisherRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePublisherRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePublisherRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePublisherRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePublisherRequestValidationError{}

// Validate checks the field values on PublisherResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PublisherResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublisherResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublisherResourceMultiError, or nil if none found.
func (m *PublisherResource) ValidateAll() error {
	return m.validate(true)
}

func (m *PublisherResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	for idx, item := range m.GetCountryTop() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherResourceValidationError{
						field:  fmt.Sprintf("CountryTop[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherResourceValidationError{
						field:  fmt.Sprintf("CountryTop[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherResourceValidationError{
					field:  fmt.Sprintf("CountryTop[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Traffic

	// no validation rules for Introduction

	if all {
		switch v := interface{}(m.GetGenderRatio()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublisherResourceValidationError{
					field:  "GenderRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublisherResourceValidationError{
					field:  "GenderRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGenderRatio()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublisherResourceValidationError{
				field:  "GenderRatio",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherResourceValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UpdateTime

	// no validation rules for LogoUrl

	// no validation rules for PublisherPlat

	// no validation rules for PublisherPlatId

	// no validation rules for PublisherPlatName

	// no validation rules for Size

	for idx, item := range m.GetLinks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherResourceValidationError{
						field:  fmt.Sprintf("Links[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherResourceValidationError{
						field:  fmt.Sprintf("Links[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherResourceValidationError{
					field:  fmt.Sprintf("Links[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Type

	// no validation rules for MediaViscosity

	if len(errors) > 0 {
		return PublisherResourceMultiError(errors)
	}

	return nil
}

// PublisherResourceMultiError is an error wrapping multiple validation errors
// returned by PublisherResource.ValidateAll() if the designated constraints
// aren't met.
type PublisherResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublisherResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublisherResourceMultiError) AllErrors() []error { return m }

// PublisherResourceValidationError is the validation error returned by
// PublisherResource.Validate if the designated constraints aren't met.
type PublisherResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublisherResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublisherResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublisherResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublisherResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublisherResourceValidationError) ErrorName() string {
	return "PublisherResourceValidationError"
}

// Error satisfies the builtin error interface
func (e PublisherResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublisherResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublisherResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublisherResourceValidationError{}

// Validate checks the field values on PublisherLink with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublisherLink) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublisherLink with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublisherLinkMultiError, or
// nil if none found.
func (m *PublisherLink) ValidateAll() error {
	return m.validate(true)
}

func (m *PublisherLink) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Link

	// no validation rules for Fans

	// no validation rules for MonthlyVisits

	if m.LinkType != nil {
		// no validation rules for LinkType
	}

	if m.WebsiteIndicator != nil {

		if all {
			switch v := interface{}(m.GetWebsiteIndicator()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherLinkValidationError{
						field:  "WebsiteIndicator",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherLinkValidationError{
						field:  "WebsiteIndicator",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWebsiteIndicator()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherLinkValidationError{
					field:  "WebsiteIndicator",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.MediaIndicator != nil {

		if all {
			switch v := interface{}(m.GetMediaIndicator()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherLinkValidationError{
						field:  "MediaIndicator",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherLinkValidationError{
						field:  "MediaIndicator",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMediaIndicator()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherLinkValidationError{
					field:  "MediaIndicator",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Indicator != nil {

		if all {
			switch v := interface{}(m.GetIndicator()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherLinkValidationError{
						field:  "Indicator",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherLinkValidationError{
						field:  "Indicator",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIndicator()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherLinkValidationError{
					field:  "Indicator",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PublisherLinkMultiError(errors)
	}

	return nil
}

// PublisherLinkMultiError is an error wrapping multiple validation errors
// returned by PublisherLink.ValidateAll() if the designated constraints
// aren't met.
type PublisherLinkMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublisherLinkMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublisherLinkMultiError) AllErrors() []error { return m }

// PublisherLinkValidationError is the validation error returned by
// PublisherLink.Validate if the designated constraints aren't met.
type PublisherLinkValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublisherLinkValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublisherLinkValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublisherLinkValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublisherLinkValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublisherLinkValidationError) ErrorName() string { return "PublisherLinkValidationError" }

// Error satisfies the builtin error interface
func (e PublisherLinkValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublisherLink.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublisherLinkValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublisherLinkValidationError{}

// Validate checks the field values on WebsiteIndicator with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WebsiteIndicator) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebsiteIndicator with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebsiteIndicatorMultiError, or nil if none found.
func (m *WebsiteIndicator) ValidateAll() error {
	return m.validate(true)
}

func (m *WebsiteIndicator) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MozSpamScore

	// no validation rules for MozDomainAuthority

	// no validation rules for SimilarWebGlobalRank

	// no validation rules for SimilarWebVerified

	if len(errors) > 0 {
		return WebsiteIndicatorMultiError(errors)
	}

	return nil
}

// WebsiteIndicatorMultiError is an error wrapping multiple validation errors
// returned by WebsiteIndicator.ValidateAll() if the designated constraints
// aren't met.
type WebsiteIndicatorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebsiteIndicatorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebsiteIndicatorMultiError) AllErrors() []error { return m }

// WebsiteIndicatorValidationError is the validation error returned by
// WebsiteIndicator.Validate if the designated constraints aren't met.
type WebsiteIndicatorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebsiteIndicatorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebsiteIndicatorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebsiteIndicatorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebsiteIndicatorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebsiteIndicatorValidationError) ErrorName() string { return "WebsiteIndicatorValidationError" }

// Error satisfies the builtin error interface
func (e WebsiteIndicatorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebsiteIndicator.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebsiteIndicatorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebsiteIndicatorValidationError{}

// Validate checks the field values on MediaIndicator with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MediaIndicator) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MediaIndicator with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MediaIndicatorMultiError,
// or nil if none found.
func (m *MediaIndicator) ValidateAll() error {
	return m.validate(true)
}

func (m *MediaIndicator) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AverageViews

	// no validation rules for AverageShare

	// no validation rules for AverageLikes

	// no validation rules for AverageReviews

	// no validation rules for AverageInteractionRate

	// no validation rules for AverageFavorites

	if len(errors) > 0 {
		return MediaIndicatorMultiError(errors)
	}

	return nil
}

// MediaIndicatorMultiError is an error wrapping multiple validation errors
// returned by MediaIndicator.ValidateAll() if the designated constraints
// aren't met.
type MediaIndicatorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MediaIndicatorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MediaIndicatorMultiError) AllErrors() []error { return m }

// MediaIndicatorValidationError is the validation error returned by
// MediaIndicator.Validate if the designated constraints aren't met.
type MediaIndicatorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MediaIndicatorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MediaIndicatorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MediaIndicatorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MediaIndicatorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MediaIndicatorValidationError) ErrorName() string { return "MediaIndicatorValidationError" }

// Error satisfies the builtin error interface
func (e MediaIndicatorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMediaIndicator.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MediaIndicatorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MediaIndicatorValidationError{}

// Validate checks the field values on Indicator with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Indicator) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Indicator with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IndicatorMultiError, or nil
// if none found.
func (m *Indicator) ValidateAll() error {
	return m.validate(true)
}

func (m *Indicator) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MozSpamScore

	// no validation rules for MozDomainAuthority

	// no validation rules for SimilarWebGlobalRank

	// no validation rules for SimilarWebVerified

	// no validation rules for AverageViews

	// no validation rules for AverageShare

	// no validation rules for AverageLikes

	// no validation rules for AverageReviews

	// no validation rules for AverageInteractionRate

	// no validation rules for AverageFavorites

	if len(errors) > 0 {
		return IndicatorMultiError(errors)
	}

	return nil
}

// IndicatorMultiError is an error wrapping multiple validation errors returned
// by Indicator.ValidateAll() if the designated constraints aren't met.
type IndicatorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndicatorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndicatorMultiError) AllErrors() []error { return m }

// IndicatorValidationError is the validation error returned by
// Indicator.Validate if the designated constraints aren't met.
type IndicatorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndicatorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndicatorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndicatorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndicatorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndicatorValidationError) ErrorName() string { return "IndicatorValidationError" }

// Error satisfies the builtin error interface
func (e IndicatorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndicator.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndicatorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndicatorValidationError{}

// Validate checks the field values on PublisherData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublisherData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublisherData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublisherDataMultiError, or
// nil if none found.
func (m *PublisherData) ValidateAll() error {
	return m.validate(true)
}

func (m *PublisherData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublisherDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublisherDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublisherDataValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublisherDataValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublisherDataValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublisherDataValidationError{
				field:  "AffiliateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetContactInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherDataValidationError{
						field:  fmt.Sprintf("ContactInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherDataValidationError{
						field:  fmt.Sprintf("ContactInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherDataValidationError{
					field:  fmt.Sprintf("ContactInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PublisherDataMultiError(errors)
	}

	return nil
}

// PublisherDataMultiError is an error wrapping multiple validation errors
// returned by PublisherData.ValidateAll() if the designated constraints
// aren't met.
type PublisherDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublisherDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublisherDataMultiError) AllErrors() []error { return m }

// PublisherDataValidationError is the validation error returned by
// PublisherData.Validate if the designated constraints aren't met.
type PublisherDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublisherDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublisherDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublisherDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublisherDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublisherDataValidationError) ErrorName() string { return "PublisherDataValidationError" }

// Error satisfies the builtin error interface
func (e PublisherDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublisherData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublisherDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublisherDataValidationError{}

// Validate checks the field values on GetPublisherRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPublisherRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPublisherRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPublisherRequestMultiError, or nil if none found.
func (m *GetPublisherRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPublisherRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetPublisherRequestMultiError(errors)
	}

	return nil
}

// GetPublisherRequestMultiError is an error wrapping multiple validation
// errors returned by GetPublisherRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPublisherRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPublisherRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPublisherRequestMultiError) AllErrors() []error { return m }

// GetPublisherRequestValidationError is the validation error returned by
// GetPublisherRequest.Validate if the designated constraints aren't met.
type GetPublisherRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPublisherRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPublisherRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPublisherRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPublisherRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPublisherRequestValidationError) ErrorName() string {
	return "GetPublisherRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPublisherRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPublisherRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPublisherRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPublisherRequestValidationError{}

// Validate checks the field values on GetPublisherReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPublisherReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPublisherReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPublisherReplyMultiError, or nil if none found.
func (m *GetPublisherReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPublisherReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPublisherReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPublisherReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPublisherReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPublisherReplyMultiError(errors)
	}

	return nil
}

// GetPublisherReplyMultiError is an error wrapping multiple validation errors
// returned by GetPublisherReply.ValidateAll() if the designated constraints
// aren't met.
type GetPublisherReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPublisherReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPublisherReplyMultiError) AllErrors() []error { return m }

// GetPublisherReplyValidationError is the validation error returned by
// GetPublisherReply.Validate if the designated constraints aren't met.
type GetPublisherReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPublisherReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPublisherReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPublisherReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPublisherReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPublisherReplyValidationError) ErrorName() string {
	return "GetPublisherReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetPublisherReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPublisherReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPublisherReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPublisherReplyValidationError{}

// Validate checks the field values on PublisherListItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PublisherListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublisherListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublisherListItemMultiError, or nil if none found.
func (m *PublisherListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PublisherListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	// no validation rules for Introduction

	// no validation rules for Size

	// no validation rules for Type

	// no validation rules for LogoUrl

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherListItemValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherListItemValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherListItemValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PublisherPlatformName

	// no validation rules for PublisherPlatform

	if len(errors) > 0 {
		return PublisherListItemMultiError(errors)
	}

	return nil
}

// PublisherListItemMultiError is an error wrapping multiple validation errors
// returned by PublisherListItem.ValidateAll() if the designated constraints
// aren't met.
type PublisherListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublisherListItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublisherListItemMultiError) AllErrors() []error { return m }

// PublisherListItemValidationError is the validation error returned by
// PublisherListItem.Validate if the designated constraints aren't met.
type PublisherListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublisherListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublisherListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublisherListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublisherListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublisherListItemValidationError) ErrorName() string {
	return "PublisherListItemValidationError"
}

// Error satisfies the builtin error interface
func (e PublisherListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublisherListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublisherListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublisherListItemValidationError{}

// Validate checks the field values on PublisherList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublisherList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublisherList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublisherListMultiError, or
// nil if none found.
func (m *PublisherList) ValidateAll() error {
	return m.validate(true)
}

func (m *PublisherList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublisherListValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublisherListValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublisherListValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublisherListValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublisherListValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublisherListValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PublisherListMultiError(errors)
	}

	return nil
}

// PublisherListMultiError is an error wrapping multiple validation errors
// returned by PublisherList.ValidateAll() if the designated constraints
// aren't met.
type PublisherListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublisherListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublisherListMultiError) AllErrors() []error { return m }

// PublisherListValidationError is the validation error returned by
// PublisherList.Validate if the designated constraints aren't met.
type PublisherListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublisherListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublisherListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublisherListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublisherListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublisherListValidationError) ErrorName() string { return "PublisherListValidationError" }

// Error satisfies the builtin error interface
func (e PublisherListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublisherList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublisherListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublisherListValidationError{}

// Validate checks the field values on ListPublisherRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPublisherRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPublisherRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPublisherRequestMultiError, or nil if none found.
func (m *ListPublisherRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPublisherRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPublisherRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPublisherRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPublisherRequestValidationError{
				field:  "BaseParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPublisherRequestValidationError{
					field:  "AffiliateParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPublisherRequestValidationError{
					field:  "AffiliateParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPublisherRequestValidationError{
				field:  "AffiliateParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPublisherRequestValidationError{
					field:  "OrderParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPublisherRequestValidationError{
					field:  "OrderParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPublisherRequestValidationError{
				field:  "OrderParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MinFans

	// no validation rules for MaxFans

	// no validation rules for MinMonthlyVisits

	// no validation rules for MaxMonthlyVisits

	for idx, item := range m.GetCountryTop() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPublisherRequestValidationError{
						field:  fmt.Sprintf("CountryTop[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPublisherRequestValidationError{
						field:  fmt.Sprintf("CountryTop[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPublisherRequestValidationError{
					field:  fmt.Sprintf("CountryTop[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PublisherLink

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return ListPublisherRequestMultiError(errors)
	}

	return nil
}

// ListPublisherRequestMultiError is an error wrapping multiple validation
// errors returned by ListPublisherRequest.ValidateAll() if the designated
// constraints aren't met.
type ListPublisherRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPublisherRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPublisherRequestMultiError) AllErrors() []error { return m }

// ListPublisherRequestValidationError is the validation error returned by
// ListPublisherRequest.Validate if the designated constraints aren't met.
type ListPublisherRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPublisherRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPublisherRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPublisherRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPublisherRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPublisherRequestValidationError) ErrorName() string {
	return "ListPublisherRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListPublisherRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPublisherRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPublisherRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPublisherRequestValidationError{}

// Validate checks the field values on ListPublisherReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPublisherReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPublisherReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPublisherReplyMultiError, or nil if none found.
func (m *ListPublisherReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPublisherReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPublisherReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPublisherReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPublisherReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListPublisherReplyMultiError(errors)
	}

	return nil
}

// ListPublisherReplyMultiError is an error wrapping multiple validation errors
// returned by ListPublisherReply.ValidateAll() if the designated constraints
// aren't met.
type ListPublisherReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPublisherReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPublisherReplyMultiError) AllErrors() []error { return m }

// ListPublisherReplyValidationError is the validation error returned by
// ListPublisherReply.Validate if the designated constraints aren't met.
type ListPublisherReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPublisherReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPublisherReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPublisherReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPublisherReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPublisherReplyValidationError) ErrorName() string {
	return "ListPublisherReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListPublisherReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPublisherReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPublisherReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPublisherReplyValidationError{}
