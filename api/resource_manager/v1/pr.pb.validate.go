// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/resource_manager/v1/pr.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PrListRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PrListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrListRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PrListRequestMultiError, or
// nil if none found.
func (m *PrListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PrListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrListRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrListRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrListRequestValidationError{
				field:  "BaseParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrListRequestValidationError{
					field:  "BrandParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrListRequestValidationError{
					field:  "BrandParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrListRequestValidationError{
				field:  "BrandParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrListRequestValidationError{
					field:  "AffiliateParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrListRequestValidationError{
					field:  "AffiliateParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrListRequestValidationError{
				field:  "AffiliateParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return PrListRequestMultiError(errors)
	}

	return nil
}

// PrListRequestMultiError is an error wrapping multiple validation errors
// returned by PrListRequest.ValidateAll() if the designated constraints
// aren't met.
type PrListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrListRequestMultiError) AllErrors() []error { return m }

// PrListRequestValidationError is the validation error returned by
// PrListRequest.Validate if the designated constraints aren't met.
type PrListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrListRequestValidationError) ErrorName() string { return "PrListRequestValidationError" }

// Error satisfies the builtin error interface
func (e PrListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrListRequestValidationError{}

// Validate checks the field values on PrResource with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PrResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrResource with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PrResourceMultiError, or
// nil if none found.
func (m *PrResource) ValidateAll() error {
	return m.validate(true)
}

func (m *PrResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	// no validation rules for Homepage

	// no validation rules for Introduction

	// no validation rules for Size

	// no validation rules for MonthlyVisit

	if all {
		switch v := interface{}(m.GetGenderRatio()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrResourceValidationError{
					field:  "GenderRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrResourceValidationError{
					field:  "GenderRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGenderRatio()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrResourceValidationError{
				field:  "GenderRatio",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IntroductionDate

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrResourceValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UpdateTime

	// no validation rules for ProductType

	// no validation rules for IncludeStatus

	// no validation rules for HasTranslate

	// no validation rules for HasBottomInfo

	// no validation rules for ImageCost

	// no validation rules for ProductDetail

	// no validation rules for ServiceProcess

	// no validation rules for SpecificRequirement

	if m.ReleaseSpeed != nil {

		if all {
			switch v := interface{}(m.GetReleaseSpeed()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrResourceValidationError{
						field:  "ReleaseSpeed",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrResourceValidationError{
						field:  "ReleaseSpeed",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReleaseSpeed()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrResourceValidationError{
					field:  "ReleaseSpeed",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PrResourceMultiError(errors)
	}

	return nil
}

// PrResourceMultiError is an error wrapping multiple validation errors
// returned by PrResource.ValidateAll() if the designated constraints aren't met.
type PrResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrResourceMultiError) AllErrors() []error { return m }

// PrResourceValidationError is the validation error returned by
// PrResource.Validate if the designated constraints aren't met.
type PrResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrResourceValidationError) ErrorName() string { return "PrResourceValidationError" }

// Error satisfies the builtin error interface
func (e PrResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrResourceValidationError{}

// Validate checks the field values on PrListData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PrListData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrListData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PrListDataMultiError, or
// nil if none found.
func (m *PrListData) ValidateAll() error {
	return m.validate(true)
}

func (m *PrListData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrListDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrListDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrListDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PrListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PrListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PrListDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PrListDataMultiError(errors)
	}

	return nil
}

// PrListDataMultiError is an error wrapping multiple validation errors
// returned by PrListData.ValidateAll() if the designated constraints aren't met.
type PrListDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrListDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrListDataMultiError) AllErrors() []error { return m }

// PrListDataValidationError is the validation error returned by
// PrListData.Validate if the designated constraints aren't met.
type PrListDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrListDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrListDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrListDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrListDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrListDataValidationError) ErrorName() string { return "PrListDataValidationError" }

// Error satisfies the builtin error interface
func (e PrListDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrListData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrListDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrListDataValidationError{}

// Validate checks the field values on PrResourceListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PrResourceListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrResourceListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PrResourceListResponseMultiError, or nil if none found.
func (m *PrResourceListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PrResourceListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrResourceListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrResourceListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrResourceListResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PrResourceListResponseMultiError(errors)
	}

	return nil
}

// PrResourceListResponseMultiError is an error wrapping multiple validation
// errors returned by PrResourceListResponse.ValidateAll() if the designated
// constraints aren't met.
type PrResourceListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrResourceListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrResourceListResponseMultiError) AllErrors() []error { return m }

// PrResourceListResponseValidationError is the validation error returned by
// PrResourceListResponse.Validate if the designated constraints aren't met.
type PrResourceListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrResourceListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrResourceListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrResourceListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrResourceListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrResourceListResponseValidationError) ErrorName() string {
	return "PrResourceListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PrResourceListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrResourceListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrResourceListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrResourceListResponseValidationError{}

// Validate checks the field values on PrData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PrData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PrDataMultiError, or nil if none found.
func (m *PrData) ValidateAll() error {
	return m.validate(true)
}

func (m *PrData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrDataValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrDataValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrDataValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrDataValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrDataValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrDataValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrDataValidationError{
				field:  "AffiliateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PrDataMultiError(errors)
	}

	return nil
}

// PrDataMultiError is an error wrapping multiple validation errors returned by
// PrData.ValidateAll() if the designated constraints aren't met.
type PrDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrDataMultiError) AllErrors() []error { return m }

// PrDataValidationError is the validation error returned by PrData.Validate if
// the designated constraints aren't met.
type PrDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrDataValidationError) ErrorName() string { return "PrDataValidationError" }

// Error satisfies the builtin error interface
func (e PrDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrDataValidationError{}

// Validate checks the field values on PrResourceDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PrResourceDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrResourceDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PrResourceDetailResponseMultiError, or nil if none found.
func (m *PrResourceDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PrResourceDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrResourceDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrResourceDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrResourceDetailResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PrResourceDetailResponseMultiError(errors)
	}

	return nil
}

// PrResourceDetailResponseMultiError is an error wrapping multiple validation
// errors returned by PrResourceDetailResponse.ValidateAll() if the designated
// constraints aren't met.
type PrResourceDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrResourceDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrResourceDetailResponseMultiError) AllErrors() []error { return m }

// PrResourceDetailResponseValidationError is the validation error returned by
// PrResourceDetailResponse.Validate if the designated constraints aren't met.
type PrResourceDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrResourceDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrResourceDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrResourceDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrResourceDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrResourceDetailResponseValidationError) ErrorName() string {
	return "PrResourceDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PrResourceDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrResourceDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrResourceDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrResourceDetailResponseValidationError{}

// Validate checks the field values on PrAddRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PrAddRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrAddRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PrAddRequestMultiError, or
// nil if none found.
func (m *PrAddRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PrAddRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrAddRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrAddRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrAddRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrAddRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrAddRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrAddRequestValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrAddRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrAddRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrAddRequestValidationError{
				field:  "AffiliateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PrAddRequestMultiError(errors)
	}

	return nil
}

// PrAddRequestMultiError is an error wrapping multiple validation errors
// returned by PrAddRequest.ValidateAll() if the designated constraints aren't met.
type PrAddRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrAddRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrAddRequestMultiError) AllErrors() []error { return m }

// PrAddRequestValidationError is the validation error returned by
// PrAddRequest.Validate if the designated constraints aren't met.
type PrAddRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrAddRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrAddRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrAddRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrAddRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrAddRequestValidationError) ErrorName() string { return "PrAddRequestValidationError" }

// Error satisfies the builtin error interface
func (e PrAddRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrAddRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrAddRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrAddRequestValidationError{}

// Validate checks the field values on PrUpdateRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PrUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrUpdateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PrUpdateRequestMultiError, or nil if none found.
func (m *PrUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PrUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrUpdateRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrUpdateRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrUpdateRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrUpdateRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrUpdateRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrUpdateRequestValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrUpdateRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrUpdateRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrUpdateRequestValidationError{
				field:  "AffiliateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PrUpdateRequestMultiError(errors)
	}

	return nil
}

// PrUpdateRequestMultiError is an error wrapping multiple validation errors
// returned by PrUpdateRequest.ValidateAll() if the designated constraints
// aren't met.
type PrUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrUpdateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrUpdateRequestMultiError) AllErrors() []error { return m }

// PrUpdateRequestValidationError is the validation error returned by
// PrUpdateRequest.Validate if the designated constraints aren't met.
type PrUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrUpdateRequestValidationError) ErrorName() string { return "PrUpdateRequestValidationError" }

// Error satisfies the builtin error interface
func (e PrUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrUpdateRequestValidationError{}
