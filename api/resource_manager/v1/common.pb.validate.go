// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/resource_manager/v1/common.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on BrandParam with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BrandParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BrandParam with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BrandParamMultiError, or
// nil if none found.
func (m *BrandParam) ValidateAll() error {
	return m.validate(true)
}

func (m *BrandParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ContactType

	if len(errors) > 0 {
		return BrandParamMultiError(errors)
	}

	return nil
}

// BrandParamMultiError is an error wrapping multiple validation errors
// returned by BrandParam.ValidateAll() if the designated constraints aren't met.
type BrandParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BrandParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BrandParamMultiError) AllErrors() []error { return m }

// BrandParamValidationError is the validation error returned by
// BrandParam.Validate if the designated constraints aren't met.
type BrandParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BrandParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BrandParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BrandParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BrandParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BrandParamValidationError) ErrorName() string { return "BrandParamValidationError" }

// Error satisfies the builtin error interface
func (e BrandParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBrandParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BrandParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BrandParamValidationError{}

// Validate checks the field values on AffiliateParam with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AffiliateParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AffiliateParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AffiliateParamMultiError,
// or nil if none found.
func (m *AffiliateParam) ValidateAll() error {
	return m.validate(true)
}

func (m *AffiliateParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AffPlatformPublisherId

	// no validation rules for AffPlatformPublisherName

	// no validation rules for Size

	if len(errors) > 0 {
		return AffiliateParamMultiError(errors)
	}

	return nil
}

// AffiliateParamMultiError is an error wrapping multiple validation errors
// returned by AffiliateParam.ValidateAll() if the designated constraints
// aren't met.
type AffiliateParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AffiliateParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AffiliateParamMultiError) AllErrors() []error { return m }

// AffiliateParamValidationError is the validation error returned by
// AffiliateParam.Validate if the designated constraints aren't met.
type AffiliateParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AffiliateParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AffiliateParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AffiliateParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AffiliateParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AffiliateParamValidationError) ErrorName() string { return "AffiliateParamValidationError" }

// Error satisfies the builtin error interface
func (e AffiliateParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAffiliateParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AffiliateParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AffiliateParamValidationError{}

// Validate checks the field values on GenderRatio with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GenderRatio) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenderRatio with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GenderRatioMultiError, or
// nil if none found.
func (m *GenderRatio) ValidateAll() error {
	return m.validate(true)
}

func (m *GenderRatio) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Male

	// no validation rules for Female

	if len(errors) > 0 {
		return GenderRatioMultiError(errors)
	}

	return nil
}

// GenderRatioMultiError is an error wrapping multiple validation errors
// returned by GenderRatio.ValidateAll() if the designated constraints aren't met.
type GenderRatioMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenderRatioMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenderRatioMultiError) AllErrors() []error { return m }

// GenderRatioValidationError is the validation error returned by
// GenderRatio.Validate if the designated constraints aren't met.
type GenderRatioValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenderRatioValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenderRatioValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenderRatioValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenderRatioValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenderRatioValidationError) ErrorName() string { return "GenderRatioValidationError" }

// Error satisfies the builtin error interface
func (e GenderRatioValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenderRatio.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenderRatioValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenderRatioValidationError{}

// Validate checks the field values on BrandTagInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BrandTagInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BrandTagInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BrandTagInfoMultiError, or
// nil if none found.
func (m *BrandTagInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BrandTagInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BrandTagInfoMultiError(errors)
	}

	return nil
}

// BrandTagInfoMultiError is an error wrapping multiple validation errors
// returned by BrandTagInfo.ValidateAll() if the designated constraints aren't met.
type BrandTagInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BrandTagInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BrandTagInfoMultiError) AllErrors() []error { return m }

// BrandTagInfoValidationError is the validation error returned by
// BrandTagInfo.Validate if the designated constraints aren't met.
type BrandTagInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BrandTagInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BrandTagInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BrandTagInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BrandTagInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BrandTagInfoValidationError) ErrorName() string { return "BrandTagInfoValidationError" }

// Error satisfies the builtin error interface
func (e BrandTagInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBrandTagInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BrandTagInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BrandTagInfoValidationError{}

// Validate checks the field values on BrandInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BrandInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BrandInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BrandInfoMultiError, or nil
// if none found.
func (m *BrandInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BrandInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBusinessInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BrandInfoValidationError{
						field:  fmt.Sprintf("BusinessInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BrandInfoValidationError{
						field:  fmt.Sprintf("BusinessInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BrandInfoValidationError{
					field:  fmt.Sprintf("BusinessInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTagInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BrandInfoValidationError{
					field:  "TagInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BrandInfoValidationError{
					field:  "TagInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTagInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BrandInfoValidationError{
				field:  "TagInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BrandInfoMultiError(errors)
	}

	return nil
}

// BrandInfoMultiError is an error wrapping multiple validation errors returned
// by BrandInfo.ValidateAll() if the designated constraints aren't met.
type BrandInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BrandInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BrandInfoMultiError) AllErrors() []error { return m }

// BrandInfoValidationError is the validation error returned by
// BrandInfo.Validate if the designated constraints aren't met.
type BrandInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BrandInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BrandInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BrandInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BrandInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BrandInfoValidationError) ErrorName() string { return "BrandInfoValidationError" }

// Error satisfies the builtin error interface
func (e BrandInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBrandInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BrandInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BrandInfoValidationError{}

// Validate checks the field values on AffiliateChannelInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AffiliateChannelInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AffiliateChannelInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AffiliateChannelInfoMultiError, or nil if none found.
func (m *AffiliateChannelInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AffiliateChannelInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerJourneyStage

	// no validation rules for Size

	// no validation rules for MediaViscosity

	if len(errors) > 0 {
		return AffiliateChannelInfoMultiError(errors)
	}

	return nil
}

// AffiliateChannelInfoMultiError is an error wrapping multiple validation
// errors returned by AffiliateChannelInfo.ValidateAll() if the designated
// constraints aren't met.
type AffiliateChannelInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AffiliateChannelInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AffiliateChannelInfoMultiError) AllErrors() []error { return m }

// AffiliateChannelInfoValidationError is the validation error returned by
// AffiliateChannelInfo.Validate if the designated constraints aren't met.
type AffiliateChannelInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AffiliateChannelInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AffiliateChannelInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AffiliateChannelInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AffiliateChannelInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AffiliateChannelInfoValidationError) ErrorName() string {
	return "AffiliateChannelInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AffiliateChannelInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAffiliateChannelInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AffiliateChannelInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AffiliateChannelInfoValidationError{}

// Validate checks the field values on PlatformInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PlatformInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlatformInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PlatformInfoMultiError, or
// nil if none found.
func (m *PlatformInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PlatformInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AffPlatformName

	// no validation rules for AffPlatformPublisherId

	// no validation rules for AffPlatformPublisherName

	if len(errors) > 0 {
		return PlatformInfoMultiError(errors)
	}

	return nil
}

// PlatformInfoMultiError is an error wrapping multiple validation errors
// returned by PlatformInfo.ValidateAll() if the designated constraints aren't met.
type PlatformInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlatformInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlatformInfoMultiError) AllErrors() []error { return m }

// PlatformInfoValidationError is the validation error returned by
// PlatformInfo.Validate if the designated constraints aren't met.
type PlatformInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlatformInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlatformInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlatformInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlatformInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlatformInfoValidationError) ErrorName() string { return "PlatformInfoValidationError" }

// Error satisfies the builtin error interface
func (e PlatformInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlatformInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlatformInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlatformInfoValidationError{}

// Validate checks the field values on AffiliateInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AffiliateInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AffiliateInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AffiliateInfoMultiError, or
// nil if none found.
func (m *AffiliateInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AffiliateInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBusinessInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AffiliateInfoValidationError{
						field:  fmt.Sprintf("BusinessInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AffiliateInfoValidationError{
						field:  fmt.Sprintf("BusinessInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AffiliateInfoValidationError{
					field:  fmt.Sprintf("BusinessInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPlatformInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AffiliateInfoValidationError{
						field:  fmt.Sprintf("PlatformInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AffiliateInfoValidationError{
						field:  fmt.Sprintf("PlatformInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AffiliateInfoValidationError{
					field:  fmt.Sprintf("PlatformInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetChannelInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AffiliateInfoValidationError{
					field:  "ChannelInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AffiliateInfoValidationError{
					field:  "ChannelInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChannelInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AffiliateInfoValidationError{
				field:  "ChannelInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AffiliateInfoMultiError(errors)
	}

	return nil
}

// AffiliateInfoMultiError is an error wrapping multiple validation errors
// returned by AffiliateInfo.ValidateAll() if the designated constraints
// aren't met.
type AffiliateInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AffiliateInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AffiliateInfoMultiError) AllErrors() []error { return m }

// AffiliateInfoValidationError is the validation error returned by
// AffiliateInfo.Validate if the designated constraints aren't met.
type AffiliateInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AffiliateInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AffiliateInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AffiliateInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AffiliateInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AffiliateInfoValidationError) ErrorName() string { return "AffiliateInfoValidationError" }

// Error satisfies the builtin error interface
func (e AffiliateInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAffiliateInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AffiliateInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AffiliateInfoValidationError{}

// Validate checks the field values on Empty with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Empty) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Empty with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EmptyMultiError, or nil if none found.
func (m *Empty) ValidateAll() error {
	return m.validate(true)
}

func (m *Empty) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EmptyMultiError(errors)
	}

	return nil
}

// EmptyMultiError is an error wrapping multiple validation errors returned by
// Empty.ValidateAll() if the designated constraints aren't met.
type EmptyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmptyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmptyMultiError) AllErrors() []error { return m }

// EmptyValidationError is the validation error returned by Empty.Validate if
// the designated constraints aren't met.
type EmptyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmptyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmptyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmptyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmptyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmptyValidationError) ErrorName() string { return "EmptyValidationError" }

// Error satisfies the builtin error interface
func (e EmptyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmpty.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmptyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmptyValidationError{}

// Validate checks the field values on CommonResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommonResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommonResponseMultiError,
// or nil if none found.
func (m *CommonResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if len(errors) > 0 {
		return CommonResponseMultiError(errors)
	}

	return nil
}

// CommonResponseMultiError is an error wrapping multiple validation errors
// returned by CommonResponse.ValidateAll() if the designated constraints
// aren't met.
type CommonResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonResponseMultiError) AllErrors() []error { return m }

// CommonResponseValidationError is the validation error returned by
// CommonResponse.Validate if the designated constraints aren't met.
type CommonResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonResponseValidationError) ErrorName() string { return "CommonResponseValidationError" }

// Error satisfies the builtin error interface
func (e CommonResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonResponseValidationError{}

// Validate checks the field values on CommonResponseWithData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CommonResponseWithData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonResponseWithData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CommonResponseWithDataMultiError, or nil if none found.
func (m *CommonResponseWithData) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonResponseWithData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if len(errors) > 0 {
		return CommonResponseWithDataMultiError(errors)
	}

	return nil
}

// CommonResponseWithDataMultiError is an error wrapping multiple validation
// errors returned by CommonResponseWithData.ValidateAll() if the designated
// constraints aren't met.
type CommonResponseWithDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonResponseWithDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonResponseWithDataMultiError) AllErrors() []error { return m }

// CommonResponseWithDataValidationError is the validation error returned by
// CommonResponseWithData.Validate if the designated constraints aren't met.
type CommonResponseWithDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonResponseWithDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonResponseWithDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonResponseWithDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonResponseWithDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonResponseWithDataValidationError) ErrorName() string {
	return "CommonResponseWithDataValidationError"
}

// Error satisfies the builtin error interface
func (e CommonResponseWithDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonResponseWithData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonResponseWithDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonResponseWithDataValidationError{}

// Validate checks the field values on Pagination with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Pagination) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Pagination with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PaginationMultiError, or
// nil if none found.
func (m *Pagination) ValidateAll() error {
	return m.validate(true)
}

func (m *Pagination) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for PageSize

	// no validation rules for PageNum

	if len(errors) > 0 {
		return PaginationMultiError(errors)
	}

	return nil
}

// PaginationMultiError is an error wrapping multiple validation errors
// returned by Pagination.ValidateAll() if the designated constraints aren't met.
type PaginationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaginationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaginationMultiError) AllErrors() []error { return m }

// PaginationValidationError is the validation error returned by
// Pagination.Validate if the designated constraints aren't met.
type PaginationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaginationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaginationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaginationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaginationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaginationValidationError) ErrorName() string { return "PaginationValidationError" }

// Error satisfies the builtin error interface
func (e PaginationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPagination.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaginationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaginationValidationError{}

// Validate checks the field values on CommonIdParam with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommonIdParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonIdParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommonIdParamMultiError, or
// nil if none found.
func (m *CommonIdParam) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonIdParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := CommonIdParamValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CommonIdParamMultiError(errors)
	}

	return nil
}

// CommonIdParamMultiError is an error wrapping multiple validation errors
// returned by CommonIdParam.ValidateAll() if the designated constraints
// aren't met.
type CommonIdParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonIdParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonIdParamMultiError) AllErrors() []error { return m }

// CommonIdParamValidationError is the validation error returned by
// CommonIdParam.Validate if the designated constraints aren't met.
type CommonIdParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonIdParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonIdParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonIdParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonIdParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonIdParamValidationError) ErrorName() string { return "CommonIdParamValidationError" }

// Error satisfies the builtin error interface
func (e CommonIdParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonIdParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonIdParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonIdParamValidationError{}

// Validate checks the field values on BaseParam with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BaseParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BaseParam with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BaseParamMultiError, or nil
// if none found.
func (m *BaseParam) ValidateAll() error {
	return m.validate(true)
}

func (m *BaseParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageNum

	// no validation rules for PageSize

	// no validation rules for DisplayName

	if all {
		switch v := interface{}(m.GetPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BaseParamValidationError{
					field:  "Price",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BaseParamValidationError{
					field:  "Price",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BaseParamValidationError{
				field:  "Price",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BaseParamValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BaseParamValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BaseParamValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetGenderRatio()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BaseParamValidationError{
					field:  "GenderRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BaseParamValidationError{
					field:  "GenderRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGenderRatio()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BaseParamValidationError{
				field:  "GenderRatio",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SupplierAttributes

	if all {
		switch v := interface{}(m.GetReleaseSpeed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BaseParamValidationError{
					field:  "ReleaseSpeed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BaseParamValidationError{
					field:  "ReleaseSpeed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReleaseSpeed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BaseParamValidationError{
				field:  "ReleaseSpeed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HasTranslate

	// no validation rules for HasBottomInfo

	if len(errors) > 0 {
		return BaseParamMultiError(errors)
	}

	return nil
}

// BaseParamMultiError is an error wrapping multiple validation errors returned
// by BaseParam.ValidateAll() if the designated constraints aren't met.
type BaseParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BaseParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BaseParamMultiError) AllErrors() []error { return m }

// BaseParamValidationError is the validation error returned by
// BaseParam.Validate if the designated constraints aren't met.
type BaseParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BaseParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BaseParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BaseParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BaseParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BaseParamValidationError) ErrorName() string { return "BaseParamValidationError" }

// Error satisfies the builtin error interface
func (e BaseParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBaseParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BaseParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BaseParamValidationError{}

// Validate checks the field values on StringArray with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StringArray) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StringArray with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StringArrayMultiError, or
// nil if none found.
func (m *StringArray) ValidateAll() error {
	return m.validate(true)
}

func (m *StringArray) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return StringArrayMultiError(errors)
	}

	return nil
}

// StringArrayMultiError is an error wrapping multiple validation errors
// returned by StringArray.ValidateAll() if the designated constraints aren't met.
type StringArrayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StringArrayMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StringArrayMultiError) AllErrors() []error { return m }

// StringArrayValidationError is the validation error returned by
// StringArray.Validate if the designated constraints aren't met.
type StringArrayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StringArrayValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StringArrayValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StringArrayValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StringArrayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StringArrayValidationError) ErrorName() string { return "StringArrayValidationError" }

// Error satisfies the builtin error interface
func (e StringArrayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStringArray.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StringArrayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StringArrayValidationError{}

// Validate checks the field values on Price with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Price) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Price with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PriceMultiError, or nil if none found.
func (m *Price) ValidateAll() error {
	return m.validate(true)
}

func (m *Price) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Min

	// no validation rules for Max

	// no validation rules for MinUsd

	// no validation rules for MaxUsd

	// no validation rules for Currency

	if len(errors) > 0 {
		return PriceMultiError(errors)
	}

	return nil
}

// PriceMultiError is an error wrapping multiple validation errors returned by
// Price.ValidateAll() if the designated constraints aren't met.
type PriceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PriceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PriceMultiError) AllErrors() []error { return m }

// PriceValidationError is the validation error returned by Price.Validate if
// the designated constraints aren't met.
type PriceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PriceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PriceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PriceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PriceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PriceValidationError) ErrorName() string { return "PriceValidationError" }

// Error satisfies the builtin error interface
func (e PriceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PriceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PriceValidationError{}

// Validate checks the field values on Interval with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Interval) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Interval with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IntervalMultiError, or nil
// if none found.
func (m *Interval) ValidateAll() error {
	return m.validate(true)
}

func (m *Interval) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Min

	// no validation rules for Max

	// no validation rules for Unit

	if len(errors) > 0 {
		return IntervalMultiError(errors)
	}

	return nil
}

// IntervalMultiError is an error wrapping multiple validation errors returned
// by Interval.ValidateAll() if the designated constraints aren't met.
type IntervalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IntervalMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IntervalMultiError) AllErrors() []error { return m }

// IntervalValidationError is the validation error returned by
// Interval.Validate if the designated constraints aren't met.
type IntervalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IntervalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IntervalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IntervalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IntervalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IntervalValidationError) ErrorName() string { return "IntervalValidationError" }

// Error satisfies the builtin error interface
func (e IntervalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterval.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IntervalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IntervalValidationError{}

// Validate checks the field values on ContactInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ContactInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContactInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ContactInfoMultiError, or
// nil if none found.
func (m *ContactInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Phone

	// no validation rules for Wechat

	// no validation rules for Whatsapp

	// no validation rules for DisplayName

	if len(errors) > 0 {
		return ContactInfoMultiError(errors)
	}

	return nil
}

// ContactInfoMultiError is an error wrapping multiple validation errors
// returned by ContactInfo.ValidateAll() if the designated constraints aren't met.
type ContactInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactInfoMultiError) AllErrors() []error { return m }

// ContactInfoValidationError is the validation error returned by
// ContactInfo.Validate if the designated constraints aren't met.
type ContactInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactInfoValidationError) ErrorName() string { return "ContactInfoValidationError" }

// Error satisfies the builtin error interface
func (e ContactInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactInfoValidationError{}

// Validate checks the field values on Contact with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Contact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Contact with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ContactMultiError, or nil if none found.
func (m *Contact) ValidateAll() error {
	return m.validate(true)
}

func (m *Contact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisplayName

	if all {
		switch v := interface{}(m.GetContactInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "ContactInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "ContactInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContactInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactValidationError{
				field:  "ContactInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetResponsiblePerson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "ResponsiblePerson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactValidationError{
					field:  "ResponsiblePerson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponsiblePerson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactValidationError{
				field:  "ResponsiblePerson",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ContactMultiError(errors)
	}

	return nil
}

// ContactMultiError is an error wrapping multiple validation errors returned
// by Contact.ValidateAll() if the designated constraints aren't met.
type ContactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactMultiError) AllErrors() []error { return m }

// ContactValidationError is the validation error returned by Contact.Validate
// if the designated constraints aren't met.
type ContactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactValidationError) ErrorName() string { return "ContactValidationError" }

// Error satisfies the builtin error interface
func (e ContactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactValidationError{}

// Validate checks the field values on ReporterContactInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReporterContactInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterContactInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReporterContactInfoMultiError, or nil if none found.
func (m *ReporterContactInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterContactInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _ReporterContactInfo_Type_InLookup[m.GetType()]; !ok {
		err := ReporterContactInfoValidationError{
			field:  "Type",
			reason: "value must be in list [email phone wechat whatsapp]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Value

	// no validation rules for Remark

	if len(errors) > 0 {
		return ReporterContactInfoMultiError(errors)
	}

	return nil
}

// ReporterContactInfoMultiError is an error wrapping multiple validation
// errors returned by ReporterContactInfo.ValidateAll() if the designated
// constraints aren't met.
type ReporterContactInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterContactInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterContactInfoMultiError) AllErrors() []error { return m }

// ReporterContactInfoValidationError is the validation error returned by
// ReporterContactInfo.Validate if the designated constraints aren't met.
type ReporterContactInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterContactInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterContactInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterContactInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterContactInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterContactInfoValidationError) ErrorName() string {
	return "ReporterContactInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ReporterContactInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterContactInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterContactInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterContactInfoValidationError{}

var _ReporterContactInfo_Type_InLookup = map[string]struct{}{
	"email":    {},
	"phone":    {},
	"wechat":   {},
	"whatsapp": {},
}

// Validate checks the field values on BrandBusiness with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BrandBusiness) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BrandBusiness with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BrandBusinessMultiError, or
// nil if none found.
func (m *BrandBusiness) ValidateAll() error {
	return m.validate(true)
}

func (m *BrandBusiness) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ContactType

	// no validation rules for ServiceContent

	if all {
		switch v := interface{}(m.GetPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BrandBusinessValidationError{
					field:  "Price",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BrandBusinessValidationError{
					field:  "Price",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BrandBusinessValidationError{
				field:  "Price",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentTerms

	// no validation rules for Remarks

	// no validation rules for OperateMethod

	if len(errors) > 0 {
		return BrandBusinessMultiError(errors)
	}

	return nil
}

// BrandBusinessMultiError is an error wrapping multiple validation errors
// returned by BrandBusiness.ValidateAll() if the designated constraints
// aren't met.
type BrandBusinessMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BrandBusinessMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BrandBusinessMultiError) AllErrors() []error { return m }

// BrandBusinessValidationError is the validation error returned by
// BrandBusiness.Validate if the designated constraints aren't met.
type BrandBusinessValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BrandBusinessValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BrandBusinessValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BrandBusinessValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BrandBusinessValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BrandBusinessValidationError) ErrorName() string { return "BrandBusinessValidationError" }

// Error satisfies the builtin error interface
func (e BrandBusinessValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBrandBusiness.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BrandBusinessValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BrandBusinessValidationError{}

// Validate checks the field values on BrandBusinessInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BrandBusinessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BrandBusinessInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BrandBusinessInfoMultiError, or nil if none found.
func (m *BrandBusinessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BrandBusinessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBusiness()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BrandBusinessInfoValidationError{
					field:  "Business",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BrandBusinessInfoValidationError{
					field:  "Business",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBusiness()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BrandBusinessInfoValidationError{
				field:  "Business",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContact()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BrandBusinessInfoValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BrandBusinessInfoValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContact()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BrandBusinessInfoValidationError{
				field:  "Contact",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BrandBusinessInfoMultiError(errors)
	}

	return nil
}

// BrandBusinessInfoMultiError is an error wrapping multiple validation errors
// returned by BrandBusinessInfo.ValidateAll() if the designated constraints
// aren't met.
type BrandBusinessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BrandBusinessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BrandBusinessInfoMultiError) AllErrors() []error { return m }

// BrandBusinessInfoValidationError is the validation error returned by
// BrandBusinessInfo.Validate if the designated constraints aren't met.
type BrandBusinessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BrandBusinessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BrandBusinessInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BrandBusinessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BrandBusinessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BrandBusinessInfoValidationError) ErrorName() string {
	return "BrandBusinessInfoValidationError"
}

// Error satisfies the builtin error interface
func (e BrandBusinessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBrandBusinessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BrandBusinessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BrandBusinessInfoValidationError{}

// Validate checks the field values on AffiliateBusiness with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AffiliateBusiness) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AffiliateBusiness with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AffiliateBusinessMultiError, or nil if none found.
func (m *AffiliateBusiness) ValidateAll() error {
	return m.validate(true)
}

func (m *AffiliateBusiness) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CooperationRequirements

	if all {
		switch v := interface{}(m.GetPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AffiliateBusinessValidationError{
					field:  "Price",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AffiliateBusinessValidationError{
					field:  "Price",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AffiliateBusinessValidationError{
				field:  "Price",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	if len(errors) > 0 {
		return AffiliateBusinessMultiError(errors)
	}

	return nil
}

// AffiliateBusinessMultiError is an error wrapping multiple validation errors
// returned by AffiliateBusiness.ValidateAll() if the designated constraints
// aren't met.
type AffiliateBusinessMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AffiliateBusinessMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AffiliateBusinessMultiError) AllErrors() []error { return m }

// AffiliateBusinessValidationError is the validation error returned by
// AffiliateBusiness.Validate if the designated constraints aren't met.
type AffiliateBusinessValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AffiliateBusinessValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AffiliateBusinessValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AffiliateBusinessValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AffiliateBusinessValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AffiliateBusinessValidationError) ErrorName() string {
	return "AffiliateBusinessValidationError"
}

// Error satisfies the builtin error interface
func (e AffiliateBusinessValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAffiliateBusiness.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AffiliateBusinessValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AffiliateBusinessValidationError{}

// Validate checks the field values on AffiliateBusinessInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AffiliateBusinessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AffiliateBusinessInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AffiliateBusinessInfoMultiError, or nil if none found.
func (m *AffiliateBusinessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AffiliateBusinessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBusiness()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AffiliateBusinessInfoValidationError{
					field:  "Business",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AffiliateBusinessInfoValidationError{
					field:  "Business",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBusiness()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AffiliateBusinessInfoValidationError{
				field:  "Business",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContact()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AffiliateBusinessInfoValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AffiliateBusinessInfoValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContact()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AffiliateBusinessInfoValidationError{
				field:  "Contact",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AffiliateBusinessInfoMultiError(errors)
	}

	return nil
}

// AffiliateBusinessInfoMultiError is an error wrapping multiple validation
// errors returned by AffiliateBusinessInfo.ValidateAll() if the designated
// constraints aren't met.
type AffiliateBusinessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AffiliateBusinessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AffiliateBusinessInfoMultiError) AllErrors() []error { return m }

// AffiliateBusinessInfoValidationError is the validation error returned by
// AffiliateBusinessInfo.Validate if the designated constraints aren't met.
type AffiliateBusinessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AffiliateBusinessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AffiliateBusinessInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AffiliateBusinessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AffiliateBusinessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AffiliateBusinessInfoValidationError) ErrorName() string {
	return "AffiliateBusinessInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AffiliateBusinessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAffiliateBusinessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AffiliateBusinessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AffiliateBusinessInfoValidationError{}

// Validate checks the field values on OrderParam with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OrderParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderParam with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OrderParamMultiError, or
// nil if none found.
func (m *OrderParam) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Order

	if len(errors) > 0 {
		return OrderParamMultiError(errors)
	}

	return nil
}

// OrderParamMultiError is an error wrapping multiple validation errors
// returned by OrderParam.ValidateAll() if the designated constraints aren't met.
type OrderParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderParamMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderParamMultiError) AllErrors() []error { return m }

// OrderParamValidationError is the validation error returned by
// OrderParam.Validate if the designated constraints aren't met.
type OrderParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderParamValidationError) ErrorName() string { return "OrderParamValidationError" }

// Error satisfies the builtin error interface
func (e OrderParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderParamValidationError{}
