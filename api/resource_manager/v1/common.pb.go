// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/resource_manager/v1/common.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 品牌参数
type BrandParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContactType      string   `protobuf:"bytes,1,opt,name=contact_type,proto3" json:"contact_type,omitempty"`
	PaymentTermsList []int32  `protobuf:"varint,2,rep,packed,name=payment_terms_list,proto3" json:"payment_terms_list,omitempty"`
	TagList          []string `protobuf:"bytes,3,rep,name=tag_list,proto3" json:"tag_list,omitempty"`
	SizeList         []string `protobuf:"bytes,4,rep,name=size_list,proto3" json:"size_list,omitempty"`
	ServiceContent   []string `protobuf:"bytes,5,rep,name=service_content,proto3" json:"service_content,omitempty"`
}

func (x *BrandParam) Reset() {
	*x = BrandParam{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BrandParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandParam) ProtoMessage() {}

func (x *BrandParam) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandParam.ProtoReflect.Descriptor instead.
func (*BrandParam) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *BrandParam) GetContactType() string {
	if x != nil {
		return x.ContactType
	}
	return ""
}

func (x *BrandParam) GetPaymentTermsList() []int32 {
	if x != nil {
		return x.PaymentTermsList
	}
	return nil
}

func (x *BrandParam) GetTagList() []string {
	if x != nil {
		return x.TagList
	}
	return nil
}

func (x *BrandParam) GetSizeList() []string {
	if x != nil {
		return x.SizeList
	}
	return nil
}

func (x *BrandParam) GetServiceContent() []string {
	if x != nil {
		return x.ServiceContent
	}
	return nil
}

// 联盟参数
type AffiliateParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AffPlatformNameList      []string `protobuf:"bytes,1,rep,name=aff_platform_name_list,proto3" json:"aff_platform_name_list,omitempty"`
	ChannelTypeList          []string `protobuf:"bytes,2,rep,name=channel_type_list,proto3" json:"channel_type_list,omitempty"`
	PromotionCategoryList    []string `protobuf:"bytes,3,rep,name=promotion_category_list,proto3" json:"promotion_category_list,omitempty"`
	PromotionTypeList        []string `protobuf:"bytes,4,rep,name=promotion_type_list,proto3" json:"promotion_type_list,omitempty"`
	CustomerJourneyStageList []string `protobuf:"bytes,5,rep,name=customer_journey_stage_list,proto3" json:"customer_journey_stage_list,omitempty"`
	MarketingObjectivesList  []string `protobuf:"bytes,6,rep,name=marketing_objectives_list,proto3" json:"marketing_objectives_list,omitempty"`
	SizeList                 []string `protobuf:"bytes,7,rep,name=size_list,proto3" json:"size_list,omitempty"`
	TagList                  []string `protobuf:"bytes,8,rep,name=tag_list,proto3" json:"tag_list,omitempty"`
	AffPlatformPublisherId   string   `protobuf:"bytes,9,opt,name=aff_platform_publisher_id,proto3" json:"aff_platform_publisher_id,omitempty"`
	AffPlatformPublisherName string   `protobuf:"bytes,10,opt,name=aff_platform_publisher_name,proto3" json:"aff_platform_publisher_name,omitempty"`
	Size                     string   `protobuf:"bytes,11,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *AffiliateParam) Reset() {
	*x = AffiliateParam{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AffiliateParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AffiliateParam) ProtoMessage() {}

func (x *AffiliateParam) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AffiliateParam.ProtoReflect.Descriptor instead.
func (*AffiliateParam) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *AffiliateParam) GetAffPlatformNameList() []string {
	if x != nil {
		return x.AffPlatformNameList
	}
	return nil
}

func (x *AffiliateParam) GetChannelTypeList() []string {
	if x != nil {
		return x.ChannelTypeList
	}
	return nil
}

func (x *AffiliateParam) GetPromotionCategoryList() []string {
	if x != nil {
		return x.PromotionCategoryList
	}
	return nil
}

func (x *AffiliateParam) GetPromotionTypeList() []string {
	if x != nil {
		return x.PromotionTypeList
	}
	return nil
}

func (x *AffiliateParam) GetCustomerJourneyStageList() []string {
	if x != nil {
		return x.CustomerJourneyStageList
	}
	return nil
}

func (x *AffiliateParam) GetMarketingObjectivesList() []string {
	if x != nil {
		return x.MarketingObjectivesList
	}
	return nil
}

func (x *AffiliateParam) GetSizeList() []string {
	if x != nil {
		return x.SizeList
	}
	return nil
}

func (x *AffiliateParam) GetTagList() []string {
	if x != nil {
		return x.TagList
	}
	return nil
}

func (x *AffiliateParam) GetAffPlatformPublisherId() string {
	if x != nil {
		return x.AffPlatformPublisherId
	}
	return ""
}

func (x *AffiliateParam) GetAffPlatformPublisherName() string {
	if x != nil {
		return x.AffPlatformPublisherName
	}
	return ""
}

func (x *AffiliateParam) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

// 性别比例
type GenderRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Male   float32 `protobuf:"fixed32,1,opt,name=male,proto3" json:"male,omitempty"`
	Female float32 `protobuf:"fixed32,2,opt,name=female,proto3" json:"female,omitempty"`
}

func (x *GenderRatio) Reset() {
	*x = GenderRatio{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenderRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenderRatio) ProtoMessage() {}

func (x *GenderRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenderRatio.ProtoReflect.Descriptor instead.
func (*GenderRatio) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{2}
}

func (x *GenderRatio) GetMale() float32 {
	if x != nil {
		return x.Male
	}
	return 0
}

func (x *GenderRatio) GetFemale() float32 {
	if x != nil {
		return x.Female
	}
	return 0
}

// 品牌渠道信息
type BrandTagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name []string `protobuf:"bytes,1,rep,name=name,proto3" json:"name,omitempty"`
}

func (x *BrandTagInfo) Reset() {
	*x = BrandTagInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BrandTagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandTagInfo) ProtoMessage() {}

func (x *BrandTagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandTagInfo.ProtoReflect.Descriptor instead.
func (*BrandTagInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{3}
}

func (x *BrandTagInfo) GetName() []string {
	if x != nil {
		return x.Name
	}
	return nil
}

// 品牌参数信息
type BrandInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessInfo []*BrandBusinessInfo `protobuf:"bytes,1,rep,name=business_info,proto3" json:"business_info,omitempty"`
	TagInfo      *BrandTagInfo        `protobuf:"bytes,2,opt,name=tag_info,proto3" json:"tag_info,omitempty"`
}

func (x *BrandInfo) Reset() {
	*x = BrandInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BrandInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandInfo) ProtoMessage() {}

func (x *BrandInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandInfo.ProtoReflect.Descriptor instead.
func (*BrandInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{4}
}

func (x *BrandInfo) GetBusinessInfo() []*BrandBusinessInfo {
	if x != nil {
		return x.BusinessInfo
	}
	return nil
}

func (x *BrandInfo) GetTagInfo() *BrandTagInfo {
	if x != nil {
		return x.TagInfo
	}
	return nil
}

// 联盟渠道信息
type AffiliateChannelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChannelType          []string `protobuf:"bytes,1,rep,name=channel_type,proto3" json:"channel_type,omitempty"`
	PromotionCategory    []string `protobuf:"bytes,2,rep,name=promotion_category,proto3" json:"promotion_category,omitempty"`
	PromotionType        []string `protobuf:"bytes,3,rep,name=promotion_type,proto3" json:"promotion_type,omitempty"`
	CustomerJourneyStage string   `protobuf:"bytes,4,opt,name=customer_journey_stage,proto3" json:"customer_journey_stage,omitempty"`
	MarketingObjectives  []string `protobuf:"bytes,5,rep,name=marketing_objectives,proto3" json:"marketing_objectives,omitempty"`
	Size                 string   `protobuf:"bytes,6,opt,name=size,proto3" json:"size,omitempty"`
	Tag                  []string `protobuf:"bytes,7,rep,name=tag,proto3" json:"tag,omitempty"`
	MediaViscosity       string   `protobuf:"bytes,8,opt,name=media_viscosity,proto3" json:"media_viscosity,omitempty"`
}

func (x *AffiliateChannelInfo) Reset() {
	*x = AffiliateChannelInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AffiliateChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AffiliateChannelInfo) ProtoMessage() {}

func (x *AffiliateChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AffiliateChannelInfo.ProtoReflect.Descriptor instead.
func (*AffiliateChannelInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{5}
}

func (x *AffiliateChannelInfo) GetChannelType() []string {
	if x != nil {
		return x.ChannelType
	}
	return nil
}

func (x *AffiliateChannelInfo) GetPromotionCategory() []string {
	if x != nil {
		return x.PromotionCategory
	}
	return nil
}

func (x *AffiliateChannelInfo) GetPromotionType() []string {
	if x != nil {
		return x.PromotionType
	}
	return nil
}

func (x *AffiliateChannelInfo) GetCustomerJourneyStage() string {
	if x != nil {
		return x.CustomerJourneyStage
	}
	return ""
}

func (x *AffiliateChannelInfo) GetMarketingObjectives() []string {
	if x != nil {
		return x.MarketingObjectives
	}
	return nil
}

func (x *AffiliateChannelInfo) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *AffiliateChannelInfo) GetTag() []string {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *AffiliateChannelInfo) GetMediaViscosity() string {
	if x != nil {
		return x.MediaViscosity
	}
	return ""
}

// 联盟平台信息
type PlatformInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AffPlatformName          string `protobuf:"bytes,1,opt,name=aff_platform_name,proto3" json:"aff_platform_name,omitempty"`
	AffPlatformPublisherId   string `protobuf:"bytes,2,opt,name=aff_platform_publisher_id,proto3" json:"aff_platform_publisher_id,omitempty"`
	AffPlatformPublisherName string `protobuf:"bytes,3,opt,name=aff_platform_publisher_name,proto3" json:"aff_platform_publisher_name,omitempty"`
}

func (x *PlatformInfo) Reset() {
	*x = PlatformInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlatformInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformInfo) ProtoMessage() {}

func (x *PlatformInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformInfo.ProtoReflect.Descriptor instead.
func (*PlatformInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{6}
}

func (x *PlatformInfo) GetAffPlatformName() string {
	if x != nil {
		return x.AffPlatformName
	}
	return ""
}

func (x *PlatformInfo) GetAffPlatformPublisherId() string {
	if x != nil {
		return x.AffPlatformPublisherId
	}
	return ""
}

func (x *PlatformInfo) GetAffPlatformPublisherName() string {
	if x != nil {
		return x.AffPlatformPublisherName
	}
	return ""
}

// 联盟参数信息
type AffiliateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessInfo []*AffiliateBusinessInfo `protobuf:"bytes,1,rep,name=business_info,proto3" json:"business_info,omitempty"`
	PlatformInfo []*PlatformInfo          `protobuf:"bytes,2,rep,name=platform_info,proto3" json:"platform_info,omitempty"`
	ChannelInfo  *AffiliateChannelInfo    `protobuf:"bytes,3,opt,name=channel_info,proto3" json:"channel_info,omitempty"`
}

func (x *AffiliateInfo) Reset() {
	*x = AffiliateInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AffiliateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AffiliateInfo) ProtoMessage() {}

func (x *AffiliateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AffiliateInfo.ProtoReflect.Descriptor instead.
func (*AffiliateInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{7}
}

func (x *AffiliateInfo) GetBusinessInfo() []*AffiliateBusinessInfo {
	if x != nil {
		return x.BusinessInfo
	}
	return nil
}

func (x *AffiliateInfo) GetPlatformInfo() []*PlatformInfo {
	if x != nil {
		return x.PlatformInfo
	}
	return nil
}

func (x *AffiliateInfo) GetChannelInfo() *AffiliateChannelInfo {
	if x != nil {
		return x.ChannelInfo
	}
	return nil
}

// 空请求结构
type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{8}
}

// 通用返回
type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32  `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{9}
}

func (x *CommonResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *CommonResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *CommonResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

// 通用带数据返回
type CommonResponseWithData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32  `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
}

func (x *CommonResponseWithData) Reset() {
	*x = CommonResponseWithData{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponseWithData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponseWithData) ProtoMessage() {}

func (x *CommonResponseWithData) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponseWithData.ProtoReflect.Descriptor instead.
func (*CommonResponseWithData) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{10}
}

func (x *CommonResponseWithData) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *CommonResponseWithData) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *CommonResponseWithData) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

// 分页参数
type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,proto3" json:"page_size,omitempty"`
	PageNum  int32 `protobuf:"varint,3,opt,name=page_num,proto3" json:"page_num,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{11}
}

func (x *Pagination) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Pagination) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *Pagination) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

// ================ 业务通用message ======================
type CommonIdParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CommonIdParam) Reset() {
	*x = CommonIdParam{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonIdParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonIdParam) ProtoMessage() {}

func (x *CommonIdParam) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonIdParam.ProtoReflect.Descriptor instead.
func (*CommonIdParam) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{12}
}

func (x *CommonIdParam) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 通用基础请求参数
type BaseParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum               int32          `protobuf:"varint,1,opt,name=page_num,proto3" json:"page_num,omitempty"`
	PageSize              int32          `protobuf:"varint,2,opt,name=page_size,proto3" json:"page_size,omitempty"`
	Name                  string         `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Price                 *Price         `protobuf:"bytes,6,opt,name=price,proto3" json:"price,omitempty"`
	SizeList              []string       `protobuf:"bytes,7,rep,name=size_list,proto3" json:"size_list,omitempty"`
	TypeList              []string       `protobuf:"bytes,8,rep,name=type_list,proto3" json:"type_list,omitempty"`
	RegionCountry         []*StringArray `protobuf:"bytes,9,rep,name=region_country,proto3" json:"region_country,omitempty"`
	AgeList               []string       `protobuf:"bytes,10,rep,name=age_list,proto3" json:"age_list,omitempty"`
	GenderRatio           *GenderRatio   `protobuf:"bytes,11,opt,name=gender_ratio,proto3" json:"gender_ratio,omitempty"`
	CountryTop            []string       `protobuf:"bytes,12,rep,name=country_top,proto3" json:"country_top,omitempty"`
	MainBusinessList      []string       `protobuf:"bytes,13,rep,name=main_business_list,proto3" json:"main_business_list,omitempty"`
	AdvantageIndustryList []string       `protobuf:"bytes,14,rep,name=advantage_industry_list,proto3" json:"advantage_industry_list,omitempty"`
	SupplierAttributes    string         `protobuf:"bytes,15,opt,name=supplier_attributes,proto3" json:"supplier_attributes,omitempty"`
	// pr additional param
	LanguageList    []string  `protobuf:"bytes,16,rep,name=language_list,proto3" json:"language_list,omitempty"`
	ProductTypeList []string  `protobuf:"bytes,17,rep,name=product_type_list,proto3" json:"product_type_list,omitempty"`
	ReleaseSpeed    *Interval `protobuf:"bytes,18,opt,name=release_speed,proto3" json:"release_speed,omitempty"`
	HasTranslate    string    `protobuf:"bytes,19,opt,name=has_translate,proto3" json:"has_translate,omitempty"`
	HasBottomInfo   string    `protobuf:"bytes,20,opt,name=has_bottom_info,proto3" json:"has_bottom_info,omitempty"`
	SupplierList    []string  `protobuf:"bytes,21,rep,name=supplier_list,proto3" json:"supplier_list,omitempty"`
}

func (x *BaseParam) Reset() {
	*x = BaseParam{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseParam) ProtoMessage() {}

func (x *BaseParam) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseParam.ProtoReflect.Descriptor instead.
func (*BaseParam) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{13}
}

func (x *BaseParam) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *BaseParam) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BaseParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BaseParam) GetPrice() *Price {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *BaseParam) GetSizeList() []string {
	if x != nil {
		return x.SizeList
	}
	return nil
}

func (x *BaseParam) GetTypeList() []string {
	if x != nil {
		return x.TypeList
	}
	return nil
}

func (x *BaseParam) GetRegionCountry() []*StringArray {
	if x != nil {
		return x.RegionCountry
	}
	return nil
}

func (x *BaseParam) GetAgeList() []string {
	if x != nil {
		return x.AgeList
	}
	return nil
}

func (x *BaseParam) GetGenderRatio() *GenderRatio {
	if x != nil {
		return x.GenderRatio
	}
	return nil
}

func (x *BaseParam) GetCountryTop() []string {
	if x != nil {
		return x.CountryTop
	}
	return nil
}

func (x *BaseParam) GetMainBusinessList() []string {
	if x != nil {
		return x.MainBusinessList
	}
	return nil
}

func (x *BaseParam) GetAdvantageIndustryList() []string {
	if x != nil {
		return x.AdvantageIndustryList
	}
	return nil
}

func (x *BaseParam) GetSupplierAttributes() string {
	if x != nil {
		return x.SupplierAttributes
	}
	return ""
}

func (x *BaseParam) GetLanguageList() []string {
	if x != nil {
		return x.LanguageList
	}
	return nil
}

func (x *BaseParam) GetProductTypeList() []string {
	if x != nil {
		return x.ProductTypeList
	}
	return nil
}

func (x *BaseParam) GetReleaseSpeed() *Interval {
	if x != nil {
		return x.ReleaseSpeed
	}
	return nil
}

func (x *BaseParam) GetHasTranslate() string {
	if x != nil {
		return x.HasTranslate
	}
	return ""
}

func (x *BaseParam) GetHasBottomInfo() string {
	if x != nil {
		return x.HasBottomInfo
	}
	return ""
}

func (x *BaseParam) GetSupplierList() []string {
	if x != nil {
		return x.SupplierList
	}
	return nil
}

type StringArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *StringArray) Reset() {
	*x = StringArray{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringArray) ProtoMessage() {}

func (x *StringArray) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringArray.ProtoReflect.Descriptor instead.
func (*StringArray) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{14}
}

func (x *StringArray) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

// 价格
type Price struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min      float32 `protobuf:"fixed32,1,opt,name=min,proto3" json:"min,omitempty"`
	Max      float32 `protobuf:"fixed32,2,opt,name=max,proto3" json:"max,omitempty"`
	MinUsd   float32 `protobuf:"fixed32,3,opt,name=min_usd,proto3" json:"min_usd,omitempty"`
	MaxUsd   float32 `protobuf:"fixed32,4,opt,name=max_usd,proto3" json:"max_usd,omitempty"`
	Currency string  `protobuf:"bytes,5,opt,name=currency,proto3" json:"currency,omitempty"`
}

func (x *Price) Reset() {
	*x = Price{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Price) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Price) ProtoMessage() {}

func (x *Price) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Price.ProtoReflect.Descriptor instead.
func (*Price) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{15}
}

func (x *Price) GetMin() float32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *Price) GetMax() float32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *Price) GetMinUsd() float32 {
	if x != nil {
		return x.MinUsd
	}
	return 0
}

func (x *Price) GetMaxUsd() float32 {
	if x != nil {
		return x.MaxUsd
	}
	return 0
}

func (x *Price) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

type Interval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min  float32 `protobuf:"fixed32,1,opt,name=min,proto3" json:"min,omitempty"`
	Max  float32 `protobuf:"fixed32,2,opt,name=max,proto3" json:"max,omitempty"`
	Unit string  `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
}

func (x *Interval) Reset() {
	*x = Interval{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Interval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Interval) ProtoMessage() {}

func (x *Interval) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Interval.ProtoReflect.Descriptor instead.
func (*Interval) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{16}
}

func (x *Interval) GetMin() float32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *Interval) GetMax() float32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *Interval) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

// 联系信息
type ContactInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email    string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Phone    string `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	Wechat   string `protobuf:"bytes,3,opt,name=wechat,proto3" json:"wechat,omitempty"`
	Whatsapp string `protobuf:"bytes,4,opt,name=whatsapp,proto3" json:"whatsapp,omitempty"`
	Name     string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ContactInfo) Reset() {
	*x = ContactInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactInfo) ProtoMessage() {}

func (x *ContactInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactInfo.ProtoReflect.Descriptor instead.
func (*ContactInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{17}
}

func (x *ContactInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ContactInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ContactInfo) GetWechat() string {
	if x != nil {
		return x.Wechat
	}
	return ""
}

func (x *ContactInfo) GetWhatsapp() string {
	if x != nil {
		return x.Whatsapp
	}
	return ""
}

func (x *ContactInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 联系人
type Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string       `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ContactInfo       *ContactInfo `protobuf:"bytes,2,opt,name=contact_info,proto3" json:"contact_info,omitempty"`
	Remarks           string       `protobuf:"bytes,3,opt,name=remarks,proto3" json:"remarks,omitempty"`
	ResponsiblePerson *ContactInfo `protobuf:"bytes,4,opt,name=responsible_person,proto3" json:"responsible_person,omitempty"`
}

func (x *Contact) Reset() {
	*x = Contact{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Contact) ProtoMessage() {}

func (x *Contact) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Contact.ProtoReflect.Descriptor instead.
func (*Contact) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{18}
}

func (x *Contact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Contact) GetContactInfo() *ContactInfo {
	if x != nil {
		return x.ContactInfo
	}
	return nil
}

func (x *Contact) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *Contact) GetResponsiblePerson() *ContactInfo {
	if x != nil {
		return x.ResponsiblePerson
	}
	return nil
}

type ReporterContactInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Value  string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Remark string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
}

func (x *ReporterContactInfo) Reset() {
	*x = ReporterContactInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterContactInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterContactInfo) ProtoMessage() {}

func (x *ReporterContactInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterContactInfo.ProtoReflect.Descriptor instead.
func (*ReporterContactInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{19}
}

func (x *ReporterContactInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ReporterContactInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *ReporterContactInfo) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// 品牌商务信息
type BrandBusiness struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContactType    string `protobuf:"bytes,1,opt,name=contact_type,proto3" json:"contact_type,omitempty"`
	ServiceContent string `protobuf:"bytes,2,opt,name=service_content,proto3" json:"service_content,omitempty"`
	Price          *Price `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	PaymentTerms   int32  `protobuf:"varint,4,opt,name=payment_terms,proto3" json:"payment_terms,omitempty"`
	Remarks        string `protobuf:"bytes,5,opt,name=remarks,proto3" json:"remarks,omitempty"`
	OperateMethod  string `protobuf:"bytes,6,opt,name=operate_method,proto3" json:"operate_method,omitempty"`
}

func (x *BrandBusiness) Reset() {
	*x = BrandBusiness{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BrandBusiness) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandBusiness) ProtoMessage() {}

func (x *BrandBusiness) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandBusiness.ProtoReflect.Descriptor instead.
func (*BrandBusiness) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{20}
}

func (x *BrandBusiness) GetContactType() string {
	if x != nil {
		return x.ContactType
	}
	return ""
}

func (x *BrandBusiness) GetServiceContent() string {
	if x != nil {
		return x.ServiceContent
	}
	return ""
}

func (x *BrandBusiness) GetPrice() *Price {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *BrandBusiness) GetPaymentTerms() int32 {
	if x != nil {
		return x.PaymentTerms
	}
	return 0
}

func (x *BrandBusiness) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *BrandBusiness) GetOperateMethod() string {
	if x != nil {
		return x.OperateMethod
	}
	return ""
}

// 品牌商务信息 + 联系人
type BrandBusinessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Business *BrandBusiness `protobuf:"bytes,1,opt,name=business,proto3" json:"business,omitempty"`
	Contact  *Contact       `protobuf:"bytes,2,opt,name=contact,proto3" json:"contact,omitempty"`
}

func (x *BrandBusinessInfo) Reset() {
	*x = BrandBusinessInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BrandBusinessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandBusinessInfo) ProtoMessage() {}

func (x *BrandBusinessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandBusinessInfo.ProtoReflect.Descriptor instead.
func (*BrandBusinessInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{21}
}

func (x *BrandBusinessInfo) GetBusiness() *BrandBusiness {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *BrandBusinessInfo) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

// 联盟商务信息
type AffiliateBusiness struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CooperationRequirements string   `protobuf:"bytes,1,opt,name=cooperation_requirements,proto3" json:"cooperation_requirements,omitempty"`
	MediaKit                []string `protobuf:"bytes,2,rep,name=media_kit,proto3" json:"media_kit,omitempty"`
	Price                   *Price   `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	Remarks                 string   `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
}

func (x *AffiliateBusiness) Reset() {
	*x = AffiliateBusiness{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AffiliateBusiness) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AffiliateBusiness) ProtoMessage() {}

func (x *AffiliateBusiness) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AffiliateBusiness.ProtoReflect.Descriptor instead.
func (*AffiliateBusiness) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{22}
}

func (x *AffiliateBusiness) GetCooperationRequirements() string {
	if x != nil {
		return x.CooperationRequirements
	}
	return ""
}

func (x *AffiliateBusiness) GetMediaKit() []string {
	if x != nil {
		return x.MediaKit
	}
	return nil
}

func (x *AffiliateBusiness) GetPrice() *Price {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *AffiliateBusiness) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

// 联盟商务信息 + 联系人
type AffiliateBusinessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Business *AffiliateBusiness `protobuf:"bytes,1,opt,name=business,proto3" json:"business,omitempty"`
	Contact  *Contact           `protobuf:"bytes,2,opt,name=contact,proto3" json:"contact,omitempty"`
}

func (x *AffiliateBusinessInfo) Reset() {
	*x = AffiliateBusinessInfo{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AffiliateBusinessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AffiliateBusinessInfo) ProtoMessage() {}

func (x *AffiliateBusinessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AffiliateBusinessInfo.ProtoReflect.Descriptor instead.
func (*AffiliateBusinessInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{23}
}

func (x *AffiliateBusinessInfo) GetBusiness() *AffiliateBusiness {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *AffiliateBusinessInfo) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

// 排序参数
type OrderParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Order string `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *OrderParam) Reset() {
	*x = OrderParam{}
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderParam) ProtoMessage() {}

func (x *OrderParam) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_common_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderParam.ProtoReflect.Descriptor instead.
func (*OrderParam) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_common_proto_rawDescGZIP(), []int{24}
}

func (x *OrderParam) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *OrderParam) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

var File_api_resource_manager_v1_common_proto protoreflect.FileDescriptor

var file_api_resource_manager_v1_common_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc4, 0x01, 0x0a, 0x0a, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x05, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x72,
	0x6d, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xb0, 0x04, 0x0a, 0x0e,
	0x41, 0x66, 0x66, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x36,
	0x0a, 0x16, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16,
	0x61, 0x66, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x17, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x13, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x40, 0x0a, 0x1b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6a, 0x6f, 0x75,
	0x72, 0x6e, 0x65, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x3c, 0x0a, 0x19, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x19, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x74, 0x61, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x19, 0x61, 0x66,
	0x66, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x61,
	0x66, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x40, 0x0a, 0x1b, 0x61, 0x66, 0x66, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x61,
	0x66, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x39,
	0x0a, 0x0b, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x61, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x6d, 0x61, 0x6c,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x65, 0x6d, 0x61, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x06, 0x66, 0x65, 0x6d, 0x61, 0x6c, 0x65, 0x22, 0x22, 0x0a, 0x0c, 0x42, 0x72, 0x61,
	0x6e, 0x64, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x98, 0x01,
	0x0a, 0x09, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x0d, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x08, 0x74, 0x61, 0x67,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x74, 0x61, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0xce, 0x02, 0x0a, 0x14, 0x41, 0x66, 0x66,
	0x69, 0x6c, 0x69, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a,
	0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6a, 0x6f, 0x75, 0x72, 0x6e, 0x65, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x14, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x74, 0x61, 0x67, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12,
	0x28, 0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x76, 0x69, 0x73, 0x63, 0x6f, 0x73, 0x69,
	0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f,
	0x76, 0x69, 0x73, 0x63, 0x6f, 0x73, 0x69, 0x74, 0x79, 0x22, 0xbc, 0x01, 0x0a, 0x0c, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x66,
	0x66, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x19, 0x61, 0x66, 0x66, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x61, 0x66, 0x66,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x40, 0x0a, 0x1b, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x61, 0x66, 0x66,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xf9, 0x01, 0x0a, 0x0d, 0x41, 0x66, 0x66,
	0x69, 0x6c, 0x69, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x0d, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x66, 0x66, 0x69, 0x6c, 0x69, 0x61, 0x74,
	0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x47, 0x0a, 0x0d,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x66, 0x66, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x58, 0x0a,
	0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x60, 0x0a, 0x16, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x69, 0x74, 0x68, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x5c, 0x0a, 0x0a, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1c, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x22, 0x28, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x49, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x22, 0xc0, 0x06, 0x0a, 0x09, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x44, 0x0a, 0x0c, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x0c, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x74, 0x6f, 0x70, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x6f, 0x70, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x61, 0x69,
	0x6e, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x17, 0x61, 0x64, 0x76,
	0x61, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x17, 0x61, 0x64, 0x76, 0x61,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0d, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52,
	0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x68, 0x61, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x68, 0x61, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x68, 0x61, 0x73, 0x5f, 0x62, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x68,
	0x61, 0x73, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x24,
	0x0a, 0x0d, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x15, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x22, 0x23, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72,
	0x72, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x7b, 0x0a, 0x05, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x75, 0x73,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x75, 0x73, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x75, 0x73, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x75, 0x73, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x22, 0x42, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x0b, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xcf,
	0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x44,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x50,
	0x0a, 0x12, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x22, 0x7e, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0xfa, 0x42, 0x22, 0x72, 0x20, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x06, 0x77, 0x65, 0x63, 0x68,
	0x61, 0x74, 0x52, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x22, 0xf7, 0x01, 0x0a, 0x0d, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x30, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x65,
	0x72, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x11, 0x42,
	0x72, 0x61, 0x6e, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x3e, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x22, 0xb9, 0x01, 0x0a, 0x11, 0x41, 0x66, 0x66,
	0x69, 0x6c, 0x69, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x3a,
	0x0a, 0x18, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x18, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x5f, 0x6b, 0x69, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x6b, 0x69, 0x74, 0x12, 0x30, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x15, 0x41, 0x66, 0x66, 0x69, 0x6c, 0x69, 0x61,
	0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x42,
	0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x66, 0x66, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x65,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x22, 0x38, 0x0a, 0x0a, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x19, 0x5a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_resource_manager_v1_common_proto_rawDescOnce sync.Once
	file_api_resource_manager_v1_common_proto_rawDescData = file_api_resource_manager_v1_common_proto_rawDesc
)

func file_api_resource_manager_v1_common_proto_rawDescGZIP() []byte {
	file_api_resource_manager_v1_common_proto_rawDescOnce.Do(func() {
		file_api_resource_manager_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_resource_manager_v1_common_proto_rawDescData)
	})
	return file_api_resource_manager_v1_common_proto_rawDescData
}

var file_api_resource_manager_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_api_resource_manager_v1_common_proto_goTypes = []any{
	(*BrandParam)(nil),             // 0: resource_manager.v1.BrandParam
	(*AffiliateParam)(nil),         // 1: resource_manager.v1.AffiliateParam
	(*GenderRatio)(nil),            // 2: resource_manager.v1.GenderRatio
	(*BrandTagInfo)(nil),           // 3: resource_manager.v1.BrandTagInfo
	(*BrandInfo)(nil),              // 4: resource_manager.v1.BrandInfo
	(*AffiliateChannelInfo)(nil),   // 5: resource_manager.v1.AffiliateChannelInfo
	(*PlatformInfo)(nil),           // 6: resource_manager.v1.PlatformInfo
	(*AffiliateInfo)(nil),          // 7: resource_manager.v1.AffiliateInfo
	(*Empty)(nil),                  // 8: resource_manager.v1.Empty
	(*CommonResponse)(nil),         // 9: resource_manager.v1.CommonResponse
	(*CommonResponseWithData)(nil), // 10: resource_manager.v1.CommonResponseWithData
	(*Pagination)(nil),             // 11: resource_manager.v1.Pagination
	(*CommonIdParam)(nil),          // 12: resource_manager.v1.CommonIdParam
	(*BaseParam)(nil),              // 13: resource_manager.v1.BaseParam
	(*StringArray)(nil),            // 14: resource_manager.v1.StringArray
	(*Price)(nil),                  // 15: resource_manager.v1.Price
	(*Interval)(nil),               // 16: resource_manager.v1.Interval
	(*ContactInfo)(nil),            // 17: resource_manager.v1.ContactInfo
	(*Contact)(nil),                // 18: resource_manager.v1.Contact
	(*ReporterContactInfo)(nil),    // 19: resource_manager.v1.ReporterContactInfo
	(*BrandBusiness)(nil),          // 20: resource_manager.v1.BrandBusiness
	(*BrandBusinessInfo)(nil),      // 21: resource_manager.v1.BrandBusinessInfo
	(*AffiliateBusiness)(nil),      // 22: resource_manager.v1.AffiliateBusiness
	(*AffiliateBusinessInfo)(nil),  // 23: resource_manager.v1.AffiliateBusinessInfo
	(*OrderParam)(nil),             // 24: resource_manager.v1.OrderParam
}
var file_api_resource_manager_v1_common_proto_depIdxs = []int32{
	21, // 0: resource_manager.v1.BrandInfo.business_info:type_name -> resource_manager.v1.BrandBusinessInfo
	3,  // 1: resource_manager.v1.BrandInfo.tag_info:type_name -> resource_manager.v1.BrandTagInfo
	23, // 2: resource_manager.v1.AffiliateInfo.business_info:type_name -> resource_manager.v1.AffiliateBusinessInfo
	6,  // 3: resource_manager.v1.AffiliateInfo.platform_info:type_name -> resource_manager.v1.PlatformInfo
	5,  // 4: resource_manager.v1.AffiliateInfo.channel_info:type_name -> resource_manager.v1.AffiliateChannelInfo
	15, // 5: resource_manager.v1.BaseParam.price:type_name -> resource_manager.v1.Price
	14, // 6: resource_manager.v1.BaseParam.region_country:type_name -> resource_manager.v1.StringArray
	2,  // 7: resource_manager.v1.BaseParam.gender_ratio:type_name -> resource_manager.v1.GenderRatio
	16, // 8: resource_manager.v1.BaseParam.release_speed:type_name -> resource_manager.v1.Interval
	17, // 9: resource_manager.v1.Contact.contact_info:type_name -> resource_manager.v1.ContactInfo
	17, // 10: resource_manager.v1.Contact.responsible_person:type_name -> resource_manager.v1.ContactInfo
	15, // 11: resource_manager.v1.BrandBusiness.price:type_name -> resource_manager.v1.Price
	20, // 12: resource_manager.v1.BrandBusinessInfo.business:type_name -> resource_manager.v1.BrandBusiness
	18, // 13: resource_manager.v1.BrandBusinessInfo.contact:type_name -> resource_manager.v1.Contact
	15, // 14: resource_manager.v1.AffiliateBusiness.price:type_name -> resource_manager.v1.Price
	22, // 15: resource_manager.v1.AffiliateBusinessInfo.business:type_name -> resource_manager.v1.AffiliateBusiness
	18, // 16: resource_manager.v1.AffiliateBusinessInfo.contact:type_name -> resource_manager.v1.Contact
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_resource_manager_v1_common_proto_init() }
func file_api_resource_manager_v1_common_proto_init() {
	if File_api_resource_manager_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_resource_manager_v1_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_resource_manager_v1_common_proto_goTypes,
		DependencyIndexes: file_api_resource_manager_v1_common_proto_depIdxs,
		MessageInfos:      file_api_resource_manager_v1_common_proto_msgTypes,
	}.Build()
	File_api_resource_manager_v1_common_proto = out.File
	file_api_resource_manager_v1_common_proto_rawDesc = nil
	file_api_resource_manager_v1_common_proto_goTypes = nil
	file_api_resource_manager_v1_common_proto_depIdxs = nil
}
