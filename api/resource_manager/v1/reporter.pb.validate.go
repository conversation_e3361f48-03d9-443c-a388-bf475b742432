// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/resource_manager/v1/reporter.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ReporterListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReporterListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReporterListRequestMultiError, or nil if none found.
func (m *ReporterListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisplayName

	// no validation rules for Tag

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterListRequestValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterListRequestValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterListRequestValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if _, ok := _ReporterListRequest_IsEstablishConn_InLookup[m.GetIsEstablishConn()]; !ok {
		err := ReporterListRequestValidationError{
			field:  "IsEstablishConn",
			reason: "value must be in list [0 1 2]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for City

	// no validation rules for PageSize

	// no validation rules for PageNum

	// no validation rules for MediaName

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return ReporterListRequestMultiError(errors)
	}

	return nil
}

// ReporterListRequestMultiError is an error wrapping multiple validation
// errors returned by ReporterListRequest.ValidateAll() if the designated
// constraints aren't met.
type ReporterListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterListRequestMultiError) AllErrors() []error { return m }

// ReporterListRequestValidationError is the validation error returned by
// ReporterListRequest.Validate if the designated constraints aren't met.
type ReporterListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterListRequestValidationError) ErrorName() string {
	return "ReporterListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReporterListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterListRequestValidationError{}

var _ReporterListRequest_IsEstablishConn_InLookup = map[int32]struct{}{
	0: {},
	1: {},
	2: {},
}

// Validate checks the field values on SocialMedia with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SocialMedia) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SocialMedia with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SocialMediaMultiError, or
// nil if none found.
func (m *SocialMedia) ValidateAll() error {
	return m.validate(true)
}

func (m *SocialMedia) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Media

	// no validation rules for Url

	if len(errors) > 0 {
		return SocialMediaMultiError(errors)
	}

	return nil
}

// SocialMediaMultiError is an error wrapping multiple validation errors
// returned by SocialMedia.ValidateAll() if the designated constraints aren't met.
type SocialMediaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SocialMediaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SocialMediaMultiError) AllErrors() []error { return m }

// SocialMediaValidationError is the validation error returned by
// SocialMedia.Validate if the designated constraints aren't met.
type SocialMediaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SocialMediaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SocialMediaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SocialMediaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SocialMediaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SocialMediaValidationError) ErrorName() string { return "SocialMediaValidationError" }

// Error satisfies the builtin error interface
func (e SocialMediaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSocialMedia.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SocialMediaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SocialMediaValidationError{}

// Validate checks the field values on ReportMedia with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReportMedia) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportMedia with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReportMediaMultiError, or
// nil if none found.
func (m *ReportMedia) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportMedia) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MediaName

	// no validation rules for MediaProfile

	// no validation rules for SiteUrl

	// no validation rules for MuckrackUrl

	// no validation rules for MuckrackId

	// no validation rules for Scope

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReportMediaValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReportMediaValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReportMediaValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SimilarwebUvm

	// no validation rules for ComscoreUvm

	// no validation rules for DomainAuthority

	// no validation rules for SpamScore

	// no validation rules for PublishFrequency

	// no validation rules for PublishDays

	for idx, item := range m.GetSocialMedias() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReportMediaValidationError{
						field:  fmt.Sprintf("SocialMedias[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReportMediaValidationError{
						field:  fmt.Sprintf("SocialMedias[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReportMediaValidationError{
					field:  fmt.Sprintf("SocialMedias[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UpdateTime

	// no validation rules for City

	// no validation rules for State

	// no validation rules for Email

	// no validation rules for Telephone

	if len(errors) > 0 {
		return ReportMediaMultiError(errors)
	}

	return nil
}

// ReportMediaMultiError is an error wrapping multiple validation errors
// returned by ReportMedia.ValidateAll() if the designated constraints aren't met.
type ReportMediaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportMediaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportMediaMultiError) AllErrors() []error { return m }

// ReportMediaValidationError is the validation error returned by
// ReportMedia.Validate if the designated constraints aren't met.
type ReportMediaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportMediaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportMediaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportMediaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportMediaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportMediaValidationError) ErrorName() string { return "ReportMediaValidationError" }

// Error satisfies the builtin error interface
func (e ReportMediaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportMedia.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportMediaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportMediaValidationError{}

// Validate checks the field values on ReporterResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReporterResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReporterResourceMultiError, or nil if none found.
func (m *ReporterResource) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	// no validation rules for Title

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterResourceValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetReportMedia() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterResourceValidationError{
						field:  fmt.Sprintf("ReportMedia[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterResourceValidationError{
						field:  fmt.Sprintf("ReportMedia[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterResourceValidationError{
					field:  fmt.Sprintf("ReportMedia[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsEstablishConn

	// no validation rules for EstablishConnUser

	if len(errors) > 0 {
		return ReporterResourceMultiError(errors)
	}

	return nil
}

// ReporterResourceMultiError is an error wrapping multiple validation errors
// returned by ReporterResource.ValidateAll() if the designated constraints
// aren't met.
type ReporterResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterResourceMultiError) AllErrors() []error { return m }

// ReporterResourceValidationError is the validation error returned by
// ReporterResource.Validate if the designated constraints aren't met.
type ReporterResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterResourceValidationError) ErrorName() string { return "ReporterResourceValidationError" }

// Error satisfies the builtin error interface
func (e ReporterResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterResourceValidationError{}

// Validate checks the field values on ReporterListData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReporterListData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterListData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReporterListDataMultiError, or nil if none found.
func (m *ReporterListData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterListData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReporterListDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReporterListDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReporterListDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterListDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ReporterListDataMultiError(errors)
	}

	return nil
}

// ReporterListDataMultiError is an error wrapping multiple validation errors
// returned by ReporterListData.ValidateAll() if the designated constraints
// aren't met.
type ReporterListDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterListDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterListDataMultiError) AllErrors() []error { return m }

// ReporterListDataValidationError is the validation error returned by
// ReporterListData.Validate if the designated constraints aren't met.
type ReporterListDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterListDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterListDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterListDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterListDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterListDataValidationError) ErrorName() string { return "ReporterListDataValidationError" }

// Error satisfies the builtin error interface
func (e ReporterListDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterListData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterListDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterListDataValidationError{}

// Validate checks the field values on ReporterResourceListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReporterResourceListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterResourceListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReporterResourceListResponseMultiError, or nil if none found.
func (m *ReporterResourceListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterResourceListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReporterResourceListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReporterResourceListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReporterResourceListResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReporterResourceListResponseMultiError(errors)
	}

	return nil
}

// ReporterResourceListResponseMultiError is an error wrapping multiple
// validation errors returned by ReporterResourceListResponse.ValidateAll() if
// the designated constraints aren't met.
type ReporterResourceListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterResourceListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterResourceListResponseMultiError) AllErrors() []error { return m }

// ReporterResourceListResponseValidationError is the validation error returned
// by ReporterResourceListResponse.Validate if the designated constraints
// aren't met.
type ReporterResourceListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterResourceListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterResourceListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterResourceListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterResourceListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterResourceListResponseValidationError) ErrorName() string {
	return "ReporterResourceListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReporterResourceListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterResourceListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterResourceListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterResourceListResponseValidationError{}

// Validate checks the field values on Article with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Article) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Article with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ArticleMultiError, or nil if none found.
func (m *Article) ValidateAll() error {
	return m.validate(true)
}

func (m *Article) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ArticleUrl

	// no validation rules for ArticleTitle

	// no validation rules for ArticlePublishTime

	// no validation rules for ArticleDescription

	// no validation rules for MediaName

	if len(errors) > 0 {
		return ArticleMultiError(errors)
	}

	return nil
}

// ArticleMultiError is an error wrapping multiple validation errors returned
// by Article.ValidateAll() if the designated constraints aren't met.
type ArticleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArticleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArticleMultiError) AllErrors() []error { return m }

// ArticleValidationError is the validation error returned by Article.Validate
// if the designated constraints aren't met.
type ArticleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArticleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArticleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArticleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArticleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArticleValidationError) ErrorName() string { return "ArticleValidationError" }

// Error satisfies the builtin error interface
func (e ArticleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArticle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArticleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArticleValidationError{}

// Validate checks the field values on ReporterData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReporterData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReporterDataMultiError, or
// nil if none found.
func (m *ReporterData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	// no validation rules for Title

	// no validation rules for MuckrackUrl

	// no validation rules for MuckrackId

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterDataValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterDataValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterDataValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Introduction

	// no validation rules for City

	// no validation rules for State

	// no validation rules for PublishFrequency

	// no validation rules for BackgroundCheck

	// no validation rules for IsEstablishConn

	// no validation rules for EstablishConnUser

	for idx, item := range m.GetSocialMedias() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterDataValidationError{
						field:  fmt.Sprintf("SocialMedias[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterDataValidationError{
						field:  fmt.Sprintf("SocialMedias[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterDataValidationError{
					field:  fmt.Sprintf("SocialMedias[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetContact() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterDataValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterDataValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterDataValidationError{
					field:  fmt.Sprintf("Contact[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetReportMedia() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterDataValidationError{
						field:  fmt.Sprintf("ReportMedia[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterDataValidationError{
						field:  fmt.Sprintf("ReportMedia[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterDataValidationError{
					field:  fmt.Sprintf("ReportMedia[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FirstName

	// no validation rules for LastName

	if all {
		switch v := interface{}(m.GetContactInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReporterDataValidationError{
					field:  "ContactInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReporterDataValidationError{
					field:  "ContactInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContactInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReporterDataValidationError{
				field:  "ContactInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReporterDataMultiError(errors)
	}

	return nil
}

// ReporterDataMultiError is an error wrapping multiple validation errors
// returned by ReporterData.ValidateAll() if the designated constraints aren't met.
type ReporterDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterDataMultiError) AllErrors() []error { return m }

// ReporterDataValidationError is the validation error returned by
// ReporterData.Validate if the designated constraints aren't met.
type ReporterDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterDataValidationError) ErrorName() string { return "ReporterDataValidationError" }

// Error satisfies the builtin error interface
func (e ReporterDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterDataValidationError{}

// Validate checks the field values on ReporterResourceDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReporterResourceDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterResourceDetailResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ReporterResourceDetailResponseMultiError, or nil if none found.
func (m *ReporterResourceDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterResourceDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReporterResourceDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReporterResourceDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReporterResourceDetailResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReporterResourceDetailResponseMultiError(errors)
	}

	return nil
}

// ReporterResourceDetailResponseMultiError is an error wrapping multiple
// validation errors returned by ReporterResourceDetailResponse.ValidateAll()
// if the designated constraints aren't met.
type ReporterResourceDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterResourceDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterResourceDetailResponseMultiError) AllErrors() []error { return m }

// ReporterResourceDetailResponseValidationError is the validation error
// returned by ReporterResourceDetailResponse.Validate if the designated
// constraints aren't met.
type ReporterResourceDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterResourceDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterResourceDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterResourceDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterResourceDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterResourceDetailResponseValidationError) ErrorName() string {
	return "ReporterResourceDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReporterResourceDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterResourceDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterResourceDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterResourceDetailResponseValidationError{}

// Validate checks the field values on AddOrUpdateReportMedia with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddOrUpdateReportMedia) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddOrUpdateReportMedia with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddOrUpdateReportMediaMultiError, or nil if none found.
func (m *AddOrUpdateReportMedia) ValidateAll() error {
	return m.validate(true)
}

func (m *AddOrUpdateReportMedia) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetMediaName()) < 1 {
		err := AddOrUpdateReportMediaValidationError{
			field:  "MediaName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for MuckrackUrl

	if utf8.RuneCountInString(m.GetSiteUrl()) < 1 {
		err := AddOrUpdateReportMediaValidationError{
			field:  "SiteUrl",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AddOrUpdateReportMediaMultiError(errors)
	}

	return nil
}

// AddOrUpdateReportMediaMultiError is an error wrapping multiple validation
// errors returned by AddOrUpdateReportMedia.ValidateAll() if the designated
// constraints aren't met.
type AddOrUpdateReportMediaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddOrUpdateReportMediaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddOrUpdateReportMediaMultiError) AllErrors() []error { return m }

// AddOrUpdateReportMediaValidationError is the validation error returned by
// AddOrUpdateReportMedia.Validate if the designated constraints aren't met.
type AddOrUpdateReportMediaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddOrUpdateReportMediaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddOrUpdateReportMediaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddOrUpdateReportMediaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddOrUpdateReportMediaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddOrUpdateReportMediaValidationError) ErrorName() string {
	return "AddOrUpdateReportMediaValidationError"
}

// Error satisfies the builtin error interface
func (e AddOrUpdateReportMediaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddOrUpdateReportMedia.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddOrUpdateReportMediaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddOrUpdateReportMediaValidationError{}

// Validate checks the field values on AddOrUpdateArticle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddOrUpdateArticle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddOrUpdateArticle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddOrUpdateArticleMultiError, or nil if none found.
func (m *AddOrUpdateArticle) ValidateAll() error {
	return m.validate(true)
}

func (m *AddOrUpdateArticle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ArticleTitle

	// no validation rules for ArticleUrl

	if len(errors) > 0 {
		return AddOrUpdateArticleMultiError(errors)
	}

	return nil
}

// AddOrUpdateArticleMultiError is an error wrapping multiple validation errors
// returned by AddOrUpdateArticle.ValidateAll() if the designated constraints
// aren't met.
type AddOrUpdateArticleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddOrUpdateArticleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddOrUpdateArticleMultiError) AllErrors() []error { return m }

// AddOrUpdateArticleValidationError is the validation error returned by
// AddOrUpdateArticle.Validate if the designated constraints aren't met.
type AddOrUpdateArticleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddOrUpdateArticleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddOrUpdateArticleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddOrUpdateArticleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddOrUpdateArticleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddOrUpdateArticleValidationError) ErrorName() string {
	return "AddOrUpdateArticleValidationError"
}

// Error satisfies the builtin error interface
func (e AddOrUpdateArticleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddOrUpdateArticle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddOrUpdateArticleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddOrUpdateArticleValidationError{}

// Validate checks the field values on ReporterAddOrUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReporterAddOrUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterAddOrUpdateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReporterAddOrUpdateRequestMultiError, or nil if none found.
func (m *ReporterAddOrUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterAddOrUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetFirstName()) < 1 {
		err := ReporterAddOrUpdateRequestValidationError{
			field:  "FirstName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetLastName()) < 1 {
		err := ReporterAddOrUpdateRequestValidationError{
			field:  "LastName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Title

	// no validation rules for MuckrackUrl

	// no validation rules for Introduction

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterAddOrUpdateRequestValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for City

	// no validation rules for State

	// no validation rules for PublishFrequency

	if _, ok := _ReporterAddOrUpdateRequest_IsEstablishConn_InLookup[m.GetIsEstablishConn()]; !ok {
		err := ReporterAddOrUpdateRequestValidationError{
			field:  "IsEstablishConn",
			reason: "value must be in list [0 1 2]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BackgroundCheck

	for idx, item := range m.GetContact() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterAddOrUpdateRequestValidationError{
					field:  fmt.Sprintf("Contact[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSocialMedias() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("SocialMedias[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("SocialMedias[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterAddOrUpdateRequestValidationError{
					field:  fmt.Sprintf("SocialMedias[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetReportMedia() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("ReportMedia[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("ReportMedia[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterAddOrUpdateRequestValidationError{
					field:  fmt.Sprintf("ReportMedia[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetArticles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("Articles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterAddOrUpdateRequestValidationError{
						field:  fmt.Sprintf("Articles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterAddOrUpdateRequestValidationError{
					field:  fmt.Sprintf("Articles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ReporterAddOrUpdateRequestMultiError(errors)
	}

	return nil
}

// ReporterAddOrUpdateRequestMultiError is an error wrapping multiple
// validation errors returned by ReporterAddOrUpdateRequest.ValidateAll() if
// the designated constraints aren't met.
type ReporterAddOrUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterAddOrUpdateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterAddOrUpdateRequestMultiError) AllErrors() []error { return m }

// ReporterAddOrUpdateRequestValidationError is the validation error returned
// by ReporterAddOrUpdateRequest.Validate if the designated constraints aren't met.
type ReporterAddOrUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterAddOrUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterAddOrUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterAddOrUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterAddOrUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterAddOrUpdateRequestValidationError) ErrorName() string {
	return "ReporterAddOrUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReporterAddOrUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterAddOrUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterAddOrUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterAddOrUpdateRequestValidationError{}

var _ReporterAddOrUpdateRequest_IsEstablishConn_InLookup = map[int32]struct{}{
	0: {},
	1: {},
	2: {},
}

// Validate checks the field values on GetReporterArticleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReporterArticleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReporterArticleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReporterArticleRequestMultiError, or nil if none found.
func (m *GetReporterArticleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReporterArticleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageSize

	// no validation rules for PageNum

	// no validation rules for ReporterId

	if len(errors) > 0 {
		return GetReporterArticleRequestMultiError(errors)
	}

	return nil
}

// GetReporterArticleRequestMultiError is an error wrapping multiple validation
// errors returned by GetReporterArticleRequest.ValidateAll() if the
// designated constraints aren't met.
type GetReporterArticleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReporterArticleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReporterArticleRequestMultiError) AllErrors() []error { return m }

// GetReporterArticleRequestValidationError is the validation error returned by
// GetReporterArticleRequest.Validate if the designated constraints aren't met.
type GetReporterArticleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReporterArticleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReporterArticleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReporterArticleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReporterArticleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReporterArticleRequestValidationError) ErrorName() string {
	return "GetReporterArticleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetReporterArticleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReporterArticleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReporterArticleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReporterArticleRequestValidationError{}

// Validate checks the field values on GetReporterArticleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReporterArticleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReporterArticleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReporterArticleResponseMultiError, or nil if none found.
func (m *GetReporterArticleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReporterArticleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReporterArticleResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReporterArticleResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReporterArticleResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetReporterArticleResponseMultiError(errors)
	}

	return nil
}

// GetReporterArticleResponseMultiError is an error wrapping multiple
// validation errors returned by GetReporterArticleResponse.ValidateAll() if
// the designated constraints aren't met.
type GetReporterArticleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReporterArticleResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReporterArticleResponseMultiError) AllErrors() []error { return m }

// GetReporterArticleResponseValidationError is the validation error returned
// by GetReporterArticleResponse.Validate if the designated constraints aren't met.
type GetReporterArticleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReporterArticleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReporterArticleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReporterArticleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReporterArticleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReporterArticleResponseValidationError) ErrorName() string {
	return "GetReporterArticleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetReporterArticleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReporterArticleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReporterArticleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReporterArticleResponseValidationError{}

// Validate checks the field values on ReporterArticleData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReporterArticleData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReporterArticleData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReporterArticleDataMultiError, or nil if none found.
func (m *ReporterArticleData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReporterArticleData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReporterArticleDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReporterArticleDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReporterArticleDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReporterArticleDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReporterArticleDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReporterArticleDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ReporterArticleDataMultiError(errors)
	}

	return nil
}

// ReporterArticleDataMultiError is an error wrapping multiple validation
// errors returned by ReporterArticleData.ValidateAll() if the designated
// constraints aren't met.
type ReporterArticleDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReporterArticleDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReporterArticleDataMultiError) AllErrors() []error { return m }

// ReporterArticleDataValidationError is the validation error returned by
// ReporterArticleData.Validate if the designated constraints aren't met.
type ReporterArticleDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReporterArticleDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReporterArticleDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReporterArticleDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReporterArticleDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReporterArticleDataValidationError) ErrorName() string {
	return "ReporterArticleDataValidationError"
}

// Error satisfies the builtin error interface
func (e ReporterArticleDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReporterArticleData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReporterArticleDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReporterArticleDataValidationError{}
