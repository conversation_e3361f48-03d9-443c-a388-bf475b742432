// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/resource_manager/v1/reporter.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 记者列表请求
type ReporterListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string         `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                            //记者名称
	Tag               string         `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`                              // 标签
	RegionCountry     []*StringArray `protobuf:"bytes,3,rep,name=region_country,proto3" json:"region_country,omitempty"`        //国家地区
	Beats             []string       `protobuf:"bytes,4,rep,name=beats,proto3" json:"beats,omitempty"`                          //领域专业
	IsEstablishConn   int32          `protobuf:"varint,5,opt,name=is_establish_conn,proto3" json:"is_establish_conn,omitempty"` //是否建联
	City              string         `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`                            //记者所在城市
	PageSize          int32          `protobuf:"varint,7,opt,name=page_size,proto3" json:"page_size,omitempty"`                 //分页大小
	PageNum           int32          `protobuf:"varint,8,opt,name=page_num,proto3" json:"page_num,omitempty"`                   //分页页码
	MediaName         string         `protobuf:"bytes,9,opt,name=media_name,proto3" json:"media_name,omitempty"`                //媒体名称
	CustomerProjectId int32          `protobuf:"varint,10,opt,name=customer_project_id,proto3" json:"customer_project_id,omitempty"`
}

func (x *ReporterListRequest) Reset() {
	*x = ReporterListRequest{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterListRequest) ProtoMessage() {}

func (x *ReporterListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterListRequest.ProtoReflect.Descriptor instead.
func (*ReporterListRequest) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{0}
}

func (x *ReporterListRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReporterListRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ReporterListRequest) GetRegionCountry() []*StringArray {
	if x != nil {
		return x.RegionCountry
	}
	return nil
}

func (x *ReporterListRequest) GetBeats() []string {
	if x != nil {
		return x.Beats
	}
	return nil
}

func (x *ReporterListRequest) GetIsEstablishConn() int32 {
	if x != nil {
		return x.IsEstablishConn
	}
	return 0
}

func (x *ReporterListRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *ReporterListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReporterListRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReporterListRequest) GetMediaName() string {
	if x != nil {
		return x.MediaName
	}
	return ""
}

func (x *ReporterListRequest) GetCustomerProjectId() int32 {
	if x != nil {
		return x.CustomerProjectId
	}
	return 0
}

type SocialMedia struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Media string `protobuf:"bytes,1,opt,name=media,proto3" json:"media,omitempty"` // 媒体名称
	Url   string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`     // 社交媒体个人主页
}

func (x *SocialMedia) Reset() {
	*x = SocialMedia{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SocialMedia) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialMedia) ProtoMessage() {}

func (x *SocialMedia) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialMedia.ProtoReflect.Descriptor instead.
func (*SocialMedia) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{1}
}

func (x *SocialMedia) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *SocialMedia) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// 媒体信息
type ReportMedia struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                               // 报道媒体ID
	MediaName        string         `protobuf:"bytes,2,opt,name=media_name,proto3" json:"media_name,omitempty"`                // 媒体名称
	MediaProfile     string         `protobuf:"bytes,3,opt,name=media_profile,proto3" json:"media_profile,omitempty"`          // 媒体简介
	SiteUrl          string         `protobuf:"bytes,4,opt,name=site_url,proto3" json:"site_url,omitempty"`                    // 媒体官方主页链接
	MuckrackUrl      string         `protobuf:"bytes,5,opt,name=muckrack_url,proto3" json:"muckrack_url,omitempty"`            // 媒体在muckrack主页链接
	MuckrackId       string         `protobuf:"bytes,6,opt,name=muckrack_id,proto3" json:"muckrack_id,omitempty"`              // 媒体在muckrack的唯一id
	Scope            string         `protobuf:"bytes,7,opt,name=scope,proto3" json:"scope,omitempty"`                          // 媒体范围
	Language         []string       `protobuf:"bytes,8,rep,name=language,proto3" json:"language,omitempty"`                    // 语言
	RegionCountry    []*StringArray `protobuf:"bytes,9,rep,name=region_country,proto3" json:"region_country,omitempty"`        //记者所在国家地区
	SimilarwebUvm    int32          `protobuf:"varint,10,opt,name=similarweb_uvm,proto3" json:"similarweb_uvm,omitempty"`      // Similarweb统计的月访问量
	ComscoreUvm      int32          `protobuf:"varint,11,opt,name=comscore_uvm,proto3" json:"comscore_uvm,omitempty"`          // Comscore统计的月访问量
	DomainAuthority  string         `protobuf:"bytes,12,opt,name=domain_authority,proto3" json:"domain_authority,omitempty"`   // 域名权威评分
	SpamScore        string         `protobuf:"bytes,13,opt,name=spam_score,proto3" json:"spam_score,omitempty"`               // 媒体的垃圾邮件评分
	PublishFrequency string         `protobuf:"bytes,14,opt,name=publish_frequency,proto3" json:"publish_frequency,omitempty"` // 发布频率
	PublishDays      string         `protobuf:"bytes,15,opt,name=publish_days,proto3" json:"publish_days,omitempty"`           // 每周发布日期
	SocialMedias     []*SocialMedia `protobuf:"bytes,16,rep,name=social_medias,proto3" json:"social_medias,omitempty"`         // 媒体的社交媒体信息
	UpdateTime       string         `protobuf:"bytes,17,opt,name=update_time,proto3" json:"update_time,omitempty"`             // 更新时间
	City             string         `protobuf:"bytes,18,opt,name=city,proto3" json:"city,omitempty"`                           // 媒体所在城市
	State            string         `protobuf:"bytes,19,opt,name=state,proto3" json:"state,omitempty"`                         // 媒体所在州
	Email            string         `protobuf:"bytes,20,opt,name=email,proto3" json:"email,omitempty"`                         // 媒体邮箱
	Telephone        string         `protobuf:"bytes,21,opt,name=telephone,proto3" json:"telephone,omitempty"`                 // 媒体电话
}

func (x *ReportMedia) Reset() {
	*x = ReportMedia{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportMedia) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportMedia) ProtoMessage() {}

func (x *ReportMedia) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportMedia.ProtoReflect.Descriptor instead.
func (*ReportMedia) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{2}
}

func (x *ReportMedia) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReportMedia) GetMediaName() string {
	if x != nil {
		return x.MediaName
	}
	return ""
}

func (x *ReportMedia) GetMediaProfile() string {
	if x != nil {
		return x.MediaProfile
	}
	return ""
}

func (x *ReportMedia) GetSiteUrl() string {
	if x != nil {
		return x.SiteUrl
	}
	return ""
}

func (x *ReportMedia) GetMuckrackUrl() string {
	if x != nil {
		return x.MuckrackUrl
	}
	return ""
}

func (x *ReportMedia) GetMuckrackId() string {
	if x != nil {
		return x.MuckrackId
	}
	return ""
}

func (x *ReportMedia) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *ReportMedia) GetLanguage() []string {
	if x != nil {
		return x.Language
	}
	return nil
}

func (x *ReportMedia) GetRegionCountry() []*StringArray {
	if x != nil {
		return x.RegionCountry
	}
	return nil
}

func (x *ReportMedia) GetSimilarwebUvm() int32 {
	if x != nil {
		return x.SimilarwebUvm
	}
	return 0
}

func (x *ReportMedia) GetComscoreUvm() int32 {
	if x != nil {
		return x.ComscoreUvm
	}
	return 0
}

func (x *ReportMedia) GetDomainAuthority() string {
	if x != nil {
		return x.DomainAuthority
	}
	return ""
}

func (x *ReportMedia) GetSpamScore() string {
	if x != nil {
		return x.SpamScore
	}
	return ""
}

func (x *ReportMedia) GetPublishFrequency() string {
	if x != nil {
		return x.PublishFrequency
	}
	return ""
}

func (x *ReportMedia) GetPublishDays() string {
	if x != nil {
		return x.PublishDays
	}
	return ""
}

func (x *ReportMedia) GetSocialMedias() []*SocialMedia {
	if x != nil {
		return x.SocialMedias
	}
	return nil
}

func (x *ReportMedia) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ReportMedia) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *ReportMedia) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ReportMedia) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ReportMedia) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

// 记者资源
type ReporterResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                  //记者资源ID
	Name              string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                               //记者名称
	Title             string         `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`                             //记者职称
	Beats             []string       `protobuf:"bytes,4,rep,name=beats,proto3" json:"beats,omitempty"`                             //记者领域专业
	RegionCountry     []*StringArray `protobuf:"bytes,5,rep,name=region_country,proto3" json:"region_country,omitempty"`           //记者所在国家地区
	Tags              []string       `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`                               //记者标签
	ReportMedia       []*ReportMedia `protobuf:"bytes,7,rep,name=report_media,proto3" json:"report_media,omitempty"`               //记者媒体
	IsEstablishConn   int32          `protobuf:"varint,8,opt,name=is_establish_conn,proto3" json:"is_establish_conn,omitempty"`    //是否建联
	EstablishConnUser string         `protobuf:"bytes,9,opt,name=establish_conn_user,proto3" json:"establish_conn_user,omitempty"` //建联用户
}

func (x *ReporterResource) Reset() {
	*x = ReporterResource{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterResource) ProtoMessage() {}

func (x *ReporterResource) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterResource.ProtoReflect.Descriptor instead.
func (*ReporterResource) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{3}
}

func (x *ReporterResource) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReporterResource) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReporterResource) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ReporterResource) GetBeats() []string {
	if x != nil {
		return x.Beats
	}
	return nil
}

func (x *ReporterResource) GetRegionCountry() []*StringArray {
	if x != nil {
		return x.RegionCountry
	}
	return nil
}

func (x *ReporterResource) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ReporterResource) GetReportMedia() []*ReportMedia {
	if x != nil {
		return x.ReportMedia
	}
	return nil
}

func (x *ReporterResource) GetIsEstablishConn() int32 {
	if x != nil {
		return x.IsEstablishConn
	}
	return 0
}

func (x *ReporterResource) GetEstablishConnUser() string {
	if x != nil {
		return x.EstablishConnUser
	}
	return ""
}

// 记者资源列表返回
type ReporterListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination         `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*ReporterResource `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ReporterListData) Reset() {
	*x = ReporterListData{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterListData) ProtoMessage() {}

func (x *ReporterListData) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterListData.ProtoReflect.Descriptor instead.
func (*ReporterListData) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{4}
}

func (x *ReporterListData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ReporterListData) GetList() []*ReporterResource {
	if x != nil {
		return x.List
	}
	return nil
}

type ReporterResourceListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32             `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string            `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string            `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *ReporterListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ReporterResourceListResponse) Reset() {
	*x = ReporterResourceListResponse{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterResourceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterResourceListResponse) ProtoMessage() {}

func (x *ReporterResourceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterResourceListResponse.ProtoReflect.Descriptor instead.
func (*ReporterResourceListResponse) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{5}
}

func (x *ReporterResourceListResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *ReporterResourceListResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *ReporterResourceListResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *ReporterResourceListResponse) GetData() *ReporterListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type Article struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                    // 文章ID
	ArticleUrl         string `protobuf:"bytes,2,opt,name=article_url,proto3" json:"article_url,omitempty"`                   // 文章链接
	ArticleTitle       string `protobuf:"bytes,3,opt,name=article_title,proto3" json:"article_title,omitempty"`               // 文章标题
	ArticlePublishTime string `protobuf:"bytes,4,opt,name=article_publish_time,proto3" json:"article_publish_time,omitempty"` // 文章发布时间
	ArticleDescription string `protobuf:"bytes,5,opt,name=article_description,proto3" json:"article_description,omitempty"`   // 文章描述
	MediaName          string `protobuf:"bytes,6,opt,name=media_name,proto3" json:"media_name,omitempty"`                     // 媒体名称
}

func (x *Article) Reset() {
	*x = Article{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Article) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Article) ProtoMessage() {}

func (x *Article) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Article.ProtoReflect.Descriptor instead.
func (*Article) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{6}
}

func (x *Article) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Article) GetArticleUrl() string {
	if x != nil {
		return x.ArticleUrl
	}
	return ""
}

func (x *Article) GetArticleTitle() string {
	if x != nil {
		return x.ArticleTitle
	}
	return ""
}

func (x *Article) GetArticlePublishTime() string {
	if x != nil {
		return x.ArticlePublishTime
	}
	return ""
}

func (x *Article) GetArticleDescription() string {
	if x != nil {
		return x.ArticleDescription
	}
	return ""
}

func (x *Article) GetMediaName() string {
	if x != nil {
		return x.MediaName
	}
	return ""
}

// Reporter资源详情返回
type ReporterData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                   //记者资源ID
	Name              string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                //记者名称
	Title             string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`                              //记者职称
	MuckrackUrl       string                 `protobuf:"bytes,4,opt,name=muckrack_url,proto3" json:"muckrack_url,omitempty"`                //记者在muckrack主页链接
	MuckrackId        string                 `protobuf:"bytes,5,opt,name=muckrack_id,proto3" json:"muckrack_id,omitempty"`                  //记者在muckrack的唯一id
	Beats             []string               `protobuf:"bytes,6,rep,name=beats,proto3" json:"beats,omitempty"`                              //记者领域专业
	RegionCountry     []*StringArray         `protobuf:"bytes,7,rep,name=region_country,proto3" json:"region_country,omitempty"`            //记者所在国家地区
	Tags              []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                //记者标签
	Introduction      string                 `protobuf:"bytes,9,opt,name=introduction,proto3" json:"introduction,omitempty"`                //记者简介
	City              string                 `protobuf:"bytes,10,opt,name=city,proto3" json:"city,omitempty"`                               //记者所在城市
	State             string                 `protobuf:"bytes,11,opt,name=state,proto3" json:"state,omitempty"`                             //记者所在州
	PublishFrequency  string                 `protobuf:"bytes,12,opt,name=publish_frequency,proto3" json:"publish_frequency,omitempty"`     // 发布频率
	BackgroundCheck   string                 `protobuf:"bytes,13,opt,name=background_check,proto3" json:"background_check,omitempty"`       // 背景调查
	IsEstablishConn   int32                  `protobuf:"varint,14,opt,name=is_establish_conn,proto3" json:"is_establish_conn,omitempty"`    //是否建联
	EstablishConnUser string                 `protobuf:"bytes,15,opt,name=establish_conn_user,proto3" json:"establish_conn_user,omitempty"` //建联用户
	SocialMedias      []*SocialMedia         `protobuf:"bytes,16,rep,name=social_medias,proto3" json:"social_medias,omitempty"`             // 记者的社交媒体信息
	Contact           []*ReporterContactInfo `protobuf:"bytes,17,rep,name=contact,proto3" json:"contact,omitempty"`                         // 记者的联系方式
	ReportMedia       []*ReportMedia         `protobuf:"bytes,18,rep,name=report_media,proto3" json:"report_media,omitempty"`               //记者媒体
	FirstName         string                 `protobuf:"bytes,19,opt,name=first_name,proto3" json:"first_name,omitempty"`                   //记者名
	LastName          string                 `protobuf:"bytes,20,opt,name=last_name,proto3" json:"last_name,omitempty"`                     //记者姓
	// 详情的联系方式特别拿出来
	ContactInfo *ContactInfo `protobuf:"bytes,21,opt,name=contact_info,proto3" json:"contact_info,omitempty"` // 记者的联系方式
}

func (x *ReporterData) Reset() {
	*x = ReporterData{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterData) ProtoMessage() {}

func (x *ReporterData) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterData.ProtoReflect.Descriptor instead.
func (*ReporterData) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{7}
}

func (x *ReporterData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReporterData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReporterData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ReporterData) GetMuckrackUrl() string {
	if x != nil {
		return x.MuckrackUrl
	}
	return ""
}

func (x *ReporterData) GetMuckrackId() string {
	if x != nil {
		return x.MuckrackId
	}
	return ""
}

func (x *ReporterData) GetBeats() []string {
	if x != nil {
		return x.Beats
	}
	return nil
}

func (x *ReporterData) GetRegionCountry() []*StringArray {
	if x != nil {
		return x.RegionCountry
	}
	return nil
}

func (x *ReporterData) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ReporterData) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *ReporterData) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *ReporterData) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ReporterData) GetPublishFrequency() string {
	if x != nil {
		return x.PublishFrequency
	}
	return ""
}

func (x *ReporterData) GetBackgroundCheck() string {
	if x != nil {
		return x.BackgroundCheck
	}
	return ""
}

func (x *ReporterData) GetIsEstablishConn() int32 {
	if x != nil {
		return x.IsEstablishConn
	}
	return 0
}

func (x *ReporterData) GetEstablishConnUser() string {
	if x != nil {
		return x.EstablishConnUser
	}
	return ""
}

func (x *ReporterData) GetSocialMedias() []*SocialMedia {
	if x != nil {
		return x.SocialMedias
	}
	return nil
}

func (x *ReporterData) GetContact() []*ReporterContactInfo {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *ReporterData) GetReportMedia() []*ReportMedia {
	if x != nil {
		return x.ReportMedia
	}
	return nil
}

func (x *ReporterData) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ReporterData) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *ReporterData) GetContactInfo() *ContactInfo {
	if x != nil {
		return x.ContactInfo
	}
	return nil
}

type ReporterResourceDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32         `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string        `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string        `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *ReporterData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ReporterResourceDetailResponse) Reset() {
	*x = ReporterResourceDetailResponse{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterResourceDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterResourceDetailResponse) ProtoMessage() {}

func (x *ReporterResourceDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterResourceDetailResponse.ProtoReflect.Descriptor instead.
func (*ReporterResourceDetailResponse) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{8}
}

func (x *ReporterResourceDetailResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *ReporterResourceDetailResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *ReporterResourceDetailResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *ReporterResourceDetailResponse) GetData() *ReporterData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AddOrUpdateReportMedia struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MediaName   string `protobuf:"bytes,1,opt,name=media_name,proto3" json:"media_name,omitempty"`     // 媒体名称
	MuckrackUrl string `protobuf:"bytes,2,opt,name=muckrack_url,proto3" json:"muckrack_url,omitempty"` // 媒体在muckrack主页链接
	SiteUrl     string `protobuf:"bytes,3,opt,name=site_url,proto3" json:"site_url,omitempty"`         // 媒体官方主页链接
}

func (x *AddOrUpdateReportMedia) Reset() {
	*x = AddOrUpdateReportMedia{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOrUpdateReportMedia) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOrUpdateReportMedia) ProtoMessage() {}

func (x *AddOrUpdateReportMedia) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOrUpdateReportMedia.ProtoReflect.Descriptor instead.
func (*AddOrUpdateReportMedia) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{9}
}

func (x *AddOrUpdateReportMedia) GetMediaName() string {
	if x != nil {
		return x.MediaName
	}
	return ""
}

func (x *AddOrUpdateReportMedia) GetMuckrackUrl() string {
	if x != nil {
		return x.MuckrackUrl
	}
	return ""
}

func (x *AddOrUpdateReportMedia) GetSiteUrl() string {
	if x != nil {
		return x.SiteUrl
	}
	return ""
}

type AddOrUpdateArticle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleTitle string `protobuf:"bytes,1,opt,name=article_title,proto3" json:"article_title,omitempty"` // 文章标题
	ArticleUrl   string `protobuf:"bytes,2,opt,name=article_url,proto3" json:"article_url,omitempty"`     // 文章链接
}

func (x *AddOrUpdateArticle) Reset() {
	*x = AddOrUpdateArticle{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOrUpdateArticle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOrUpdateArticle) ProtoMessage() {}

func (x *AddOrUpdateArticle) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOrUpdateArticle.ProtoReflect.Descriptor instead.
func (*AddOrUpdateArticle) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{10}
}

func (x *AddOrUpdateArticle) GetArticleTitle() string {
	if x != nil {
		return x.ArticleTitle
	}
	return ""
}

func (x *AddOrUpdateArticle) GetArticleUrl() string {
	if x != nil {
		return x.ArticleUrl
	}
	return ""
}

// Reporter新增/编辑请求
type ReporterAddOrUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int32                     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                //记者资源ID
	FirstName        string                    `protobuf:"bytes,2,opt,name=first_name,proto3" json:"first_name,omitempty"`                 //记者名
	LastName         string                    `protobuf:"bytes,3,opt,name=last_name,proto3" json:"last_name,omitempty"`                   //记者姓
	Title            string                    `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`                           //记者职称
	MuckrackUrl      string                    `protobuf:"bytes,5,opt,name=muckrack_url,proto3" json:"muckrack_url,omitempty"`             //记者在muckrack主页链接
	Introduction     string                    `protobuf:"bytes,6,opt,name=introduction,proto3" json:"introduction,omitempty"`             //记者简介
	RegionCountry    []*StringArray            `protobuf:"bytes,7,rep,name=region_country,proto3" json:"region_country,omitempty"`         //记者所在国家地区
	Beats            []string                  `protobuf:"bytes,8,rep,name=beats,proto3" json:"beats,omitempty"`                           //记者领域专业
	City             string                    `protobuf:"bytes,9,opt,name=city,proto3" json:"city,omitempty"`                             //记者所在城市
	State            string                    `protobuf:"bytes,10,opt,name=state,proto3" json:"state,omitempty"`                          //记者所在州
	PublishFrequency string                    `protobuf:"bytes,11,opt,name=publish_frequency,proto3" json:"publish_frequency,omitempty"`  // 发布频率
	IsEstablishConn  int32                     `protobuf:"varint,12,opt,name=is_establish_conn,proto3" json:"is_establish_conn,omitempty"` //是否建联
	Tags             []string                  `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty"`                            //记者标签
	BackgroundCheck  string                    `protobuf:"bytes,14,opt,name=background_check,proto3" json:"background_check,omitempty"`    // 背景调查
	Contact          []*ReporterContactInfo    `protobuf:"bytes,15,rep,name=contact,proto3" json:"contact,omitempty"`                      // 记者的联系方式
	SocialMedias     []*SocialMedia            `protobuf:"bytes,16,rep,name=social_medias,proto3" json:"social_medias,omitempty"`          // 记者的社交媒体信息
	ReportMedia      []*AddOrUpdateReportMedia `protobuf:"bytes,17,rep,name=report_media,proto3" json:"report_media,omitempty"`            //记者媒体
	Articles         []*AddOrUpdateArticle     `protobuf:"bytes,18,rep,name=articles,proto3" json:"articles,omitempty"`                    // 记者的文章
}

func (x *ReporterAddOrUpdateRequest) Reset() {
	*x = ReporterAddOrUpdateRequest{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterAddOrUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterAddOrUpdateRequest) ProtoMessage() {}

func (x *ReporterAddOrUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterAddOrUpdateRequest.ProtoReflect.Descriptor instead.
func (*ReporterAddOrUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{11}
}

func (x *ReporterAddOrUpdateRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReporterAddOrUpdateRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetMuckrackUrl() string {
	if x != nil {
		return x.MuckrackUrl
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetRegionCountry() []*StringArray {
	if x != nil {
		return x.RegionCountry
	}
	return nil
}

func (x *ReporterAddOrUpdateRequest) GetBeats() []string {
	if x != nil {
		return x.Beats
	}
	return nil
}

func (x *ReporterAddOrUpdateRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetPublishFrequency() string {
	if x != nil {
		return x.PublishFrequency
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetIsEstablishConn() int32 {
	if x != nil {
		return x.IsEstablishConn
	}
	return 0
}

func (x *ReporterAddOrUpdateRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ReporterAddOrUpdateRequest) GetBackgroundCheck() string {
	if x != nil {
		return x.BackgroundCheck
	}
	return ""
}

func (x *ReporterAddOrUpdateRequest) GetContact() []*ReporterContactInfo {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *ReporterAddOrUpdateRequest) GetSocialMedias() []*SocialMedia {
	if x != nil {
		return x.SocialMedias
	}
	return nil
}

func (x *ReporterAddOrUpdateRequest) GetReportMedia() []*AddOrUpdateReportMedia {
	if x != nil {
		return x.ReportMedia
	}
	return nil
}

func (x *ReporterAddOrUpdateRequest) GetArticles() []*AddOrUpdateArticle {
	if x != nil {
		return x.Articles
	}
	return nil
}

type GetReporterArticleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize   int32 `protobuf:"varint,1,opt,name=page_size,proto3" json:"page_size,omitempty"`     //分页大小
	PageNum    int32 `protobuf:"varint,2,opt,name=page_num,proto3" json:"page_num,omitempty"`       //分页页码
	ReporterId int32 `protobuf:"varint,3,opt,name=reporter_id,proto3" json:"reporter_id,omitempty"` // 记者ID
}

func (x *GetReporterArticleRequest) Reset() {
	*x = GetReporterArticleRequest{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReporterArticleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReporterArticleRequest) ProtoMessage() {}

func (x *GetReporterArticleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReporterArticleRequest.ProtoReflect.Descriptor instead.
func (*GetReporterArticleRequest) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{12}
}

func (x *GetReporterArticleRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetReporterArticleRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetReporterArticleRequest) GetReporterId() int32 {
	if x != nil {
		return x.ReporterId
	}
	return 0
}

type GetReporterArticleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32                `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string               `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string               `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *ReporterArticleData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetReporterArticleResponse) Reset() {
	*x = GetReporterArticleResponse{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReporterArticleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReporterArticleResponse) ProtoMessage() {}

func (x *GetReporterArticleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReporterArticleResponse.ProtoReflect.Descriptor instead.
func (*GetReporterArticleResponse) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{13}
}

func (x *GetReporterArticleResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetReporterArticleResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetReporterArticleResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetReporterArticleResponse) GetData() *ReporterArticleData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ReporterArticleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*Article  `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ReporterArticleData) Reset() {
	*x = ReporterArticleData{}
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReporterArticleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterArticleData) ProtoMessage() {}

func (x *ReporterArticleData) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_manager_v1_reporter_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterArticleData.ProtoReflect.Descriptor instead.
func (*ReporterArticleData) Descriptor() ([]byte, []int) {
	return file_api_resource_manager_v1_reporter_proto_rawDescGZIP(), []int{14}
}

func (x *ReporterArticleData) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ReporterArticleData) GetList() []*Article {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_resource_manager_v1_reporter_proto protoreflect.FileDescriptor

var file_api_resource_manager_v1_reporter_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf6, 0x02, 0x0a, 0x13, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72,
	0x61, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x65, 0x61, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x62, 0x65, 0x61, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x65,
	0x73, 0x74, 0x61, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x30, 0x00, 0x30, 0x01, 0x30, 0x02,
	0x52, 0x11, 0x69, 0x73, 0x5f, 0x65, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x63,
	0x6f, 0x6e, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x22, 0x35, 0x0a, 0x0b, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xf3, 0x05, 0x0a, 0x0b, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c,
	0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c,
	0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x0e,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x26,
	0x0a, 0x0e, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x77, 0x65, 0x62, 0x5f, 0x75, 0x76, 0x6d,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x77,
	0x65, 0x62, 0x5f, 0x75, 0x76, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x5f, 0x75, 0x76, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f,
	0x6d, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x75, 0x76, 0x6d, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x70, 0x61, 0x6d, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x70, 0x61, 0x6d,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f,
	0x64, 0x61, 0x79, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x12, 0x46, 0x0a, 0x0d, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x52, 0x0d, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x22, 0xe6, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x62, 0x65, 0x61, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05,
	0x62, 0x65, 0x61, 0x74, 0x73, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52,
	0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x12, 0x44, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x73, 0x5f,
	0x65, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x69, 0x73, 0x5f, 0x65, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x12, 0x30, 0x0a, 0x13, 0x65, 0x73, 0x74, 0x61, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f,
	0x63, 0x6f, 0x6e, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x22, 0x8e, 0x01, 0x0a, 0x10, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x39, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xa1, 0x01, 0x0a, 0x1c, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65,
	0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72,
	0x4d, 0x73, 0x67, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xe7,
	0x01, 0x0a, 0x07, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0d,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xe0, 0x06, 0x0a, 0x0c, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x75, 0x63, 0x6b, 0x72,
	0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x75, 0x63, 0x6b, 0x72,
	0x61, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x75,
	0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x65, 0x61,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x62, 0x65, 0x61, 0x74, 0x73, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x22, 0x0a,
	0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x65, 0x73, 0x74, 0x61,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x11, 0x69, 0x73, 0x5f, 0x65, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x63,
	0x6f, 0x6e, 0x6e, 0x12, 0x30, 0x0a, 0x13, 0x65, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x65, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x6e,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x0d, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x0d,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x73, 0x12, 0x42, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x12, 0x44, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x9f, 0x01, 0x0a, 0x1e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x35, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x8a, 0x01,
	0x0a, 0x16, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x27, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63,
	0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x08, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x08, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x5c, 0x0a, 0x12, 0x41, 0x64,
	0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0xaf, 0x06, 0x0a, 0x1a, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x75, 0x63, 0x6b, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72,
	0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52,
	0x0e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x62, 0x65, 0x61, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05,
	0x62, 0x65, 0x61, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x2c, 0x0a, 0x11, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x39, 0x0a,
	0x11, 0x69, 0x73, 0x5f, 0x65, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x63, 0x6f,
	0x6e, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x30,
	0x00, 0x30, 0x01, 0x30, 0x02, 0x52, 0x11, 0x69, 0x73, 0x5f, 0x65, 0x73, 0x74, 0x61, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x10,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x42, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x46, 0x0a, 0x0d,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x73, 0x18, 0x10, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c,
	0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x0d, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x73, 0x12, 0x4f, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x43, 0x0a, 0x08, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x52, 0x08, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x22, 0x77, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x3c, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x88, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3f, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x30, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x32, 0xb4, 0x07, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x28, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x22, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x49, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x33, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x91, 0x01, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2f, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x12, 0x97, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x2f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21,
	0x3a, 0x01, 0x2a, 0x1a, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x87, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x49, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x1a, 0x23, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x2a, 0x1c, 0x2f,
	0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x12, 0x2e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x31,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x72, 0x5f, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x19, 0x5a, 0x17, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_resource_manager_v1_reporter_proto_rawDescOnce sync.Once
	file_api_resource_manager_v1_reporter_proto_rawDescData = file_api_resource_manager_v1_reporter_proto_rawDesc
)

func file_api_resource_manager_v1_reporter_proto_rawDescGZIP() []byte {
	file_api_resource_manager_v1_reporter_proto_rawDescOnce.Do(func() {
		file_api_resource_manager_v1_reporter_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_resource_manager_v1_reporter_proto_rawDescData)
	})
	return file_api_resource_manager_v1_reporter_proto_rawDescData
}

var file_api_resource_manager_v1_reporter_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_api_resource_manager_v1_reporter_proto_goTypes = []any{
	(*ReporterListRequest)(nil),            // 0: resource_manager.v1.ReporterListRequest
	(*SocialMedia)(nil),                    // 1: resource_manager.v1.SocialMedia
	(*ReportMedia)(nil),                    // 2: resource_manager.v1.ReportMedia
	(*ReporterResource)(nil),               // 3: resource_manager.v1.ReporterResource
	(*ReporterListData)(nil),               // 4: resource_manager.v1.ReporterListData
	(*ReporterResourceListResponse)(nil),   // 5: resource_manager.v1.ReporterResourceListResponse
	(*Article)(nil),                        // 6: resource_manager.v1.Article
	(*ReporterData)(nil),                   // 7: resource_manager.v1.ReporterData
	(*ReporterResourceDetailResponse)(nil), // 8: resource_manager.v1.ReporterResourceDetailResponse
	(*AddOrUpdateReportMedia)(nil),         // 9: resource_manager.v1.AddOrUpdateReportMedia
	(*AddOrUpdateArticle)(nil),             // 10: resource_manager.v1.AddOrUpdateArticle
	(*ReporterAddOrUpdateRequest)(nil),     // 11: resource_manager.v1.ReporterAddOrUpdateRequest
	(*GetReporterArticleRequest)(nil),      // 12: resource_manager.v1.GetReporterArticleRequest
	(*GetReporterArticleResponse)(nil),     // 13: resource_manager.v1.GetReporterArticleResponse
	(*ReporterArticleData)(nil),            // 14: resource_manager.v1.ReporterArticleData
	(*StringArray)(nil),                    // 15: resource_manager.v1.StringArray
	(*Pagination)(nil),                     // 16: resource_manager.v1.Pagination
	(*ReporterContactInfo)(nil),            // 17: resource_manager.v1.ReporterContactInfo
	(*ContactInfo)(nil),                    // 18: resource_manager.v1.ContactInfo
	(*CommonIdParam)(nil),                  // 19: resource_manager.v1.CommonIdParam
	(*CommonResponse)(nil),                 // 20: resource_manager.v1.CommonResponse
}
var file_api_resource_manager_v1_reporter_proto_depIdxs = []int32{
	15, // 0: resource_manager.v1.ReporterListRequest.region_country:type_name -> resource_manager.v1.StringArray
	15, // 1: resource_manager.v1.ReportMedia.region_country:type_name -> resource_manager.v1.StringArray
	1,  // 2: resource_manager.v1.ReportMedia.social_medias:type_name -> resource_manager.v1.SocialMedia
	15, // 3: resource_manager.v1.ReporterResource.region_country:type_name -> resource_manager.v1.StringArray
	2,  // 4: resource_manager.v1.ReporterResource.report_media:type_name -> resource_manager.v1.ReportMedia
	16, // 5: resource_manager.v1.ReporterListData.pagination:type_name -> resource_manager.v1.Pagination
	3,  // 6: resource_manager.v1.ReporterListData.list:type_name -> resource_manager.v1.ReporterResource
	4,  // 7: resource_manager.v1.ReporterResourceListResponse.data:type_name -> resource_manager.v1.ReporterListData
	15, // 8: resource_manager.v1.ReporterData.region_country:type_name -> resource_manager.v1.StringArray
	1,  // 9: resource_manager.v1.ReporterData.social_medias:type_name -> resource_manager.v1.SocialMedia
	17, // 10: resource_manager.v1.ReporterData.contact:type_name -> resource_manager.v1.ReporterContactInfo
	2,  // 11: resource_manager.v1.ReporterData.report_media:type_name -> resource_manager.v1.ReportMedia
	18, // 12: resource_manager.v1.ReporterData.contact_info:type_name -> resource_manager.v1.ContactInfo
	7,  // 13: resource_manager.v1.ReporterResourceDetailResponse.data:type_name -> resource_manager.v1.ReporterData
	15, // 14: resource_manager.v1.ReporterAddOrUpdateRequest.region_country:type_name -> resource_manager.v1.StringArray
	17, // 15: resource_manager.v1.ReporterAddOrUpdateRequest.contact:type_name -> resource_manager.v1.ReporterContactInfo
	1,  // 16: resource_manager.v1.ReporterAddOrUpdateRequest.social_medias:type_name -> resource_manager.v1.SocialMedia
	9,  // 17: resource_manager.v1.ReporterAddOrUpdateRequest.report_media:type_name -> resource_manager.v1.AddOrUpdateReportMedia
	10, // 18: resource_manager.v1.ReporterAddOrUpdateRequest.articles:type_name -> resource_manager.v1.AddOrUpdateArticle
	14, // 19: resource_manager.v1.GetReporterArticleResponse.data:type_name -> resource_manager.v1.ReporterArticleData
	16, // 20: resource_manager.v1.ReporterArticleData.pagination:type_name -> resource_manager.v1.Pagination
	6,  // 21: resource_manager.v1.ReporterArticleData.list:type_name -> resource_manager.v1.Article
	0,  // 22: resource_manager.v1.ResourceManagerReporterService.GetReporterResourceList:input_type -> resource_manager.v1.ReporterListRequest
	19, // 23: resource_manager.v1.ResourceManagerReporterService.GetReporterResourceDetail:input_type -> resource_manager.v1.CommonIdParam
	11, // 24: resource_manager.v1.ResourceManagerReporterService.AddReporterResource:input_type -> resource_manager.v1.ReporterAddOrUpdateRequest
	11, // 25: resource_manager.v1.ResourceManagerReporterService.UpdateReporterResource:input_type -> resource_manager.v1.ReporterAddOrUpdateRequest
	19, // 26: resource_manager.v1.ResourceManagerReporterService.DeleteReporterResource:input_type -> resource_manager.v1.CommonIdParam
	12, // 27: resource_manager.v1.ResourceManagerReporterService.GetReporterArticle:input_type -> resource_manager.v1.GetReporterArticleRequest
	5,  // 28: resource_manager.v1.ResourceManagerReporterService.GetReporterResourceList:output_type -> resource_manager.v1.ReporterResourceListResponse
	8,  // 29: resource_manager.v1.ResourceManagerReporterService.GetReporterResourceDetail:output_type -> resource_manager.v1.ReporterResourceDetailResponse
	20, // 30: resource_manager.v1.ResourceManagerReporterService.AddReporterResource:output_type -> resource_manager.v1.CommonResponse
	20, // 31: resource_manager.v1.ResourceManagerReporterService.UpdateReporterResource:output_type -> resource_manager.v1.CommonResponse
	20, // 32: resource_manager.v1.ResourceManagerReporterService.DeleteReporterResource:output_type -> resource_manager.v1.CommonResponse
	13, // 33: resource_manager.v1.ResourceManagerReporterService.GetReporterArticle:output_type -> resource_manager.v1.GetReporterArticleResponse
	28, // [28:34] is the sub-list for method output_type
	22, // [22:28] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_api_resource_manager_v1_reporter_proto_init() }
func file_api_resource_manager_v1_reporter_proto_init() {
	if File_api_resource_manager_v1_reporter_proto != nil {
		return
	}
	file_api_resource_manager_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_resource_manager_v1_reporter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_resource_manager_v1_reporter_proto_goTypes,
		DependencyIndexes: file_api_resource_manager_v1_reporter_proto_depIdxs,
		MessageInfos:      file_api_resource_manager_v1_reporter_proto_msgTypes,
	}.Build()
	File_api_resource_manager_v1_reporter_proto = out.File
	file_api_resource_manager_v1_reporter_proto_rawDesc = nil
	file_api_resource_manager_v1_reporter_proto_goTypes = nil
	file_api_resource_manager_v1_reporter_proto_depIdxs = nil
}
