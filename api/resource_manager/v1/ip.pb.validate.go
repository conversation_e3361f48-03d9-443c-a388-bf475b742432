// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/resource_manager/v1/ip.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on IpListRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IpListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IpListRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IpListRequestMultiError, or
// nil if none found.
func (m *IpListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IpListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpListRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpListRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpListRequestValidationError{
				field:  "BaseParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpListRequestValidationError{
					field:  "BrandParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpListRequestValidationError{
					field:  "BrandParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpListRequestValidationError{
				field:  "BrandParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpListRequestValidationError{
					field:  "AffiliateParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpListRequestValidationError{
					field:  "AffiliateParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpListRequestValidationError{
				field:  "AffiliateParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return IpListRequestMultiError(errors)
	}

	return nil
}

// IpListRequestMultiError is an error wrapping multiple validation errors
// returned by IpListRequest.ValidateAll() if the designated constraints
// aren't met.
type IpListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IpListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IpListRequestMultiError) AllErrors() []error { return m }

// IpListRequestValidationError is the validation error returned by
// IpListRequest.Validate if the designated constraints aren't met.
type IpListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IpListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IpListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IpListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IpListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IpListRequestValidationError) ErrorName() string { return "IpListRequestValidationError" }

// Error satisfies the builtin error interface
func (e IpListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIpListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IpListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IpListRequestValidationError{}

// Validate checks the field values on IpResource with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IpResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IpResource with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IpResourceMultiError, or
// nil if none found.
func (m *IpResource) ValidateAll() error {
	return m.validate(true)
}

func (m *IpResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	// no validation rules for Homepage

	// no validation rules for Introduction

	// no validation rules for IntroductionDate

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IpResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IpResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IpResourceValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UpdateTime

	if len(errors) > 0 {
		return IpResourceMultiError(errors)
	}

	return nil
}

// IpResourceMultiError is an error wrapping multiple validation errors
// returned by IpResource.ValidateAll() if the designated constraints aren't met.
type IpResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IpResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IpResourceMultiError) AllErrors() []error { return m }

// IpResourceValidationError is the validation error returned by
// IpResource.Validate if the designated constraints aren't met.
type IpResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IpResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IpResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IpResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IpResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IpResourceValidationError) ErrorName() string { return "IpResourceValidationError" }

// Error satisfies the builtin error interface
func (e IpResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIpResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IpResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IpResourceValidationError{}

// Validate checks the field values on IpListData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IpListData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IpListData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IpListDataMultiError, or
// nil if none found.
func (m *IpListData) ValidateAll() error {
	return m.validate(true)
}

func (m *IpListData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpListDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpListDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpListDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IpListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IpListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IpListDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return IpListDataMultiError(errors)
	}

	return nil
}

// IpListDataMultiError is an error wrapping multiple validation errors
// returned by IpListData.ValidateAll() if the designated constraints aren't met.
type IpListDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IpListDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IpListDataMultiError) AllErrors() []error { return m }

// IpListDataValidationError is the validation error returned by
// IpListData.Validate if the designated constraints aren't met.
type IpListDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IpListDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IpListDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IpListDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IpListDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IpListDataValidationError) ErrorName() string { return "IpListDataValidationError" }

// Error satisfies the builtin error interface
func (e IpListDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIpListData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IpListDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IpListDataValidationError{}

// Validate checks the field values on IpResourceListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IpResourceListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IpResourceListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IpResourceListResponseMultiError, or nil if none found.
func (m *IpResourceListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IpResourceListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpResourceListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpResourceListResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpResourceListResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IpResourceListResponseMultiError(errors)
	}

	return nil
}

// IpResourceListResponseMultiError is an error wrapping multiple validation
// errors returned by IpResourceListResponse.ValidateAll() if the designated
// constraints aren't met.
type IpResourceListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IpResourceListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IpResourceListResponseMultiError) AllErrors() []error { return m }

// IpResourceListResponseValidationError is the validation error returned by
// IpResourceListResponse.Validate if the designated constraints aren't met.
type IpResourceListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IpResourceListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IpResourceListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IpResourceListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IpResourceListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IpResourceListResponseValidationError) ErrorName() string {
	return "IpResourceListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IpResourceListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIpResourceListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IpResourceListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IpResourceListResponseValidationError{}

// Validate checks the field values on IpData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IpData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IpData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in IpDataMultiError, or nil if none found.
func (m *IpData) ValidateAll() error {
	return m.validate(true)
}

func (m *IpData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpDataValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpDataValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpDataValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpDataValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpDataValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpDataValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpDataValidationError{
				field:  "AffiliateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IpDataMultiError(errors)
	}

	return nil
}

// IpDataMultiError is an error wrapping multiple validation errors returned by
// IpData.ValidateAll() if the designated constraints aren't met.
type IpDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IpDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IpDataMultiError) AllErrors() []error { return m }

// IpDataValidationError is the validation error returned by IpData.Validate if
// the designated constraints aren't met.
type IpDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IpDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IpDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IpDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IpDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IpDataValidationError) ErrorName() string { return "IpDataValidationError" }

// Error satisfies the builtin error interface
func (e IpDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIpData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IpDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IpDataValidationError{}

// Validate checks the field values on IpResourceDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IpResourceDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IpResourceDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IpResourceDetailResponseMultiError, or nil if none found.
func (m *IpResourceDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IpResourceDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpResourceDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpResourceDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpResourceDetailResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IpResourceDetailResponseMultiError(errors)
	}

	return nil
}

// IpResourceDetailResponseMultiError is an error wrapping multiple validation
// errors returned by IpResourceDetailResponse.ValidateAll() if the designated
// constraints aren't met.
type IpResourceDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IpResourceDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IpResourceDetailResponseMultiError) AllErrors() []error { return m }

// IpResourceDetailResponseValidationError is the validation error returned by
// IpResourceDetailResponse.Validate if the designated constraints aren't met.
type IpResourceDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IpResourceDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IpResourceDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IpResourceDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IpResourceDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IpResourceDetailResponseValidationError) ErrorName() string {
	return "IpResourceDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IpResourceDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIpResourceDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IpResourceDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IpResourceDetailResponseValidationError{}

// Validate checks the field values on IpAddRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IpAddRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IpAddRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IpAddRequestMultiError, or
// nil if none found.
func (m *IpAddRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IpAddRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpAddRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpAddRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpAddRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpAddRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpAddRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpAddRequestValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpAddRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpAddRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpAddRequestValidationError{
				field:  "AffiliateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IpAddRequestMultiError(errors)
	}

	return nil
}

// IpAddRequestMultiError is an error wrapping multiple validation errors
// returned by IpAddRequest.ValidateAll() if the designated constraints aren't met.
type IpAddRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IpAddRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IpAddRequestMultiError) AllErrors() []error { return m }

// IpAddRequestValidationError is the validation error returned by
// IpAddRequest.Validate if the designated constraints aren't met.
type IpAddRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IpAddRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IpAddRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IpAddRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IpAddRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IpAddRequestValidationError) ErrorName() string { return "IpAddRequestValidationError" }

// Error satisfies the builtin error interface
func (e IpAddRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIpAddRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IpAddRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IpAddRequestValidationError{}

// Validate checks the field values on IpUpdateRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IpUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IpUpdateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IpUpdateRequestMultiError, or nil if none found.
func (m *IpUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IpUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpUpdateRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpUpdateRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpUpdateRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpUpdateRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpUpdateRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpUpdateRequestValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IpUpdateRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IpUpdateRequestValidationError{
					field:  "AffiliateInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IpUpdateRequestValidationError{
				field:  "AffiliateInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IpUpdateRequestMultiError(errors)
	}

	return nil
}

// IpUpdateRequestMultiError is an error wrapping multiple validation errors
// returned by IpUpdateRequest.ValidateAll() if the designated constraints
// aren't met.
type IpUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IpUpdateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IpUpdateRequestMultiError) AllErrors() []error { return m }

// IpUpdateRequestValidationError is the validation error returned by
// IpUpdateRequest.Validate if the designated constraints aren't met.
type IpUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IpUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IpUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IpUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IpUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IpUpdateRequestValidationError) ErrorName() string { return "IpUpdateRequestValidationError" }

// Error satisfies the builtin error interface
func (e IpUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIpUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IpUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IpUpdateRequestValidationError{}
