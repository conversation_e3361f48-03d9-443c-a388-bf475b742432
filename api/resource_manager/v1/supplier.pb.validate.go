// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/resource_manager/v1/supplier.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSupplierRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSupplierRequestMultiError, or nil if none found.
func (m *CreateSupplierRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSupplierRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSupplierRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSupplierRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSupplierRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSupplierRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSupplierRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSupplierRequestValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateSupplierRequestMultiError(errors)
	}

	return nil
}

// CreateSupplierRequestMultiError is an error wrapping multiple validation
// errors returned by CreateSupplierRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateSupplierRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSupplierRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSupplierRequestMultiError) AllErrors() []error { return m }

// CreateSupplierRequestValidationError is the validation error returned by
// CreateSupplierRequest.Validate if the designated constraints aren't met.
type CreateSupplierRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSupplierRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSupplierRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSupplierRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSupplierRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSupplierRequestValidationError) ErrorName() string {
	return "CreateSupplierRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSupplierRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSupplierRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSupplierRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSupplierRequestValidationError{}

// Validate checks the field values on UpdateSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSupplierRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSupplierRequestMultiError, or nil if none found.
func (m *UpdateSupplierRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSupplierRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSupplierRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSupplierRequestValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSupplierRequestValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSupplierRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSupplierRequestValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSupplierRequestValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSupplierRequestMultiError(errors)
	}

	return nil
}

// UpdateSupplierRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateSupplierRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateSupplierRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSupplierRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSupplierRequestMultiError) AllErrors() []error { return m }

// UpdateSupplierRequestValidationError is the validation error returned by
// UpdateSupplierRequest.Validate if the designated constraints aren't met.
type UpdateSupplierRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSupplierRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSupplierRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSupplierRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSupplierRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSupplierRequestValidationError) ErrorName() string {
	return "UpdateSupplierRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSupplierRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSupplierRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSupplierRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSupplierRequestValidationError{}

// Validate checks the field values on DeleteSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSupplierRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSupplierRequestMultiError, or nil if none found.
func (m *DeleteSupplierRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSupplierRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteSupplierRequestMultiError(errors)
	}

	return nil
}

// DeleteSupplierRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteSupplierRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteSupplierRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSupplierRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSupplierRequestMultiError) AllErrors() []error { return m }

// DeleteSupplierRequestValidationError is the validation error returned by
// DeleteSupplierRequest.Validate if the designated constraints aren't met.
type DeleteSupplierRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSupplierRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSupplierRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSupplierRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSupplierRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSupplierRequestValidationError) ErrorName() string {
	return "DeleteSupplierRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSupplierRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSupplierRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSupplierRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSupplierRequestValidationError{}

// Validate checks the field values on SupplierResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SupplierResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SupplierResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SupplierResourceMultiError, or nil if none found.
func (m *SupplierResource) ValidateAll() error {
	return m.validate(true)
}

func (m *SupplierResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	// no validation rules for SupplierAttributes

	// no validation rules for CompanyIntroduction

	// no validation rules for Remarks

	// no validation rules for IntroductionDate

	for idx, item := range m.GetRegionCountry() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SupplierResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SupplierResourceValidationError{
						field:  fmt.Sprintf("RegionCountry[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SupplierResourceValidationError{
					field:  fmt.Sprintf("RegionCountry[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UpdateTime

	if len(errors) > 0 {
		return SupplierResourceMultiError(errors)
	}

	return nil
}

// SupplierResourceMultiError is an error wrapping multiple validation errors
// returned by SupplierResource.ValidateAll() if the designated constraints
// aren't met.
type SupplierResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SupplierResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SupplierResourceMultiError) AllErrors() []error { return m }

// SupplierResourceValidationError is the validation error returned by
// SupplierResource.Validate if the designated constraints aren't met.
type SupplierResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SupplierResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SupplierResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SupplierResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SupplierResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SupplierResourceValidationError) ErrorName() string { return "SupplierResourceValidationError" }

// Error satisfies the builtin error interface
func (e SupplierResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSupplierResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SupplierResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SupplierResourceValidationError{}

// Validate checks the field values on SupplierData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SupplierData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SupplierData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SupplierDataMultiError, or
// nil if none found.
func (m *SupplierData) ValidateAll() error {
	return m.validate(true)
}

func (m *SupplierData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SupplierDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SupplierDataValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SupplierDataValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrandInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SupplierDataValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SupplierDataValidationError{
					field:  "BrandInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrandInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SupplierDataValidationError{
				field:  "BrandInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SupplierDataMultiError(errors)
	}

	return nil
}

// SupplierDataMultiError is an error wrapping multiple validation errors
// returned by SupplierData.ValidateAll() if the designated constraints aren't met.
type SupplierDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SupplierDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SupplierDataMultiError) AllErrors() []error { return m }

// SupplierDataValidationError is the validation error returned by
// SupplierData.Validate if the designated constraints aren't met.
type SupplierDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SupplierDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SupplierDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SupplierDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SupplierDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SupplierDataValidationError) ErrorName() string { return "SupplierDataValidationError" }

// Error satisfies the builtin error interface
func (e SupplierDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSupplierData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SupplierDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SupplierDataValidationError{}

// Validate checks the field values on GetSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSupplierRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSupplierRequestMultiError, or nil if none found.
func (m *GetSupplierRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupplierRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetSupplierRequestMultiError(errors)
	}

	return nil
}

// GetSupplierRequestMultiError is an error wrapping multiple validation errors
// returned by GetSupplierRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSupplierRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupplierRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupplierRequestMultiError) AllErrors() []error { return m }

// GetSupplierRequestValidationError is the validation error returned by
// GetSupplierRequest.Validate if the designated constraints aren't met.
type GetSupplierRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupplierRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupplierRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupplierRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupplierRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupplierRequestValidationError) ErrorName() string {
	return "GetSupplierRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupplierRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupplierRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupplierRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupplierRequestValidationError{}

// Validate checks the field values on GetSupplierReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSupplierReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupplierReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSupplierReplyMultiError, or nil if none found.
func (m *GetSupplierReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupplierReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupplierReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupplierReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupplierReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupplierReplyMultiError(errors)
	}

	return nil
}

// GetSupplierReplyMultiError is an error wrapping multiple validation errors
// returned by GetSupplierReply.ValidateAll() if the designated constraints
// aren't met.
type GetSupplierReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupplierReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupplierReplyMultiError) AllErrors() []error { return m }

// GetSupplierReplyValidationError is the validation error returned by
// GetSupplierReply.Validate if the designated constraints aren't met.
type GetSupplierReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupplierReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupplierReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupplierReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupplierReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupplierReplyValidationError) ErrorName() string { return "GetSupplierReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetSupplierReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupplierReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupplierReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupplierReplyValidationError{}

// Validate checks the field values on SupplierList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SupplierList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SupplierList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SupplierListMultiError, or
// nil if none found.
func (m *SupplierList) ValidateAll() error {
	return m.validate(true)
}

func (m *SupplierList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SupplierListValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SupplierListValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SupplierListValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SupplierListValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SupplierListValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SupplierListValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SupplierListMultiError(errors)
	}

	return nil
}

// SupplierListMultiError is an error wrapping multiple validation errors
// returned by SupplierList.ValidateAll() if the designated constraints aren't met.
type SupplierListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SupplierListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SupplierListMultiError) AllErrors() []error { return m }

// SupplierListValidationError is the validation error returned by
// SupplierList.Validate if the designated constraints aren't met.
type SupplierListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SupplierListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SupplierListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SupplierListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SupplierListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SupplierListValidationError) ErrorName() string { return "SupplierListValidationError" }

// Error satisfies the builtin error interface
func (e SupplierListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSupplierList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SupplierListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SupplierListValidationError{}

// Validate checks the field values on ListSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSupplierRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSupplierRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSupplierRequestMultiError, or nil if none found.
func (m *ListSupplierRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSupplierRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListSupplierRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListSupplierRequestValidationError{
					field:  "BaseParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListSupplierRequestValidationError{
				field:  "BaseParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAffiliateParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListSupplierRequestValidationError{
					field:  "AffiliateParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListSupplierRequestValidationError{
					field:  "AffiliateParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAffiliateParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListSupplierRequestValidationError{
				field:  "AffiliateParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerProjectId

	if len(errors) > 0 {
		return ListSupplierRequestMultiError(errors)
	}

	return nil
}

// ListSupplierRequestMultiError is an error wrapping multiple validation
// errors returned by ListSupplierRequest.ValidateAll() if the designated
// constraints aren't met.
type ListSupplierRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSupplierRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSupplierRequestMultiError) AllErrors() []error { return m }

// ListSupplierRequestValidationError is the validation error returned by
// ListSupplierRequest.Validate if the designated constraints aren't met.
type ListSupplierRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSupplierRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSupplierRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSupplierRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSupplierRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSupplierRequestValidationError) ErrorName() string {
	return "ListSupplierRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSupplierRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSupplierRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSupplierRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSupplierRequestValidationError{}

// Validate checks the field values on ListSupplierReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListSupplierReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSupplierReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSupplierReplyMultiError, or nil if none found.
func (m *ListSupplierReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSupplierReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListSupplierReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListSupplierReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListSupplierReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListSupplierReplyMultiError(errors)
	}

	return nil
}

// ListSupplierReplyMultiError is an error wrapping multiple validation errors
// returned by ListSupplierReply.ValidateAll() if the designated constraints
// aren't met.
type ListSupplierReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSupplierReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSupplierReplyMultiError) AllErrors() []error { return m }

// ListSupplierReplyValidationError is the validation error returned by
// ListSupplierReply.Validate if the designated constraints aren't met.
type ListSupplierReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSupplierReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSupplierReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSupplierReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSupplierReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSupplierReplyValidationError) ErrorName() string {
	return "ListSupplierReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListSupplierReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSupplierReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSupplierReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSupplierReplyValidationError{}
