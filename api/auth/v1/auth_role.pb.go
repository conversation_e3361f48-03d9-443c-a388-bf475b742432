// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/auth/v1/auth_role.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddOrUpdateRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`               // 角色名称
	Code        string   `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`               // 角色编码
	Description string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"` // 角色描述
	MenuKeys    []string `protobuf:"bytes,6,rep,name=menu_keys,proto3" json:"menu_keys,omitempty"`     // 菜单的唯一key，这里可打平
}

func (x *AddOrUpdateRoleRequest) Reset() {
	*x = AddOrUpdateRoleRequest{}
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOrUpdateRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOrUpdateRoleRequest) ProtoMessage() {}

func (x *AddOrUpdateRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOrUpdateRoleRequest.ProtoReflect.Descriptor instead.
func (*AddOrUpdateRoleRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_auth_role_proto_rawDescGZIP(), []int{0}
}

func (x *AddOrUpdateRoleRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddOrUpdateRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddOrUpdateRoleRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *AddOrUpdateRoleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddOrUpdateRoleRequest) GetMenuKeys() []string {
	if x != nil {
		return x.MenuKeys
	}
	return nil
}

type GetOrDelRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetOrDelRoleRequest) Reset() {
	*x = GetOrDelRoleRequest{}
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrDelRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrDelRoleRequest) ProtoMessage() {}

func (x *GetOrDelRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrDelRoleRequest.ProtoReflect.Descriptor instead.
func (*GetOrDelRoleRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_auth_role_proto_rawDescGZIP(), []int{1}
}

func (x *GetOrDelRoleRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAuthRoleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32  `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *Role  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetAuthRoleReply) Reset() {
	*x = GetAuthRoleReply{}
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthRoleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthRoleReply) ProtoMessage() {}

func (x *GetAuthRoleReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthRoleReply.ProtoReflect.Descriptor instead.
func (*GetAuthRoleReply) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_auth_role_proto_rawDescGZIP(), []int{2}
}

func (x *GetAuthRoleReply) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *GetAuthRoleReply) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *GetAuthRoleReply) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *GetAuthRoleReply) GetData() *Role {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListAuthRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int32  `protobuf:"varint,1,opt,name=page_num,proto3" json:"page_num,omitempty"`
	PageSize int32  `protobuf:"varint,2,opt,name=page_size,proto3" json:"page_size,omitempty"`
	RoleName string `protobuf:"bytes,3,opt,name=role_name,proto3" json:"role_name,omitempty"`
}

func (x *ListAuthRoleRequest) Reset() {
	*x = ListAuthRoleRequest{}
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAuthRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuthRoleRequest) ProtoMessage() {}

func (x *ListAuthRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuthRoleRequest.ProtoReflect.Descriptor instead.
func (*ListAuthRoleRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_auth_role_proto_rawDescGZIP(), []int{3}
}

func (x *ListAuthRoleRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ListAuthRoleRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAuthRoleRequest) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

type ListAuthRoleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32         `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string        `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string        `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
	Data    *RoleListItem `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListAuthRoleReply) Reset() {
	*x = ListAuthRoleReply{}
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAuthRoleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuthRoleReply) ProtoMessage() {}

func (x *ListAuthRoleReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuthRoleReply.ProtoReflect.Descriptor instead.
func (*ListAuthRoleReply) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_auth_role_proto_rawDescGZIP(), []int{4}
}

func (x *ListAuthRoleReply) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *ListAuthRoleReply) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *ListAuthRoleReply) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *ListAuthRoleReply) GetData() *RoleListItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type RoleListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagination *Pagination `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	List       []*Role     `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *RoleListItem) Reset() {
	*x = RoleListItem{}
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleListItem) ProtoMessage() {}

func (x *RoleListItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_auth_role_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleListItem.ProtoReflect.Descriptor instead.
func (*RoleListItem) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_auth_role_proto_rawDescGZIP(), []int{5}
}

func (x *RoleListItem) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *RoleListItem) GetList() []*Role {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_auth_v1_auth_role_proto protoreflect.FileDescriptor

var file_api_auth_v1_auth_role_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90,
	0x01, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x6e, 0x75, 0x5f, 0x6b, 0x65, 0x79, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x6e, 0x75, 0x5f, 0x6b, 0x65, 0x79,
	0x73, 0x22, 0x25, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x44, 0x65, 0x6c, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x7d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72,
	0x72, 0x4d, 0x73, 0x67, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6d, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72,
	0x72, 0x4d, 0x73, 0x67, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x66, 0x0a, 0x0c, 0x52, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x33, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0xfe, 0x03, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x63, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x3a, 0x01, 0x2a, 0x22, 0x0c, 0x2f, 0x76, 0x31,
	0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x12, 0x66, 0x0a, 0x0e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a,
	0x1a, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x60, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52,
	0x6f, 0x6c, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x44, 0x65, 0x6c, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x11, 0x2a, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x2f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x12, 0x5f, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x52, 0x6f,
	0x6c, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x44, 0x65, 0x6c, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x2f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x62, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x75, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x75, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x6f, 0x6c, 0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x0d, 0x5a, 0x0b, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_v1_auth_role_proto_rawDescOnce sync.Once
	file_api_auth_v1_auth_role_proto_rawDescData = file_api_auth_v1_auth_role_proto_rawDesc
)

func file_api_auth_v1_auth_role_proto_rawDescGZIP() []byte {
	file_api_auth_v1_auth_role_proto_rawDescOnce.Do(func() {
		file_api_auth_v1_auth_role_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_v1_auth_role_proto_rawDescData)
	})
	return file_api_auth_v1_auth_role_proto_rawDescData
}

var file_api_auth_v1_auth_role_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_auth_v1_auth_role_proto_goTypes = []any{
	(*AddOrUpdateRoleRequest)(nil), // 0: auth.v1.AddOrUpdateRoleRequest
	(*GetOrDelRoleRequest)(nil),    // 1: auth.v1.GetOrDelRoleRequest
	(*GetAuthRoleReply)(nil),       // 2: auth.v1.GetAuthRoleReply
	(*ListAuthRoleRequest)(nil),    // 3: auth.v1.ListAuthRoleRequest
	(*ListAuthRoleReply)(nil),      // 4: auth.v1.ListAuthRoleReply
	(*RoleListItem)(nil),           // 5: auth.v1.RoleListItem
	(*Role)(nil),                   // 6: auth.v1.Role
	(*Pagination)(nil),             // 7: auth.v1.Pagination
	(*CommonResponse)(nil),         // 8: auth.v1.CommonResponse
}
var file_api_auth_v1_auth_role_proto_depIdxs = []int32{
	6, // 0: auth.v1.GetAuthRoleReply.data:type_name -> auth.v1.Role
	5, // 1: auth.v1.ListAuthRoleReply.data:type_name -> auth.v1.RoleListItem
	7, // 2: auth.v1.RoleListItem.pagination:type_name -> auth.v1.Pagination
	6, // 3: auth.v1.RoleListItem.list:type_name -> auth.v1.Role
	0, // 4: auth.v1.AuthRole.CreateAuthRole:input_type -> auth.v1.AddOrUpdateRoleRequest
	0, // 5: auth.v1.AuthRole.UpdateAuthRole:input_type -> auth.v1.AddOrUpdateRoleRequest
	1, // 6: auth.v1.AuthRole.DeleteAuthRole:input_type -> auth.v1.GetOrDelRoleRequest
	1, // 7: auth.v1.AuthRole.GetAuthRole:input_type -> auth.v1.GetOrDelRoleRequest
	3, // 8: auth.v1.AuthRole.ListAuthRole:input_type -> auth.v1.ListAuthRoleRequest
	8, // 9: auth.v1.AuthRole.CreateAuthRole:output_type -> auth.v1.CommonResponse
	8, // 10: auth.v1.AuthRole.UpdateAuthRole:output_type -> auth.v1.CommonResponse
	8, // 11: auth.v1.AuthRole.DeleteAuthRole:output_type -> auth.v1.CommonResponse
	2, // 12: auth.v1.AuthRole.GetAuthRole:output_type -> auth.v1.GetAuthRoleReply
	4, // 13: auth.v1.AuthRole.ListAuthRole:output_type -> auth.v1.ListAuthRoleReply
	9, // [9:14] is the sub-list for method output_type
	4, // [4:9] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_auth_v1_auth_role_proto_init() }
func file_api_auth_v1_auth_role_proto_init() {
	if File_api_auth_v1_auth_role_proto != nil {
		return
	}
	file_api_auth_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_v1_auth_role_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_v1_auth_role_proto_goTypes,
		DependencyIndexes: file_api_auth_v1_auth_role_proto_depIdxs,
		MessageInfos:      file_api_auth_v1_auth_role_proto_msgTypes,
	}.Build()
	File_api_auth_v1_auth_role_proto = out.File
	file_api_auth_v1_auth_role_proto_rawDesc = nil
	file_api_auth_v1_auth_role_proto_goTypes = nil
	file_api_auth_v1_auth_role_proto_depIdxs = nil
}
