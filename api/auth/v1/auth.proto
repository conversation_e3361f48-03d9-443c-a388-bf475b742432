syntax = "proto3";

package auth.v1;

option go_package = "api/auth/v1";

import "google/api/annotations.proto";
import "api/auth/v1/common.proto";


// ============== 服务定义START ========================

service AuthService {
  rpc GetUserInfo(Empty) returns (UserInfoResponse) {
    option (google.api.http) = {
      get: "/v1/auth/user_info"
    };
  }

  // 用户管理
  rpc Login (LoginRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/v1/auth/login"
      body: "*"
    };
  }
  rpc FeishuLogin (FeishuLoginRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/v1/auth/feishu_login"
      body: "*"
    };
  }
  rpc AddUser (AddUserRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/auth/add"
      body: "*"
    };
  }
  rpc UserPageList (UserPageListRequest) returns (UserPageListResponse) {
    option (google.api.http) = {
      post: "/v1/auth/list"
      body: "*"
    };
  }
  rpc GetUserSelectList (GetUserSelectListRequest) returns (GetUserSelectListResponse) {
    option (google.api.http) = {
      post: "/v1/auth/select"
      body: "*"
    };
  }
  rpc ResetPassword (ResetPasswordRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/auth/reset_password"
      body: "*"
    };
  }
  rpc UpdateUser (UpdateUserRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/auth/update"
      body: "*"
    };
  }
  rpc DeleteUser (DeleteUserRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/auth/delete"
      body: "*"
    };
  }

  // 组织管理
  rpc AddOrg (AddOrgRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/org/add"
      body: "*"
    };
  }
  rpc UpdateOrg (UpdateOrgRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/org/update"
      body: "*"
    };
  }
  // QueryOrg 获取同级部门人员及下级
  rpc QueryOrgByUser (QueryOrgByUserRequest) returns (QueryOrgByUserResponse) {
    option (google.api.http) = {
      post: "/v1/org/query_by_user"
      body: "*"
    };
  }
  rpc DeleteOrg (DeleteOrgRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/v1/org/delete"
      body: "*"
    };
  }
  // QueryOrgList 获取部门列表
  rpc QueryOrgList (QueryOrgListRequest) returns (QueryOrgListResponse) {
    option (google.api.http) = {
      get: "/v1/org/list"
    };
  }

  // 获取菜单树
  rpc QueryMenuTree (Empty) returns (QueryMenuTreeResponse) {
    option (google.api.http) = {
      get: "/v1/menu/tree"
    };
  }
}

// ============== 服务定义END ===========================


// ================ 请求定义 START ======================
message UserInfoRequest {
  int32 id = 1;
}

message GetUserSelectListRequest {
  string search = 1;
  int32 page_size = 2 [json_name = "page_size"];
  repeated int32 ids = 3;
}

message LoginRequest {
  string email = 1;
  string password = 2;
}

message AddUserRequest {
  string password = 1;
  string email = 2;     // 邮箱
  string first_name = 3 [json_name = "first_name"]; // 名
  string last_name = 4 [json_name = "last_name"];  // 姓
  string phone = 5;      // 电话
  int32 org_id = 6 [json_name = "org_id"];// 部门 ID
  repeated int32 role_ids = 7 [json_name = "role_ids"]; // 角色ID
 }

message AddOrgRequest {
  string org_name = 1 [json_name = "org_name"]; // 组织名称
  int32 parent_id = 2 [json_name = "parent_id"]; // 父组织 ID
  repeated int64 user_ids = 3 [json_name = "user_ids"]; // 用户 ID 列表
}

message UpdateOrgRequest {
  int32 org_id = 1 [json_name = "org_id"];
  string org_name = 2 [json_name = "org_name"];
  repeated int64 user_ids = 3 [json_name = "user_ids"]; // 用户 ID 列表
}

message UserPageListRequest {
  int32 page_num = 1 [json_name = "page_num"];
  int32 page_size = 2 [json_name = "page_size"];
  string email = 3 [json_name = "email"];
  string user_name = 4 [json_name = "user_name"];
  int32 org_id = 5 [json_name = "org_id"];
  int32 user_id = 6 [json_name = "user_id"];
}

message ResetPasswordRequest {
  int32 user_id = 1 [json_name = "user_id"];
  string password = 2 [json_name = "password"];
  string confirm_password = 3 [json_name = "confirm_password"];
}

message UpdateUserRequest {
  int64 id = 1;
  string email = 2;     // 邮箱
  string first_name = 3 [json_name = "first_name"]; // 名
  string last_name = 4 [json_name = "last_name"];  // 姓
  string phone = 5;      // 电话
  int32 org_id = 6 [json_name = "org_id"];// 部门 ID
  repeated int32 role_ids = 7 [json_name = "role_ids"]; // 角色ID
}

message DeleteUserRequest {
  repeated int32 user_ids = 1 [json_name = "user_ids"];
}

message FeishuLoginRequest {
  string code = 1;
  string source_url = 2 [json_name = "source_url"]; // 登录来源 URL
}

message DeleteOrgRequest{
  int32 org_id = 1 [json_name = "org_id"];
}

message QueryOrgByUserRequest{
  string search = 1;
  int32 page_size = 2 [json_name = "page_size"];
  repeated int32 ids = 3;
}

message QueryOrgListRequest{
  int32 org_id = 1 [json_name = "org_id"];
  string search = 2;
  bool is_show_user = 3 [json_name = "is_show_user"];
}
// ================ 请求定义 END ========================


// ================ 响应定义 START ======================
message UserInfo {
  int32 user_id = 1 [json_name = "user_id"];
  string user_name = 2 [json_name = "user_name"];
  string email = 3;
  string first_name = 4 [json_name = "first_name"];
  string last_name = 5 [json_name = "last_name"];
  string phone = 6;
  int32 is_staff = 7 [json_name = "is_staff"];
  repeated GroupInfo group = 8;
}

message GroupInfo {
  int32 id = 1;
  string code = 2;
}

message UserInfoResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  UserInfo data = 4;
}

message LoginResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  LoginInfo data = 4;
}

message LoginInfo {
  string token = 1;
  string email = 2;     // 用户邮箱
  string user_name = 3 [json_name = "user_name"]; // 用户全名
}

message RegisterResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
}

message QueryOrgByUserResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  repeated OrgUser data = 4; // 用户列表
}
message OrgUser{
  int32 user_id = 1 [json_name = "user_id"];
  string email = 2;
  string user_name = 3 [json_name = "user_name"];
}
// 组织数据结构
message OrgData {
  string org_name = 1 [json_name = "org_name"]; // 组织名称
  int32 org_id = 2 [json_name = "org_id"];    // 组织 ID
  repeated OrgUser org_user = 3 [json_name = "org_user"]; // 用户列表
  repeated OrgData children = 4; // 子组织列表
}

message UserPageListResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  UserPageListData data = 4;
}

message UserPageListData {
  repeated UserPageListDataItem list = 1;
  Pagination pagination = 2;
}

message UserPageListDataItem {
  int32 id = 1;
  string email = 2;
  string user_name = 3 [json_name = "user_name"];
  int32 org_id = 4 [json_name = "org_id"];
  string org_name = 5 [json_name = "org_name"];
  string phone = 6;
  string create_time = 7 [json_name = "create_time"];
  string update_time = 8 [json_name = "update_time"];
  string first_name = 9 [json_name = "first_name"]; // 名
  string last_name = 10 [json_name = "last_name"];  // 姓
  repeated int32 role_ids = 11 [json_name = "role_ids"]; // 角色id
  repeated string role_names = 12 [json_name = "role_names"]; // 角色名称
}

message GetUserSelectListResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  repeated CommonUserInfo data = 4;
}

message QueryOrgListResponse{
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  repeated OrgData data = 4;
}

message QueryMenuTreeResponse{
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  repeated MenuTree data = 4;
}

// ================ 响应定义 END ========================
