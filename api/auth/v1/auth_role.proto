syntax = "proto3";

package auth.v1;

option go_package = "api/auth/v1";

import "google/api/annotations.proto";
import "api/auth/v1/common.proto";

service AuthRole {
	rpc CreateAuthRole (AddOrUpdateRoleRequest) returns (CommonResponse){
		option (google.api.http) = {
			post: "/v1/role/add"
			body: "*"
		};
	}
	rpc UpdateAuthRole (AddOrUpdateRoleRequest) returns (CommonResponse){
		option (google.api.http) = {
			put: "/v1/role/update"
			body: "*"
		};
	};
	rpc DeleteAuthRole (GetOrDelRoleRequest) returns (CommonResponse){
		option (google.api.http) = {
			delete: "/v1/role/delete"
		};
	};
	rpc GetAuthRole (GetOrDelRoleRequest) returns (GetAuthRoleReply){
		option (google.api.http) = {
			get: "/v1/role/detail"
		};
	};
	rpc ListAuthRole (ListAuthRoleRequest) returns (ListAuthRoleReply){
		option (google.api.http) = {
			post: "/v1/role/list"
			body: "*"
		};
	};
}

message AddOrUpdateRoleRequest{
	int32 id = 1 [json_name = "id"];
	string name = 2 [json_name = "name"]; // 角色名称
	string code = 3 [json_name = "code"]; // 角色编码
	string description = 4 [json_name = "description"]; // 角色描述
	repeated string menu_keys = 6 [json_name = "menu_keys"]; // 菜单的唯一key，这里可打平
}

message GetOrDelRoleRequest{
	int32 id = 1 [json_name = "id"];
}

message GetAuthRoleReply {
	int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
	string errCode = 2; // 错误标识
	string errMsg = 3; // 错误描述
	Role data = 4;
}

message ListAuthRoleRequest {
	int32 page_num = 1 [json_name = "page_num"];
	int32 page_size = 2 [json_name = "page_size"];
	string role_name = 3 [json_name = "role_name"];
}
message ListAuthRoleReply {
	int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
	string errCode = 2; // 错误标识
	string errMsg = 3; // 错误描述
	RoleListItem data = 4;
}

message RoleListItem{
	Pagination pagination = 1;
	repeated Role list = 2;
}
