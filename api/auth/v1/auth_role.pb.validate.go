// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/v1/auth_role.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AddOrUpdateRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddOrUpdateRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddOrUpdateRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddOrUpdateRoleRequestMultiError, or nil if none found.
func (m *AddOrUpdateRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddOrUpdateRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Code

	// no validation rules for Description

	if len(errors) > 0 {
		return AddOrUpdateRoleRequestMultiError(errors)
	}

	return nil
}

// AddOrUpdateRoleRequestMultiError is an error wrapping multiple validation
// errors returned by AddOrUpdateRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type AddOrUpdateRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddOrUpdateRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddOrUpdateRoleRequestMultiError) AllErrors() []error { return m }

// AddOrUpdateRoleRequestValidationError is the validation error returned by
// AddOrUpdateRoleRequest.Validate if the designated constraints aren't met.
type AddOrUpdateRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddOrUpdateRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddOrUpdateRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddOrUpdateRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddOrUpdateRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddOrUpdateRoleRequestValidationError) ErrorName() string {
	return "AddOrUpdateRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddOrUpdateRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddOrUpdateRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddOrUpdateRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddOrUpdateRoleRequestValidationError{}

// Validate checks the field values on GetOrDelRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOrDelRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOrDelRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOrDelRoleRequestMultiError, or nil if none found.
func (m *GetOrDelRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOrDelRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetOrDelRoleRequestMultiError(errors)
	}

	return nil
}

// GetOrDelRoleRequestMultiError is an error wrapping multiple validation
// errors returned by GetOrDelRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOrDelRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOrDelRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOrDelRoleRequestMultiError) AllErrors() []error { return m }

// GetOrDelRoleRequestValidationError is the validation error returned by
// GetOrDelRoleRequest.Validate if the designated constraints aren't met.
type GetOrDelRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOrDelRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOrDelRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOrDelRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOrDelRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOrDelRoleRequestValidationError) ErrorName() string {
	return "GetOrDelRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOrDelRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOrDelRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOrDelRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOrDelRoleRequestValidationError{}

// Validate checks the field values on GetAuthRoleReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAuthRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthRoleReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAuthRoleReplyMultiError, or nil if none found.
func (m *GetAuthRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthRoleReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthRoleReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthRoleReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAuthRoleReplyMultiError(errors)
	}

	return nil
}

// GetAuthRoleReplyMultiError is an error wrapping multiple validation errors
// returned by GetAuthRoleReply.ValidateAll() if the designated constraints
// aren't met.
type GetAuthRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthRoleReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthRoleReplyMultiError) AllErrors() []error { return m }

// GetAuthRoleReplyValidationError is the validation error returned by
// GetAuthRoleReply.Validate if the designated constraints aren't met.
type GetAuthRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthRoleReplyValidationError) ErrorName() string { return "GetAuthRoleReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetAuthRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthRoleReplyValidationError{}

// Validate checks the field values on ListAuthRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAuthRoleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAuthRoleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAuthRoleRequestMultiError, or nil if none found.
func (m *ListAuthRoleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAuthRoleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageNum

	// no validation rules for PageSize

	// no validation rules for RoleName

	if len(errors) > 0 {
		return ListAuthRoleRequestMultiError(errors)
	}

	return nil
}

// ListAuthRoleRequestMultiError is an error wrapping multiple validation
// errors returned by ListAuthRoleRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAuthRoleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAuthRoleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAuthRoleRequestMultiError) AllErrors() []error { return m }

// ListAuthRoleRequestValidationError is the validation error returned by
// ListAuthRoleRequest.Validate if the designated constraints aren't met.
type ListAuthRoleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAuthRoleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAuthRoleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAuthRoleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAuthRoleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAuthRoleRequestValidationError) ErrorName() string {
	return "ListAuthRoleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAuthRoleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAuthRoleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAuthRoleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAuthRoleRequestValidationError{}

// Validate checks the field values on ListAuthRoleReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListAuthRoleReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAuthRoleReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAuthRoleReplyMultiError, or nil if none found.
func (m *ListAuthRoleReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAuthRoleReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAuthRoleReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAuthRoleReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAuthRoleReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAuthRoleReplyMultiError(errors)
	}

	return nil
}

// ListAuthRoleReplyMultiError is an error wrapping multiple validation errors
// returned by ListAuthRoleReply.ValidateAll() if the designated constraints
// aren't met.
type ListAuthRoleReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAuthRoleReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAuthRoleReplyMultiError) AllErrors() []error { return m }

// ListAuthRoleReplyValidationError is the validation error returned by
// ListAuthRoleReply.Validate if the designated constraints aren't met.
type ListAuthRoleReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAuthRoleReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAuthRoleReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAuthRoleReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAuthRoleReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAuthRoleReplyValidationError) ErrorName() string {
	return "ListAuthRoleReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListAuthRoleReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAuthRoleReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAuthRoleReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAuthRoleReplyValidationError{}

// Validate checks the field values on RoleListItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RoleListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RoleListItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RoleListItemMultiError, or
// nil if none found.
func (m *RoleListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *RoleListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RoleListItemValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RoleListItemValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RoleListItemValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RoleListItemValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RoleListItemValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RoleListItemValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RoleListItemMultiError(errors)
	}

	return nil
}

// RoleListItemMultiError is an error wrapping multiple validation errors
// returned by RoleListItem.ValidateAll() if the designated constraints aren't met.
type RoleListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RoleListItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RoleListItemMultiError) AllErrors() []error { return m }

// RoleListItemValidationError is the validation error returned by
// RoleListItem.Validate if the designated constraints aren't met.
type RoleListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RoleListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RoleListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RoleListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RoleListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RoleListItemValidationError) ErrorName() string { return "RoleListItemValidationError" }

// Error satisfies the builtin error interface
func (e RoleListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRoleListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RoleListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RoleListItemValidationError{}
