// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.29.3
// source: api/auth/v1/auth_role.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAuthRoleCreateAuthRole = "/auth.v1.AuthRole/CreateAuthRole"
const OperationAuthRoleDeleteAuthRole = "/auth.v1.AuthRole/DeleteAuthRole"
const OperationAuthRoleGetAuthRole = "/auth.v1.AuthRole/GetAuthRole"
const OperationAuthRoleListAuthRole = "/auth.v1.AuthRole/ListAuthRole"
const OperationAuthRoleUpdateAuthRole = "/auth.v1.AuthRole/UpdateAuthRole"

type AuthRoleHTTPServer interface {
	CreateAuthRole(context.Context, *AddOrUpdateRoleRequest) (*CommonResponse, error)
	DeleteAuthRole(context.Context, *GetOrDelRoleRequest) (*CommonResponse, error)
	GetAuthRole(context.Context, *GetOrDelRoleRequest) (*GetAuthRoleReply, error)
	ListAuthRole(context.Context, *ListAuthRoleRequest) (*ListAuthRoleReply, error)
	UpdateAuthRole(context.Context, *AddOrUpdateRoleRequest) (*CommonResponse, error)
}

func RegisterAuthRoleHTTPServer(s *http.Server, srv AuthRoleHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/role/add", _AuthRole_CreateAuthRole0_HTTP_Handler(srv))
	r.PUT("/v1/role/update", _AuthRole_UpdateAuthRole0_HTTP_Handler(srv))
	r.DELETE("/v1/role/delete", _AuthRole_DeleteAuthRole0_HTTP_Handler(srv))
	r.GET("/v1/role/detail", _AuthRole_GetAuthRole0_HTTP_Handler(srv))
	r.POST("/v1/role/list", _AuthRole_ListAuthRole0_HTTP_Handler(srv))
}

func _AuthRole_CreateAuthRole0_HTTP_Handler(srv AuthRoleHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddOrUpdateRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthRoleCreateAuthRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAuthRole(ctx, req.(*AddOrUpdateRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _AuthRole_UpdateAuthRole0_HTTP_Handler(srv AuthRoleHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddOrUpdateRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthRoleUpdateAuthRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAuthRole(ctx, req.(*AddOrUpdateRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _AuthRole_DeleteAuthRole0_HTTP_Handler(srv AuthRoleHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOrDelRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthRoleDeleteAuthRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAuthRole(ctx, req.(*GetOrDelRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _AuthRole_GetAuthRole0_HTTP_Handler(srv AuthRoleHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOrDelRoleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthRoleGetAuthRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAuthRole(ctx, req.(*GetOrDelRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAuthRoleReply)
		return ctx.Result(200, reply)
	}
}

func _AuthRole_ListAuthRole0_HTTP_Handler(srv AuthRoleHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListAuthRoleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthRoleListAuthRole)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAuthRole(ctx, req.(*ListAuthRoleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAuthRoleReply)
		return ctx.Result(200, reply)
	}
}

type AuthRoleHTTPClient interface {
	CreateAuthRole(ctx context.Context, req *AddOrUpdateRoleRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	DeleteAuthRole(ctx context.Context, req *GetOrDelRoleRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	GetAuthRole(ctx context.Context, req *GetOrDelRoleRequest, opts ...http.CallOption) (rsp *GetAuthRoleReply, err error)
	ListAuthRole(ctx context.Context, req *ListAuthRoleRequest, opts ...http.CallOption) (rsp *ListAuthRoleReply, err error)
	UpdateAuthRole(ctx context.Context, req *AddOrUpdateRoleRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
}

type AuthRoleHTTPClientImpl struct {
	cc *http.Client
}

func NewAuthRoleHTTPClient(client *http.Client) AuthRoleHTTPClient {
	return &AuthRoleHTTPClientImpl{client}
}

func (c *AuthRoleHTTPClientImpl) CreateAuthRole(ctx context.Context, in *AddOrUpdateRoleRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/role/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthRoleCreateAuthRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthRoleHTTPClientImpl) DeleteAuthRole(ctx context.Context, in *GetOrDelRoleRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/role/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthRoleDeleteAuthRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthRoleHTTPClientImpl) GetAuthRole(ctx context.Context, in *GetOrDelRoleRequest, opts ...http.CallOption) (*GetAuthRoleReply, error) {
	var out GetAuthRoleReply
	pattern := "/v1/role/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthRoleGetAuthRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthRoleHTTPClientImpl) ListAuthRole(ctx context.Context, in *ListAuthRoleRequest, opts ...http.CallOption) (*ListAuthRoleReply, error) {
	var out ListAuthRoleReply
	pattern := "/v1/role/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthRoleListAuthRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthRoleHTTPClientImpl) UpdateAuthRole(ctx context.Context, in *AddOrUpdateRoleRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/v1/role/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthRoleUpdateAuthRole))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
