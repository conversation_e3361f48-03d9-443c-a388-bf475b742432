syntax = "proto3";

package auth.v1;

option go_package = "api/auth/v1";

// 空请求结构
message Empty {}

message CommonUserInfo {
  int32 user_id = 1 [json_name = "user_id"];
  string user_name = 2 [json_name = "user_name"];
  string user_email = 3 [json_name = "user_email"];
}

message Pagination {
  int32 total = 1;
  int32 page_num = 2 [json_name = "page_num"];
  int32 page_size = 3 [json_name = "page_size"];
}

message CommonResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
}


// 业务上单个实体的标准返回
message Group{
  int32 id = 1 [json_name = "id"];
  string name = 2 [json_name = "name"]; // 角色名称
  string code = 3 [json_name = "code"]; // 角色编码
  string description = 4 [json_name = "description"]; // 角色描述
  repeated CommonUserInfo group_users = 5 [json_name = "group_users"]; // 角色成员
}

message MenuTree{
  int32 id = 1 [json_name = "id"];
  string menu_name = 2 [json_name = "menu_name"]; // 菜单名称
  int32 parent_id = 3 [json_name = "parent_id"]; // 父菜单ID
  string api_name = 4 [json_name = "api_name"]; // 菜单路由标识
  string front_api_name = 5 [json_name = "front_api_name"]; // 前端路由标识
  repeated MenuTree children = 6 [json_name = "children"]; // 子菜单
}