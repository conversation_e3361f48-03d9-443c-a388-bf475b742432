// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v5.29.3
// source: api/auth/v1/common.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 空请求结构
type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_api_auth_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_common_proto_rawDescGZIP(), []int{0}
}

type CommonUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int32  `protobuf:"varint,1,opt,name=user_id,proto3" json:"user_id,omitempty"`
	UserName  string `protobuf:"bytes,2,opt,name=user_name,proto3" json:"user_name,omitempty"`
	UserEmail string `protobuf:"bytes,3,opt,name=user_email,proto3" json:"user_email,omitempty"`
}

func (x *CommonUserInfo) Reset() {
	*x = CommonUserInfo{}
	mi := &file_api_auth_v1_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonUserInfo) ProtoMessage() {}

func (x *CommonUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonUserInfo.ProtoReflect.Descriptor instead.
func (*CommonUserInfo) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *CommonUserInfo) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CommonUserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *CommonUserInfo) GetUserEmail() string {
	if x != nil {
		return x.UserEmail
	}
	return ""
}

type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	PageNum  int32 `protobuf:"varint,2,opt,name=page_num,proto3" json:"page_num,omitempty"`
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,proto3" json:"page_size,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_api_auth_v1_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_common_proto_rawDescGZIP(), []int{2}
}

func (x *Pagination) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Pagination) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *Pagination) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrId   int32  `protobuf:"varint,1,opt,name=errId,proto3" json:"errId,omitempty"`    // 错误码，0 表示成功，非 0 表示错误
	ErrCode string `protobuf:"bytes,2,opt,name=errCode,proto3" json:"errCode,omitempty"` // 错误标识
	ErrMsg  string `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`   // 错误描述
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	mi := &file_api_auth_v1_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_common_proto_rawDescGZIP(), []int{3}
}

func (x *CommonResponse) GetErrId() int32 {
	if x != nil {
		return x.ErrId
	}
	return 0
}

func (x *CommonResponse) GetErrCode() string {
	if x != nil {
		return x.ErrCode
	}
	return ""
}

func (x *CommonResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

// 业务上单个实体的标准返回
type Group struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`               // 角色名称
	Code        string            `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`               // 角色编码
	Description string            `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"` // 角色描述
	GroupUsers  []*CommonUserInfo `protobuf:"bytes,5,rep,name=group_users,proto3" json:"group_users,omitempty"` // 角色成员
}

func (x *Group) Reset() {
	*x = Group{}
	mi := &file_api_auth_v1_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Group) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Group) ProtoMessage() {}

func (x *Group) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Group.ProtoReflect.Descriptor instead.
func (*Group) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_common_proto_rawDescGZIP(), []int{4}
}

func (x *Group) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Group) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Group) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Group) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Group) GetGroupUsers() []*CommonUserInfo {
	if x != nil {
		return x.GroupUsers
	}
	return nil
}

type MenuTree struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MenuName     string      `protobuf:"bytes,2,opt,name=menu_name,proto3" json:"menu_name,omitempty"`           // 菜单名称
	ParentId     int32       `protobuf:"varint,3,opt,name=parent_id,proto3" json:"parent_id,omitempty"`          // 父菜单ID
	ApiName      string      `protobuf:"bytes,4,opt,name=api_name,proto3" json:"api_name,omitempty"`             // 菜单路由标识
	FrontApiName string      `protobuf:"bytes,5,opt,name=front_api_name,proto3" json:"front_api_name,omitempty"` // 前端路由标识
	Children     []*MenuTree `protobuf:"bytes,6,rep,name=children,proto3" json:"children,omitempty"`             // 子菜单
}

func (x *MenuTree) Reset() {
	*x = MenuTree{}
	mi := &file_api_auth_v1_common_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MenuTree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MenuTree) ProtoMessage() {}

func (x *MenuTree) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_v1_common_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MenuTree.ProtoReflect.Descriptor instead.
func (*MenuTree) Descriptor() ([]byte, []int) {
	return file_api_auth_v1_common_proto_rawDescGZIP(), []int{5}
}

func (x *MenuTree) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MenuTree) GetMenuName() string {
	if x != nil {
		return x.MenuName
	}
	return ""
}

func (x *MenuTree) GetParentId() int32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *MenuTree) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

func (x *MenuTree) GetFrontApiName() string {
	if x != nil {
		return x.FrontApiName
	}
	return ""
}

func (x *MenuTree) GetChildren() []*MenuTree {
	if x != nil {
		return x.Children
	}
	return nil
}

var File_api_auth_v1_common_proto protoreflect.FileDescriptor

var file_api_auth_v1_common_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x76, 0x31, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x68, 0x0a, 0x0e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x5c, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x22, 0x58, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x72, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x9c,
	0x01, 0x0a, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0xc9, 0x01,
	0x0a, 0x08, 0x4d, 0x65, 0x6e, 0x75, 0x54, 0x72, 0x65, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65,
	0x6e, 0x75, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x65, 0x6e, 0x75, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x63, 0x68,
	0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6e, 0x75, 0x54, 0x72, 0x65, 0x65, 0x52,
	0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x42, 0x0d, 0x5a, 0x0b, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_v1_common_proto_rawDescOnce sync.Once
	file_api_auth_v1_common_proto_rawDescData = file_api_auth_v1_common_proto_rawDesc
)

func file_api_auth_v1_common_proto_rawDescGZIP() []byte {
	file_api_auth_v1_common_proto_rawDescOnce.Do(func() {
		file_api_auth_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_v1_common_proto_rawDescData)
	})
	return file_api_auth_v1_common_proto_rawDescData
}

var file_api_auth_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_auth_v1_common_proto_goTypes = []any{
	(*Empty)(nil),          // 0: auth.v1.Empty
	(*CommonUserInfo)(nil), // 1: auth.v1.CommonUserInfo
	(*Pagination)(nil),     // 2: auth.v1.Pagination
	(*CommonResponse)(nil), // 3: auth.v1.CommonResponse
	(*Group)(nil),          // 4: auth.v1.Group
	(*MenuTree)(nil),       // 5: auth.v1.MenuTree
}
var file_api_auth_v1_common_proto_depIdxs = []int32{
	1, // 0: auth.v1.Group.group_users:type_name -> auth.v1.CommonUserInfo
	5, // 1: auth.v1.MenuTree.children:type_name -> auth.v1.MenuTree
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_auth_v1_common_proto_init() }
func file_api_auth_v1_common_proto_init() {
	if File_api_auth_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_v1_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_auth_v1_common_proto_goTypes,
		DependencyIndexes: file_api_auth_v1_common_proto_depIdxs,
		MessageInfos:      file_api_auth_v1_common_proto_msgTypes,
	}.Build()
	File_api_auth_v1_common_proto = out.File
	file_api_auth_v1_common_proto_rawDesc = nil
	file_api_auth_v1_common_proto_goTypes = nil
	file_api_auth_v1_common_proto_depIdxs = nil
}
