// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/auth/v1/auth_role.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AuthRole_CreateAuthRole_FullMethodName = "/auth.v1.AuthRole/CreateAuthRole"
	AuthRole_UpdateAuthRole_FullMethodName = "/auth.v1.AuthRole/UpdateAuthRole"
	AuthRole_DeleteAuthRole_FullMethodName = "/auth.v1.AuthRole/DeleteAuthRole"
	AuthRole_GetAuthRole_FullMethodName    = "/auth.v1.AuthRole/GetAuthRole"
	AuthRole_ListAuthRole_FullMethodName   = "/auth.v1.AuthRole/ListAuthRole"
)

// AuthRoleClient is the client API for AuthRole service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuthRoleClient interface {
	CreateAuthRole(ctx context.Context, in *AddOrUpdateRoleRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	UpdateAuthRole(ctx context.Context, in *AddOrUpdateRoleRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	DeleteAuthRole(ctx context.Context, in *GetOrDelRoleRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	GetAuthRole(ctx context.Context, in *GetOrDelRoleRequest, opts ...grpc.CallOption) (*GetAuthRoleReply, error)
	ListAuthRole(ctx context.Context, in *ListAuthRoleRequest, opts ...grpc.CallOption) (*ListAuthRoleReply, error)
}

type authRoleClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthRoleClient(cc grpc.ClientConnInterface) AuthRoleClient {
	return &authRoleClient{cc}
}

func (c *authRoleClient) CreateAuthRole(ctx context.Context, in *AddOrUpdateRoleRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AuthRole_CreateAuthRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authRoleClient) UpdateAuthRole(ctx context.Context, in *AddOrUpdateRoleRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AuthRole_UpdateAuthRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authRoleClient) DeleteAuthRole(ctx context.Context, in *GetOrDelRoleRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AuthRole_DeleteAuthRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authRoleClient) GetAuthRole(ctx context.Context, in *GetOrDelRoleRequest, opts ...grpc.CallOption) (*GetAuthRoleReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAuthRoleReply)
	err := c.cc.Invoke(ctx, AuthRole_GetAuthRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authRoleClient) ListAuthRole(ctx context.Context, in *ListAuthRoleRequest, opts ...grpc.CallOption) (*ListAuthRoleReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAuthRoleReply)
	err := c.cc.Invoke(ctx, AuthRole_ListAuthRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthRoleServer is the server API for AuthRole service.
// All implementations must embed UnimplementedAuthRoleServer
// for forward compatibility.
type AuthRoleServer interface {
	CreateAuthRole(context.Context, *AddOrUpdateRoleRequest) (*CommonResponse, error)
	UpdateAuthRole(context.Context, *AddOrUpdateRoleRequest) (*CommonResponse, error)
	DeleteAuthRole(context.Context, *GetOrDelRoleRequest) (*CommonResponse, error)
	GetAuthRole(context.Context, *GetOrDelRoleRequest) (*GetAuthRoleReply, error)
	ListAuthRole(context.Context, *ListAuthRoleRequest) (*ListAuthRoleReply, error)
	mustEmbedUnimplementedAuthRoleServer()
}

// UnimplementedAuthRoleServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAuthRoleServer struct{}

func (UnimplementedAuthRoleServer) CreateAuthRole(context.Context, *AddOrUpdateRoleRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuthRole not implemented")
}
func (UnimplementedAuthRoleServer) UpdateAuthRole(context.Context, *AddOrUpdateRoleRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuthRole not implemented")
}
func (UnimplementedAuthRoleServer) DeleteAuthRole(context.Context, *GetOrDelRoleRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAuthRole not implemented")
}
func (UnimplementedAuthRoleServer) GetAuthRole(context.Context, *GetOrDelRoleRequest) (*GetAuthRoleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthRole not implemented")
}
func (UnimplementedAuthRoleServer) ListAuthRole(context.Context, *ListAuthRoleRequest) (*ListAuthRoleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuthRole not implemented")
}
func (UnimplementedAuthRoleServer) mustEmbedUnimplementedAuthRoleServer() {}
func (UnimplementedAuthRoleServer) testEmbeddedByValue()                  {}

// UnsafeAuthRoleServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthRoleServer will
// result in compilation errors.
type UnsafeAuthRoleServer interface {
	mustEmbedUnimplementedAuthRoleServer()
}

func RegisterAuthRoleServer(s grpc.ServiceRegistrar, srv AuthRoleServer) {
	// If the following call pancis, it indicates UnimplementedAuthRoleServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AuthRole_ServiceDesc, srv)
}

func _AuthRole_CreateAuthRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOrUpdateRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthRoleServer).CreateAuthRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthRole_CreateAuthRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthRoleServer).CreateAuthRole(ctx, req.(*AddOrUpdateRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthRole_UpdateAuthRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOrUpdateRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthRoleServer).UpdateAuthRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthRole_UpdateAuthRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthRoleServer).UpdateAuthRole(ctx, req.(*AddOrUpdateRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthRole_DeleteAuthRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrDelRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthRoleServer).DeleteAuthRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthRole_DeleteAuthRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthRoleServer).DeleteAuthRole(ctx, req.(*GetOrDelRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthRole_GetAuthRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrDelRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthRoleServer).GetAuthRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthRole_GetAuthRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthRoleServer).GetAuthRole(ctx, req.(*GetOrDelRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthRole_ListAuthRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuthRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthRoleServer).ListAuthRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthRole_ListAuthRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthRoleServer).ListAuthRole(ctx, req.(*ListAuthRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AuthRole_ServiceDesc is the grpc.ServiceDesc for AuthRole service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuthRole_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.v1.AuthRole",
	HandlerType: (*AuthRoleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAuthRole",
			Handler:    _AuthRole_CreateAuthRole_Handler,
		},
		{
			MethodName: "UpdateAuthRole",
			Handler:    _AuthRole_UpdateAuthRole_Handler,
		},
		{
			MethodName: "DeleteAuthRole",
			Handler:    _AuthRole_DeleteAuthRole_Handler,
		},
		{
			MethodName: "GetAuthRole",
			Handler:    _AuthRole_GetAuthRole_Handler,
		},
		{
			MethodName: "ListAuthRole",
			Handler:    _AuthRole_ListAuthRole_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/auth/v1/auth_role.proto",
}
