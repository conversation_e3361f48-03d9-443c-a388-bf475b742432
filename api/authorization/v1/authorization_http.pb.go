// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.29.3
// source: api/authorization/v1/authorization.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAuthorizationServiceCreateAuthorization = "/authorization.v1.AuthorizationService/CreateAuthorization"
const OperationAuthorizationServiceListAuthorization = "/authorization.v1.AuthorizationService/ListAuthorization"
const OperationAuthorizationServiceRemoveAuthorization = "/authorization.v1.AuthorizationService/RemoveAuthorization"
const OperationAuthorizationServiceUpdateAuthorization = "/authorization.v1.AuthorizationService/UpdateAuthorization"

type AuthorizationServiceHTTPServer interface {
	CreateAuthorization(context.Context, *CreateAuthorizationRequest) (*CommonResponse, error)
	ListAuthorization(context.Context, *ListAuthorizationRequest) (*ListAuthorizationResponse, error)
	RemoveAuthorization(context.Context, *RemoveAuthorizationRequest) (*CommonResponse, error)
	UpdateAuthorization(context.Context, *UpdateAuthorizationRequest) (*CommonResponse, error)
}

func RegisterAuthorizationServiceHTTPServer(s *http.Server, srv AuthorizationServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/authorization/v1/authorization", _AuthorizationService_CreateAuthorization0_HTTP_Handler(srv))
	r.PUT("/authorization/v1/authorization", _AuthorizationService_UpdateAuthorization0_HTTP_Handler(srv))
	r.GET("/authorization/v1/authorization", _AuthorizationService_ListAuthorization0_HTTP_Handler(srv))
	r.DELETE("/authorization/v1/authorization", _AuthorizationService_RemoveAuthorization0_HTTP_Handler(srv))
}

func _AuthorizationService_CreateAuthorization0_HTTP_Handler(srv AuthorizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAuthorizationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthorizationServiceCreateAuthorization)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAuthorization(ctx, req.(*CreateAuthorizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _AuthorizationService_UpdateAuthorization0_HTTP_Handler(srv AuthorizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAuthorizationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthorizationServiceUpdateAuthorization)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAuthorization(ctx, req.(*UpdateAuthorizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

func _AuthorizationService_ListAuthorization0_HTTP_Handler(srv AuthorizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListAuthorizationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthorizationServiceListAuthorization)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAuthorization(ctx, req.(*ListAuthorizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAuthorizationResponse)
		return ctx.Result(200, reply)
	}
}

func _AuthorizationService_RemoveAuthorization0_HTTP_Handler(srv AuthorizationServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoveAuthorizationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthorizationServiceRemoveAuthorization)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveAuthorization(ctx, req.(*RemoveAuthorizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonResponse)
		return ctx.Result(200, reply)
	}
}

type AuthorizationServiceHTTPClient interface {
	CreateAuthorization(ctx context.Context, req *CreateAuthorizationRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	ListAuthorization(ctx context.Context, req *ListAuthorizationRequest, opts ...http.CallOption) (rsp *ListAuthorizationResponse, err error)
	RemoveAuthorization(ctx context.Context, req *RemoveAuthorizationRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
	UpdateAuthorization(ctx context.Context, req *UpdateAuthorizationRequest, opts ...http.CallOption) (rsp *CommonResponse, err error)
}

type AuthorizationServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAuthorizationServiceHTTPClient(client *http.Client) AuthorizationServiceHTTPClient {
	return &AuthorizationServiceHTTPClientImpl{client}
}

func (c *AuthorizationServiceHTTPClientImpl) CreateAuthorization(ctx context.Context, in *CreateAuthorizationRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/authorization/v1/authorization"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthorizationServiceCreateAuthorization))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthorizationServiceHTTPClientImpl) ListAuthorization(ctx context.Context, in *ListAuthorizationRequest, opts ...http.CallOption) (*ListAuthorizationResponse, error) {
	var out ListAuthorizationResponse
	pattern := "/authorization/v1/authorization"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthorizationServiceListAuthorization))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthorizationServiceHTTPClientImpl) RemoveAuthorization(ctx context.Context, in *RemoveAuthorizationRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/authorization/v1/authorization"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthorizationServiceRemoveAuthorization))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthorizationServiceHTTPClientImpl) UpdateAuthorization(ctx context.Context, in *UpdateAuthorizationRequest, opts ...http.CallOption) (*CommonResponse, error) {
	var out CommonResponse
	pattern := "/authorization/v1/authorization"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthorizationServiceUpdateAuthorization))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
