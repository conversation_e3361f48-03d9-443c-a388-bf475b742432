// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/authorization/v1/authorization.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Authorization with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Authorization) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Authorization with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AuthorizationMultiError, or
// nil if none found.
func (m *Authorization) ValidateAll() error {
	return m.validate(true)
}

func (m *Authorization) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for DisplayName

	// no validation rules for AuthorizationKey

	// no validation rules for AuthorizationType

	// no validation rules for AuthorizationPlatform

	if all {
		switch v := interface{}(m.GetAuthorizationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthorizationValidationError{
					field:  "AuthorizationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthorizationValidationError{
					field:  "AuthorizationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthorizationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthorizationValidationError{
				field:  "AuthorizationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for ExpiresAt

	// no validation rules for CreateUserId

	// no validation rules for CreateTime

	// no validation rules for UpdateTime

	if len(errors) > 0 {
		return AuthorizationMultiError(errors)
	}

	return nil
}

// AuthorizationMultiError is an error wrapping multiple validation errors
// returned by Authorization.ValidateAll() if the designated constraints
// aren't met.
type AuthorizationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthorizationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthorizationMultiError) AllErrors() []error { return m }

// AuthorizationValidationError is the validation error returned by
// Authorization.Validate if the designated constraints aren't met.
type AuthorizationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthorizationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthorizationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthorizationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthorizationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthorizationValidationError) ErrorName() string { return "AuthorizationValidationError" }

// Error satisfies the builtin error interface
func (e AuthorizationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthorization.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthorizationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthorizationValidationError{}

// Validate checks the field values on CreateAuthorizationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAuthorizationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAuthorizationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAuthorizationRequestMultiError, or nil if none found.
func (m *CreateAuthorizationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAuthorizationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisplayName

	// no validation rules for AuthorizationType

	// no validation rules for AuthorizationPlatform

	if all {
		switch v := interface{}(m.GetAuthorizationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAuthorizationRequestValidationError{
					field:  "AuthorizationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAuthorizationRequestValidationError{
					field:  "AuthorizationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthorizationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAuthorizationRequestValidationError{
				field:  "AuthorizationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAuthorizationRequestMultiError(errors)
	}

	return nil
}

// CreateAuthorizationRequestMultiError is an error wrapping multiple
// validation errors returned by CreateAuthorizationRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateAuthorizationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAuthorizationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAuthorizationRequestMultiError) AllErrors() []error { return m }

// CreateAuthorizationRequestValidationError is the validation error returned
// by CreateAuthorizationRequest.Validate if the designated constraints aren't met.
type CreateAuthorizationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAuthorizationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAuthorizationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAuthorizationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAuthorizationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAuthorizationRequestValidationError) ErrorName() string {
	return "CreateAuthorizationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAuthorizationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAuthorizationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAuthorizationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAuthorizationRequestValidationError{}

// Validate checks the field values on UpdateAuthorizationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAuthorizationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAuthorizationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAuthorizationRequestMultiError, or nil if none found.
func (m *UpdateAuthorizationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAuthorizationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AuthorizationType

	// no validation rules for AuthorizationPlatform

	if all {
		switch v := interface{}(m.GetAuthorizationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAuthorizationRequestValidationError{
					field:  "AuthorizationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAuthorizationRequestValidationError{
					field:  "AuthorizationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthorizationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAuthorizationRequestValidationError{
				field:  "AuthorizationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAuthorizationRequestMultiError(errors)
	}

	return nil
}

// UpdateAuthorizationRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateAuthorizationRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateAuthorizationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAuthorizationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAuthorizationRequestMultiError) AllErrors() []error { return m }

// UpdateAuthorizationRequestValidationError is the validation error returned
// by UpdateAuthorizationRequest.Validate if the designated constraints aren't met.
type UpdateAuthorizationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAuthorizationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAuthorizationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAuthorizationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAuthorizationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAuthorizationRequestValidationError) ErrorName() string {
	return "UpdateAuthorizationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAuthorizationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAuthorizationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAuthorizationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAuthorizationRequestValidationError{}

// Validate checks the field values on ListAuthorizationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAuthorizationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAuthorizationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAuthorizationRequestMultiError, or nil if none found.
func (m *ListAuthorizationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAuthorizationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisplayName

	// no validation rules for PageNum

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ListAuthorizationRequestMultiError(errors)
	}

	return nil
}

// ListAuthorizationRequestMultiError is an error wrapping multiple validation
// errors returned by ListAuthorizationRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAuthorizationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAuthorizationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAuthorizationRequestMultiError) AllErrors() []error { return m }

// ListAuthorizationRequestValidationError is the validation error returned by
// ListAuthorizationRequest.Validate if the designated constraints aren't met.
type ListAuthorizationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAuthorizationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAuthorizationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAuthorizationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAuthorizationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAuthorizationRequestValidationError) ErrorName() string {
	return "ListAuthorizationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAuthorizationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAuthorizationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAuthorizationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAuthorizationRequestValidationError{}

// Validate checks the field values on ListAuthorizationResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAuthorizationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAuthorizationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAuthorizationResponseMultiError, or nil if none found.
func (m *ListAuthorizationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAuthorizationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAuthorizationResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAuthorizationResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAuthorizationResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAuthorizationResponseMultiError(errors)
	}

	return nil
}

// ListAuthorizationResponseMultiError is an error wrapping multiple validation
// errors returned by ListAuthorizationResponse.ValidateAll() if the
// designated constraints aren't met.
type ListAuthorizationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAuthorizationResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAuthorizationResponseMultiError) AllErrors() []error { return m }

// ListAuthorizationResponseValidationError is the validation error returned by
// ListAuthorizationResponse.Validate if the designated constraints aren't met.
type ListAuthorizationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAuthorizationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAuthorizationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAuthorizationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAuthorizationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAuthorizationResponseValidationError) ErrorName() string {
	return "ListAuthorizationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAuthorizationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAuthorizationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAuthorizationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAuthorizationResponseValidationError{}

// Validate checks the field values on AuthorizationListData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthorizationListData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthorizationListData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthorizationListDataMultiError, or nil if none found.
func (m *AuthorizationListData) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthorizationListData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AuthorizationListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AuthorizationListDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AuthorizationListDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthorizationListDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthorizationListDataValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthorizationListDataValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthorizationListDataMultiError(errors)
	}

	return nil
}

// AuthorizationListDataMultiError is an error wrapping multiple validation
// errors returned by AuthorizationListData.ValidateAll() if the designated
// constraints aren't met.
type AuthorizationListDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthorizationListDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthorizationListDataMultiError) AllErrors() []error { return m }

// AuthorizationListDataValidationError is the validation error returned by
// AuthorizationListData.Validate if the designated constraints aren't met.
type AuthorizationListDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthorizationListDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthorizationListDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthorizationListDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthorizationListDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthorizationListDataValidationError) ErrorName() string {
	return "AuthorizationListDataValidationError"
}

// Error satisfies the builtin error interface
func (e AuthorizationListDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthorizationListData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthorizationListDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthorizationListDataValidationError{}

// Validate checks the field values on RemoveAuthorizationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveAuthorizationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveAuthorizationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveAuthorizationRequestMultiError, or nil if none found.
func (m *RemoveAuthorizationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveAuthorizationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return RemoveAuthorizationRequestMultiError(errors)
	}

	return nil
}

// RemoveAuthorizationRequestMultiError is an error wrapping multiple
// validation errors returned by RemoveAuthorizationRequest.ValidateAll() if
// the designated constraints aren't met.
type RemoveAuthorizationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveAuthorizationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveAuthorizationRequestMultiError) AllErrors() []error { return m }

// RemoveAuthorizationRequestValidationError is the validation error returned
// by RemoveAuthorizationRequest.Validate if the designated constraints aren't met.
type RemoveAuthorizationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveAuthorizationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveAuthorizationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveAuthorizationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveAuthorizationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveAuthorizationRequestValidationError) ErrorName() string {
	return "RemoveAuthorizationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveAuthorizationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveAuthorizationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveAuthorizationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveAuthorizationRequestValidationError{}

// Validate checks the field values on GetAuthorizationDetailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAuthorizationDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthorizationDetailRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAuthorizationDetailRequestMultiError, or nil if none found.
func (m *GetAuthorizationDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthorizationDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetAuthorizationDetailRequestMultiError(errors)
	}

	return nil
}

// GetAuthorizationDetailRequestMultiError is an error wrapping multiple
// validation errors returned by GetAuthorizationDetailRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAuthorizationDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthorizationDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthorizationDetailRequestMultiError) AllErrors() []error { return m }

// GetAuthorizationDetailRequestValidationError is the validation error
// returned by GetAuthorizationDetailRequest.Validate if the designated
// constraints aren't met.
type GetAuthorizationDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthorizationDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthorizationDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthorizationDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthorizationDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthorizationDetailRequestValidationError) ErrorName() string {
	return "GetAuthorizationDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthorizationDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthorizationDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthorizationDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthorizationDetailRequestValidationError{}

// Validate checks the field values on GetAuthorizationDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAuthorizationDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAuthorizationDetailResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAuthorizationDetailResponseMultiError, or nil if none found.
func (m *GetAuthorizationDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAuthorizationDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrId

	// no validation rules for ErrCode

	// no validation rules for ErrMsg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAuthorizationDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAuthorizationDetailResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAuthorizationDetailResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAuthorizationDetailResponseMultiError(errors)
	}

	return nil
}

// GetAuthorizationDetailResponseMultiError is an error wrapping multiple
// validation errors returned by GetAuthorizationDetailResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAuthorizationDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAuthorizationDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAuthorizationDetailResponseMultiError) AllErrors() []error { return m }

// GetAuthorizationDetailResponseValidationError is the validation error
// returned by GetAuthorizationDetailResponse.Validate if the designated
// constraints aren't met.
type GetAuthorizationDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAuthorizationDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAuthorizationDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAuthorizationDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAuthorizationDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAuthorizationDetailResponseValidationError) ErrorName() string {
	return "GetAuthorizationDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAuthorizationDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAuthorizationDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAuthorizationDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAuthorizationDetailResponseValidationError{}
