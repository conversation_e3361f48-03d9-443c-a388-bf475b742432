// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/authorization/v1/authorization.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AuthorizationService_CreateAuthorization_FullMethodName    = "/authorization.v1.AuthorizationService/CreateAuthorization"
	AuthorizationService_UpdateAuthorization_FullMethodName    = "/authorization.v1.AuthorizationService/UpdateAuthorization"
	AuthorizationService_ListAuthorization_FullMethodName      = "/authorization.v1.AuthorizationService/ListAuthorization"
	AuthorizationService_RemoveAuthorization_FullMethodName    = "/authorization.v1.AuthorizationService/RemoveAuthorization"
	AuthorizationService_GetAuthorizationDetail_FullMethodName = "/authorization.v1.AuthorizationService/GetAuthorizationDetail"
)

// AuthorizationServiceClient is the client API for AuthorizationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuthorizationServiceClient interface {
	CreateAuthorization(ctx context.Context, in *CreateAuthorizationRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	UpdateAuthorization(ctx context.Context, in *UpdateAuthorizationRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	ListAuthorization(ctx context.Context, in *ListAuthorizationRequest, opts ...grpc.CallOption) (*ListAuthorizationResponse, error)
	RemoveAuthorization(ctx context.Context, in *RemoveAuthorizationRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	GetAuthorizationDetail(ctx context.Context, in *GetAuthorizationDetailRequest, opts ...grpc.CallOption) (*GetAuthorizationDetailResponse, error)
}

type authorizationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthorizationServiceClient(cc grpc.ClientConnInterface) AuthorizationServiceClient {
	return &authorizationServiceClient{cc}
}

func (c *authorizationServiceClient) CreateAuthorization(ctx context.Context, in *CreateAuthorizationRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AuthorizationService_CreateAuthorization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authorizationServiceClient) UpdateAuthorization(ctx context.Context, in *UpdateAuthorizationRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AuthorizationService_UpdateAuthorization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authorizationServiceClient) ListAuthorization(ctx context.Context, in *ListAuthorizationRequest, opts ...grpc.CallOption) (*ListAuthorizationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAuthorizationResponse)
	err := c.cc.Invoke(ctx, AuthorizationService_ListAuthorization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authorizationServiceClient) RemoveAuthorization(ctx context.Context, in *RemoveAuthorizationRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, AuthorizationService_RemoveAuthorization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authorizationServiceClient) GetAuthorizationDetail(ctx context.Context, in *GetAuthorizationDetailRequest, opts ...grpc.CallOption) (*GetAuthorizationDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAuthorizationDetailResponse)
	err := c.cc.Invoke(ctx, AuthorizationService_GetAuthorizationDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthorizationServiceServer is the server API for AuthorizationService service.
// All implementations must embed UnimplementedAuthorizationServiceServer
// for forward compatibility.
type AuthorizationServiceServer interface {
	CreateAuthorization(context.Context, *CreateAuthorizationRequest) (*CommonResponse, error)
	UpdateAuthorization(context.Context, *UpdateAuthorizationRequest) (*CommonResponse, error)
	ListAuthorization(context.Context, *ListAuthorizationRequest) (*ListAuthorizationResponse, error)
	RemoveAuthorization(context.Context, *RemoveAuthorizationRequest) (*CommonResponse, error)
	GetAuthorizationDetail(context.Context, *GetAuthorizationDetailRequest) (*GetAuthorizationDetailResponse, error)
	mustEmbedUnimplementedAuthorizationServiceServer()
}

// UnimplementedAuthorizationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAuthorizationServiceServer struct{}

func (UnimplementedAuthorizationServiceServer) CreateAuthorization(context.Context, *CreateAuthorizationRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuthorization not implemented")
}
func (UnimplementedAuthorizationServiceServer) UpdateAuthorization(context.Context, *UpdateAuthorizationRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuthorization not implemented")
}
func (UnimplementedAuthorizationServiceServer) ListAuthorization(context.Context, *ListAuthorizationRequest) (*ListAuthorizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuthorization not implemented")
}
func (UnimplementedAuthorizationServiceServer) RemoveAuthorization(context.Context, *RemoveAuthorizationRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAuthorization not implemented")
}
func (UnimplementedAuthorizationServiceServer) GetAuthorizationDetail(context.Context, *GetAuthorizationDetailRequest) (*GetAuthorizationDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthorizationDetail not implemented")
}
func (UnimplementedAuthorizationServiceServer) mustEmbedUnimplementedAuthorizationServiceServer() {}
func (UnimplementedAuthorizationServiceServer) testEmbeddedByValue()                              {}

// UnsafeAuthorizationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthorizationServiceServer will
// result in compilation errors.
type UnsafeAuthorizationServiceServer interface {
	mustEmbedUnimplementedAuthorizationServiceServer()
}

func RegisterAuthorizationServiceServer(s grpc.ServiceRegistrar, srv AuthorizationServiceServer) {
	// If the following call pancis, it indicates UnimplementedAuthorizationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AuthorizationService_ServiceDesc, srv)
}

func _AuthorizationService_CreateAuthorization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAuthorizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthorizationServiceServer).CreateAuthorization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthorizationService_CreateAuthorization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthorizationServiceServer).CreateAuthorization(ctx, req.(*CreateAuthorizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthorizationService_UpdateAuthorization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAuthorizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthorizationServiceServer).UpdateAuthorization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthorizationService_UpdateAuthorization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthorizationServiceServer).UpdateAuthorization(ctx, req.(*UpdateAuthorizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthorizationService_ListAuthorization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuthorizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthorizationServiceServer).ListAuthorization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthorizationService_ListAuthorization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthorizationServiceServer).ListAuthorization(ctx, req.(*ListAuthorizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthorizationService_RemoveAuthorization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveAuthorizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthorizationServiceServer).RemoveAuthorization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthorizationService_RemoveAuthorization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthorizationServiceServer).RemoveAuthorization(ctx, req.(*RemoveAuthorizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthorizationService_GetAuthorizationDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthorizationDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthorizationServiceServer).GetAuthorizationDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthorizationService_GetAuthorizationDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthorizationServiceServer).GetAuthorizationDetail(ctx, req.(*GetAuthorizationDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AuthorizationService_ServiceDesc is the grpc.ServiceDesc for AuthorizationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuthorizationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "authorization.v1.AuthorizationService",
	HandlerType: (*AuthorizationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAuthorization",
			Handler:    _AuthorizationService_CreateAuthorization_Handler,
		},
		{
			MethodName: "UpdateAuthorization",
			Handler:    _AuthorizationService_UpdateAuthorization_Handler,
		},
		{
			MethodName: "ListAuthorization",
			Handler:    _AuthorizationService_ListAuthorization_Handler,
		},
		{
			MethodName: "RemoveAuthorization",
			Handler:    _AuthorizationService_RemoveAuthorization_Handler,
		},
		{
			MethodName: "GetAuthorizationDetail",
			Handler:    _AuthorizationService_GetAuthorizationDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/authorization/v1/authorization.proto",
}
