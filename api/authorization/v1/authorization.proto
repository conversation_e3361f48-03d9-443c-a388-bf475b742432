syntax = "proto3";

package authorization.v1;

option go_package = "api/authorization/v1";

import "google/api/annotations.proto";
import "api/authorization/v1/common.proto";
import "google/protobuf/struct.proto";

service AuthorizationService {
  rpc CreateAuthorization(CreateAuthorizationRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/authorization/v1/authorization"
      body: "*"
    };
  }
  rpc UpdateAuthorization(UpdateAuthorizationRequest) returns (CommonResponse) {
    option (google.api.http) = {
      put: "/authorization/v1/authorization"
      body: "*"
    };
  }
  rpc ListAuthorization(ListAuthorizationRequest) returns (ListAuthorizationResponse) {
    option (google.api.http) = {
      get: "/authorization/v1/authorization"
    };
  }
  rpc RemoveAuthorization(RemoveAuthorizationRequest) returns (CommonResponse) {
    option (google.api.http) = {
      delete: "/authorization/v1/authorization"
    };
  }
  rpc GetAuthorizationDetail(GetAuthorizationDetailRequest) returns (GetAuthorizationDetailResponse) {
    option (google.api.http) = {
      get: "/authorization/v1/authorization/detail"
    };
  }
}
// ============== 服务定义START ========================

// 授权信息
message Authorization {
  int32 id = 1 [json_name = "id"];
  string name = 2 [json_name = "name"];
  string authorization_type = 3 [json_name = "authorization_type"];
  string authorization_platform = 4 [json_name = "authorization_platform"];
  google.protobuf.Struct authorization_config = 5 [json_name = "authorization_config"];
  string status = 6 [json_name = "status"];
  string expires_at = 7 [json_name = "expires_at"];
  int32 create_user_id = 8 [json_name = "create_user_id"];
}

// 新增授权
message CreateAuthorizationRequest {
  string name = 1 [json_name = "name"];
  string authorization_type = 2 [json_name = "authorization_type"];
  string authorization_platform = 3 [json_name = "authorization_platform"];
  google.protobuf.Struct authorization_config = 4 [json_name = "authorization_config"];
}

// 更新授权
message UpdateAuthorizationRequest {
  int32 id = 1 [json_name = "id"];
  string name = 2 [json_name = "name"];
  string authorization_type = 3 [json_name = "authorization_type"];
  string authorization_platform = 4 [json_name = "authorization_platform"];
  google.protobuf.Struct authorization_config = 5 [json_name = "authorization_config"];
  string status = 6 [json_name = "status"];
}

// 授权列表
message ListAuthorizationRequest {
  string name = 1 [json_name = "name"];
  repeated string authorization_platform = 2 [json_name = "authorization_platform"];
  int32 page_num = 3 [json_name = "page_num"];
  int32 page_size = 4 [json_name = "page_size"];
}

message ListAuthorizationResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  AuthorizationListData data = 4;
}

message AuthorizationListData {
  repeated Authorization list = 1 [json_name = "list"];
  Pagination pagination = 2 [json_name = "pagination"];
}

// 移除授权
message RemoveAuthorizationRequest {
  int32 id = 1 [json_name = "id"];
}

// 获取授权详情
message GetAuthorizationDetailRequest {
  int32 id = 1 [json_name = "id"];
}

message GetAuthorizationDetailResponse {
  int32 errId = 1; // 错误码，0 表示成功，非 0 表示错误
  string errCode = 2; // 错误标识
  string errMsg = 3; // 错误描述
  Authorization data = 4;
}




